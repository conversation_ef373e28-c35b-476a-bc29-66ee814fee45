﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region ThPio and List
    [Serializable]
    [Description("Port通讯历史")]
    [LinqToDB.Mapping.Table("TH_PIO")]
    public partial class ThPio : GEntity<ThPio>
    {
        #region Contructor(s)

        private ThPio()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_ConnectModule = RegisterProperty<String>(p => p.ConnectModule);
        private static readonly PropertyInfo<String> pty_Direction = RegisterProperty<String>(p => p.Direction);
        private static readonly PropertyInfo<Int64> pty_Id = RegisterProperty<Int64>(p => p.Id);
        private static readonly PropertyInfo<String> pty_IoName = RegisterProperty<String>(p => p.IoName);
        private static readonly PropertyInfo<Int32?> pty_IoValue = RegisterProperty<Int32?>(p => p.IoValue);
        private static readonly PropertyInfo<String> pty_PortName = RegisterProperty<String>(p => p.PortName);
        private static readonly PropertyInfo<DateTime?> pty_Time = RegisterProperty<DateTime?>(p => p.Time);
        #endregion

        /// <summary>
        /// ConnectModule
        /// </summary>
        [Description("ConnectModule")]
        [LinqToDB.Mapping.Column("Connect_Module")]
        public String ConnectModule
        {
            get { return GetProperty(pty_ConnectModule); }
            set { SetProperty(pty_ConnectModule, value); }
        }
        /// <summary>
        /// Direction
        /// </summary>
        [Description("Direction")]
        [LinqToDB.Mapping.Column("Direction")]
        public String Direction
        {
            get { return GetProperty(pty_Direction); }
            set { SetProperty(pty_Direction, value); }
        }
        /// <summary>
        /// PrimaryKey
        /// </summary>
        [Description("PrimaryKey")]
        [LinqToDB.Mapping.Column("ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int64 Id
        {
            get { return GetProperty(pty_Id); }
            set { SetProperty(pty_Id, value); }
        }
        /// <summary>
        /// IOName
        /// </summary>
        [Description("IOName")]
        [LinqToDB.Mapping.Column("IO_Name")]
        public String IoName
        {
            get { return GetProperty(pty_IoName); }
            set { SetProperty(pty_IoName, value); }
        }
        /// <summary>
        /// IOValue
        /// </summary>
        [Description("IOValue")]
        [LinqToDB.Mapping.Column("IO_Value")]
        public Int32? IoValue
        {
            get { return GetProperty(pty_IoValue); }
            set { SetProperty(pty_IoValue, value); }
        }
        /// <summary>
        /// PortName
        /// </summary>
        [Description("PortName")]
        [LinqToDB.Mapping.Column("Port_Name")]
        public String PortName
        {
            get { return GetProperty(pty_PortName); }
            set { SetProperty(pty_PortName, value); }
        }
        /// <summary>
        /// Time
        /// </summary>
        [Description("Time")]
        [LinqToDB.Mapping.Column("Time")]
        public DateTime? Time
        {
            get { return GetProperty(pty_Time); }
            set { SetProperty(pty_Time, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_ConnectModule, 64, "ConnectModule不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Direction, 32, "Direction不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Id, "PrimaryKey是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_IoName, 64, "IOName不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_PortName, 32, "PortName不能超过32个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Id.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class ThPioList : GEntityList<ThPioList, ThPio>
    {
        private ThPioList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
