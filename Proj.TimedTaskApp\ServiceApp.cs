﻿
using Proj.Entity;
using System;
using System.Collections;
using System.IO;
using System.Reflection;
using System.ServiceProcess;
using System.Threading;
using System.Xml;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using SS.Base;
using SS.Entity;
using SS.CslaBase;
using SS.BLL.Base;
using Proj.TimedTaskApp.Base;

namespace Proj.TimedTaskApp
{
    public partial class ServiceApp : ServiceBase
    {
        //用哈希表存放任务项
        private Hashtable hashJobs;
        private bool isRunning = true;
        private string Log = System.Windows.Forms.Application.StartupPath.ToString() + @"\" + "AppLog.txt";
        /// <summary>
        /// 框架代码
        /// 首先把所有任务都放进内存（Hashtable），这个过程使用了反射。
        /// 然后使用托管的线程池执行多任务。
        /// </summary>
        public ServiceApp()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            //启动服务
            this.runJobs();
        }

        protected override void OnStop()
        {
            //停止服务
            this.stopJobs();
        }

        protected override void OnCustomCommand(int command)
        {
            if(command == 128)
            {
                this.isRunning = false;
            }
            else if(command == 129)
            {
                this.isRunning = true;
                //启动服务
                this.restartJobs();
            }
        }

        #region 自定义方法

        private void restartJobs()
        {
            try
            {
                TntJobsgroupList list = TntJobsgroupList.GetByLambda(x => x.CEnabled.Equals("1"));
                Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + "\\" + "Error.txt", list.Count.ToString());

                foreach (TntJobsgroup item in list)
                {
                    if (!this.hashJobs.ContainsKey(item.CPk))
                    {
                        //创建工作对象
                        Job job = new Base.Job(item);
                        //将工作对象加载进HashTable
                        this.hashJobs.Add(item.CPk, job);
                        System.Threading.ThreadPool.QueueUserWorkItem(threadCallBack, item.CPk);
                    }
                    else
                    {
                        (this.hashJobs[item.CPk] as Job).ConfigObject = item;
                    }
                }
                // 获取失效列表
                TntJobsgroupList disablelist = TntJobsgroupList.GetByLambda(x => x.CEnabled.Equals("0"));
                foreach (TntJobsgroup item in disablelist)
                {
                    if (this.hashJobs.ContainsKey(item.CPk))
                    {
                        // 移除失效列表
                        this.hashJobs.Remove(item.CPk);
                    }
                }
            }
            catch (Exception error)
            {
                Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + "\\" + "Error.txt", error.ToString());
            }
        }

        private void runJobs()
        {
            try
            {             
                //加载工作项
                if (this.hashJobs == null || this.hashJobs.Count == 0)
                {
                    hashJobs = new Hashtable();
                    TntJobsgroupList list = TntJobsgroupList.GetByLambda(x => x.CEnabled.Equals("1"));
                    Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + "\\" + "Error.txt", "--" + list.Count.ToString());

                    foreach (TntJobsgroup item in list)
                    {
                        //创建工作对象
                        Job job = new Base.Job(item);

                        //将工作对象加载进HashTable
                        this.hashJobs.Add(item.CPk, job);
                    }
                }

                //执行工作项
                if (this.hashJobs.Keys.Count > 0)
                {
                    foreach (Base.Job job in hashJobs.Values)
                    {                    
                        //插入一个新的请求到线程池
                        if (System.Threading.ThreadPool.QueueUserWorkItem(threadCallBack, job.ConfigObject.CPk))
                        {
                            //方法成功排入队列
                        }
                        else
                        {
                            //失败
                        }
                    }
                }
            }
            catch (Exception error)
            {
                Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() +"\\" + "Error.txt", error.ToString());
            }
        }

        private void stopJobs()
        {
            //停止
            if (this.hashJobs != null)
            {
                this.hashJobs.Clear();
            }
        }

        /// <summary>
        /// 线程池回调方法
        /// </summary>
        /// <param name="state"></param>
        private void threadCallBack(Object state)
        {
            while (true)
            {
                if (isRunning)
                {
                    if (this.hashJobs.ContainsKey(state))
                    {
                        ((Base.Job)this.hashJobs[state]).StartJob();
                    }
                }
                //休眠1分钟
                Thread.Sleep(1000 * 60);
            }
        }

        #endregion

    }//end class
}

