using Microsoft.AspNetCore.SignalR.Client;
using Proj.Log;

namespace Proj.Service.Tests
{
    /// <summary>
    /// SignalR测试客户端
    /// </summary>
    public class SignalRTestClient : IDisposable
    {
        private readonly Logger _logger;
        private readonly HubConnection _connection;
        private bool _disposed = false;

        public SignalRTestClient(string hubUrl = "http://localhost:9900/stockerhub")
        {
            _logger = Logger.Instance;
            
            _connection = new HubConnectionBuilder()
                .WithUrl(hubUrl)
                .WithAutomaticReconnect()
                .Build();

            // 注册服务端消息处理器
            _connection.On<string, Dictionary<string, object>>("ServerSendMessage", OnServerSendMessage);
            _connection.On<object>("OnRegistered", OnRegistered);
            _connection.On<object>("OnError", OnError);
        }

        /// <summary>
        /// 连接到服务器
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectAsync()
        {
            try
            {
                _logger.Info("Connecting to SignalR hub...");
                await _connection.StartAsync();
                _logger.Info($"Connected to SignalR hub. Connection ID: {_connection.ConnectionId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"ConnectAsync error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            try
            {
                if (_connection.State == HubConnectionState.Connected)
                {
                    await _connection.StopAsync();
                    _logger.Info("Disconnected from SignalR hub");
                }
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"DisconnectAsync error: {ex.Message}");
            }
        }

        /// <summary>
        /// 注册客户端
        /// </summary>
        /// <returns>注册是否成功</returns>
        public async Task<bool> RegisterAsync()
        {
            try
            {
                _logger.Info("Registering client...");
                await _connection.InvokeAsync("Register");
                return true;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"RegisterAsync error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送消息到服务器
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>服务器响应</returns>
        public async Task<object?> SendMessageAsync(string function, Dictionary<string, object> parameters)
        {
            try
            {
                _logger.Info($"Sending message: {function}");
                var result = await _connection.InvokeAsync<object>("ClientSendMessage", function, parameters);
                _logger.Info($"Received response: {result}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"SendMessageAsync error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取状态
        /// </summary>
        /// <param name="stateName">状态名称</param>
        /// <returns>状态值</returns>
        public async Task<string?> GetStateAsync(string stateName)
        {
            try
            {
                _logger.Info($"Getting state: {stateName}");
                var result = await _connection.InvokeAsync<string>("GetState", stateName);
                _logger.Info($"State value: {result}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetStateAsync error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 连接测试
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                _logger.Info("Testing connection...");
                var result = await _connection.InvokeAsync<bool>("ConnectTest");
                _logger.Info($"Connection test result: {result}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"TestConnectionAsync error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 运行完整测试
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> RunFullTestAsync()
        {
            try
            {
                _logger.Info("Starting SignalR full test...");

                // 1. 连接
                if (!await ConnectAsync())
                {
                    _logger.Error("Failed to connect");
                    return false;
                }

                // 2. 注册
                if (!await RegisterAsync())
                {
                    _logger.Error("Failed to register");
                    return false;
                }

                // 3. 连接测试
                if (!await TestConnectionAsync())
                {
                    _logger.Error("Connection test failed");
                    return false;
                }

                // 4. 发送消息测试
                var messageResult = await SendMessageAsync("ConnectTest", new Dictionary<string, object>());
                if (messageResult == null || !messageResult.Equals(true))
                {
                    _logger.Error("Send message test failed");
                    return false;
                }

                // 5. 获取状态测试
                var stateResult = await GetStateAsync("HSMS State");
                if (stateResult == null)
                {
                    _logger.Error("Get state test failed");
                    return false;
                }

                _logger.Info("SignalR full test completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"RunFullTestAsync error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 处理服务端发送的消息
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        private void OnServerSendMessage(string function, Dictionary<string, object> parameters)
        {
            var logMessage = $"Received from server: {function}";
            foreach (var kvp in parameters)
            {
                logMessage += $", {kvp.Key}: {kvp.Value}";
            }
            _logger.Info(logMessage);
        }

        /// <summary>
        /// 处理注册成功消息
        /// </summary>
        /// <param name="data">注册数据</param>
        private void OnRegistered(object data)
        {
            _logger.Info($"Registration confirmed: {data}");
        }

        /// <summary>
        /// 处理错误消息
        /// </summary>
        /// <param name="data">错误数据</param>
        private void OnError(object data)
        {
            _logger.Error($"Received error from server: {data}");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                DisconnectAsync().Wait();
                _connection?.DisposeAsync().AsTask().Wait();
                _disposed = true;
            }
        }
    }
}
