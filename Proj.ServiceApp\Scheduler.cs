﻿using Proj.WCF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Proj.Service.Core
{
    public class Scheduler
    {
        private static Scheduler m_Instanse;
        private static readonly object mSyncObject = new object();

        public Scheduler()
        {
            
        }

        private bool Instance_eventWCFClientSendMessage(string strFunction, Dictionary<string, object> dicParameter)
        {
            //TODO: 根据不同的消息，进行不同的处理
            return true;
        }

        public static Scheduler Instance
        {
            get
            {
                if(m_Instanse == null)
                {
                    lock(mSyncObject)
                    {
                        if(m_Instanse == null)
                        {
                            m_Instanse = new Scheduler();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public bool Init()
        {
            Proj.CacheData.LocalParameters.Instance.SetParameterValue("TestNumber", 111);
            Proj.CacheData.LocalParameters.Instance.SetParameterValue("TestString", "Test");
            return true;
        }

        public void ClientSendMessage(string Function, string Parameter)
        {

        }

        public string GetValue(string strDevice, string strParameter)
        {
            return "";
        }
    }
}
