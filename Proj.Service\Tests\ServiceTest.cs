using System.Text;
using System.Text.Json;
using Proj.Service.Models;
using Proj.Service.Services;
using Proj.Log;

namespace Proj.Service.Tests
{
    /// <summary>
    /// 服务测试类
    /// </summary>
    public class ServiceTest : IDisposable
    {
        private readonly Logger _logger;
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public ServiceTest(string baseUrl = "http://localhost:9900")
        {
            _logger = Logger.Instance;
            _baseUrl = baseUrl;
            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri(_baseUrl);
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                _logger.Info("Testing connection...");
                
                var response = await _httpClient.GetAsync("/api/stocker/test");
                var content = await response.Content.ReadAsStringAsync();
                
                _logger.Info($"Connection test response: {response.StatusCode}, Content: {content}");
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"TestConnectionAsync error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试发送消息
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> TestSendMessageAsync()
        {
            try
            {
                _logger.Info("Testing send message...");

                var request = new ClientMessageRequest
                {
                    Function = "ConnectTest",
                    Parameters = new Dictionary<string, object>()
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/stocker/message", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.Info($"Send message test response: {response.StatusCode}, Content: {responseContent}");

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"TestSendMessageAsync error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试获取状态
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> TestGetStateAsync()
        {
            try
            {
                _logger.Info("Testing get state...");

                var request = new StateRequest
                {
                    Name = "HSMS State"
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/stocker/state", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.Info($"Get state test response: {response.StatusCode}, Content: {responseContent}");

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"TestGetStateAsync error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试获取所有状态
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> TestGetAllStatesAsync()
        {
            try
            {
                _logger.Info("Testing get all states...");

                var response = await _httpClient.GetAsync("/api/stocker/states");
                var content = await response.Content.ReadAsStringAsync();

                _logger.Info($"Get all states test response: {response.StatusCode}, Content: {content}");

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"TestGetAllStatesAsync error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试获取标签值
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> TestGetTagValueAsync()
        {
            try
            {
                _logger.Info("Testing get tag value...");

                var request = new TagValueRequest
                {
                    TagName = "TestTag"
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/stocker/tag", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.Info($"Get tag value test response: {response.StatusCode}, Content: {responseContent}");

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"TestGetTagValueAsync error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试健康检查
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> TestHealthCheckAsync()
        {
            try
            {
                _logger.Info("Testing health check...");

                var response = await _httpClient.GetAsync("/health");
                var content = await response.Content.ReadAsStringAsync();

                _logger.Info($"Health check test response: {response.StatusCode}, Content: {content}");

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"TestHealthCheckAsync error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> RunAllTestsAsync()
        {
            _logger.Info("Starting all tests...");

            var tests = new List<(string Name, Func<Task<bool>> Test)>
            {
                ("Health Check", TestHealthCheckAsync),
                ("Connection Test", TestConnectionAsync),
                ("Send Message", TestSendMessageAsync),
                ("Get State", TestGetStateAsync),
                ("Get All States", TestGetAllStatesAsync),
                ("Get Tag Value", TestGetTagValueAsync)
            };

            var results = new List<bool>();

            foreach (var (name, test) in tests)
            {
                _logger.Info($"Running test: {name}");
                var result = await test();
                results.Add(result);
                _logger.Info($"Test {name} result: {(result ? "PASSED" : "FAILED")}");
                
                // 等待一秒再执行下一个测试
                await Task.Delay(1000);
            }

            var passedCount = results.Count(r => r);
            var totalCount = results.Count;

            _logger.Info($"Test summary: {passedCount}/{totalCount} tests passed");

            return passedCount == totalCount;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
