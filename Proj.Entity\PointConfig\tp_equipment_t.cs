﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Proj.Entity.PointConfig
{
    /// <summary>
    /// 设备配置
    /// </summary>
    public class tp_equipment_t
    {
        
        public tp_equipment_t() { }
        [SugarColumn(IsPrimaryKey = true)]
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EQUIPMENT_NAME { get; set; }
        /// <summary>
        /// 设备型号
        /// </summary>
        public string EQUIPMENT_MODEL { get; set; }
        /// <summary>
        /// 设备IP
        /// </summary>
        public string EQUIPMENT_IP { get; set; }
        /// <summary>
        /// 设备端口
        /// </summary>
        public string EQUIPMENT_PORT { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnable { get; set; }
        /// <summary>
        /// 设备描述
        /// </summary>
        public string EQUIPMENT_DES { get; set; }
    }
}
