﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Proj.DataTypeDef
{
    public enum CraneTaskType : uint
    {
        None,
        Transfer,
        Scan,
        Pick,
        Place,
        Move,
        Home,
        DoubleCheck = 7
    }

    public enum CraneAction : uint
    {
        None,
        Start,
        Resume,
        Pause,
        Abort
    }

    public enum CraneWorkingStatus : uint
    {
        None,
        Idle,
        Running,
        Paused
    }

    public enum PortWorkingStatus : uint
    {
        None,
        LoadingPosReady,
        OutPosReady,
        OtherPosReady,
        Moving
    }

    public enum IOPortWorkingStatus : uint
    {
        Init,
        Ready,
        Transferring,
        WaitVehiclePlace, //LoadWait
        VehiclePlacing,
        VehiclePlaceCompleted,
        WaitCranePick, //UnloadWait
        CranePicking,
        CranePickCompleted,
        WaitCranePlace, //LoadWait
        CranePlacing,
        CranePlaceCompleted,
        WaitVehiclePick, //UnloadWait
        VehiclePicking,
        VehiclePickCompleted,
        MovingIn = 15,
        MovingOut = 16,
        WaitCVPlace = 17,
        WaitCVPick = 18
    }

    //public enum BufferWorkingStatus : uint
    //{
    //    Init,
    //    Ready,
    //    Transferring,
    //    WaitVehiclePlace, //LoadWait
    //    VehiclePlacing,
    //    VehiclePlaceCompleted,
    //    WaitCranePick, //UnloadWait
    //    CranePicking,
    //    CranePickCompleted,
    //    WaitCranePlace, //LoadWait
    //    CranePlacing,
    //    CranePlaceCompleted,
    //    WaitVehiclePick, //UnloadWait
    //    VehiclePicking,
    //    VehiclePickCompleted,
    //    MovingIn = 15,
    //    MovingOut = 16,
    //    WaitCVPlace = 17,
    //    WaitCVPick = 18
    //}

    public enum CVVehicleWorkingStatus : uint
    {
        Init = 0,
        Ready = 1,
        BAYtoSTK = 15,
        STKtoBAY = 16
    }

    public enum AlarmCode : uint
    {
        SourceEmpty = 1146,
        DoubleStorage = 1145
    }

    public enum PortTransferState : uint
    {
        None,
        OutOfService,
        InService
    }


    public enum IDReadStatus : uint
    {
        Success,
        Failure,
        Duplicate,
        Mismatch,
        NoCarrier, //(SCAN)
        TypeMismatch
    }


    public enum IDPortReadStatus : uint
    {
        None,
        Success,
        Duplicate,
        Failure,
        NoCarrier, 
    }

    public enum CraneState : uint
    {
        None,
        Idle,
        Active
    }
    

    public enum ControlState : uint
    {
        None = 0,
        EqOffline = 1,
        AttemptOnline = 2,
        HostOffline = 3,
        OnlineLocal =4,
        OnlineRemote = 5
    }

    public enum CassetteStockerState : uint
    {
        Normal,
        PartialDown1,
        PartialDown2,
        PartialDown3,
        PartialDown4,
        Down
    }

    public enum SCState : uint
    {
        None,
        SCInit,
        Paused,
        Auto,
        Pausing
    }

    public enum SCMode : uint
    {
        Normal,
        Maintenance,
        Test,
        Simulation
    }

    public enum EqReqStatus : uint
    {
        ReqOff,
        LReq,
        UReq
    }

    public enum EqPresenceStatus : uint
    {
        Presence,
        NoPresence
    }

    public enum PortUnitType : uint
    {
        Input,
        Output,
        Bidirection,
        None
    }

    public enum ZoneType : uint
    {
        None = 0,
        Shelf,
        Port,
        Other,
        Reserved = 4,
        Handoff = 9
    }

    public enum LocationType : uint
    {
        Unknown,
        Shelf,
        Crane,
        EqPort,
        IoPort
    }

    public enum CraneOperationState : uint
    {
        Down,    //OutOfService
        Normal,  //InService
        None
    }

    public enum CraneModeState : uint
    {
        None,
        Idle,
        Active
    }

    public enum CarrierState : uint
    {
        None,
        WaitIn,
        Transferring,
        Compeleted,
        Alternate,
        WaitOut,
        Installed
    }

    public enum TransferState : uint
    {
        None,
        Queue,
        Transferring,
        Paused,
        Canceling,
        Aborting
    }

    public enum TransferType : uint
    {
        Unknown,
        Transfer,
        Pick,
        Place, //Transfer、Pick、Place不够详细
        Shelf2Shelf,
        Shelf2OutPort,
        Shelf2EqPort,
        InPort2Shelf,
        InPort2OutPort,
        InPort2EqPort,
        EqPort2Shlef,
        EqPort2OutPort,
        EqPort2EqPort,
        Crane2Shelf,
        Crane2OutPort,
        Crane2EqPort,
        Shelf2Crane,
        InPort2Crane,
        EqPort2Crane,
        Scan,
        Move,
        DoubleCheck
    }

    public enum ResultCode : uint
    {
        Success,
        OtherError,
        ShelfZoneFull,
        DuplicateID,
        MismatchID,
        IDReadFail,
        TypeMismatch,
        EqInterLockNG = 64,
        NoCarrier,
        None
    }

    public enum StockerUnitState : uint
    {
        Normal,
        DoubleStorage,
        EmptyRetrieval,
        ErrorUnit,
        Errormode
    }

    public enum HandoffType : uint
    {
        None,
        Manual,
        Automated
    }

    public enum EmptyCarrier : uint
    {
        Empty,
        NotEmpty
    }

    public enum GlassExist : uint
    {
        Empty,
        Exist
    }

    public enum IDReadFailOptions
    {
        Reject,
        HostControlled
    }

    public enum IDRResultCode
    {
        None,
        Success,
        Mismatch,
        Failed,
        NoCarrier,
        DoubleCheckRight = 11,
        DoubleCheckError = 12
    }
    
    public enum OperMod
    {
        Manual = 1,
        Local = 2,
        Auto = 3
    }

    public enum MGVPortOperMod
    {
        Manual = 1,
        Auto = 2
    }

    public enum CVPortOperMod
    {
        Manual = 1,
        Local = 2,
        Auto = 3
    }

    public enum STKLightMode
    {
        NoTiming,
        Timing,
    }

    public enum STKLightColor
    {
        All,
        Yellow,
        White,
    }

    public enum CommandSource
    {
        MCS,
        GUI,
        SC
    }

    public enum HSMSState
    {
        NotConnected,
        Connected
    }

    public enum MCSCommunication
    {
        Disabled,
        NotCommunicating,
        Communicating,
        WaitCRFromHost,
        WaitDelay,
        WaitCRA
    }
    
    public enum EqPortRequest
    {
        NoRequest,
        LoadRequest,
        UnLoadRequest,
    }

    public class PortInfo
    {
        public string strPortID;
        public PortTransferState transferState;
    }

    public class EqPortInfo
    {
        public string strPortID;
        public EqReqStatus eqReqStatus;
        public EqPresenceStatus eqPresenceStatus;
    }

    public class PortTypeInfo
    {
        public string strPortID;
        public PortUnitType portUnitType;
    }


    public struct ZoneData
    {
        public string strZoneName;
        public uint u2ZoneCapacity;
    }

    public struct EnhancedZoneData
    {
        public string strZoneName;
        public uint u2ZoneCapacity;
        public uint u2ZoneSize;
        public ZoneType zoneType;
    }
    public class ExtendedZoneData
    {
        public string strZoneName;
        public uint u2ZoneCapacity;
        public uint u2ZoneTotalSize;
        public ZoneType zoneType;
        public List<DisabledLoc> disableLocations = new List<DisabledLoc>();
    }

    public class DisabledLoc
    {
        public string strCarrierLoc;
        public string strCarrierId;
    }

    public struct CraneOperationInfo
    {
        public string strStockerCraneID;
        public CraneOperationState craneOperationState;
    }

    public struct CraneModeStateInfo
    {
        public string strStockerCraneID;
        public CraneModeState craneModeState;
    }

    public class CurrentCraneState
    {
        public string strStockerCraneID;
        public CraneModeState craneModeState;
        public CraneOperationState craneOperationState;
        public string strCommandID;
    }

    public struct CarrierInfo
    {
        public string strCarrierID;
        public string strCarrierLoc;
    }


    public class EnhancedCarrierInfo
    {
        public string strCarrierID;
        public string strCarrierLoc;
        public string strLocationName;
        public string strCarrierZoneName;
        public string strInstallTime;
        public LocationType locType = LocationType.Unknown;
        public CarrierState carrierState;
    }

    public class LocationInfo
    {
        public string strAddress;
        public string strName;
        public LocationType locType;
        public string strCarrierID;
        public bool IsOccupied;
        public bool IsProhibited;
        public bool IsReserved;
        public string strZoneName;
        public int iOrder;
    }

    public struct TransferCommand
    {
        public CommandInfo commandInfo;
        public TransferInfo transferInfo;
    }

    public class EnhancedTransferCommand
    {
        public string strCommandName;
        public string strCommandID;
        public uint u2Priority;
        public string strCarrierID;
        public string strCarrierLoc;  //指令中的盒子源地址，可能是Zone/Shelf/Crane/Port
        public string strDest;        //指令中的盒子目的地址，可能是Zone/Shelf/Port
        public string strStockerCraneID;
        public int iFirstSecendCrane;
        public TransferState transferState;
        public string strRealSource;        //解析后的盒子源地址
        public string strRealDest;          //解析后的盒子目的地址
        public LocationType destLocType;    //解析后的盒子目的地址类型
        public LocationType sourceLocType;  //解析后的盒子源地址类型
        public TransferType transferType;   //解析后判读的传送类型，方便指令执行过程的判断
        public ResultCode resultCode;       //指令执行结果
        public string strSourceZone;
        public string strDestZone;
		public bool bCraneComplete = false;         //Crane是否执行完成
        public DealExceptionOption dealOption = DealExceptionOption.None;
        public string strDelayReason;
        public int iAltStep = 0;
        public string strOriginalSrc;
        public string strOriginalDest;
        public string createTime;

        //public CommandInfo commandInfo;
        //public TransferInfo transferInfo;
    }

    public class PortTypeChgCommand
    {
        public string strPortID;
        public PortUnitType unitType;
    }

    public struct CommandInfo
    {
        public string strCommandID;
        public uint u2Priority;
    }

    public struct TransferInfo
    {
        public string strCarrierID;
        public string strCarrierLoc;
        public string strDest;
        public string strStockerCraneID;
    }

    public struct CarrierLocationInfo
    {
        public string strCarrierID;
        public string strCarrierLoc;
        public string strCarrierZoneName;
    }

    public struct StockerUnitInfo
    {
        public string strStockerUnitID;
        public StockerUnitState stockerUnitState;
    }

    public struct CraneTaskData
    {
        public CraneTaskType taskType;
        public string strCarrierID;
        public string strSourceAddr;//起点
        public string strDestAddr;//终点
    }

    public struct CraneTaskStep
    {
        public int iStep;
        public int iAction;
    }

    public struct CraneSpeed
    {
        public int emptySpeed;
        public int loadSpeed;
        public int xAxisSpeed;
        public int yAxisSpeed;
        public int zAxisSpeed;
        public int tAxisSpeed;
    }
    
    public struct AxisMileage
    {
        public double travelMileage;
        public double elevationMileage;
        public double turnMileage;
        public double forkMileage;
    }

    public struct AxisUsage
    {
        public int DriveCount;   //运行次数
        public int DriveDistance;//运行里程数
        public int DriveTime;    //运行时间
    }

    //需要根据实际情况进行修改
    public struct AxisServoData
    {
        public int ErrorCode;  //错误码
        public float CurrPos;  //当前位置
        public float CmdPos;   //指令位置
        public float CurrSpeed;//当前速度
        public float Torque;   //转矩
    }
    
    //需要根据实际情况进行修改
    public struct AxisStaus
    {
        public bool ServoEnable;
        public bool Alarm;
        public bool Warning;
        public bool StandStill;
        public bool Synchronizing; 
        //public int  iErrorCode;         //报警代码
        //public bool isReady;           //轴启动准备完成
        //public bool isDisabled;        //轴无效
        //public bool isDrvServoOn;      //伺服 ON
        //public bool isDrvReady;        //伺服待机
        //public bool isDrvMainPower;    //主电路电源
        //public bool isDrvPOT;          //正方向极限输入
        //public bool isDrvNOT;          //负方向极限输入
        //public bool isDrvAlarm;        //驱动器错误输入
        //public bool isDrvWarning;      //驱动器警告输入
        //public bool isHomed;           //原点确定
        //public bool isMFaultLvlActive; //轴轻度故障发生中
        //public bool isStandStill;      //停止中
        //public bool isDiscrete;        //定位动作中
        //public bool isContinuous;      //连续动作中
        //public bool isSynchronized;    //同步动作中
        //public bool isHoming;          //原点复位中
        //public bool isStopping;        //减速停止中
        //public bool isErrorStop;       //错误减速停止中
        //public bool isCoordinated;     //多轴协调动作中
    }

    public struct IDRResult
    {
        public int iResultCode;
        public string strCarrierID;
    }


    public struct PortSpeed
    {
        public int xAxisSpeed;
        public int yAxisSpeed;
        public int zAxisSpeed;
        public int tAxisSpeed;
    }

    public struct EqPortStatus
    {
        public bool pLReq;
        public bool pUReq;
        public bool pReady;
        public bool pAbnormal;
        public bool pInterLock;
        public bool pCaseContain;
        public bool aTransReq;
        public bool aBusy;
        public bool aCompleted;
        public bool aAbnormal;
        public bool aInterLock;
        public bool aCaseContain;
    }

    public struct PortPIOStatus
    {
        public bool P_L_REQ;
        public bool P_U_REQ;
        public bool P_Ready;
        public bool P_Abnormal;
        public bool P_InterLock;
        public bool P_CSTContain;
        public bool A_TR_REQ;
        public bool A_Busy;
        public bool A_COMPT;
        public bool A_Abnormal;
        public bool A_InterLock;
        public bool A_CSTContain;
    }

    public class CycleTestInfo
    {
        public string strCarrierID;
        public bool bUsePort;
        public string strPortName;
        public int iCycleTimes;
        public List<string> Locations;
    }

    public class CraneIDRReadRule
    {
        public int iMaxReadFailCount;     //Crane读码失败次数，达到次数后，Crane禁用。该值为0，不启用该功能
        public bool bShowIDRBreakDown; //是否显示（报告）Crane的IDR BreakDown
        public bool bShelf2ShelfReadBypass = true; //是否屏蔽Shelf2Shelf过程的读码
        public bool bShelf2OutPortReadBypass = true;
        public bool bShelf2EqPortReadBypass = false;
        public bool bInPort2ShelfReadBypass = false;
        public bool bInPort2OutPortReadBypass = false;
        public bool bInPort2EqPortReadBypass = false;
        public bool bEqPort2ShelfReadBypass = false;
        public bool bEqPort2OutPortReadBypass = false;
        public bool bEqPort2EqPortReadBypass = false;
        public bool bShelf2CraneReadBypass = true;
        public bool bInPort2CraneReadBypass = false;
        public bool bEqPort2CraneReadBypass = false;
    }

    public struct TimeOut   //单位：秒
    {
        public int iCraneOccupied;
        public int iInPortOccupied;
    }
    public enum DealExceptionOption
    {
        None,
        Retry,
        Abort
    }

    public struct InPortCSTIDTime
    {
        public bool bIsReadSuccess;
        public bool bInputFlag;
        public string strCasstileID;
        public DateTime timeInPut;
    }
}
