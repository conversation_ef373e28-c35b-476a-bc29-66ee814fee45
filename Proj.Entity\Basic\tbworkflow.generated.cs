﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbWorkflow and List
    [Serializable]
    [Description("工艺路线表")]
    [LinqToDB.Mapping.Table("TB_WORKFLOW")]
    public partial class TbWorkflow : GEntity<TbWorkflow>, ITimestamp
    {
        #region Contructor(s)

        private TbWorkflow()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPkId = RegisterProperty<String>(p => p.CPkId);
        private static readonly PropertyInfo<String> pty_CWorkflowId = RegisterProperty<String>(p => p.CWorkflowId);
        private static readonly PropertyInfo<String> pty_CWorkflowName = RegisterProperty<String>(p => p.CWorkflowName);
        private static readonly PropertyInfo<String> pty_CWorkflownum = RegisterProperty<String>(p => p.CWorkflownum);
        private static readonly PropertyInfo<String> pty_CProcessId = RegisterProperty<String>(p => p.CProcessId);
        private static readonly PropertyInfo<String> pty_CProcessName = RegisterProperty<String>(p => p.CProcessName);
        private static readonly PropertyInfo<String> pty_CProcesshours = RegisterProperty<String>(p => p.CProcesshours);
        private static readonly PropertyInfo<String> pty_CProcessnum = RegisterProperty<String>(p => p.CProcessnum);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 唯一ID
        /// </summary>
        [Description("唯一ID")]
        [LinqToDB.Mapping.Column("C_PKID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPkId
        {
            get { return GetProperty(pty_CPkId); }
            set { SetProperty(pty_CPkId, value); }
        }
        /// <summary>
        /// 工艺路线ID
        /// </summary>
        [Description("工艺路线ID")]
        [LinqToDB.Mapping.Column("C_WORKFLOWID")]
        public String CWorkflowId
        {
            get { return GetProperty(pty_CWorkflowId); }
            set { SetProperty(pty_CWorkflowId, value); }
        }
        /// <summary>
        /// 工艺路线名称
        /// </summary>
        [Description("工艺路线名称")]
        [LinqToDB.Mapping.Column("C_WORKFLOWNAME")]
        public String CWorkflowName
        {
            get { return GetProperty(pty_CWorkflowName); }
            set { SetProperty(pty_CWorkflowName, value); }
        }
        /// <summary>
        /// 工序顺序号
        /// </summary>
        [Description("工序顺序号")]
        [LinqToDB.Mapping.Column("C_WORKFLOWNUM")]
        public String CWorkflownum
        {
            get { return GetProperty(pty_CWorkflownum); }
            set { SetProperty(pty_CWorkflownum, value); }
        }
        /// <summary>
        /// 工序ID
        /// </summary>
        [Description("工序ID")]
        [LinqToDB.Mapping.Column("C_PROCESSID")]
        public String CProcessId
        {
            get { return GetProperty(pty_CProcessId); }
            set { SetProperty(pty_CProcessId, value); }
        }
        /// <summary>
        /// 工序名称
        /// </summary>
        [Description("工序名称")]
        [LinqToDB.Mapping.Column("C_PROCESSNAME")]
        public String CProcessName
        {
            get { return GetProperty(pty_CProcessName); }
            set { SetProperty(pty_CProcessName, value); }
        }
        /// <summary>
        /// 工时
        /// </summary>
        [Description("工时")]
        [LinqToDB.Mapping.Column("C_PROCESSHOURS")]
        public String CProcesshours
        {
            get { return GetProperty(pty_CProcesshours); }
            set { SetProperty(pty_CProcesshours, value); }
        }
        /// <summary>
        /// 投入人员
        /// </summary>
        [Description("投入人员")]
        [LinqToDB.Mapping.Column("C_PROCESSNUM")]
        public String CProcessnum
        {
            get { return GetProperty(pty_CProcessnum); }
            set { SetProperty(pty_CProcessnum, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPkId, "唯一ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPkId, 40, "唯一ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkflowId, 40, "工艺路线ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkflowName, 80, "工艺路线名称不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkflownum, 10, "工序顺序号不能超过10个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProcessId, 40, "工序ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProcessName, 80, "工序名称不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProcesshours, 40, "工时不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProcessnum, 10, "投入人员不能超过10个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPkId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbWorkflowList : GEntityList<TbWorkflowList, TbWorkflow>
    {
        private TbWorkflowList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
