﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpBatchmethod and List
    [Serializable]
    [Description("TP_BatchMethod")]
    [LinqToDB.Mapping.Table("TP_BatchMethod")]
    public partial class TpBatchmethod : GEntity<TpBatchmethod>
    {
        #region Contructor(s)

        private TpBatchmethod()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CClass = RegisterProperty<String>(p => p.CClass);
        private static readonly PropertyInfo<String> pty_CMethod = RegisterProperty<String>(p => p.CMethod);
        #endregion

        /// <summary>
        /// c_Pk
        /// </summary>
        [Description("c_Pk")]
        [LinqToDB.Mapping.Column("c_Pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// c_Class
        /// </summary>
        [Description("c_Class")]
        [LinqToDB.Mapping.Column("c_Class")]
        public String CClass
        {
            get { return GetProperty(pty_CClass); }
            set { SetProperty(pty_CClass, value); }
        }
        /// <summary>
        /// c_Method
        /// </summary>
        [Description("c_Method")]
        [LinqToDB.Mapping.Column("c_Method")]
        public String CMethod
        {
            get { return GetProperty(pty_CMethod); }
            set { SetProperty(pty_CMethod, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "c_Pk是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 40, "c_Pk不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CClass, 400, "c_Class不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMethod, 400, "c_Method不能超过400个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TpBatchmethodList : GEntityList<TpBatchmethodList, TpBatchmethod>
    {
        private TpBatchmethodList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
