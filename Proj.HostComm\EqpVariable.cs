﻿
namespace Proj.HostComm
{
    //SVID
    public enum EqpVariable : uint
    {
        //1. Status Variables(SV)
        // S1F3, S6F11 , Equipment Status
        Clock = 5,
        AlarmEnabled = 3,
        AlarmSet = 4,
        EventEnabled = 7,
            SpecVersion = 114,
        ControlState = 6,
            CassetteStockerState = 401,
        SCState = 79,
        CurrentPortStates = 118,
        PortTypes = 351,
            PortTypeInfo_i = 122,
            PortUnitType = 123,
        //S1F3 , Zone Information 
        ActiveZones = 53,//no use
            EnhancedActiveZones = 85,
            ExtendedActiveZones = 95,
            ExtendedZoneData = 97,
        //S1F3 , Crane Status
            CraneOperationInformation = 411,
            CraneModeStateInformation = 412,
        //S1F3 , Carrier Information 
        ActiveCarriers = 51, //有定义无具体数据赋值
        EnhancedCarriers = 81,
        //S1F3 , Transfer Command
        ActiveTransfers = 52,//有定义无具体数据赋值
        EnhancedTransfers = 83,
        EnhancedTransferCommand = 205, //*不知道何时赋值new add DV
        ShelfAllStats = 256, //new add
        //MainteState = 257, //new add
        ActiveZones_2 = 375,  //new add
        ZoneTotalSize = 377, //new add

        //2. Data Values (DV)
        //S6F11
        TransferState = 202,//有定义,单独GlobalData.Instance.gbTransferState无数据
        CarrierState = 203, //有定义无具体数据赋值
            PortTransferState_i = 251,
        ResultCode = 69,
        IDReadStatus = 65,
            CraneState = 252,
            EqPresenceStatus = 353,
        TransferCommand = 74,
        CommandInfo = 59,
        TransferInfo = 75,
        CarrierInfo = 55,
        ZoneData = 77,
        EnhancedZoneData = 356,
        StockerUnitInfo = 72,
            CommandName = 57,
        CommandID = 58,
        CommandType = 80,
        Priority = 67,//有定义,单独GlobalData.Instance.gbPriority无数据
        CarrierID = 54,
        CarrierLoc = 56,
        Dest = 60,
        Source = 70,
        CarrierZoneName = 90,
        CarrierLocations = 94,//new add
        ZoneName = 78,
        ZoneCapacity = 76,
            ZoneType = 357,
        StockerCraneID = 88,
        StockerUnitID = 71,
        StockerUnitState = 73,
        RecoveryOptions = 68,
        PortID = 115,
        HandoffType = 64,
        PortType = 66,
        ErrorID = 63,
        EmptyCarrier = 61,//*有定义无具体数据赋值
            UnitID = 11,
        AlarmID = 1,
        AlarmText = 212,
            DisabledLocations = 82,
            DisabledLoc_i = 84,
            GlassExist = 100,

        //3. Equipment Constants (EC)
        //S2F13, S2F15, S6F11 : Equipment Parameter 
        EqpName = 62,
        EstablishCommunicationTimeOut = 2,//有定义无具体数据赋值
        IDReadDuplicateOption = 111, //有定义无具体数据赋值
        IDReadFailureOption = 112,//有定义无具体数据赋值
        IDReadMismatchOption = 113,//有定义无具体数据赋值

        //S2F13, S2F15, S6F11 : HSMS Parameter 
            T3TimeOut = 501,
            T5TimeOut = 502,
            T6TimeOut = 503,
            T7TimeOut = 504,
            T8TimeOut = 505,
            RetryCount = 506,
        MainteState = 257,
            AllEnhancedDisableLocations = 376,

        CurrentEqPortStatus = 350, //EQ
        EqReqStatus = 352,
        UnitAlarmStatList = 254
    }
}
