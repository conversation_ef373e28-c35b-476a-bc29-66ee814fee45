﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region Operation20191025 and List
    [Serializable]
    [Description("OPERATION_20191025")]
    [LinqToDB.Mapping.Table("OPERATION_20191025")]
    public partial class Operation20191025 : GEntity<Operation20191025>
    {
        #region Contructor(s)

        private Operation20191025()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<Int64> pty_Time = RegisterProperty<Int64>(p => p.Time);
        private static readonly PropertyInfo<String> pty_Client = RegisterProperty<String>(p => p.Client);
        private static readonly PropertyInfo<String> pty_User = RegisterProperty<String>(p => p.User);
        private static readonly PropertyInfo<String> pty_UiName = RegisterProperty<String>(p => p.UiName);
        private static readonly PropertyInfo<String> pty_FunctionName = RegisterProperty<String>(p => p.FunctionName);
        private static readonly PropertyInfo<String> pty_FunctionData = RegisterProperty<String>(p => p.FunctionData);
        #endregion

        /// <summary>
        /// time
        /// </summary>
        [Description("time")]
        [LinqToDB.Mapping.Column("time")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int64 Time
        {
            get { return GetProperty(pty_Time); }
            set { SetProperty(pty_Time, value); }
        }
        /// <summary>
        /// client
        /// </summary>
        [Description("client")]
        [LinqToDB.Mapping.Column("client")]
        public String Client
        {
            get { return GetProperty(pty_Client); }
            set { SetProperty(pty_Client, value); }
        }
        /// <summary>
        /// user
        /// </summary>
        [Description("user")]
        [LinqToDB.Mapping.Column("user")]
        public String User
        {
            get { return GetProperty(pty_User); }
            set { SetProperty(pty_User, value); }
        }
        /// <summary>
        /// ui_name
        /// </summary>
        [Description("ui_name")]
        [LinqToDB.Mapping.Column("ui_name")]
        public String UiName
        {
            get { return GetProperty(pty_UiName); }
            set { SetProperty(pty_UiName, value); }
        }
        /// <summary>
        /// function_name
        /// </summary>
        [Description("function_name")]
        [LinqToDB.Mapping.Column("function_name")]
        public String FunctionName
        {
            get { return GetProperty(pty_FunctionName); }
            set { SetProperty(pty_FunctionName, value); }
        }
        /// <summary>
        /// function_data
        /// </summary>
        [Description("function_data")]
        [LinqToDB.Mapping.Column("function_data")]
        public String FunctionData
        {
            get { return GetProperty(pty_FunctionData); }
            set { SetProperty(pty_FunctionData, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Time, "time是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Client, 32, "client不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_User, 32, "user不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_UiName, 32, "ui_name不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_FunctionName, 32, "function_name不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_FunctionData, 64, "function_data不能超过64个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Time.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class Operation20191025List : GEntityList<Operation20191025List, Operation20191025>
    {
        private Operation20191025List() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
