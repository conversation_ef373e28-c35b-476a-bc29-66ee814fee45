﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	<UseWindowsForms>true</UseWindowsForms>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Proj.Alarm\Proj.Alarm.csproj" />
    <ProjectReference Include="..\Proj.CacheData\Proj.CacheData.csproj" />
    <ProjectReference Include="..\Proj.Controller\Proj.Controller.csproj" />
    <ProjectReference Include="..\Proj.WCFService\Proj.WCFService.csproj" />
  </ItemGroup>

</Project>
