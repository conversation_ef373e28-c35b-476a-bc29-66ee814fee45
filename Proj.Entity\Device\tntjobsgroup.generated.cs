﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntJobsgroup and List
    [Serializable]
    [Description("作业任务组")]
    [LinqToDB.Mapping.Table("Tnt_JobsGroup")]
    public partial class TntJobsgroup : GEntity<TntJobsgroup>, ITimestamp
    {
        #region Contructor(s)

        private TntJobsgroup()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CJobgroupCode = RegisterProperty<String>(p => p.CJobgroupCode);
        private static readonly PropertyInfo<String> pty_CJobgroupName = RegisterProperty<String>(p => p.CJobgroupName);
        private static readonly PropertyInfo<String> pty_CDescription = RegisterProperty<String>(p => p.CDescription);
        private static readonly PropertyInfo<String> pty_CMode = RegisterProperty<String>(p => p.CMode);
        private static readonly PropertyInfo<String> pty_CEnabled = RegisterProperty<String>(p => p.CEnabled);
        private static readonly PropertyInfo<Int64> pty_CInterval = RegisterProperty<Int64>(p => p.CInterval);
        private static readonly PropertyInfo<string> pty_CIntervalUnit = RegisterProperty<string>(p => p.CUnit);
        private static readonly PropertyInfo<DateTime> pty_CStart = RegisterProperty<DateTime>(p => p.CStart);
        private static readonly PropertyInfo<String> pty_CAssembly = RegisterProperty<String>(p => p.CAssembly);
        private static readonly PropertyInfo<String> pty_CClass = RegisterProperty<String>(p => p.CClass);
        private static readonly PropertyInfo<String> pty_CMethod = RegisterProperty<String>(p => p.CMethod);
        private static readonly PropertyInfo<String> pty_CMessageType = RegisterProperty<String>(p => p.CMessageType);
        private static readonly PropertyInfo<String> pty_CMessage = RegisterProperty<String>(p => p.CMessage);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        private static readonly PropertyInfo<Int64> pty_CExcutetimes = RegisterProperty<Int64>(p => p.CExcutetimes);
        private static readonly PropertyInfo<DateTime> pty_CEnd = RegisterProperty<DateTime>(p => p.CEnd);
        private static readonly PropertyInfo<String> pty_CExpression = RegisterProperty<String>(p => p.CExpression);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_Pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 作业组代码
        /// </summary>
        [Description("作业组代码")]
        [LinqToDB.Mapping.Column("c_JobGroupCode")]
        public String CJobgroupCode
        {
            get { return GetProperty(pty_CJobgroupCode); }
            set { SetProperty(pty_CJobgroupCode, value); }
        }
        /// <summary>
        /// 作业组名称
        /// </summary>
        [Description("作业组名称")]
        [LinqToDB.Mapping.Column("c_JobGroupName")]
        public String CJobgroupName
        {
            get { return GetProperty(pty_CJobgroupName); }
            set { SetProperty(pty_CJobgroupName, value); }
        }
        /// <summary>
        /// 描述
        /// </summary>
        [Description("描述")]
        [LinqToDB.Mapping.Column("c_Description")]
        public String CDescription
        {
            get { return GetProperty(pty_CDescription); }
            set { SetProperty(pty_CDescription, value); }
        }
        /// <summary>
        /// 执行方式
        /// </summary>
        [Description("执行方式")]
        [LinqToDB.Mapping.Column("c_Mode")]
        public String CMode
        {
            get { return GetProperty(pty_CMode); }
            set { SetProperty(pty_CMode, value); }
        }
        /// <summary>
        /// 是否有效
        /// </summary>
        [Description("是否有效")]
        [LinqToDB.Mapping.Column("c_Enabled")]
        public String CEnabled
        {
            get { return GetProperty(pty_CEnabled); }
            set { SetProperty(pty_CEnabled, value); }
        }
        /// <summary>
        /// 时间间隔
        /// </summary>
        [Description("时间间隔")]
        [LinqToDB.Mapping.Column("c_Interval")]
        public Int64 CInterval
        {
            get { return GetProperty(pty_CInterval); }
            set { SetProperty(pty_CInterval, value); }
        }
        /// <summary>
        /// 时间间隔单位
        /// </summary>
        [Description("时间间隔单位")]
        [LinqToDB.Mapping.Column("c_Unit")]
        public string CUnit
        {
            get { return GetProperty(pty_CIntervalUnit); }
            set { SetProperty(pty_CIntervalUnit, value); }
        }
        /// <summary>
        /// 开始时间
        /// </summary>
        [Description("开始时间")]
        [LinqToDB.Mapping.Column("c_Start")]
        public DateTime CStart
        {
            get { return GetProperty(pty_CStart); }
            set { SetProperty(pty_CStart, value); }
        }
        /// <summary>
        /// 程序集名称
        /// </summary>
        [Description("程序集名称")]
        [LinqToDB.Mapping.Column("c_Assembly")]
        public String CAssembly
        {
            get { return GetProperty(pty_CAssembly); }
            set { SetProperty(pty_CAssembly, value); }
        }
        /// <summary>
        /// 执行类
        /// </summary>
        [Description("执行类")]
        [LinqToDB.Mapping.Column("c_Class")]
        public String CClass
        {
            get { return GetProperty(pty_CClass); }
            set { SetProperty(pty_CClass, value); }
        }
        /// <summary>
        /// 执行方法
        /// </summary>
        [Description("执行方法")]
        [LinqToDB.Mapping.Column("c_Method")]
        public String CMethod
        {
            get { return GetProperty(pty_CMethod); }
            set { SetProperty(pty_CMethod, value); }
        }
        /// <summary>
        /// 信息类型
        /// </summary>
        [Description("信息类型")]
        [LinqToDB.Mapping.Column("c_MessageType")]
        public String CMessageType
        {
            get { return GetProperty(pty_CMessageType); }
            set { SetProperty(pty_CMessageType, value); }
        }
        /// <summary>
        /// 失败信息
        /// </summary>
        [Description("失败信息")]
        [LinqToDB.Mapping.Column("c_Message")]
        public String CMessage
        {
            get { return GetProperty(pty_CMessage); }
            set { SetProperty(pty_CMessage, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
        /// <summary>
        /// 执行次数
        /// </summary>
        [Description("执行次数")]
        [LinqToDB.Mapping.Column("c_ExcuteTimes")]
        public Int64 CExcutetimes
        {
            get { return GetProperty(pty_CExcutetimes); }
            set { SetProperty(pty_CExcutetimes, value); }
        }
        /// <summary>
        /// 结束时间
        /// </summary>
        [Description("结束时间")]
        [LinqToDB.Mapping.Column("c_End")]
        public DateTime CEnd
        {
            get { return GetProperty(pty_CEnd); }
            set { SetProperty(pty_CEnd, value); }
        }
        /// <summary>
        /// 表达式
        /// </summary>
        [Description("表达式")]
        [LinqToDB.Mapping.Column("c_Expression")]
        public String CExpression
        {
            get { return GetProperty(pty_CExpression); }
            set { SetProperty(pty_CExpression, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 80, "主键不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CJobgroupCode, 80, "作业组代码不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CJobgroupName, 200, "作业组名称不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDescription, 200, "描述不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMode, 40, "执行方式不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CEnabled, 20, "是否有效不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CAssembly, 200, "程序集名称不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CClass, 200, "执行类不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMethod, 200, "执行方法不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMessageType, 2, "信息类型不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMessage, 200, "失败信息不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExcutetimes, 4000, "执行次数不能超过4000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CEnd, 4000, "结束时间不能超过4000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExpression, 200, "表达式不能超过200个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntJobsgroupList : GEntityList<TntJobsgroupList, TntJobsgroup>
    {
        private TntJobsgroupList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
