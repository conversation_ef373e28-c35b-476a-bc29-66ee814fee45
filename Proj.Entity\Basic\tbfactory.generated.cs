﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;
 
namespace Proj.Entity
{
    #region TbFactory and List
    [Serializable]
    [Description("工厂表")]
    [LinqToDB.Mapping.Table("TB_FACTORY")]
    public partial class TbFactory : GEntity<TbFactory>, ITimestamp
    {
        #region Contructor(s)

        private TbFactory()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CFactoryId = RegisterProperty<String>(p => p.CFactoryId);
        private static readonly PropertyInfo<String> pty_CFactoryName = RegisterProperty<String>(p => p.CFactoryName);
        #endregion

        /// <summary>
        /// 工厂编码
        /// </summary>
        [Description("工厂编码")]
        [LinqToDB.Mapping.Column("C_FACTORY_ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CFactoryId
        {
            get { return GetProperty(pty_CFactoryId); }
            set { SetProperty(pty_CFactoryId, value); }
        }
        /// <summary>
        /// 工厂描述
        /// </summary>
        [Description("工厂描述")]
        [LinqToDB.Mapping.Column("C_FACTORY_NAME")]
        public String CFactoryName
        {
            get { return GetProperty(pty_CFactoryName); }
            set { SetProperty(pty_CFactoryName, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }

        
        
        #endregion 
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CFactoryId, "工厂编码是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryId, 20, "工厂编码不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryName, 200, "工厂描述不能超过200个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CFactoryId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbFactoryList : GEntityList<TbFactoryList, TbFactory>
    {
        private TbFactoryList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
