﻿using System;
using System.Collections.Generic;

using Proj.Common;
using Proj.DataTypeDef;
using Proj.DevComm;
using Proj.HostComm;
using Proj.History;
using Proj.Alarm;
using Proj.Setting;
using System.Threading;
using Proj.WCF;
using Proj.CacheData;
using Proj.Entity;

namespace Proj.UnitMng
{
    //将DevObj工程放到UnitMng中，可以不用下面这些委托
    //public delegate void deleTransferChange2Port(string strPortAddress, EnhancedTransferCommand task);

    public class CraneObj
    {
        private ThreadBaseModel taskExecuteThread = null; //
        private ThreadBaseModel taskExecuteThreadAlarm = null; //alarm
        private ThreadBaseModel taskExecuteCheckStepAction = null;
        private bool craneAlarmState = false;
        private bool stkAlarmState = false;
        ControlState LastControlState = ControlState.None;
        bool bLastShelfFullFlag = true;
        bool bIsFirstFlag = true;

        public static uint uiIsRetryFlag = 0;//0=初始化； 1=Isretry； 2=not retry

        private int iCraneNo = 0;
        public string strCraneName
        {
            get
            { 
                return craneName;
            }
        }
        public bool IsHaveIDR
        {
            get
            {
                return isHaveIDR;
            }
        }
        public CraneOperationState OperState
        {
            get
            {
                return operState;
            }
            set
            {
                operState = value;
            }
        }
        public CraneModeState ModeState
        {
            get
            {
                return modeState;
            }
        }

        private string craneTag = "";
        private string craneName = "";
        private bool isHaveIDR = false;
        private CraneOperationState operState = CraneOperationState.Normal;
        private CraneModeState modeState = CraneModeState.Idle;

        private EnhancedTransferCommand currCommand = null;
        private int taskStep = 0;
        private string strCarrierID = "";
       // private bool bTaskAbortInitiated = false;
        private CraneTaskStep craneTaskStep = new CraneTaskStep();
        private bool bAction1Started = false;
        private bool bAction2Started = false;
        private bool bAction3Started = false;
        private bool bAction4Started = false;
        private bool bAction9Started = false;
        private bool bDoubleStorage = false;
        private bool bEmptyRetrieval = false;
        private IDRResult idrResult = new IDRResult();
        private ResultCode resultCode = ResultCode.None;
        private string dupCarrierRealLoc = "";
        private int iFailCount = 0;
        private bool bCraneAlarmed = false;
        private bool bStepAction2040 = false;
        //private bool bSTKAlarmed = false;

        private int iReadDFailCounter = 0;
        private int iAutoPlaceCounter = 0;

        private int iLastPlcStep = 0;
        private int iLastPlcAction = 0;

        private int iLastScanStep = 0;
        private int iLastScanAction = 0;

        //public event deleTransferChange2Port TransferChange2Port = null;
        //是否屏幕扫码
        public bool bReadBypass;
        private DealExceptionOption exceptionOption = DealExceptionOption.None;//
        public CraneObj(int no, string name, bool isHaveIDR, CraneOperationState state)
        {
            iCraneNo = no;
            craneName = name;
            this.isHaveIDR = isHaveIDR;
            operState = state;
            modeState = CraneModeState.Idle;
            craneTag = "Crane" + no.ToString();
        }

        public bool IsHaveTask()
        {
            if(currCommand != null)
            {
                return true;
            }
            else
            {

                return false;
            }
        }
        public bool IsHaveCase()
        {
            return CraneDev.Instance.IsCraneForkHasCarrier(craneTag);
        }

        public bool ResetAlarm()
        {
            CraneDev.Instance.CraneAlarmClear(craneTag);
            Thread.Sleep(200);
            CraneDev.Instance.ResetCraneAlarmClear(craneTag);
            return true;
        }

        public void DealException(DealExceptionOption option)
        {
            exceptionOption = option;
        }

        public void SetAbortStepValue(EnhancedTransferCommand abortCommand)
        {
            if (abortCommand.strCommandID == currCommand.strCommandID
                && abortCommand.transferState  == TransferState.Aborting)
            {
                taskStep = 40;
            }
        }
        private void executeScanCommand()
        {
            switch (currCommand.transferState)
            {
                case TransferState.Queue:
                    {
                        taskStep = 1;
                        TransferMng.Instance.UpdateTransferCrane(currCommand.strCommandID, strCraneName);
                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Transferring, ResultCode.None);
                        HistoryWriter.Instance.CraneTransferStart(currCommand.strCommandID, currCommand.iFirstSecendCrane, strCraneName);
                    }
                    break;
                case TransferState.Transferring:
                    {
                        CraneDev.Instance.CraneServoOn(craneTag);
                        if (IsHaveIDR)
                        {
                            ScanUseCraneIDR();
                        }
                        else
                        {
                            ScanUseSomeShelfIDR();
                        }
                    }
                    break;
                case TransferState.Aborting:
                    {
                        AbortSchedule();
                    }
                    break;
                case TransferState.Canceling:
                    break;
                case TransferState.Paused:
                    break;
                case TransferState.None:   //不应该在此出现
                    {
                        //结束Crane的当前任务
                        resultCode = ResultCode.OtherError;
                        endCurrentCommand(TransferState.None, resultCode);
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand-TransferState.None");
                    }
                    break;
            }
        }

        private void AbortSchedule()
        {
            if (taskStep > 1 && taskStep < 40)
            {
                taskStep = 40;
            }
            switch (taskStep)
            {
                case 40:
                    {
                        if (bEmptyRetrieval)
                        {
                            CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);
                            CraneMng.Instance.ResetCraneAlarmWithEmptyOrDouble(craneName);
                            AlarmController.Instance.ClearAlarmByCode((int)AlarmCode.SourceEmpty, GlobalData.Instance.gbStockerCraneID);
                            HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty"+ currCommand.strStockerCraneID, currCommand.strStockerCraneID, "RETRY, ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmCleared);
                            HostIF.Instance.ClearAlarm((uint)AlarmCode.SourceEmpty);
                        }
                        else if (bDoubleStorage)
                        {
                            string strUnknownID = CarrierMng.Instance.GenDoubleStorageCarrierID(currCommand.strCarrierID);
                            CarrierMng.Instance.AddCarrierInfo(strUnknownID, currCommand.strRealDest);
                            CraneMng.Instance.ResetCraneAlarmWithEmptyOrDouble(craneName);
                            AlarmController.Instance.ClearAlarmByCode((int)AlarmCode.DoubleStorage, GlobalData.Instance.gbStockerCraneID);
                            HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "DestOccupied" + currCommand.strStockerCraneID, GlobalData.Instance.gbStockerCraneID, "RETRY, ABORT", (int)AlarmCode.DoubleStorage, EqpEvent.AlarmCleared);
                            HostIF.Instance.ClearAlarm((uint)AlarmCode.DoubleStorage);
                        }
                        //if (bEmptyRetrieval)
                        //{
                        //    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty", currCommand.strStockerCraneID, "ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmCleared);
                        //    HostIF.Instance.ClearAlarm((uint)AlarmCode.SourceEmpty);
                        //}
                        //else if (bDoubleStorage)
                        //{
                        //    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "Double Storage", currCommand.strStockerCraneID, "ABORT", (int)AlarmCode.DoubleStorage, EqpEvent.AlarmCleared);
                        //    HostIF.Instance.ClearAlarm((uint)AlarmCode.DoubleStorage);
                        //}
                        else
                        {
                            HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "Abort", currCommand.strStockerCraneID, "ABORT", 222, EqpEvent.AlarmCleared);
                            HostIF.Instance.ClearAlarm(222);
                        }

                        CraneDev.Instance.CraneTaskAbort(craneTag);
                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferAbortInitiated);

                        taskStep++;
                    }
                    break;
                case 41:
                    {
                        CraneDev.Instance.CraneAbortCompleteAck(craneTag);
                        CraneDev.Instance.CraneTaskCompleteAck(craneTag);

                        taskStep++;
                    }

                    break;
                case 42:
                    {
                        if (CraneDev.Instance.IsCraneAbortComplete(craneTag))
                        {
                            return;
                        }

                        LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);
                        LocationMng.Instance.UnReserveLocation(currCommand.strRealDest);

                        //重置Crane任务启动信号
                        CraneDev.Instance.ResetCraneTaskStart(craneTag);

                        CraneDev.Instance.ResetCraneAbortCompleteAck(craneTag);
                        CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);

                        modeState = CraneModeState.Idle;

                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferAbortCompleted);

                        if (bEmptyRetrieval)
                        {
                            HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                            CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);
                            ////是否还要添加一个UnKnown的盒子？
                            //string strUnknownID = CarrierMng.Instance.GenEmptyRetrvScanCarrierID(currCommand.strRealSource);
                            //CarrierMng.Instance.AddCarrierInfo(strUnknownID, currCommand.strRealSource);
                            //HostIF.Instance.PostCarrierEvent(strUnknownID, EqpEvent.CarrierInstallCompleted);
                        }
                        else if (bDoubleStorage)
                        {
                            string strUnknownID = CarrierMng.Instance.GenDoubleStorageCarrierID(currCommand.strRealDest);
                            CarrierMng.Instance.AddCarrierInfo(strUnknownID, currCommand.strRealDest);
                            HostIF.Instance.PostCarrierEvent(strUnknownID, EqpEvent.CarrierInstallCompleted);
                        }

                        //结束Crane的当前任务
                        endCurrentCommand(TransferState.None, resultCode);
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand");
                    }
                    break;
            }
        }

        private void DealReadCardWithScan()
        {
            Thread.Sleep(1000);
            //处理读码结果（什么情况不读码，要有可配置的控制逻辑）lzt
            idrResult = CraneDev.Instance.GetIDRResult(craneTag);
            if (Proj.CacheData.GlobalData.Instance.gbSimulation)
            {
                idrResult.iResultCode = 1;
                idrResult.strCarrierID = "CAR001";
            }
            switch ((IDRResultCode)idrResult.iResultCode)
            {
                case IDRResultCode.Success:
                    {
                        if (CarrierMng.Instance.IsCarrierDuplicate(idrResult.strCarrierID, currCommand.strRealSource, ref dupCarrierRealLoc))
                        {
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead DuplicateID:{idrResult.strCarrierID}");
                            resultCode = ResultCode.DuplicateID;
                            HostIF.Instance.PostIDREvent(idrResult.strCarrierID, currCommand.strRealSource, IDReadStatus.Duplicate, EqpEvent.CarrierIDRead);
                        }
                        else
                        {
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead Success:{idrResult.strCarrierID}");
                            resultCode = ResultCode.Success;
                            HostIF.Instance.PostIDREvent(idrResult.strCarrierID, currCommand.strRealSource, IDReadStatus.Success, EqpEvent.CarrierIDRead);
                        }
                    }
                    break;
                case IDRResultCode.Mismatch: //进行Duplicate的检查
                    {
                        if (CarrierMng.Instance.IsCarrierDuplicate(idrResult.strCarrierID, currCommand.strRealSource, ref dupCarrierRealLoc))
                        {
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead DuplicateID:{idrResult.strCarrierID}");
                            resultCode = ResultCode.DuplicateID;
                            HostIF.Instance.PostIDREvent(idrResult.strCarrierID, currCommand.strRealSource, IDReadStatus.Duplicate, EqpEvent.CarrierIDRead);
                        }
                        else
                        {
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead MismatchID:{idrResult.strCarrierID}");
                            resultCode = ResultCode.MismatchID;
                            HostIF.Instance.PostIDREvent(idrResult.strCarrierID, currCommand.strRealSource, IDReadStatus.Mismatch, EqpEvent.CarrierIDRead);
                        }
                    }
                    break;
                case IDRResultCode.Failed:
                    {
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                            , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead IDReadFail");
                        resultCode = ResultCode.IDReadFail;
                        HostIF.Instance.PostIDREvent("Unknown", currCommand.strRealSource, IDReadStatus.Failure, EqpEvent.CarrierIDRead);
                        if (++iReadDFailCounter == SysSettings.Instance.CraneIDRReadRules.iMaxReadFailCount)
                        {
                            iReadDFailCounter = 0;
                            if (SysSettings.Instance.CraneIDRReadRules.bShowIDRBreakDown)
                            {
                                HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "", "IDR", "ABORT", 1000, EqpEvent.AlarmSet);
                                //报警：IDR Break Down
                                AlarmController.Instance.SetAlarm(1000, "IDR", "Crane IDR Break Down");
                            }
                        }
                    }
                    break;
                case IDRResultCode.NoCarrier:
                    {
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                            , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead NoCarrier");
                        resultCode = ResultCode.NoCarrier;
                        HostIF.Instance.PostIDREvent("", currCommand.strRealSource, IDReadStatus.NoCarrier, EqpEvent.CarrierIDRead);
                    }
                    break;
                default:
                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                        , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead Failed ResultCode: " + idrResult.iResultCode.ToString());
                    resultCode = ResultCode.OtherError;
                    HostIF.Instance.PostIDREvent("Unknown", currCommand.strRealSource, IDReadStatus.Failure, EqpEvent.CarrierIDRead);
                    break;
            }
        }

        private void ScanUseCraneIDR()
        {
            switch (taskStep)
            {
                case 1:
                    {
                        if (!Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            //检查Fork上没有盒子，向Crane发生任务数据，启动任务
                            if (!CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                            {
                                if (IsHaveIDR)
                                {
                                    //如果Shelf2Shelf设置了IDRead bypass，告诉Crane 不读码
                                    if (SysSettings.Instance.CraneIDRReadRules.bShelf2ShelfReadBypass)
                                    {
                                        CraneDev.Instance.IDReadBypass(craneTag, true);
                                    }
                                    else
                                    {
                                        CraneDev.Instance.IDReadBypass(craneTag, false);
                                    }
                                }

                                CraneTaskData taskData = new CraneTaskData();
                                taskData.taskType = CraneTaskType.Scan;
                                taskData.strCarrierID = currCommand.strCarrierID;
                                taskData.strSourceAddr = currCommand.strRealSource;
                                taskData.strDestAddr = currCommand.strRealDest;
                                //向Crane发生任务数据
                                if (CraneDev.Instance.SendCraneCommand(craneTag, taskData))
                                {
                                    //记录Crane日志：成功向Crane发生任务数据
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                        , $"发送成功:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");

                                    //Crane任务启动
                                    if (CraneDev.Instance.CraneTaskStart(craneTag))
                                    {
                                        //记录Crane日志：启动Crane任务成功
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");

                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                        CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                        taskStep++;
                                    }
                                    else
                                    {
                                        //记录Crane日志：启动Crane任务失败
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动失败");
                                        CraneDev.Instance.CleanCraneCommand(craneTag);
                                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, ResultCode.OtherError);
                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                    }
                                }
                                else  //SendCraneCommand失败
                                {
                                    //记录Crane日志：向Crane发生任务数据失败
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                        , $"发送失败:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");
                                    if (++iFailCount == 3)
                                    {
                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                        endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                        modeState = CraneModeState.Idle;
                                    }
                                }
                            }
                            else //检查Fork上有盒子
                            {
                                //记录Crane日志：向Crane发送任务数据前，Fork上检测到盒子
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "向Crane发送任务数据前，Fork上检测到盒子");
                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                modeState = CraneModeState.Idle;
                            }
                        }
                        else
                        {
                            //记录Crane日志：启动Crane任务成功
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");

                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                            CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                            taskStep++;

                            CraneDev.Instance.setCraneTaskInfo(craneTag, 10, 10);
                        }
                        
                    }
                    break;
                case 2:
                    {

                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                        //Crane还未向源地址移动
                        if (craneTaskStep.iStep < 10)
                        {
                            if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                            {
                                //DealReadCardWithScan();
                                //if (resultCode != ResultCode.Success)
                                //{
                                //    //读码有问题的情况下才报告读码事件
                                //    HostIF.Instance.PostIDREvent(currCommand.strCarrierID, currCommand.strRealSource, IDReadStatus.Success, EqpEvent.CarrierIDRead);
                                //}
                               
                                //任务结束的处理
                                taskStep = 6;
                                
                                return;
                            }
                            return;
                        }
                        else if (craneTaskStep.iStep == 10)
                        {
                            if (IsHaveIDR && !SysSettings.Instance.CraneIDRReadRules.bShelf2ShelfReadBypass)
                            {
                                //重置Crane任务启动信号
                                CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                //Crane开始向源地址移动
                                modeState = CraneModeState.Active;
                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.CraneActive);
                                //记录Crane日志：Crane开始向源地址移动
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane开始向源地址移动");
                                taskStep++;

                                //仿真专用
                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                {
                                    CraneDev.Instance.setCraneTaskInfo(craneTag, 20, 10);
                                }
                            }
                            else //IC Stocker无IDR
                            {
                                if (bAction1Started == false && craneTaskStep.iAction == 10)
                                {
                                    //向目标位检测Shelf上有无Carrier高度移动
                                    bAction1Started = true;
                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                    //Crane开始向源地址移动
                                    modeState = CraneModeState.Active;
                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.CraneActive);
                                    //记录Crane日志：Crane开始向源地址移动
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane Step1, Action1");

                                    //仿真专用
                                    if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 10, 20);
                                    }
                                }
                                else if (bAction2Started == false && craneTaskStep.iAction == 20)
                                {
                                    //开始向取Carrier的高度移动
                                    bAction1Started = false;
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane Step1, Action2");
                                    if (CraneDev.Instance.IsCraneDetectedCarrier(craneTag))
                                    {
                                        taskStep++;

                                        //仿真专用
                                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                        {
                                            CraneDev.Instance.setCraneTaskInfo(craneTag, 20, 10);
                                        }
                                    }
                                    else
                                    {
                                        bEmptyRetrieval = true;
                                        taskStep = 10;  //EmptyRetrieval的处理
                                    }
                                }
                            }
                        }
                        else
                        {
                            taskStep++;
                        }
                    }
                    break;
                case 3:
                    {
                        //仿真专用
                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            CraneDev.Instance.setCraneTaskInfo(craneTag, 20, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10);
                        }

                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                        if (craneTaskStep.iStep < 20)
                        {
                            if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                            {
                                //DealReadCardWithScan();
                                //if (resultCode != ResultCode.Success)
                                //{
                                //    //读码有问题的情况下才报告读码事件
                                //    HostIF.Instance.PostIDREvent(currCommand.strCarrierID, currCommand.strRealSource, IDReadStatus.Success, EqpEvent.CarrierIDRead);
                                //}
                               
                                //任务结束的处理
                                taskStep = 6;
                                
                                return;
                            }
                        }
                        //Crane开始取货
                        if (craneTaskStep.iStep == 20)
                        {
                            if (bAction2Started == false && craneTaskStep.iAction == 20)
                            {
                                //Crane读码
                                bAction2Started = true;
                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ForkingStarted);
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action2");
                            }
                           
                            taskStep = 6;
                            bAction2Started = false;
                            bAction3Started = false;
                            bAction4Started = false;
                            bAction9Started = false;
                        }
                    }
                    break;
                case 6:
                    {
                        //Crane的Fork正在缩回，等待Crane任务完成信号
                        if (!CraneDev.Instance.IsCraneTaskComplete(craneTag) 
                            && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            return;
                        }
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Task Complete");
                        DealReadCardWithScan();
                        //Crane任务完成，答复完成信号
                        CraneDev.Instance.CraneTaskCompleteAck(craneTag);
                        //CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                        CraneDev.Instance.ResetCraneTaskStart(craneTag);
                        taskStep++;
                    }
                    break;
                case 7:
                    {
                        //Crane任务完成信号复位
                        if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                        {
                            return;
                        }

                        //复位答复完成信号
                        CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);

                        modeState = CraneModeState.Idle;
                        HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneIdle);
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneIdle, EqpEvent.CraneIdle.ToString(), "Crane Idle");

                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, resultCode);
                        LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);
                        LocationMng.Instance.UnReserveLocation(currCommand.strRealDest);
                        switch (resultCode)
                        {
                            case ResultCode.Success:
                                {
                                    if(CarrierMng.Instance.Exist(idrResult.strCarrierID))
                                    {
                                        CarrierMng.Instance.UpdateCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                    }
                                    else
                                    {
                                        string strOldCarrierID = LocationMng.Instance.GetLocationCarrierByAddress(currCommand.strRealSource);
                                        //删除数据库中该位置处的Carrier信息
                                        HostIF.Instance.PostCarrierEvent(strOldCarrierID, EqpEvent.CarrierRemoveCompleted);
                                        CarrierMng.Instance.DeleteCarrierInfo(strOldCarrierID);
                                        //在指令中的位置处安装读到的盒子信息
                                        CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                        HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                    }
                                }
                                break;
                            case ResultCode.IDReadFail:
                                {
                                    //1.删除原CarrierID的Carrier信息
                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);
                                    //2.添加UnKnownID的Carrier信息
                                    string strUnKnownID = CarrierMng.Instance.GetIDRFailCarrierID(currCommand.strRealSource);
                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, currCommand.strRealSource);
                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);
                                }
                                break;
                            case ResultCode.DuplicateID:
                                {
                                    //1.删除DuplicateID的盒子信息
                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                    CarrierMng.Instance.DeleteCarrierInfo(idrResult.strCarrierID);

                                    //2.在DuplicateID的位置处添加UnKnownID的Carrier信息
                                    string strUnKnownID = CarrierMng.Instance.GenDulplicateCarrierID(idrResult.strCarrierID);
                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, dupCarrierRealLoc);
                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);

                                    //3.删除指令中CarrierID位置处的Carrier信息
                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                    CarrierMng.Instance.DeleteCarrierInfo(idrResult.strCarrierID);

                                    //4.在指令中的位置处安装读到的盒子信息
                                    CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                }
                                break;
                            case ResultCode.MismatchID:
                                {
                                    //1.删除指令中CarrierID位置处的Carrier信息
                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                    //2.在指令中的位置处安装读到的盒子信息
                                    CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                }
                                break;
                            case ResultCode.NoCarrier:
                                {
                                    //1.判断此位置原来是否有CST，如果有则删除
                                    if(LocationMng.Instance.GetLocationCarrierByAddress(currCommand.strRealSource) != "")
                                    {
                                        HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                        CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);
                                    }
                                }
                                break;
                            case ResultCode.OtherError: //与读码失败处理相同
                                {
                                    //1.删除原CarrierID的Carrier信息
                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);
                                    //2.添加UnKnownID的Carrier信息
                                    string strUnKnownID = CarrierMng.Instance.GetIDRFailCarrierID(currCommand.strRealSource);
                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, currCommand.strRealSource);
                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);
                                }
                                break;
                            case ResultCode.TypeMismatch:
                            case ResultCode.ShelfZoneFull:
                            default:
                                break;
                        }

                        //结束Crane的当前任务
                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ScanCompleted);
                        endCurrentCommand(TransferState.None, ResultCode.Success);
                        modeState = CraneModeState.Idle;

                        //2OutPort时,其余过程放到PortObj中执行
                        //TransferChange2Port(currCommand.strRealDest, currCommand);
                    }
                    break;
            }
        }

        private void ScanUseSomeShelfIDR()
        {
            switch (taskStep)
            {
                case 1:
                    {
                        if (!Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            //检查Fork上没有盒子，向Crane发生任务数据，启动任务
                            if (!CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                            {
                                CraneTaskData taskData = new CraneTaskData();
                                taskData.taskType = CraneTaskType.Scan;
                                taskData.strCarrierID = currCommand.strCarrierID;
                                taskData.strSourceAddr = currCommand.strRealSource;
                                taskData.strDestAddr = currCommand.strRealDest;
                                //向Crane发生任务数据
                                if (CraneDev.Instance.SendCraneCommand(craneTag, taskData))
                                {
                                    //记录Crane日志：成功向Crane发生任务数据
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                        , $"发送成功:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");

                                    //Crane任务启动
                                    if (CraneDev.Instance.CraneTaskStart(craneTag))
                                    {
                                        //记录Crane日志：启动Crane任务成功
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.ScanInitiated, EqpEvent.ScanInitiated.ToString(), "Crane启动成功");

                                        //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ScanInitiated);
                                        CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                        taskStep++;
                                    }
                                    else
                                    {
                                        //记录Crane日志：启动Crane任务失败
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.ScanInitiated, EqpEvent.ScanInitiated.ToString(), "Crane启动失败");
                                        CraneDev.Instance.CleanCraneCommand(craneTag);
                                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, ResultCode.OtherError);
                                        //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ScanCompleted);
                                    }
                                }
                                else  //SendCraneCommand失败
                                {
                                    //记录Crane日志：向Crane发生任务数据失败
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.ScanInitiated, EqpEvent.ScanInitiated.ToString()
                                        , $"发送失败:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");
                                    if (++iFailCount == 3)
                                    {
                                        //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ScanCompleted);
                                        endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                        modeState = CraneModeState.Idle;
                                    }
                                }
                            }
                            else //检查Fork上有盒子
                            {
                                //记录Crane日志：向Crane发送任务数据前，Fork上检测到盒子
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.ScanInitiated, EqpEvent.ScanInitiated.ToString(), "向Crane发送任务数据前，Fork上检测到盒子");
                                //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ScanCompleted);
                                endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                modeState = CraneModeState.Idle;
                            }
                        }
                        else
                        {
                            //记录Crane日志：启动Crane任务成功
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.ScanInitiated, EqpEvent.ScanInitiated.ToString(), "Crane启动成功");

                            //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ScanInitiated);
                            CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                            taskStep++;

                            bAction1Started = false;
                            bAction2Started = false;
                            bAction3Started = false;
                            bAction4Started = false;
                            bAction9Started = false;

                            CraneDev.Instance.setCraneTaskInfo(craneTag, 10, 10);
                        }
                    }
                    break;
                case 2:
                    {
                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                        //Crane还未向源地址移动
                        if (craneTaskStep.iStep < 10 && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            return;
                        }
                        else if (craneTaskStep.iStep == 10)
                        {
                            if (bAction1Started == false && craneTaskStep.iAction == 10)
                            {
                                //向目标位检测Shelf上有无Carrier高度移动
                                bAction1Started = true;
                                if (!Proj.CacheData.GlobalData.Instance.gbSimulation)
                                {
                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                }
                                else
                                {
                                    taskStep++;
                                }
                                //Crane开始向源地址移动
                                modeState = CraneModeState.Active;
                                //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.CraneActive);
                                //记录Crane日志：Crane开始向源地址移动
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane Step1, Action1");
                            }
                        }
                        else  //无Carrier的处理
                        {
                            taskStep++;
                            bAction1Started = false;
                            bAction2Started = false;
                            bAction3Started = false;
                            bAction4Started = false;
                            bAction9Started = false;
                        }

                        //仿真专用
                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            CraneDev.Instance.setCraneTaskInfo(craneTag, 20, 10);
                        }
                    }
                    break;
                case 3:
                    {
                        //仿真专用
                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            CraneDev.Instance.setCraneTaskInfo(craneTag, 20, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10);
                        }

                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                        if (craneTaskStep.iStep < 20)
                        {
                            //向取Carrier的高度移动中
                            return;
                        }
                        //Crane开始取货
                        if (craneTaskStep.iStep == 20)
                        {
                            if (bAction2Started == false && craneTaskStep.iAction == 20)
                            {
                                //Crane开始伸Fork
                                bAction2Started = true;
                                //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ForkingStarted);
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action2");
                            }
                            else if (bAction3Started == false && craneTaskStep.iAction == 30)
                            {
                                //Crane去读码
                                bAction3Started = true;
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action3");
                            }
                            else if (bAction9Started == false && craneTaskStep.iAction == 40)
                            {
                                //Crane开始缩回Fork
                                bAction9Started = true;
                                //屏蔽 HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkRised);
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action4");
                                
                                bool bCranePickCarrier = CraneDev.Instance.IsCraneForkHasCarrier(craneTag);
                                if (bCranePickCarrier)
                                {
                                    resultCode = ResultCode.Success;
                                    //更新盒子位置
                                    CarrierMng.Instance.CranePickCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealSource);
                                    strCarrierID = currCommand.strCarrierID;
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                        $"CranePickCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strRealSource}");
                                    taskStep++;
                                }
                                else  //无Carrier的处理
                                {
                                    //屏蔽 HostIF.Instance.PostIDREvent(currCommand.strCarrierID, currCommand.strCarrierLoc, IDReadStatus.NoCarrier, EqpEvent.CarrierIDRead);

                                    idrResult.iResultCode = (int)IDRResultCode.NoCarrier;
                                    idrResult.strCarrierID = string.Empty;
                                    resultCode = ResultCode.OtherError;
                                    CraneDev.Instance.CraneTaskAbort(craneTag);
                                    taskStep = 14;
                                }

                                bAction2Started = false;
                                bAction3Started = false;
                                bAction4Started = false;
                                bAction9Started = false;

                                //仿真专用
                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                {
                                    CraneDev.Instance.setCraneTaskInfo(craneTag, 30, 10);
                                }
                            }
                        }
                    }
                    break;
                case 4:
                    {
                        //仿真专用
                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            CraneDev.Instance.setCraneTaskInfo(craneTag, 30, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10);
                            bAction1Started = false;
                            taskStep = 8;
                            break;
                        }

                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                        if (craneTaskStep.iStep < 30)
                        {
                            //Crane正在缩回Fork
                            return;
                        }
                        if (craneTaskStep.iStep == 30)
                        {
                            if (bAction1Started == false && craneTaskStep.iAction == 20)
                            {
                                bAction1Started = true;

                                //Crane缩回Fork完成,Fork开始向目的地检查Carrier有无的高度移动
                                //屏蔽 HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkingCompleted); //此处的该事件可能因为EmptyRetrieval而不上报
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step3, Action2");

                                if (CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                {
                                    CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Transferring);
                                    //HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierTransferring);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierTransferring, EqpEvent.CarrierTransferring.ToString(), "Crane开始搬送盒子");
                                }
                                CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);

                                //if (bDoubleStorage = CraneDev.Instance.IsCraneDetectedCarrier(craneTag))
                                //{
                                //    HistoryWriter.Instance.EqpEventLog("**********Crane有CST***********************", 44, craneTaskStep.iStep.ToString(), craneTaskStep.iAction.ToString());
                                //    //检查到目标位置有Carrier
                                //    taskStep = 30;
                                //}
                                //else
                                {
                                    bAction1Started = false;
                                    taskStep++;
                                }

                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                {
                                    CraneDev.Instance.setCraneTaskInfo(craneTag, 40, 10); ;
                                }
                            }
                        }
                    }
                    break;
                case 5:
                    {
                        //仿真专用
                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            CraneDev.Instance.setCraneTaskInfo(craneTag, 40, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10); ;
                        }

                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                        if (craneTaskStep.iStep < 40)
                        {
                            //Crane正在向目的地移动
                            if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                            {
                                if (iLastScanStep == 40 && (iLastScanAction == 20 || iLastScanAction == 30))
                                {
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, 10001, "StepError", "Get Scan step error, case5");
                                    if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                    {
                                        bDoubleStorage = true;
                                        //盒子还在货叉上，发生了DoubleStorage
                                        taskStep = 30;
                                    }
                                    else
                                    {
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, 10001, "StepError", "Get Scan step error, case5  IsCraneTaskComplete");
                                        CarrierMng.Instance.CranePlaceCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealDest);
                                        strCarrierID = "";
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                            $"CranePlaceCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strDest} ");

                                        CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Compeleted);

                                        taskStep++;
                                    }

                                    bAction2Started = false;
                                    bAction3Started = false;
                                }

                            }
                            return;
                        }
                        //Crane开始放下盒子
                        if (craneTaskStep.iStep == 40)
                        {
                            if (CraneDev.Instance.GetCraneWorkingStatus(craneTag) == CraneWorkingStatus.Paused)
                            {
                                if (CraneDev.Instance.IsDoubleStorage(craneTag))
                                {
                                    bDoubleStorage = true;
                                    taskStep = 30;  //DoubleStorage的处理
                                    break;
                                }
                            }

                            if (bAction2Started == false && craneTaskStep.iAction == 20)
                            {
                                iLastScanStep = 40;
                                iLastScanAction = 20;
                                //Crane开始伸Fork
                                bAction2Started = true;
                                //屏蔽 HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkingStarted);
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action2");
                            }
                            else if (bAction3Started == false && craneTaskStep.iAction == 30)
                            {
                                iLastScanStep = 40;
                                iLastScanAction = 30;
                                //Crane的Fork开始下降
                                bAction3Started = true;
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action3");

                            }
                            else if (bAction4Started == false && craneTaskStep.iAction == 40)
                            {
                                //Crane的Fork开始缩回(ForkDowned)
                                //屏蔽 HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkDowned);
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action4");

                                //DoubleStorage检查
                                if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                {
                                    bDoubleStorage = true;
                                    //盒子还在货叉上，发生了DoubleStorage
                                    taskStep = 30;
                                }
                                else
                                {
                                    CarrierMng.Instance.CranePlaceCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealDest);
                                    strCarrierID = "";
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                        $"CranePlaceCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strDest} ");

                                    CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Compeleted);

                                    taskStep++;
                                }

                                bAction2Started = false;
                                bAction3Started = false;
                            }
                        }
                    }
                    break;
                case 6:
                    {
                        //Crane的Fork正在缩回，等待Crane任务完成信号
                        if (!CraneDev.Instance.IsCraneTaskComplete(craneTag))
                        {
                            return;
                        }
                        iLastScanStep = 0;
                        iLastScanAction = 0;

                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Task Complete");
                        //Crane任务完成，答复完成信号
                        CraneDev.Instance.CraneTaskCompleteAck(craneTag);
                        //CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);

                        if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                        {
                            bDoubleStorage = true;
                            resultCode = ResultCode.OtherError;
                        }
                        else
                        {
                            resultCode = ResultCode.Success;
                        }
                        taskStep++;
                    }
                    break;
                case 7:
                    {
                        //Crane任务完成信号复位
                        if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                        {
                            //重置Crane任务启动信号
                            CraneDev.Instance.ResetCraneTaskStart(craneTag);
                            //Crane任务完成，答复完成信号
                            CraneDev.Instance.CraneTaskCompleteAck(craneTag);//PLC没有将Complete信号置成0，STKC重新Ack
                            return;
                        }

                        //复位答复完成信号
                        CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);

                        modeState = CraneModeState.Idle;
                        //屏蔽 HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneIdle);
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneIdle, EqpEvent.CraneIdle.ToString(), "Crane Idle");

                        switch (resultCode)
                        {
                            case ResultCode.Success:
                                {
                                    taskStep++;
                                }
                                break;
                            case ResultCode.OtherError:
                            default:
                                {
                                    TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, resultCode);
                                    LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);
                                    LocationMng.Instance.UnReserveLocation(currCommand.strRealDest);
                                    //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                    // 更新画面
                                    CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                    //结束Crane的当前任务
                                    endCurrentCommand(TransferState.None, resultCode);
                                }
                                break;
                        }
                    }
                    break;
                case 8:
                    {                       
                        idrResult = CraneDev.Instance.GetIDRResult(craneTag);

                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            idrResult.iResultCode = 1;
                            idrResult.strCarrierID = "CAR001";
                        }

                        if (0 == idrResult.iResultCode)
                        {
                            return;
                        }
                        switch (idrResult.iResultCode)
                        {
                            case 1: //成功（需要检查是否Duplicate）
                                {
                                    //第二个参数carrierLoc可能会有问题，lzt
                                    if (currCommand.strCarrierID != idrResult.strCarrierID)
                                    {
                                        if (CarrierMng.Instance.IsCarrierDuplicate(idrResult.strCarrierID, currCommand.strRealDest, ref dupCarrierRealLoc))
                                        {
                                            HistoryWriter.Instance.EqpEventLog(craneTag, (int)EqpEvent.CarrierIDRead
                                                    , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead DuplicateID:{idrResult.strCarrierID}");
                                            resultCode = ResultCode.DuplicateID;
                                            //屏蔽 HostIF.Instance.PostIDREvent(currCommand.strCarrierID, currCommand.strCarrierLoc, IDReadStatus.Duplicate, EqpEvent.CarrierIDRead);
                                        }
                                        else
                                        {
                                            HistoryWriter.Instance.EqpEventLog(craneTag, (int)EqpEvent.CarrierIDRead
                                                    , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead MismatchID:{idrResult.strCarrierID}");
                                            resultCode = ResultCode.MismatchID;
                                            //屏蔽 HostIF.Instance.PostIDREvent(currCommand.strCarrierID, currCommand.strCarrierLoc, IDReadStatus.Mismatch, EqpEvent.CarrierIDRead);
                                        }
                                    } 
                                    else
                                    {
                                        HistoryWriter.Instance.EqpEventLog(craneTag, (int)EqpEvent.CarrierIDRead
                                                    , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead Success:{idrResult.strCarrierID}");
                                        resultCode = ResultCode.Success;
                                        //屏蔽 HostIF.Instance.PostIDREvent(currCommand.strCarrierID, currCommand.strCarrierLoc, IDReadStatus.Success, EqpEvent.CarrierIDRead);
                                    }
                                }
                                break;
                            case 2: //不一致（通过Port执行Scan不会出现此种情况）
                                break;
                            case 3: //失败
                                {
                                    HistoryWriter.Instance.EqpEventLog(craneTag, (int)EqpEvent.CarrierIDRead
                                        , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead IDReadFail");
                                    resultCode = ResultCode.IDReadFail;
                                    //屏蔽 HostIF.Instance.PostIDREvent(currCommand.strCarrierID, currCommand.strCarrierLoc, IDReadStatus.Failure, EqpEvent.CarrierIDRead);
                                }
                                break;
                        }
                        taskStep++;                           
         
                    }
                    break;
                case 9:
                    {
                        if (!Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            //检查Fork上没有盒子，向Crane发生任务数据，启动任务
                            if (!CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                            {
                                CraneTaskData taskData = new CraneTaskData();
                                taskData.taskType = CraneTaskType.Scan;
                                taskData.strCarrierID = currCommand.strCarrierID;
                                taskData.strSourceAddr = currCommand.strRealDest; //交换原地址和目的地址
                                taskData.strDestAddr = currCommand.strRealSource;
                                //向Crane发生任务数据
                                if (CraneDev.Instance.SendCraneCommand(craneTag, taskData))
                                {
                                    //记录Crane日志：成功向Crane发生任务数据
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                        , $"发送成功:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");

                                    //Crane任务启动
                                    if (CraneDev.Instance.CraneTaskStart(craneTag))
                                    {
                                        //记录Crane日志：启动Crane任务成功
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");

                                        //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                        CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickStart", currCommand.strCarrierID, currCommand.strRealDest, currCommand.strRealSource);
                                        taskStep++;
                                    }
                                    else
                                    {
                                        //记录Crane日志：启动Crane任务失败
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动失败");
                                        CraneDev.Instance.CleanCraneCommand(craneTag);
                                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, ResultCode.OtherError);
                                        //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                    }
                                }
                                else  //SendCraneCommand失败
                                {
                                    //记录Crane日志：向Crane发生任务数据失败
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                        , $"发送失败:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");
                                    if (++iFailCount == 3)
                                    {
                                        //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                        endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                        modeState = CraneModeState.Idle;
                                    }
                                }
                            }
                            else //检查Fork上有盒子
                            {
                                //记录Crane日志：向Crane发送任务数据前，Fork上检测到盒子
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "向Crane发送任务数据前，Fork上检测到盒子");
                                //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                modeState = CraneModeState.Idle;
                            }
                        }
                        else
                        {
                            //记录Crane日志：启动Crane任务成功
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.ScanInitiated, EqpEvent.ScanInitiated.ToString(), "Crane启动成功");

                            //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ScanInitiated);
                            CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                            taskStep++;

                            bAction1Started = false;
                            bAction2Started = false;
                            bAction3Started = false;
                            bAction4Started = false;
                            bAction9Started = false;

                            CraneDev.Instance.setCraneTaskInfo(craneTag, 10, 10);
                        }
                    }
                    break;
                case 10:
                    {
                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                        //Crane还未向源地址移动
                        if (craneTaskStep.iStep < 10 && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            return;
                        }
                        else if (craneTaskStep.iStep == 10)
                        {
                            if (bAction1Started == false && craneTaskStep.iAction == 10)
                            {
                                //向目标位检测有无Carrier高度移动
                                bAction1Started = true;
                                if (!Proj.CacheData.GlobalData.Instance.gbSimulation)
                                {
                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                }
                                else
                                {
                                    taskStep++;
                                }
                                //Crane开始向源地址移动
                                modeState = CraneModeState.Active;
                                //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.CraneActive);
                                //记录Crane日志：Crane开始向源地址移动
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane Step1, Action1");
                            }
                                //开始向取Carrier的高度移动
                        }
                        else
                        {
                            taskStep++;
                            bAction1Started = false;
                            bAction2Started = false;
                            bAction3Started = false;
                            bAction4Started = false;
                            bAction9Started = false;
                        }

                        //仿真专用
                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            CraneDev.Instance.setCraneTaskInfo(craneTag, 20, 10);
                        }
                    }
                    break;
                case 11:
                    {
                        //仿真专用
                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            CraneDev.Instance.setCraneTaskInfo(craneTag, 20, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10);
                        }

                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                        if (craneTaskStep.iStep < 20)
                        {
                            //向取Carrier的高度移动中
                            return;
                        }
                        //Crane开始取货
                        if (craneTaskStep.iStep == 20)
                        {
                            if (bAction2Started == false && craneTaskStep.iAction == 20)
                            {
                                //Crane开始伸Fork
                                bAction2Started = true;
                                //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ForkingStarted);
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action2");
                            }
                            else if (bAction3Started == false && craneTaskStep.iAction == 30)
                            {
                                //Crane开始抬起Fork
                                bAction3Started = true;
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action3");
                            }
                            else if (bAction9Started == false && craneTaskStep.iAction == 40)
                            {
                                //Crane开始缩回Fork
                                bAction9Started = true;
                                //屏蔽 HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkRised);
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action4");
                                
                                bool bCranePickCarrier = CraneDev.Instance.IsCraneForkHasCarrier(craneTag);
                                if (bCranePickCarrier)
                                {
                                    //resultCode = ResultCode.Success;
                                    //更新盒子位置
                                    CarrierMng.Instance.CranePickCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealDest);
                                    strCarrierID = currCommand.strCarrierID;
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                        $"CranePickCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strRealDest}");
                                    taskStep++;
                                }
                                else //没有取到盒子
                                {
                                    //Source Empty
                                    bEmptyRetrieval = true;
                                    taskStep = 20;
                                }

                                bAction2Started = false;
                                bAction3Started = false;
                                bAction4Started = false;
                                bAction9Started = false;

                                //仿真专用
                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                {
                                    CraneDev.Instance.setCraneTaskInfo(craneTag, 30, 10);
                                }
                            }
                        }
                        else if (craneTaskStep.iStep > 20)
                        {
                            HistoryWriter.Instance.EqpEventLog(strCraneName, 10001, "StepError", "Get Scan step error, case11");
                            bAction9Started = true;
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action4");
                            bool bCranePickCarrier = CraneDev.Instance.IsCraneForkHasCarrier(craneTag);
                            if (bCranePickCarrier)
                            {
                                CarrierMng.Instance.CranePickCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealDest);
                                strCarrierID = currCommand.strCarrierID;
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                    $"CranePickCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strRealDest}");
                                taskStep++;
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action4");
                            }
                            else //没有取到盒子
                            {
                                bEmptyRetrieval = true;
                                taskStep = 20;
                            }
                            bAction2Started = false;
                            bAction3Started = false;
                            bAction4Started = false;
                            bAction9Started = false;
                        }
                    }
                    break;
                case 12:
                    {
                        //仿真专用
                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            CraneDev.Instance.setCraneTaskInfo(craneTag, 30, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10); ;
                        }

                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                        if (craneTaskStep.iStep < 30)
                        {
                            //Crane正在缩回Fork
                            return;
                        }
                        if (craneTaskStep.iStep == 30)
                        {
                            if (bAction1Started == false && craneTaskStep.iAction == 20)
                            {
                                bAction1Started = true;

                                //Crane缩回Fork完成,Fork开始向目的地检查Carrier有无的高度移动
                                //屏蔽 HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkingCompleted); //此处的该事件可能因为EmptyRetrieval而不上报
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step3, Action2");

                                if (CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                {
                                    CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Transferring);
                                    //HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierTransferring);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierTransferring, EqpEvent.CarrierTransferring.ToString(), "Crane开始搬送盒子");
                                }
                                CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickComplete", currCommand.strCarrierID, currCommand.strRealDest, currCommand.strRealSource);
                                CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceStart", currCommand.strCarrierID, currCommand.strRealDest, currCommand.strRealSource);

                                //if (bDoubleStorage = CraneDev.Instance.IsCraneDetectedCarrier(craneTag))
                                //{
                                //    HistoryWriter.Instance.EqpEventLog("**********Crane有CST***********************", 1212, craneTaskStep.iStep.ToString(), craneTaskStep.iAction.ToString());
                                //    //检查到目标位置有Carrier
                                //    taskStep = 30;
                                //}
                                //else
                                {
                                    bAction1Started = false;
                                    taskStep++;
                                }
                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                {
                                    CraneDev.Instance.setCraneTaskInfo(craneTag, 40, 10); ;
                                }
                            }
                        }
                    }
                    break;
                case 13:
                    {
                        //仿真专用
                        if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                        {
                            CraneDev.Instance.setCraneTaskInfo(craneTag, 40, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10); ;
                        }

                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                        if (craneTaskStep.iStep < 40)
                        {
                            //Crane正在向目的地移动
                            if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                            {
                                if (iLastScanStep == 40 && (iLastScanAction == 20 || iLastScanAction == 30))
                                {
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, 10001, "StepError", "Get Scan step error, case13");
                                    if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                    {
                                        bDoubleStorage = true;
                                        taskStep = 30;
                                    }
                                    else
                                    {
                                        CarrierMng.Instance.CranePlaceCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealDest);
                                        strCarrierID = "";
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                            $"CranePlaceCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strDest} ");
                                        CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Compeleted);
                                        taskStep++;
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, 10001, "StepError", "Get Scan step error, case13 IsCraneTaskComplete");
                                    }
                                    bAction2Started = false;
                                    bAction3Started = false;
                                }
                            }
                            return;
                        }
                        //Crane开始放下盒子
                        if (craneTaskStep.iStep == 40)
                        {
                            if (bAction2Started == false && craneTaskStep.iAction == 20)
                            {
                                iLastScanStep = 40;
                                iLastScanAction = 20;
                                //Crane开始伸Fork
                                bAction2Started = true;
                                //屏蔽 HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkingStarted);
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action2");
                            }
                            else if (bAction3Started == false && craneTaskStep.iAction == 30)
                            {
                                iLastScanStep = 40;
                                iLastScanAction = 30;
                                //Crane的Fork开始下降
                                bAction3Started = true;
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action3");

                            }
                            else if (bAction4Started == false && craneTaskStep.iAction == 40)
                            {
                                //Crane的Fork开始缩回(ForkDowned)
                                //屏蔽 HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkDowned);
                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action4");

                                //DoubleStorage检查
                                if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                {
                                    //盒子还在货叉上，发生了DoubleStorage
                                    bDoubleStorage = true;
                                    taskStep = 30;
                                }
                                else
                                {
                                    CarrierMng.Instance.CranePlaceCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealSource);
                                    strCarrierID = "";
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                        $"CranePlaceCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strRealSource} ");

                                    CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Compeleted);

                                    taskStep++;
                                }

                                bAction2Started = false;
                                bAction3Started = false;
                            }
                        }
                    }
                    break;
                case 14:
                    {
                        if (idrResult.iResultCode != (int)IDRResultCode.NoCarrier)
                        {
                            //Crane的Fork正在缩回，等待Crane任务完成信号
                            if (!CraneDev.Instance.IsCraneTaskComplete(craneTag))
                            {
                                return;
                            }
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Task Complete");
                            //Crane任务完成，答复完成信号
                            CraneDev.Instance.CraneTaskCompleteAck(craneTag);
                            //CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                        }
                        else
                        {
                            if (!CraneDev.Instance.IsCraneAbortComplete(craneTag))
                            {
                                return;
                            }
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Task Complete");
                            //Crane任务完成，答复完成信号
                            CraneDev.Instance.CraneAbortCompleteAck(craneTag);
                            //CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                        }


                        if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                        {
                            bDoubleStorage = true;
                            resultCode = ResultCode.OtherError;
                            taskStep = 30;
                        }
                        else
                        {
                           // resultCode = ResultCode.Success;
                            taskStep++;
                        }
                        iLastScanStep = 0;
                        iLastScanAction = 0;
                    }
                    break;
                case 15:
                    {
                        if (idrResult.iResultCode != (int)IDRResultCode.NoCarrier)
                        {
                            //Crane任务完成信号复位
                            if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                            {
                                //重置Crane任务启动信号
                                CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                //Crane任务完成，答复完成信号
                                CraneDev.Instance.CraneTaskCompleteAck(craneTag);//PLC没有将Complete信号置成0，STKC重新Ack
                                return;
                            }

                            //复位答复完成信号
                            CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);
                        }
                        else
                        {
                            if (CraneDev.Instance.IsCraneAbortComplete(craneTag))
                            {
                                return;
                            }
                            CraneDev.Instance.ResetCraneAbortCompleteAck(craneTag);
                        }

                        modeState = CraneModeState.Idle;
                        //屏蔽 HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneIdle);
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneIdle, EqpEvent.CraneIdle.ToString(), "Crane Idle");

                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, resultCode);
                        LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);
                        LocationMng.Instance.UnReserveLocation(currCommand.strRealDest);
                        //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ScanCompleted); //缺少IDReadStatus，lzt
                        switch (resultCode)
                        {
                            case ResultCode.Success:
                                {
                                }
                                break;
                            case ResultCode.IDReadFail:
                                {
                                    //1.删除原CarrierID的Carrier信息
                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                    //2.添加UnKnownID的Carrier信息
                                    string strUnKnownID = CarrierMng.Instance.GetIDRFailCarrierID(currCommand.strRealSource);
                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, currCommand.strRealSource);
                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);
                                }
                                break;
                            case ResultCode.DuplicateID:
                                {
                                    //1.删除DuplicateID的盒子信息
                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                    CarrierMng.Instance.DeleteCarrierInfo(idrResult.strCarrierID);

                                    //2.在DuplicateID的位置处添加UnKnownID的Carrier信息
                                    string strUnKnownID = CarrierMng.Instance.GenDulplicateCarrierID(idrResult.strCarrierID);
                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, dupCarrierRealLoc);
                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);

                                    //3.删除指令中CarrierID位置处的Carrier信息
                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                    //4.在指令中的位置处安装读到的盒子信息
                                    CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                }
                                break;
                            case ResultCode.MismatchID:
                                {
                                    //1.删除指令中CarrierID位置处的Carrier信息
                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                    //2.在指令中的位置处安装读到的盒子信息
                                    CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                }
                                break;
                            case ResultCode.OtherError: //NoCarrier
                                {
                                    if (idrResult.iResultCode == (int)IDRResultCode.NoCarrier)
                                    {
                                        //1.删除原来位置的盒子信息
                                        HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                        CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                        //2.在原来位置处添加UnKnownID的Carrier信息
                                        string strUnKnownID = CarrierMng.Instance.GenEmptyRetrvScanCarrierID(currCommand.strCarrierID);
                                        CarrierMng.Instance.AddCarrierInfo(strUnKnownID, currCommand.strCarrierLoc);
                                        HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);
                                    }
                                }
                                break;
                            case ResultCode.TypeMismatch:
                            case ResultCode.ShelfZoneFull:
                            default:
                                break;
                        }
                        // 更新画面
                        CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                        //结束Crane的当前任务
                        //屏蔽 HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ScanCompleted);
                        endCurrentCommand(TransferState.None, resultCode);                        
                    }
                    break;
                case 20:  //EmptyRetrival的处理
                    {
                        //EmptyRetrival对应的AlarmID
                        //屏蔽 HostIF.Instance.PostAlarm((uint)AlarmCode.SourceEmpty);
                        //屏蔽 HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty", currCommand.strStockerCraneID, "ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmSet);
                        taskStep++;
                    }
                    break;
                case 21:
                    {
                        //如果收到了Retry指令,回到Step3重新执行
                        //if ()
                        //{
                        //    CraneDev.Instance.CraneTaskResume();
                        //    taskStep = 3;
                        //}
                    }
                    break;
                case 30:  //DoubleStorage的处理
                    {
                        //DoubleStorage的处理对应的AlarmID
                        //屏蔽 HostIF.Instance.PostAlarm((uint)AlarmCode.DoubleStorage);
                        //屏蔽 HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "DestOccupied", GlobalData.Instance.gbStockerCraneID, "ABORT", (int)AlarmCode.DoubleStorage, EqpEvent.AlarmSet);
                        taskStep++;
                    }
                    break;
                case 31:
                    {
                        //如果收到了Retry指令,回到Step3重新执行
                        //if ()
                        //{
                        //    CraneDev.Instance.CraneTaskResume();
                        //    taskStep = 3;
                        //}
                    }
                    break;
            }
        }

        private void endCurrentCommand(/*TransferState transferState,ResultCode result*/)
        {
            iFailCount = 0;
            taskStep = 0;
            //TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, transferState, result);
            CraneMng.Instance.UpdateCraneCommandID(strCraneName, "");

            //int iIndex = CacheData.GlobalData.Instance.gbEnhancedTransfers.Count;
            //if (iIndex > 0)
            //{
            //    foreach (EnhancedTransferCommand enhancedTransferCommand in Proj.CacheData.GlobalData.Instance.gbEnhancedTransfers)
            //    {
            //        if (enhancedTransferCommand.strCommandID == currCommand.strCommandID
            //            && CacheData.GlobalData.Instance.gbSCMode != SCMode.Test)
            //        {
            //            enhancedTransferCommand.bCraneComplete = true;
            //        }
            //    }
            //}

            currCommand = null;
        }
        private void endCurrentCommand(TransferState transferState,ResultCode result)
        {
            iFailCount = 0;
            taskStep = 0;
            TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, transferState, result);
            CraneMng.Instance.UpdateCraneCommandID(strCraneName, "");
            bEmptyRetrieval = false;
            bDoubleStorage = false;
            //int iIndex = CacheData.GlobalData.Instance.gbEnhancedTransfers.Count;
            //if (iIndex > 0)
            //{
            //    foreach (EnhancedTransferCommand enhancedTransferCommand in Proj.CacheData.GlobalData.Instance.gbEnhancedTransfers)
            //    {
            //        if (enhancedTransferCommand.strCommandID == currCommand.strCommandID
            //            && CacheData.GlobalData.Instance.gbSCMode != SCMode.Test)
            //        {
            //            enhancedTransferCommand.bCraneComplete = true;
            //        }
            //    }
            //}

            currCommand = null;
        }
        private void taskExecuteThreadFunc()
        {
            if (currCommand != null)
            {
                //
                if (currCommand.strCommandName == "TRANSFER")
                {
                    if (iAutoPlaceCounter != 0)
                    {
                        iAutoPlaceCounter = 0;
                    }
                    switch (currCommand.transferType)
                    {
                        case TransferType.Shelf2Shelf:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bShelf2ShelfReadBypass;
                            TransferCommand();
                            break;
                        case TransferType.Shelf2EqPort:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bShelf2EqPortReadBypass;
                            TransferCommand();
                            break;
                        case TransferType.Shelf2OutPort:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bShelf2OutPortReadBypass;
                            TransferCommand();
                            break;
                        case TransferType.EqPort2EqPort:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bEqPort2EqPortReadBypass;
                            TransferCommand();
                            break;
                        case TransferType.EqPort2OutPort:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bEqPort2OutPortReadBypass;
                            TransferCommand();
                            break;
                        case TransferType.EqPort2Shlef:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bEqPort2ShelfReadBypass;
                            TransferCommand();
                            break;
                        case TransferType.InPort2EqPort:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bInPort2EqPortReadBypass;
                            TransferCommand();
                            break;
                        case TransferType.InPort2OutPort:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bInPort2OutPortReadBypass;
                            TransferCommand();
                            break;
                        case TransferType.InPort2Shelf:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bInPort2ShelfReadBypass;
                            TransferCommand();
                            break;
                        case TransferType.Crane2EqPort:
                            PlaceCommand();
                            break;
                        case TransferType.Crane2OutPort:
                            PlaceCommand();
                            break;
                        case TransferType.Crane2Shelf:
                            //Crane2ShelfCommand();
                            PlaceCommand();
                            break;
                        case TransferType.Shelf2Crane:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bShelf2CraneReadBypass;
                            PickCommand();
                            break;
                        case TransferType.InPort2Crane:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bInPort2CraneReadBypass;
                            PickCommand();
                            break;
                        case TransferType.EqPort2Crane:
                            bReadBypass = SysSettings.Instance.CraneIDRReadRules.bEqPort2CraneReadBypass;
                            PickCommand();
                            break;
                    }
                }
                else if (currCommand.strCommandName == "SCAN")
                {
                    executeScanCommand();
                }
                else if (currCommand.strCommandName == "MOVE")
                {
                    executeMoveCommand();
                }
                else if (currCommand.strCommandName == "DOUBLECHECK")
                {
                    DoubleCheckCommand();
                }
            }
            else  //无指令且Fork上有Carrier，自动创建放下Carrier的指令
            {
                if (!bCraneAlarmed && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                {
                    //if (ControlState.OnlineRemote == Proj.CacheData.GlobalData.Instance.gbControlState
                    //    && GlobalData.Instance.gbMCSCommunication == MCSCommunication.Communicating
                    //    && GlobalData.Instance.gbHSMSState == HSMSState.Connected)
                    //{
                    //    return;
                    //}
                    //任务队列中没有该放下Carrier的指令
                    iAutoPlaceCounter += 1;
                    strCarrierID = LocationMng.Instance.GetLocationCarrierByLocName(strCraneName);
                    if (!TransferMng.Instance.IsHaveCraneCommand(strCraneName, strCarrierID) 
                        && iAutoPlaceCounter > 50)
                    {
                        Log.Logger.Instance.ExceptionLog("taskExecuteThreadFunc:Line=1939 ");
                        iAutoPlaceCounter = 0;
                        //生成指令
                        Dictionary<string, object> dicParams = new Dictionary<string, object>();
                        dicParams.Add("CARRIERID", strCarrierID);
                        dicParams.Add("SOURCE", strCraneName);

                        CraneMng.Instance.AddTransferCommand(dicParams);
                    }
                }
            }
        }

        //检查Location和Carrier两个表一致性
        private void CheckDataBaseInfoWithLocationAndCarrier()
        {
            try
            {
                TpLocationList locList = TpLocationList.GetAll();
                foreach (TpLocation loc in locList)
                {
                    if (loc.IsOccupied == 1)
                    {
                        TpCarrier cst = TpCarrier.GetByLambda(x => x.Location == loc.Address);
                        if (cst == null)
                        {
                            HistoryWriter.Instance.EqpEventLog(loc.Address, 1, "CheckDataBaseInfoWithLocationAndCarrier", $"Delete info to tplocation:{loc.Address}");
                            TpLocation tpLocation = TpLocation.GetByLambda(x => x.Address == loc.Address);
                            if(tpLocation != null)
                            {
                                tpLocation.CarrierId = "";
                                tpLocation.IsOccupied = 0;
                                tpLocation.Save();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                HistoryWriter.Instance.ExceptionLog("CraneObj.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }
        public bool CraneHasAlarm()
        {
            return CraneDev.Instance.IsCraneHasAlarm(craneTag);
        }

        private void TaskExecuteThreadFuncCheckStepAction()
        {
            CraneTaskStep taskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
            HistoryWriter.Instance.PLCLog("==========Step Action", "Step=" + taskStep.iStep.ToString(), "Action=" + taskStep.iAction.ToString(), "");
        } 

        private void taskExecuteThreadAlarmFunc()
        {
            if (LastControlState != Proj.CacheData.GlobalData.Instance.gbControlState)
            {
                LastControlState = Proj.CacheData.GlobalData.Instance.gbControlState;
                if (ControlState.OnlineRemote == Proj.CacheData.GlobalData.Instance.gbControlState)
                {
                    StockerDev.Instance.SetMCSLinkType(true);
                }
                else
                {
                    StockerDev.Instance.SetMCSLinkType(false);
                }
            }
          
            if(bIsFirstFlag)
            {
                if (!LocationMng.Instance.bIsFullShelf())
                {
                    bLastShelfFullFlag = false;
                }
                else
                {
                    bLastShelfFullFlag = true;
                }
                StockerDev.Instance.SetSTKCIsFull(bLastShelfFullFlag);
                bIsFirstFlag = false;
            }
            else
            {
                if (!LocationMng.Instance.bIsFullShelf())
                {
                    if (bLastShelfFullFlag)
                    {
                        bLastShelfFullFlag = false;
                        StockerDev.Instance.SetSTKCIsFull(false);
                    }
                }
                else if (!bLastShelfFullFlag)
                {
                    bLastShelfFullFlag = true;
                    StockerDev.Instance.SetSTKCIsFull(true);
                }

            }

            if (CraneDev.Instance.IsCraneHasAlarm(craneTag))
            {
                List<int> craneAlarms = CraneDev.Instance.GetCraneAlarms(craneTag);
                if (craneAlarms != null && craneAlarms.Count > 0)
                {
                    if(!craneAlarmState)
                    {
                        HostIF.Instance.PostAlarm(1, craneName);
                        string strCommandID = currCommand == null ? "" : currCommand.strCommandID;
                        HostIF.Instance.PostAlarmEvent(strCommandID, "", craneName, "ABORT", 0, EqpEvent.AlarmSet);
                    }
                    craneAlarmState = true;
                    foreach (int alarmID in craneAlarms)
                    {
                        AlarmController.Instance.SetAlarm(alarmID, craneName, "");
                    }
                }
            }
            else
            {
                craneAlarmState = false;
                TpAlarmList alarmList = TpAlarmList.GetByLambda(x => x.Unit.Equals(craneName));
                if (alarmList.Count > 0)
                {
                    AlarmController.Instance.ClearAlarmByUnit(craneName);
                    string strCommandID = currCommand == null ? "" : currCommand.strCommandID;
                    HostIF.Instance.PostAlarmEvent(strCommandID, "", craneName, "ABORT", 0, EqpEvent.AlarmCleared);
                }
            }

            if (StockerDev.Instance.IsSTKAlarm())
            {
                List<int> stkAlarms = StockerDev.Instance.GetSTKAlarms();
                if (stkAlarms != null && stkAlarms.Count > 0)
                {
                    if (!stkAlarmState)
                    {
                        HostIF.Instance.PostAlarm(1, CacheData.GlobalData.Instance.gbEqpName);
                        string strCommandID = currCommand == null ? "" : currCommand.strCommandID;
                        HostIF.Instance.PostAlarmEvent(strCommandID, "", craneName, "ABORT", 0, EqpEvent.AlarmSet);
                    }
                    stkAlarmState = true;
                    foreach (int alarmID in stkAlarms)
                    {
                        AlarmController.Instance.SetAlarm(alarmID, CacheData.GlobalData.Instance.gbEqpName, "");
                    }
                }
            }
            else
            {
                stkAlarmState = false;
                TpAlarmList alarmList = TpAlarmList.GetByLambda(x => x.Unit.Equals(CacheData.GlobalData.Instance.gbEqpName));
                if (alarmList.Count > 0)
                {
                    if (!TpAlarm.Exists(65535))
                    {                   
                        AlarmController.Instance.ClearAlarmByUnit(CacheData.GlobalData.Instance.gbEqpName);
                        if (GlobalData.Instance.gbMCSCommunication == MCSCommunication.Communicating)
                        {
                            string strCommandID = currCommand == null ? "" : currCommand.strCommandID;
                            HostIF.Instance.PostAlarmEvent(strCommandID, "", CacheData.GlobalData.Instance.gbEqpName, "ABORT", 0, EqpEvent.AlarmCleared);
                        }
                    }
                }
            }

            if (Proj.CacheData.GlobalData.Instance.gbMachineState != CraneOperationState.Down && AlarmController.Instance.HasAlarm())
            {
                Proj.CacheData.GlobalData.Instance.gbMachineState = CraneOperationState.Down;
                Dictionary<string, object> dicParams = new Dictionary<string, object>();
                dicParams.Add("NAME", "Machine State");
                dicParams.Add("VALUE", CraneOperationState.Down.ToString());
                WCFService.Instance.ServerSendMessage("UpdateState", dicParams);

                WriteMachineStateToDB();
                //SCStateMng.Instance.SetState(SCState.Paused);
                //HostIF.Instance.PostScEvent(EqpEvent.SCPaused);
                return;
            }

            if (CraneDev.Instance.GetCraneOperMode(craneTag) == OperMod.Auto)
            {
                if(GlobalData.Instance.gbHandoffType != HandoffType.Automated)
                {
                    CacheData.GlobalData.Instance.gbHandoffType = HandoffType.Automated;
                    TpAlarmList alarmList = TpAlarmList.GetByLambda(x => x.Code.Equals(65535));
                    if (alarmList.Count > 0)
                    {
                        HostIF.Instance.ClearAlarm(65535, CacheData.GlobalData.Instance.gbEqpName);
                        AlarmController.Instance.ClearAlarmByCode(65535, GlobalData.Instance.gbEqpName);
                        if (GlobalData.Instance.gbMCSCommunication == MCSCommunication.Communicating)
                        {
                            string strCommandID = currCommand == null ? "" : currCommand.strCommandID;
                            HostIF.Instance.PostAlarmEvent(strCommandID, "", CacheData.GlobalData.Instance.gbEqpName, "ABORT", 65535, EqpEvent.AlarmCleared);
                        }
                    }
                }

                if (Proj.CacheData.GlobalData.Instance.gbMachineState != CraneOperationState.Normal)
                {
                    if (AlarmController.Instance.HasAlarm())
                    {
                        //if(Proj.CacheData.GlobalData.Instance.gbSCState != SCState.Paused)
                        //{
                        //    SCStateMng.Instance.SetState(SCState.Paused);
                        //    HostIF.Instance.PostScEvent(EqpEvent.SCPaused);
                        //}
                    }
                    else
                    {
                        Proj.CacheData.GlobalData.Instance.gbMachineState = CraneOperationState.Normal;
                        Dictionary<string, object> dicParams = new Dictionary<string, object>();
                        dicParams.Add("NAME", "Machine State");
                        dicParams.Add("VALUE", Proj.CacheData.GlobalData.Instance.gbMachineState.ToString());
                        WCFService.Instance.ServerSendMessage("UpdateState", dicParams);

                        WriteMachineStateToDB();
                    }
                }
            }
            else
            {
                if (GlobalData.Instance.gbHandoffType != HandoffType.Manual)
                {
                    CacheData.GlobalData.Instance.gbHandoffType = HandoffType.Manual;
                    //上报MCS
                    int iAlarmID = 65535;
                    TpAlarmConfig tAlarmCfg = TpAlarmConfig.GetById(iAlarmID);
                    if (tAlarmCfg != null)
                    {
                        HostIF.Instance.PostAlarm(Convert.ToUInt32(iAlarmID), CacheData.GlobalData.Instance.gbEqpName);
                        string strCommandID = currCommand == null ? "" : currCommand.strCommandID;
                        HostIF.Instance.PostAlarmEvent(strCommandID, "", CacheData.GlobalData.Instance.gbEqpName, "ABORT", iAlarmID, EqpEvent.AlarmSet);
                        AlarmController.Instance.SetAlarm(iAlarmID, CacheData.GlobalData.Instance.gbEqpName, "Maint");
                    }
                }
                    
            //if (Proj.CacheData.GlobalData.Instance.gbSCState != SCState.Paused)
            //{
            //    SCStateMng.Instance.SetState(SCState.Paused);
            //    HostIF.Instance.PostScEvent(EqpEvent.SCPaused);
            //}
                if (Proj.CacheData.GlobalData.Instance.gbMachineState != CraneOperationState.Down)
                {
                    Proj.CacheData.GlobalData.Instance.gbMachineState = CraneOperationState.Down;
                    Dictionary<string, object> dicParams = new Dictionary<string, object>();
                    dicParams.Add("NAME", "Machine State");
                    dicParams.Add("VALUE", CraneOperationState.Down.ToString());
                    WCFService.Instance.ServerSendMessage("UpdateState", dicParams);

                    WriteMachineStateToDB();

                    //SCStateMng.Instance.SetState(SCState.Paused);
                    //HostIF.Instance.PostScEvent(EqpEvent.SCPaused);
                }
            }
        }

        private void DealReadCardWithTransfer()
        {
            //处理读码结果（什么情况不读码，要有可配置的控制逻辑）lzt
            idrResult = CraneDev.Instance.GetIDRResult(craneTag);
            if (Proj.CacheData.GlobalData.Instance.gbSimulation)
            {
                idrResult.iResultCode = 1;
                idrResult.strCarrierID = "CAR001";
            }
            switch ((IDRResultCode)idrResult.iResultCode)
            {
                case IDRResultCode.Mismatch: //进行Duplicate的检查
                    {
                        if (CarrierMng.Instance.IsCarrierDuplicate(idrResult.strCarrierID, currCommand.strRealSource, ref dupCarrierRealLoc))
                        {
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead DuplicateID:{idrResult.strCarrierID}");
                            resultCode = ResultCode.DuplicateID;
                            HostIF.Instance.PostIDREvent(idrResult.strCarrierID, currCommand.strRealSource, IDReadStatus.Duplicate, EqpEvent.CarrierIDRead);
                        }
                        else
                        {
                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead MismatchID:{idrResult.strCarrierID}");
                            resultCode = ResultCode.MismatchID;
                            HostIF.Instance.PostIDREvent(idrResult.strCarrierID, currCommand.strRealSource, IDReadStatus.Mismatch, EqpEvent.CarrierIDRead);
                        }
                        taskStep = 6;
                    }
                    break;
                case IDRResultCode.Failed:
                    {
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                            , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead IDReadFail");
                        resultCode = ResultCode.IDReadFail;
                        HostIF.Instance.PostIDREvent("Unknwon", currCommand.strRealSource, IDReadStatus.Failure, EqpEvent.CarrierIDRead);
                        if (++iReadDFailCounter == SysSettings.Instance.CraneIDRReadRules.iMaxReadFailCount)
                        {
                            iReadDFailCounter = 0;
                            if (SysSettings.Instance.CraneIDRReadRules.bShowIDRBreakDown)
                            {
                                HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "", strCraneName, "ABORT", 1000, EqpEvent.AlarmSet);
                                //报警：IDR Break Down
                                AlarmController.Instance.SetAlarm(1000, "IDR", "Crane IDR Break Down");
                                //HostIF.Instance.PostUnitAlarmEvent(strCraneName, 1000, "", EqpEvent.UnitAlarmSet);
                            }
                        }
                        taskStep = 6;
                    }
                    break;
                default:
                    resultCode = ResultCode.OtherError;
                    break;
            }
        }
        private void TransferCommand()
        {
            switch (currCommand.transferState)
            {
                case TransferState.Queue:
                    {
                        CheckDataBaseInfoWithLocationAndCarrier();

                        taskStep = 1;
                        TransferMng.Instance.UpdateTransferCrane(currCommand.strCommandID, strCraneName);
                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Transferring, ResultCode.None);

                        LocationMng.Instance.ReserveLocation(currCommand.strRealDest);
                        HistoryWriter.Instance.CraneTransferStart(currCommand.strCommandID, currCommand.iFirstSecendCrane, strCraneName);
                    }
                    break;
                case TransferState.Transferring:
                    {
                        //特殊处理MGV In->Out
                        //if (TransferType.InPort2OutPort == currCommand.transferType)
                        //{
                        //    if(currCommand.strRealSource == currCommand.strRealDest)
                        //    {
                        //        //切入库？

                        //        //结束Crane的当前任务
                        //        endCurrentCommand(TransferState.None, resultCode);
                        //        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand");
                        //    }
                        //}

                        switch (taskStep)
                        {
                            case 1:
                                {
                                    if (TransferType.Shelf2Shelf == currCommand.transferType
                                        || TransferType.InPort2Shelf == currCommand.transferType
                                        || TransferType.EqPort2Shlef == currCommand.transferType)
                                    {
                                        if (LocationMng.Instance.bIsFullShelf())//储位是否可用
                                        {
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                            currCommand.resultCode = ResultCode.ShelfZoneFull;
                                            //?清两次?
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                            modeState = CraneModeState.Idle;
                                            return;
                                        }
                                    }

                                    if (!Proj.CacheData.GlobalData.Instance.gbSimulation)//仿真标志
                                    {
                                        CraneDev.Instance.CraneServoOn(craneTag);
                                        if((CraneDev.Instance.IsCraneServoEnable(craneTag) && CraneDev.Instance.IsCraneReady(craneTag)
                                            && !CraneDev.Instance.IsCraneHasAlarm(craneTag)) == false)
                                        {
                                            break;
                                        }

                                        //检查Fork上没有盒子，向Crane发生任务数据，启动任务
                                        if (!CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                        {
                                            if (IsHaveIDR)
                                            {
                                                //如果InPort2Shelf设置了IDRead bypass，告诉Crane 不读码
                                                if (bReadBypass)
                                                {
                                                    CraneDev.Instance.IDReadBypass(craneTag, true);
                                                }
                                                else
                                                {
                                                    CraneDev.Instance.IDReadBypass(craneTag, false);
                                                }
                                            }
                                            //任务数据
                                            CraneTaskData taskData = new CraneTaskData();
                                            taskData.taskType = CraneTaskType.Transfer;
                                            taskData.strCarrierID = currCommand.strCarrierID;
                                            taskData.strSourceAddr = currCommand.strRealSource;
                                            taskData.strDestAddr = currCommand.strRealDest;
                                            //向Crane发生任务数据
                                            if (CraneDev.Instance.SendCraneCommand(craneTag, taskData))
                                            {
                                                //记录Crane日志：成功向Crane发生任务数据
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                                    , $"发送成功:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");

                                                //Crane任务完成，答复完成信号
                                                //CraneDev.Instance.CraneTaskCompleteAck(craneTag);
                                                //Thread.Sleep(50);
                                                ////复位答复完成信号
                                                //CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);
                                                ///*Thread.Sleep(50);
                                                //重置Crane任务启动信号
                                                ///*CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                                ///*Thread.Sleep(50);
                                                //Crane任务启动
                                                if (CraneDev.Instance.CraneTaskStart(craneTag))
                                                {
                                                    //记录Crane日志：启动Crane任务成功
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");
                                                    if (currCommand.iAltStep == 2)
                                                    {
                                                        HostIF.Instance.PostCarrierEvent(strCarrierID, EqpEvent.CarrierResumed);
                                                    }
                                                    else
                                                    {
                                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                                    }
                                                    CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                                    taskStep++;
                                                    bAction1Started = false;
                                                    bAction2Started = false;
                                                    bAction3Started = false;
                                                    bAction4Started = false;
                                                    bAction9Started = false;
                                                }
                                                else
                                                {
                                                    //记录Crane日志：启动Crane任务失败
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动失败");
                                                    CraneDev.Instance.CleanCraneCommand(craneTag);
                                                    TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, ResultCode.OtherError);
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                }
                                            }
                                            else  //SendCraneCommand失败
                                            {
                                                //记录Crane日志：向Crane发生任务数据失败
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                                    , $"发送失败:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");
                                                if (++iFailCount == 3)
                                                {
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                    endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                                    modeState = CraneModeState.Idle;
                                                }
                                            }
                                        }
                                        else //检查Fork上有盒子
                                        {
                                            //记录Crane日志：向Crane发送任务数据前，Fork上检测到盒子
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "向Crane发送任务数据前，Fork上检测到盒子");
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                            modeState = CraneModeState.Idle;
                                        }
                                    }
                                    else
                                    {
                                        //记录Crane日志：启动Crane任务成功
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");
                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                        CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                        taskStep++;

                                        bAction1Started = false;
                                        bAction2Started = false;
                                        bAction3Started = false;
                                        bAction4Started = false;
                                        bAction9Started = false;

                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 10, 10);
                                    }

                                }
                                break;
                            case 2:
                                {
                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    //Crane还未向源地址移动
                                    if (craneTaskStep.iStep < 10 && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        if (CraneDev.Instance.CheckCraneE84Alarms(craneTag))
                                        {
                                            resultCode = ResultCode.EqInterLockNG;
                                            if (CraneDev.Instance.CraneTaskPause(craneTag))
                                            {
                                                CraneDev.Instance.CraneTaskAbort(craneTag);
                                                TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Aborting, ResultCode.EqInterLockNG);
                                            }
                                            else
                                            {
                                                currCommand.resultCode = ResultCode.EqInterLockNG;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                endCurrentCommand(TransferState.None, ResultCode.EqInterLockNG);
                                                modeState = CraneModeState.Idle;
                                            }
                                            return;
                                        }
                                        //}
                                        //向取Carrier的高度移动中
                                        if (IsHaveIDR && CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                        {
                                            DealReadCardWithTransfer();
                                        }
                                        return;
                                    }
                                    else if (craneTaskStep.iStep == 10)
                                    {
                                        if (IsHaveIDR && !SysSettings.Instance.CraneIDRReadRules.bInPort2ShelfReadBypass)
                                        {
                                            //重置Crane任务启动信号
                                            //if (!Proj.CacheData.GlobalData.Instance.gbSimulation)
                                            //{
                                            //    CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                            //}

                                            //Crane开始向源地址移动
                                            modeState = CraneModeState.Active;

                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.CraneActive);
                                            //记录Crane日志：Crane开始向源地址移动
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane开始向源地址移动");
                                            taskStep++;
                                            bAction1Started = false;
                                            bAction2Started = false;
                                            bAction3Started = false;
                                            bAction4Started = false;
                                            bAction9Started = false;
                                        }
                                        else //IC Stocker无IDR
                                        {
                                            if (bAction1Started == false && craneTaskStep.iAction == 10)
                                            {
                                                //向目标位检测有无Carrier高度移动
                                                bAction1Started = true;

                                                //重置Crane任务启动信号
                                                //if (!Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                //{
                                                //    CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                                //}
                                                //Crane开始向源地址移动
                                                modeState = CraneModeState.Active;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.CraneActive);
                                                //记录Crane日志：Crane开始向源地址移动
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane Step1, Action1");

                                                //仿真专用
                                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                {
                                                    CraneDev.Instance.setCraneTaskInfo(craneTag, 10, 20);
                                                }
                                            }
                                            else if (bAction2Started == false && craneTaskStep.iAction == 20)
                                            {
                                                //开始向取Carrier的高度移动
                                                bAction1Started = false;
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane Step1, Action2");
                                                taskStep++;
                                                bAction1Started = false;
                                                bAction2Started = false;
                                                bAction3Started = false;
                                                bAction4Started = false;
                                                bAction9Started = false;

                                                //仿真专用
                                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                {
                                                    CraneDev.Instance.setCraneTaskInfo(craneTag, 20, 10);
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        taskStep++;
                                        bAction1Started = false;
                                        bAction2Started = false;
                                        bAction3Started = false;
                                        bAction4Started = false;
                                        bAction9Started = false;
                                    }
                                }
                                break;
                            case 3:
                                {
                                    //仿真专用
                                    if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 20, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10);
                                    }

                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    if (craneTaskStep.iStep < 20)
                                    {
                                        if (CraneDev.Instance.CheckCraneE84Alarms(craneTag))
                                        {
                                            resultCode = ResultCode.EqInterLockNG;
                                            if (CraneDev.Instance.CraneTaskPause(craneTag))
                                            {
                                                CraneDev.Instance.CraneTaskAbort(craneTag);
                                                TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Aborting, ResultCode.EqInterLockNG);
                                            }
                                            else
                                            {
                                                currCommand.resultCode = ResultCode.EqInterLockNG;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                endCurrentCommand(TransferState.None, ResultCode.EqInterLockNG);
                                                modeState = CraneModeState.Idle;
                                            }
                                            return;
                                        }
                                        //}
                                        //向取Carrier的高度移动中

                                        return;
                                    }
                                    //Crane开始取货
                                    if (craneTaskStep.iStep == 20)
                                    {
                                        if (CraneDev.Instance.IsEmptySource(craneTag))
                                        {
                                            if (CraneDev.Instance.CraneTaskPause(craneTag))
                                            {
                                                if (GlobalData.Instance.gbRetryCount == 3)
                                                {
                                                    GlobalData.Instance.gbRetryCount = 0;
                                                    //Source Empty
                                                    bEmptyRetrieval = true;
                                                    taskStep = 10;
                                                    bAction1Started = false;
                                                    bAction2Started = false;
                                                    bAction3Started = false;
                                                    bAction4Started = false;
                                                    bAction9Started = false;
                                                }
                                                else if (uiIsRetryFlag != 2)
                                                {
                                                    uiIsRetryFlag = 2;
                                                    GlobalData.Instance.gbRetryCount += 1;

                                                    HostIF.Instance.PostAlarm((uint)AlarmCode.SourceEmpty);
                                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty"+ currCommand.strStockerCraneID, currCommand.strStockerCraneID, "RETRY, ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmSet);

                                                    Thread.Sleep(100);
                                                    CraneMng.Instance.ResetCraneAlarmWithEmptyOrDouble(craneName);
                                                    AlarmController.Instance.ClearAlarmByCode((int)AlarmCode.SourceEmpty, GlobalData.Instance.gbStockerCraneID);
                                                    Thread.Sleep(50);
                                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty" + currCommand.strStockerCraneID, currCommand.strStockerCraneID, "RETRY, ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmCleared);
                                                    HostIF.Instance.ClearAlarm((uint)AlarmCode.SourceEmpty);

                                                    if (ControlState.OnlineRemote != Proj.CacheData.GlobalData.Instance.gbControlState
                                                        || GlobalData.Instance.gbMCSCommunication != MCSCommunication.Communicating
                                                        || GlobalData.Instance.gbHSMSState != HSMSState.Connected)
                                                    {
                                                        CraneDev.Instance.CraneTaskResume(craneTag);
                                                        Thread.Sleep(100);
                                                        uiIsRetryFlag = 1;
                                                    }
                                                }
                                            }
                                        }
                                        else if (bAction2Started == false && craneTaskStep.iAction == 20)
                                        {
                                            //Crane开始伸Fork
                                            bAction2Started = true;
                                            bStepAction2040 = false;
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ForkingStarted);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action2");
                                        }
                                        else if (bAction3Started == false && craneTaskStep.iAction == 30)
                                        {
                                            bool bCranePickCarrier1 = CraneDev.Instance.IsCraneForkHasCarrier(craneTag);
                                            //Crane去读码
                                            bAction3Started = true;
                                            bStepAction2040 = false;
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action3");
                                        }
                                        else if (bAction4Started == false && craneTaskStep.iAction == 40)
                                        {
                                            //Crane开始缩回Fork
                                            bAction4Started = true;
                                            HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkRised);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action4");

                                            ////EQ Pick Interlock仿真
                                            //if (Proj.CacheData.GlobalData.Instance.gbSimulation
                                            //    && TransferType.EqPort2Shlef == currCommand.transferType)
                                            //{
                                            //    currCommand.resultCode = ResultCode.EqInterLockNG;
                                            //    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            //    endCurrentCommand(TransferState.None, ResultCode.EqInterLockNG);
                                            //    modeState = CraneModeState.Idle;
                                            //    return;
                                            //}

                                            if (IsHaveIDR && !bReadBypass)
                                            {
                                                //处理读码结果（什么情况不读码，要有可配置的控制逻辑）lzt
                                                idrResult = CraneDev.Instance.GetIDRResult(craneTag);
                                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                {
                                                    idrResult.iResultCode = 1;
                                                    idrResult.strCarrierID = "CAR001";
                                                }
                                                switch ((IDRResultCode)idrResult.iResultCode)
                                                {
                                                    case IDRResultCode.Success:
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead Success:{idrResult.strCarrierID}");
                                                            resultCode = ResultCode.Success;
                                                        }
                                                        break;
                                                    case IDRResultCode.Mismatch: //进行Duplicate的检查
                                                        {
                                                            if (CarrierMng.Instance.IsCarrierDuplicate(idrResult.strCarrierID, currCommand.strRealSource, ref dupCarrierRealLoc))
                                                            {
                                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                                    , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead DuplicateID:{idrResult.strCarrierID}");
                                                                resultCode = ResultCode.DuplicateID;
                                                                HostIF.Instance.PostIDREvent(idrResult.strCarrierID, currCommand.strRealSource, IDReadStatus.Duplicate, EqpEvent.CarrierIDRead);
                                                            }
                                                            else
                                                            {
                                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                                    , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead MismatchID:{idrResult.strCarrierID}");
                                                                resultCode = ResultCode.MismatchID;
                                                                HostIF.Instance.PostIDREvent(idrResult.strCarrierID, currCommand.strRealSource, IDReadStatus.Mismatch, EqpEvent.CarrierIDRead);
                                                            }
                                                        }
                                                        break;
                                                    case IDRResultCode.Failed:
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead IDReadFail");
                                                            resultCode = ResultCode.IDReadFail;
                                                            HostIF.Instance.PostIDREvent("Unknwon", currCommand.strRealSource, IDReadStatus.Failure, EqpEvent.CarrierIDRead);
                                                            if (++iReadDFailCounter == SysSettings.Instance.CraneIDRReadRules.iMaxReadFailCount)
                                                            {
                                                                iReadDFailCounter = 0;
                                                                if (SysSettings.Instance.CraneIDRReadRules.bShowIDRBreakDown)
                                                                {
                                                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "", strCraneName, "ABORT", 1000, EqpEvent.AlarmSet);
                                                                    //报警：IDR Break Down
                                                                    AlarmController.Instance.SetAlarm(1000, "IDR", "Crane IDR Break Down");
                                                                   // HostIF.Instance.PostUnitAlarmEvent(strCraneName, 1000, "", EqpEvent.UnitAlarmSet);
                                                                }
                                                            }
                                                        }
                                                        break;
                                                    case IDRResultCode.NoCarrier:
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead NoCarrier");
                                                            resultCode = ResultCode.OtherError;
                                                            HostIF.Instance.PostIDREvent("", currCommand.strRealSource, IDReadStatus.NoCarrier, EqpEvent.CarrierIDRead);
                                                            bEmptyRetrieval = true;
                                                            taskStep = 10;
                                                            bAction1Started = false;
                                                            bAction2Started = false;
                                                            bAction3Started = false;
                                                            bAction4Started = false;
                                                            bAction9Started = false;
                                                        }
                                                        break;
                                                    case IDRResultCode.None:
                                                    default:
                                                        resultCode = ResultCode.OtherError;
                                                        break;
                                                }

                                                //更新盒子位置
                                                if(CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                                {
                                                    CarrierMng.Instance.CranePickCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierTransferring);
                                                    if (TransferType.Shelf2OutPort == currCommand.transferType)
                                                    {
                                                        HostIF.Instance.PostZoneCapacityChangeEvent(currCommand.strCarrierID, EqpEvent.ZoneCapacityChange);
                                                    }

                                                    taskStep++;
                                                    bAction1Started = false;
                                                    bAction2Started = false;
                                                    bAction3Started = false;
                                                    bAction4Started = false;
                                                    bAction9Started = false;
                                                    bStepAction2040 = true;
                                                }

                                                //仿真专用
                                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                {
                                                    CraneDev.Instance.setCraneTaskInfo(craneTag, 30, 10);
                                                }
                                            }
                                            else
                                            {
                                                bool bCranePickCarrier = CraneDev.Instance.IsCraneForkHasCarrier(craneTag);
                                                //仿真专用
                                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                {
                                                    bCranePickCarrier = true;
                                                    CraneDev.Instance.setCraneTaskInfo(craneTag, 30, 10);
                                                }
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, bCranePickCarrier.ToString(), "Crane Step2, Action4监视Fork在位");
                                                if (bCranePickCarrier)
                                                {
                                                    resultCode = ResultCode.Success;
                                                    //更新盒子位置
                                                    CarrierMng.Instance.CranePickCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealSource);
                                                    strCarrierID = currCommand.strCarrierID;
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                                        $"CranePickCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strRealSource}");
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierTransferring);

                                                    if (TransferType.Shelf2OutPort == currCommand.transferType)
                                                    {
                                                        HostIF.Instance.PostZoneCapacityChangeEvent(currCommand.strCarrierID, EqpEvent.ZoneCapacityChange);
                                                    }

                                                    taskStep++;
                                                    bAction1Started = false;
                                                    bAction2Started = false;
                                                    bAction3Started = false;
                                                    bAction4Started = false;
                                                    bAction9Started = false;
                                                    bStepAction2040 = true;
                                                }
                                                else //没有取到盒子
                                                {
                                                    //Source Empty
                                                    bEmptyRetrieval = true;
                                                    taskStep = 10;
                                                    bAction1Started = false;
                                                    bAction2Started = false;
                                                    bAction3Started = false;
                                                    bAction4Started = false;
                                                    bAction9Started = false;
                                                }
                                            }
                                            bAction2Started = false;
                                            bAction3Started = false;
                                            bAction4Started = false;
                                            bAction9Started = false;
                                        }
                                    }
                                    else if(!bStepAction2040)// (craneTaskStep.iStep > 20)
                                    {
                                        bool bCranePickCarrier = CraneDev.Instance.IsCraneForkHasCarrier(craneTag);
                                        if (bCranePickCarrier)
                                        {
                                            HistoryWriter.Instance.EqpEventLog("收不到20-40", (int)EqpEvent.TransferCompleted, "更新Carrier位置到Crane上", "");

                                            //更新盒子位置
                                            CarrierMng.Instance.CranePickCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealSource);
                                            HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierTransferring);

                                            if (TransferType.Shelf2OutPort == currCommand.transferType)
                                            {
                                                HostIF.Instance.PostZoneCapacityChangeEvent(currCommand.strCarrierID, EqpEvent.ZoneCapacityChange);
                                            }
                                        }

                                        taskStep++;
                                    }
                                }
                                break;
                            case 4:
                                {
                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    if (craneTaskStep.iStep < 30)
                                    {
                                        if (CraneDev.Instance.CheckCraneE84Alarms(craneTag))
                                        {
                                            resultCode = ResultCode.EqInterLockNG;
                                            if (CraneDev.Instance.CraneTaskPause(craneTag))
                                            {
                                                CraneDev.Instance.CraneTaskAbort(craneTag);
                                                TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Aborting, ResultCode.EqInterLockNG);
                                            }
                                            else
                                            {
                                                currCommand.resultCode = ResultCode.EqInterLockNG;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                endCurrentCommand(TransferState.None, ResultCode.EqInterLockNG);
                                                modeState = CraneModeState.Idle;
                                            }
                                            return;
                                        }
                                        if (IsHaveIDR && CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                        {
                                            DealReadCardWithTransfer();
                                        }

                                        return;
                                    }
                                    if (craneTaskStep.iStep == 30)
                                    {
                                        if (bAction1Started == false && craneTaskStep.iAction == 10)
                                        {
                                            bAction1Started = true;
                                            //仿真专用
                                            if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                            {
                                                CraneDev.Instance.setCraneTaskInfo(craneTag, 30, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10); ;
                                            }
                                        }
                                        else if (bAction2Started == false && craneTaskStep.iAction == 20)
                                        {
                                            //Crane缩回Fork完成,Fork开始向目的地检查Carrier有无的高度移动
                                            HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkingCompleted); //此处的该事件可能因为EmptyRetrieval而不上报
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step3, Action2");

                                            CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Transferring);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierTransferring, EqpEvent.CarrierTransferring.ToString(), "Crane开始搬送盒子");
                                            CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                            CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                           
                                            taskStep++;
                                            bAction1Started = false;
                                            bAction2Started = false;
                                            bAction3Started = false;
                                            bAction4Started = false;
                                            bAction9Started = false;
                                            
                                            if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                            {
                                                CraneDev.Instance.setCraneTaskInfo(craneTag, 40, 10); ;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        taskStep++;
                                        bAction1Started = false;
                                        bAction2Started = false;
                                        bAction3Started = false;
                                        bAction4Started = false;
                                        bAction9Started = false;
                                    }
                                }
                                break;
                            case 5:
                                {
                                    //仿真专用
                                    if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 40, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10);
                                    }

                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    if (craneTaskStep.iStep < 40)
                                    {
                                        if (CraneDev.Instance.CheckCraneE84Alarms(craneTag))
                                        {
                                            resultCode = ResultCode.EqInterLockNG;
                                            if (CraneDev.Instance.CraneTaskPause(craneTag))
                                            {
                                                CraneDev.Instance.CraneTaskAbort(craneTag);
                                                TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Aborting, ResultCode.EqInterLockNG);
                                            }
                                            else
                                            {
                                                currCommand.resultCode = ResultCode.EqInterLockNG;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                endCurrentCommand(TransferState.None, ResultCode.EqInterLockNG);
                                                modeState = CraneModeState.Idle;
                                            }
                                            return;
                                        }
                                        if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                        {
                                            if (iLastPlcStep == 40 && (iLastPlcAction == 20 || iLastPlcAction == 30))
                                            {
                                                if (bDoubleStorage = CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                                {
                                                    //盒子还在货叉上，发生了DoubleStorage
                                                    taskStep = 20;
                                                }
                                                else
                                                {
                                                    if (currCommand.strDestZone.Equals("BSSTKS03M_OUT01"))
                                                    {
                                                        if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "MR2"))
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("MR2出库", 0, "WritePortOPOUTCarrierID Success!", currCommand.strCarrierID);
                                                        }
                                                        else
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("MR2出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                        }
                                                    }
                                                    if (currCommand.strDestZone.Equals("BSSTKS03A_OUT01"))
                                                    {
                                                        if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "OHT2"))
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("OHT2出库", 0, "WritePortOPOUTCarrierID", currCommand.strCarrierID);
                                                        }
                                                        else
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("OHT2出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                        }
                                                    }
                                                    if (currCommand.strDestZone.Equals("BSSTKS03A_OUT02"))
                                                    {
                                                        if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "OHT4"))
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("OHT4出库", 0, "WritePortOPOUTCarrierID", currCommand.strCarrierID);
                                                        }
                                                        else
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("OHT4出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                        }
                                                    }
                                                    CarrierMng.Instance.CranePlaceCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealDest);
                                                    strCarrierID = "";
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                                        $"CranePlaceCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strDest} ");

                                                    CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Compeleted);

                                                    if (TransferType.InPort2Shelf == currCommand.transferType)
                                                    {
                                                        HostIF.Instance.PostZoneCapacityChangeEvent(currCommand.strCarrierID, EqpEvent.ZoneCapacityChange);
                                                    }

                                                    taskStep++;
                                                    bAction1Started = false;
                                                    bAction2Started = false;
                                                    bAction3Started = false;
                                                    bAction4Started = false;
                                                    bAction9Started = false;
                                                }
                                            }
                                            else
                                            {
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, 10001, "StepError",
                                                        "Get set error, taskstep=5");
                                            }
                                        }
                                        return;
                                    }

                                    //Crane开始放下盒子
                                    if (craneTaskStep.iStep == 40)
                                    {
                                        if (CraneDev.Instance.GetCraneWorkingStatus(craneTag) == CraneWorkingStatus.Paused
                                            || Proj.CacheData.GlobalData.Instance.gbSimulation)
                                        {
                                            if (CraneDev.Instance.IsDoubleStorage(craneTag))
                                            {
                                                if (CraneDev.Instance.CraneTaskPause(craneTag))
                                                {
                                                    if (GlobalData.Instance.gbRetryCount == 3)
                                                    {
                                                        GlobalData.Instance.gbRetryCount = 0;
                                                        bDoubleStorage = true;
                                                        taskStep = 20;  //DoubleStorage的处理
                                                    }
                                                    else if(uiIsRetryFlag != 2)
                                                    {
                                                        uiIsRetryFlag = 2;
                                                        GlobalData.Instance.gbRetryCount += 1;
                                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GlobalData.Instance.gbRetryCount, "doubleStorage Retry" + GlobalData.Instance.gbRetryCount.ToString(), "");

                                                        HostIF.Instance.PostAlarm((uint)AlarmCode.DoubleStorage);
                                                        HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "DestOccupied" + currCommand.strStockerCraneID, GlobalData.Instance.gbStockerCraneID, "RETRY, ABORT", (int)AlarmCode.DoubleStorage, EqpEvent.AlarmSet);

                                                        Thread.Sleep(100);
                                                        CraneMng.Instance.ResetCraneAlarmWithEmptyOrDouble(craneName);
                                                        AlarmController.Instance.ClearAlarmByCode((int)AlarmCode.DoubleStorage, GlobalData.Instance.gbStockerCraneID);
                                                        Thread.Sleep(50);
                                                        HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "DestOccupied" + currCommand.strStockerCraneID, GlobalData.Instance.gbStockerCraneID, "RETRY, ABORT", (int)AlarmCode.DoubleStorage, EqpEvent.AlarmCleared);
                                                        HostIF.Instance.ClearAlarm((uint)AlarmCode.DoubleStorage);
                                                        
                                                        if (ControlState.OnlineRemote != Proj.CacheData.GlobalData.Instance.gbControlState
                                                            || GlobalData.Instance.gbMCSCommunication != MCSCommunication.Communicating
                                                            || GlobalData.Instance.gbHSMSState != HSMSState.Connected)
                                                        {
                                                            CraneDev.Instance.CraneTaskResume(craneTag);
                                                            Thread.Sleep(100);
                                                            uiIsRetryFlag = 1;
                                                        }
                                                    }
                                                }
                                               
                                                break;
                                            }
                                        }
                                        if (bAction2Started == false && craneTaskStep.iAction == 20)
                                        {
                                            iLastPlcStep = 40;
                                            iLastPlcAction = 20;
                                            //Crane开始伸Fork
                                            bAction2Started = true;
                                            HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkingStarted);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action2");
                                        }
                                        else if (bAction3Started == false && craneTaskStep.iAction == 30)
                                        {
                                            iLastPlcStep = 40;
                                            iLastPlcAction = 30;
                                            //Crane的Fork开始下降
                                            bAction3Started = true;
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action3");
                                        }
                                        else if (bAction4Started == false && craneTaskStep.iAction == 40)
                                        {
                                            //Crane的Fork开始缩回(ForkDowned)
                                            HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkDowned);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action4");

                                            ////EQ Place Interlock仿真
                                            //if (Proj.CacheData.GlobalData.Instance.gbSimulation
                                            //    && TransferType.Shelf2EqPort == currCommand.transferType)
                                            //{
                                            //    currCommand.resultCode = ResultCode.EqInterLockNG;
                                            //    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            //    endCurrentCommand(TransferState.None, ResultCode.EqInterLockNG);
                                            //    modeState = CraneModeState.Idle;
                                            //    return;
                                            //}

                                            //DoubleStorage检查
                                            if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                            {
                                                bDoubleStorage = true;
                                                //盒子还在货叉上，发生了DoubleStorage
                                                taskStep = 20;
                                            }
                                            else
                                            {
                                               if(currCommand.strDestZone.Equals("BSSTKS03M_OUT01"))
                                                {
                                                    if(CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "MR2"))
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("MR2出库", 0, "WritePortOPOUTCarrierID Success!", currCommand.strCarrierID);
                                                    }
                                                    else
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("MR2出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                    }
                                                }
                                                if (currCommand.strDestZone.Equals("BSSTKS03A_OUT01"))
                                                {
                                                    if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "OHT2"))
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("OHT2出库", 0, "WritePortOPOUTCarrierID", currCommand.strCarrierID);
                                                    }
                                                    else
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("OHT2出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                    }
                                                }
                                                if (currCommand.strDestZone.Equals("BSSTKS03A_OUT02"))
                                                {
                                                    if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "OHT4"))
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("OHT4出库", 0, "WritePortOPOUTCarrierID", currCommand.strCarrierID);
                                                    }
                                                    else
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("OHT4出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                    }
                                                }
                                                CarrierMng.Instance.CranePlaceCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealDest);
                                                strCarrierID = "";
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                                    $"CranePlaceCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strDest} ");

                                                CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Compeleted);

                                                if (TransferType.InPort2Shelf == currCommand.transferType)
                                                {
                                                    HostIF.Instance.PostZoneCapacityChangeEvent(currCommand.strCarrierID, EqpEvent.ZoneCapacityChange);
                                                }

                                                taskStep++;
                                                bAction1Started = false;
                                                bAction2Started = false;
                                                bAction3Started = false;
                                                bAction4Started = false;
                                                bAction9Started = false;
                                            }

                                            bAction2Started = false;
                                            bAction3Started = false;
                                        }
                                    }
                                    else
                                    {
                                        taskStep++;
                                        bAction1Started = false;
                                        bAction2Started = false;
                                        bAction3Started = false;
                                        bAction4Started = false;
                                        bAction9Started = false;
                                    }
                                }
                                break;
                            case 6:
                                {
                                    //Crane的Fork正在缩回，等待Crane任务完成信号
                                    if (!CraneDev.Instance.IsCraneTaskComplete(craneTag)
                                        && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        return;
                                    }

                                    iLastPlcStep = 0;
                                    iLastPlcAction = 0;

                                    //重置Crane任务启动信号
                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);

                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Task Complete");
                                    //Crane任务完成，答复完成信号
                                    CraneDev.Instance.CraneTaskCompleteAck(craneTag);
                                    //CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);

                                    //if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                    //{
                                    //    bDoubleStorage = true;
                                    //    resultCode = ResultCode.OtherError;
                                    //}
                                    //else
                                    //{
                                    //    resultCode = ResultCode.Success;
                                    //}

                                    taskStep++;
                                }
                                break;
                            case 7:
                                {
                                    //Crane任务完成信号复位
                                    if (CraneDev.Instance.IsCraneTaskComplete(craneTag)
                                        && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        //重置Crane任务启动信号
                                        CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                        //Crane任务完成，答复完成信号
                                        CraneDev.Instance.CraneTaskCompleteAck(craneTag);//PLC没有将Complete信号置成0，STKC重新Ack
                                        return;
                                    }

                                    //复位答复完成信号
                                    CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);

                                    modeState = CraneModeState.Idle;
                                    //HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneIdle);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneIdle, EqpEvent.CraneIdle.ToString(), "Crane Idle");

                                    //重置Crane任务启动信号
                                    //CraneDev.Instance.ResetCraneTaskStart(craneTag);

                                    TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, resultCode);
                                    //LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);
                                    LocationMng.Instance.UnReserveLocation(currCommand.strRealDest);
                                    if(IsHaveIDR)
                                    {
                                        switch (resultCode)
                                        {
                                            case ResultCode.Success:
                                                {
                                                    currCommand.resultCode = ResultCode.Success;
                                                    if (TransferType.Shelf2Shelf == currCommand.transferType
                                                        || TransferType.InPort2Shelf == currCommand.transferType
                                                        || TransferType.EqPort2Shlef == currCommand.transferType)
                                                    {
                                                        if (currCommand.iAltStep == 1)
                                                        {
                                                            HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierStoredAlt);
                                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCancelInitiated);
                                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCancelCompleted);
                                                        }
                                                        else
                                                        {
                                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                            HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierStored);
                                                        }
                                                    }
                                                    else
                                                    {
                                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                    }
                                                }
                                                break;
                                            case ResultCode.IDReadFail:
                                                {
                                                    //任务完成，错误码？
                                                    currCommand.resultCode = ResultCode.IDReadFail;
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);

                                                    string strCarrierAddress = CarrierMng.Instance.GetCarrierLocation(currCommand.strCarrierID);
                                                    //1.删除原CarrierID的Carrier信息
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);
                                                    //2.添加UnKnownID的Carrier信息
                                                    string strCarrierLocName = LocationMng.Instance.GetLocationNameByAddress(strCarrierAddress);
                                                    string strUnKnownID = CarrierMng.Instance.GetIDRFailCarrierID(strCarrierLocName);
                                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, strCarrierAddress);
                                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);
                                                }
                                                break;
                                            case ResultCode.DuplicateID:
                                                {
                                                    //任务完成，错误码？
                                                    currCommand.resultCode = ResultCode.DuplicateID;
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);

                                                    //1.删除DuplicateID的盒子信息
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(idrResult.strCarrierID);

                                                    //2.在DuplicateID的位置处添加UnKnownID的Carrier信息
                                                    string strUnKnownID = CarrierMng.Instance.GenDulplicateCarrierID(idrResult.strCarrierID);
                                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, dupCarrierRealLoc);
                                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);

                                                string strCarrierAddress = CarrierMng.Instance.GetCarrierLocation(currCommand.strCarrierID);
                                                    //3.删除指令中CarrierID位置处的Carrier信息
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                                    //4.在指令中的位置处安装读到的盒子信息
                                                CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, strCarrierAddress);
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                                }
                                                break;
                                            case ResultCode.MismatchID:
                                                {
                                                    //任务完成，错误码？
                                                    currCommand.resultCode = ResultCode.MismatchID;
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);

                                                    string strCarrierAddress = CarrierMng.Instance.GetCarrierLocation(currCommand.strCarrierID);

                                                    //{
                                                    //1.删除指令中CarrierID位置处的Carrier信息
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                                //2.在Carrier当前位置Install读码得到的盒子信息
                                                CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, strCarrierAddress);
                                                HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                            }
                                            break;
                                        case ResultCode.EqInterLockNG:
                                            {
                                                currCommand.resultCode = ResultCode.EqInterLockNG;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            }
                                            break;
                                        case ResultCode.TypeMismatch:
                                        case ResultCode.ShelfZoneFull:
                                        case ResultCode.OtherError:
                                        default:
                                            break;
                                        }
                                    }
                                    else
                                    {
                                        currCommand.resultCode = ResultCode.Success;
                                        if (TransferType.Shelf2Shelf == currCommand.transferType
                                            || TransferType.InPort2Shelf == currCommand.transferType
                                            || TransferType.EqPort2Shlef == currCommand.transferType)
                                        {
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierStored);
                                        }
                                        else
                                        {
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                        }
                                    }

                                    HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneIdle);

                                    // 更新画面
                                    CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);

                                    //if(currCommand.iAltStep == 1)
                                    //{
                                    //    string strCmdID = currCommand.strCommandID;
                                    //    string strCarrierID = currCommand.strCarrierID;
                                    //    string strOriginalSrc = currCommand.strRealSource;
                                    //    string strSrcAddress = currCommand.strRealDest;
                                    //    string strDestAddress = currCommand.strOriginalDest;
                                    //    LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);   
                                    //    endCurrentCommand(TransferState.None, resultCode);
                                    //    lock (GlobalData.Instance.objRWLock)
                                    //    {
                                    //        int iCount = GlobalData.Instance.gbEnhancedTransfers.Count;
                                    //        for (int i = 0; i < iCount; i++)
                                    //        {
                                    //            if (GlobalData.Instance.gbEnhancedTransfers[i].strCommandID == strCmdID)
                                    //            {
                                    //                GlobalData.Instance.gbEnhancedTransfers.RemoveAt(i);
                                    //            }
                                    //        }
                                    //    }

                                    //    //添加Alt Transfer指令
                                    //    EnhancedTransferCommand eTransferCommand = new EnhancedTransferCommand();
                                    //    eTransferCommand.transferState = TransferState.Queue;
                                    //    eTransferCommand.strCommandName = "TRANSFER";
                                    //    eTransferCommand.strCommandID = strCmdID;
                                    //    eTransferCommand.u2Priority = 1;
                                    //    eTransferCommand.strCarrierID = strCarrierID;
                                    //    eTransferCommand.strCarrierLoc = strSrcAddress;
                                    //    eTransferCommand.strDest = strDestAddress;
                                    //    eTransferCommand.strStockerCraneID = GlobalData.Instance.gbStockerCraneID;  //单Crane时，固定分派
                                    //    eTransferCommand.iFirstSecendCrane = 1;
                                    //    eTransferCommand.transferState = TransferState.Queue;
                                    //    eTransferCommand.strRealSource = strSrcAddress;
                                    //    eTransferCommand.strRealDest = strDestAddress;
                                    //    eTransferCommand.sourceLocType = LocationType.Shelf;
                                    //    eTransferCommand.destLocType = LocationMng.Instance.GetLocationType(strDestAddress);
                                    //    if(eTransferCommand.destLocType == LocationType.IoPort)
                                    //    {
                                    //        eTransferCommand.transferType = TransferType.Shelf2OutPort;
                                    //    }
                                    //    else if (eTransferCommand.destLocType == LocationType.EqPort)
                                    //    {
                                    //        eTransferCommand.transferType = TransferType.Shelf2EqPort;
                                    //    }
                                    //    eTransferCommand.strSourceZone = LocationMng.Instance.GetLocationZone(strSrcAddress);
                                    //    eTransferCommand.strDestZone = LocationMng.Instance.GetLocationZone(strDestAddress);
                                    //    eTransferCommand.iAltStep = 2;
                                    //    eTransferCommand.strOriginalSrc = strOriginalSrc;
                                    //    LocationMng.Instance.ReserveLocation(strDestAddress);
                                    //    lock (GlobalData.Instance.objRWLock)
                                    //    {
                                    //        GlobalData.Instance.gbEnhancedTransfers.Add(eTransferCommand);
                                    //    }
                                    //    HistoryWriter.Instance.CreateTransferCmd(DateTime.Now, strCmdID, eTransferCommand.strCarrierID, "Auto",
                                    //        eTransferCommand.strCommandName, eTransferCommand.transferType.ToString(), (int)eTransferCommand.u2Priority, eTransferCommand.strRealSource, eTransferCommand.strRealDest, (int)eTransferCommand.u2Priority);
                                    //}
                                    //else
                                    //{
                                    //结束Crane的当前任务
                                    endCurrentCommand(TransferState.None, resultCode);
                                    //}

                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand");
                                }
                                break;
                            case 10:  //EmptyRetrival的处理
                                {
                                    //EmptyRetrival对应的AlarmID
                                    HostIF.Instance.PostAlarm((uint)AlarmCode.SourceEmpty);
                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty"+ currCommand.strStockerCraneID, currCommand.strStockerCraneID, "RETRY, ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmSet);
                                    taskStep = 40;
                                }
                                break;
                            case 20:  //DoubleStorage的处理
                                {
                                    //DoubleStorage的处理对应的AlarmID
                                    HostIF.Instance.PostAlarm((uint)AlarmCode.DoubleStorage);
                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "DestOccupied"+ currCommand.strStockerCraneID, GlobalData.Instance.gbStockerCraneID, "RETRY, ABORT", (int)AlarmCode.DoubleStorage, EqpEvent.AlarmSet);
                                    taskStep++;
                                }
                                break;
                            case 21:
                                {
                                    if (bDoubleStorage)
                                    {
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "doubleStorage start");
                                       
                                        taskStep = 40;
                                    }
                                }
                                break;
                            case 30: //Abort流程的处理
                                break;
                        }
                    }
                    break;
                case TransferState.Aborting:
                    {
                        if(taskStep > 1 && taskStep < 40)
                        {
                            taskStep = 40;
                        }
                        switch (taskStep)
                        {
                            case 40:
                                {                            

                                    if (bEmptyRetrieval)
                                    {
                                        CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);
                                        CraneMng.Instance.ResetCraneAlarmWithEmptyOrDouble(craneName);
                                        AlarmController.Instance.ClearAlarmByCode((int)AlarmCode.SourceEmpty, GlobalData.Instance.gbStockerCraneID);
                                        HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty"+ currCommand.strStockerCraneID, currCommand.strStockerCraneID, "RETRY, ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmCleared);
                                        HostIF.Instance.ClearAlarm((uint)AlarmCode.SourceEmpty);
                                    }
                                    else if (bDoubleStorage)
                                    {
                                        string strUnknownID = CarrierMng.Instance.GenDoubleStorageCarrierID(currCommand.strCarrierID);
                                        CarrierMng.Instance.AddCarrierInfo(strUnknownID, currCommand.strRealDest);
                                        CraneMng.Instance.ResetCraneAlarmWithEmptyOrDouble(craneName);
                                        AlarmController.Instance.ClearAlarmByCode((int)AlarmCode.DoubleStorage, GlobalData.Instance.gbStockerCraneID);
                                        HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "DestOccupied"+ currCommand.strStockerCraneID, GlobalData.Instance.gbStockerCraneID, "RETRY, ABORT", (int)AlarmCode.DoubleStorage, EqpEvent.AlarmCleared);
                                        HostIF.Instance.ClearAlarm((uint)AlarmCode.DoubleStorage);
                                    }
                                    else if (resultCode != ResultCode.EqInterLockNG)
                                    {
                                        HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "Abort", currCommand.strStockerCraneID, "ABORT", 222, EqpEvent.AlarmCleared);
                                        HostIF.Instance.ClearAlarm(222);
                                    }

                                    CraneDev.Instance.CraneTaskAbort(craneTag);

                                    if (resultCode != ResultCode.EqInterLockNG)
                                    {
                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferAbortInitiated);
                                    }

                                    taskStep++;
                                }
                                break;
                            case 41:
                                {
                                    CraneDev.Instance.CraneAbortCompleteAck(craneTag);
                                    CraneDev.Instance.CraneTaskCompleteAck(craneTag);

                                    taskStep++;
                                }

                                break;
                            case 42:
                                {
                                    if (CraneDev.Instance.IsCraneAbortComplete(craneTag))
                                    {
                                        return;
                                    }

                                    LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);
                                    LocationMng.Instance.UnReserveLocation(currCommand.strRealDest);
                                    //重置Crane任务启动信号
                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);

                                    CraneDev.Instance.ResetCraneAbortCompleteAck(craneTag);
                                    CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);

                                    modeState = CraneModeState.Idle;

                                    if (resultCode == ResultCode.EqInterLockNG)
                                    {
                                        currCommand.resultCode = ResultCode.EqInterLockNG;
                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                    }
                                    else
                                    {
                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferAbortCompleted);
                                    }
 

                                    if (bEmptyRetrieval)
                                    {
                                       // CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);
                                        HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, currCommand.strRealSource, EqpEvent.CarrierRemoveCompleted);
                                        //还要添加一个UnKnown的盒子？
                                        string strUnknownID = CarrierMng.Instance.GenEmptyRetrvScanCarrierID(currCommand.strCarrierID);
                                        CarrierMng.Instance.AddCarrierInfo(strUnknownID, currCommand.strRealSource);
                                        HostIF.Instance.PostCarrierEvent(strUnknownID, EqpEvent.CarrierInstallCompleted);
                                    }
                                    else if (bDoubleStorage)
                                    {
                                        string strUnknownID = LocationMng.Instance.GetLocationCarrierByAddress(currCommand.strRealDest);
                                       // CarrierMng.Instance.AddCarrierInfo(strUnknownID, currCommand.strRealDest);
                                        HostIF.Instance.PostCarrierEvent(strUnknownID, EqpEvent.CarrierInstallCompleted);
                                    }

                                    //结束Crane的当前任务
                                    endCurrentCommand(TransferState.None, resultCode);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand");
                                }

                                break;

                        }
                    }
                    break;
                case TransferState.Canceling:
                    break;
                case TransferState.Paused:
                    break;
                case TransferState.None:   //不应该在此出现
                    {
                        //结束Crane的当前任务
                        resultCode = ResultCode.OtherError;
                        endCurrentCommand(TransferState.None, resultCode);
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand-TransferState.None");
                    }
                    break;
            }
        }

        private void executeMoveCommand()
        {
            switch (currCommand.transferState)
            {
                case TransferState.Queue:
                    {
                        taskStep = 1;
                        TransferMng.Instance.UpdateTransferCrane(currCommand.strCommandID, strCraneName);
                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Transferring, ResultCode.None);
                        HistoryWriter.Instance.CraneTransferStart(currCommand.strCommandID, currCommand.iFirstSecendCrane, strCraneName);
                    }
                    break;
                case TransferState.Transferring:
                    {
                        switch (taskStep)
                        {
                            case 1:
                                {
                                    CraneTaskData taskData = new CraneTaskData();
                                    taskData.taskType = CraneTaskType.Move;
                                    taskData.strCarrierID = currCommand.strCarrierID;
                                    taskData.strSourceAddr = currCommand.strRealSource;
                                    taskData.strDestAddr = currCommand.strRealDest;
                                    //向Crane发生任务数据
                                    CraneDev.Instance.CraneServoOn(craneTag);
                                    if (CraneDev.Instance.SendCraneCommand(craneTag, taskData))
                                    {
                                        //记录Crane日志：成功向Crane发生任务数据
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                            , $"发送成功:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");

                                        //Crane任务启动
                                        if (CraneDev.Instance.CraneTaskStart(craneTag))
                                        {
                                            //记录Crane日志：启动Crane任务成功
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                            taskStep++;
                                        }
                                        else
                                        {
                                            //记录Crane日志：启动Crane任务失败
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动失败");
                                            CraneDev.Instance.CleanCraneCommand(craneTag);
                                            TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, ResultCode.OtherError);
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                        }
                                    }
                                    else  //SendCraneCommand失败
                                    {
                                        //记录Crane日志：向Crane发生任务数据失败
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                            , $"发送失败:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");
                                        if (++iFailCount == 3)
                                        {
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                            modeState = CraneModeState.Idle;
                                        }
                                    }
                                }
                                break;
                            case 2:
                                // Crane开始向取货处行走
                                {
                                    if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                    {
                                        craneTaskStep.iStep = 10;
                                    }
                                    else
                                    {
                                        craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    }

                                    //Crane还未向源地址移动
                                    if (craneTaskStep.iStep < 10)
                                    {
                                        return;
                                    }
                                    else if (craneTaskStep.iStep == 10)
                                    {
                                        //Crane开始向源地址移动
                                        modeState = CraneModeState.Active;
                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.CraneActive);
                                        //记录Crane日志：Crane开始向源地址移动
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane开始向源地址移动");
                                        taskStep++;
                                    }
                                    else
                                    {
                                        taskStep++;
                                    }
                                }
                                break;
                            case 3:
                                // 动作完成
                                {
                                    //等待Crane任务完成信号
                                    if (!CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                    {
                                        return;
                                    }

                                    //重置Crane任务启动信号
                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);

                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Task Complete");
                                    //Crane任务完成，答复完成信号
                                    CraneDev.Instance.CraneTaskCompleteAck(craneTag);
                                    ////上报完成
                                    //HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                    taskStep++;
                                }
                                break;
                            case 4:
                                {
                                    //Crane任务完成信号复位
                                    if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                    {
                                        return;
                                    }

                                    //复位答复完成信号
                                    CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);

                                    modeState = CraneModeState.Idle;
                                    HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneIdle);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneIdle, EqpEvent.CraneIdle.ToString(), "Crane Idle");
                                    TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, resultCode);
                                    
                                    
                                    //上报MCS CarrierRemoveCompleted
                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ScanCompleted);

                                    //LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);
                                    LocationMng.Instance.UnReserveLocation(currCommand.strRealDest);

                                    //结束Crane的当前任务
                                    resultCode = ResultCode.Success;
                                    endCurrentCommand(TransferState.None, resultCode);
                                }
                                taskStep++;
                                break;
                            case 5:
                                // 结束命令
                                taskStep = -1;
                                break;
                        }

                    }
                    break;
                case TransferState.Aborting:
                    {
                        AbortSchedule();
                    }
                    break;
                case TransferState.Canceling:
                    break;
                case TransferState.Paused:
                    break;
                case TransferState.None:   //不应该在此出现
                    {
                        //结束Crane的当前任务
                        resultCode = ResultCode.OtherError;
                        endCurrentCommand(TransferState.None, resultCode);
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand-TransferState.None");
                    }
                    break;
            }
        }

        private void DoubleCheckCommand()
        {
            switch (currCommand.transferState)
            {
                case TransferState.Queue:
                    {
                        taskStep = 1;
                        TransferMng.Instance.UpdateTransferCrane(currCommand.strCommandID, strCraneName);
                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Transferring, ResultCode.None);
                        HistoryWriter.Instance.CraneTransferStart(currCommand.strCommandID, currCommand.iFirstSecendCrane, strCraneName);
                    }
                    break;
                case TransferState.Transferring:
                    {
                        switch (taskStep)
                        {
                            case 1:
                                {
                                    //仿真专用
                                    if (!Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        CraneDev.Instance.CraneServoOn(craneTag);
                                        //检查Fork上没有盒子，向Crane发生任务数据，启动任务
                                        if (!CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                        {
                                            CraneTaskData taskData = new CraneTaskData();
                                            taskData.taskType = CraneTaskType.DoubleCheck;
                                            taskData.strCarrierID = currCommand.strCarrierID;
                                            taskData.strSourceAddr = currCommand.strRealSource;
                                            taskData.strDestAddr = currCommand.strRealDest;
                                            //向Crane发生任务数据
                                            if (CraneDev.Instance.SendCraneCommand(craneTag, taskData))
                                            {
                                                //记录Crane日志：成功向Crane发生任务数据
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                                    , $"发送成功:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");

                                                //Crane任务启动
                                                if (CraneDev.Instance.CraneTaskStart(craneTag))
                                                {
                                                    //记录Crane日志：启动Crane任务成功
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");

                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                                    CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                                    taskStep++;
                                                }
                                                else
                                                {
                                                    //记录Crane日志：启动Crane任务失败
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动失败");
                                                    CraneDev.Instance.CleanCraneCommand(craneTag);
                                                    TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, ResultCode.OtherError);
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                }
                                            }
                                            else  //SendCraneCommand失败
                                            {
                                                //记录Crane日志：向Crane发生任务数据失败
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                                    , $"发送失败:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");
                                                if (++iFailCount == 3)
                                                {
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                    endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                                    modeState = CraneModeState.Idle;
                                                }
                                            }
                                        }
                                        else //检查Fork上有盒子
                                        {
                                            //记录Crane日志：向Crane发送任务数据前，Fork上检测到盒子
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "向Crane发送DoubleCheck任务数据前，Fork上有盒子");
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                            modeState = CraneModeState.Idle;
                                        }
                                    }
                                    else
                                    {
                                        //记录Crane日志：启动Crane任务成功
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");

                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                        CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                        taskStep++;

                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 30, 10);
                                    }
                                }
                                break;
                            case 2:
                                {
                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    if (craneTaskStep.iStep >= 30 || CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                    {
                                        //重置Crane任务启动信号
                                        CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                        HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneActive);
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step3");
                                        taskStep++;
                                    }
                                }
                                break;
                            case 3:
                                {
                                    //仿真专用
                                    if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 40, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10);
                                    }

                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    if (craneTaskStep.iStep > 0 && craneTaskStep.iStep < 40)
                                    {
                                        //Crane正在向目的地移动
                                        return;
                                    }
                                    if (craneTaskStep.iStep == 40 || CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                    {
                                        Thread.Sleep(1000);
                                        idrResult = CraneDev.Instance.GetIDRResult(craneTag);
                                        switch ((IDRResultCode)idrResult.iResultCode)
                                        {
                                            case IDRResultCode.DoubleCheckRight:
                                                {
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                        , EqpEvent.CarrierIDRead.ToString(), $"DoubleCheckRight");
                                                    resultCode = ResultCode.Success;
                                                    currCommand.resultCode = ResultCode.Success;
                                                }
                                                break;
                                            case IDRResultCode.DoubleCheckError:
                                                {
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                        , EqpEvent.CarrierIDRead.ToString(), $"DoubleCheckError");
                                                    resultCode = ResultCode.OtherError;
                                                    currCommand.resultCode = ResultCode.OtherError;
                                                }
                                                break;
                                        }
                                        taskStep++;
                                    }
                                }
                                break;
                            case 4:
                                {
                                    //Crane的Fork正在缩回，等待Crane任务完成信号
                                    if (!CraneDev.Instance.IsCraneTaskComplete(craneTag)
                                        && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        return;
                                    }

                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Task Complete");
                                    //Crane任务完成，答复完成信号
                                    CraneDev.Instance.CraneTaskCompleteAck(craneTag);
                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                    taskStep++;
                                }
                                break;
                            case 5:
                                {
                                    //Crane任务完成信号复位
                                    if (CraneDev.Instance.IsCraneTaskComplete(craneTag)
                                        && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        return;
                                    }

                                    //复位答复完成信号
                                    CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);

                                    modeState = CraneModeState.Idle;
                                    HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneIdle);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneIdle, EqpEvent.CraneIdle.ToString(), "Crane Idle");

                                    //重置Crane任务启动信号
                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);

                                    TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, resultCode);
                                    LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);
                                    LocationMng.Instance.UnReserveLocation(currCommand.strRealDest);
                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                    
                                    //结束Crane的当前任务
                                    endCurrentCommand(TransferState.None, resultCode);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand");
                                }
                                break;
                        }
                    }
                    break;
                case TransferState.Aborting:
                    {
                        AbortSchedule();
                    }
                    break;
                case TransferState.Canceling:
                    break;
                case TransferState.Paused:
                    break;
                case TransferState.None:   //不应该在此出现
                    {
                        //结束Crane的当前任务
                        resultCode = ResultCode.OtherError;
                        endCurrentCommand(TransferState.None, resultCode);
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand-TransferState.None");
                    }
                    break;
            }
        }

        private void PlaceCommand()
        {
            switch (currCommand.transferState)
            {
                case TransferState.Queue:
                    {
                        taskStep = 1;
                        TransferMng.Instance.UpdateTransferCrane(currCommand.strCommandID, strCraneName);
                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Transferring, ResultCode.None);
                        LocationMng.Instance.ReserveLocation(currCommand.strRealDest);
                        HistoryWriter.Instance.CraneTransferStart(currCommand.strCommandID, currCommand.iFirstSecendCrane, strCraneName);
                    }
                    break;
                case TransferState.Transferring:
                    {
                        switch (taskStep)
                        {
                            case 1:
                                {
                                    bEmptyRetrieval = false;
                                    bDoubleStorage = false;

                                    if (!Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        CraneDev.Instance.CraneServoOn(craneTag);
                                        //检查Fork上没有盒子，向Crane发生任务数据，启动任务
                                        if (CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                        {
                                            CraneTaskData taskData = new CraneTaskData();
                                            taskData.taskType = CraneTaskType.Place;
                                            taskData.strCarrierID = currCommand.strCarrierID;
                                            taskData.strSourceAddr = currCommand.strRealSource;
                                            taskData.strDestAddr = currCommand.strRealDest;
                                            //向Crane发生任务数据
                                            if (CraneDev.Instance.SendCraneCommand(craneTag, taskData))
                                            {
                                                //记录Crane日志：成功向Crane发生任务数据
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                                    , $"发送成功:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");

                                                //Crane任务启动
                                                if (CraneDev.Instance.CraneTaskStart(craneTag))
                                                {
                                                    //记录Crane日志：启动Crane任务成功
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");

                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                                    CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                                    taskStep++;
                                                }
                                                else
                                                {
                                                    //记录Crane日志：启动Crane任务失败
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动失败");
                                                    CraneDev.Instance.CleanCraneCommand(craneTag);
                                                    TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, ResultCode.OtherError);
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                }
                                            }
                                            else  //SendCraneCommand失败
                                            {
                                                //记录Crane日志：向Crane发生任务数据失败
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                                    , $"发送失败:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");
                                                if (++iFailCount == 3)
                                                {
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                    endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                                    modeState = CraneModeState.Idle;
                                                }
                                            }
                                        }
                                        else //检查Fork上有盒子
                                        {
                                            //记录Crane日志：向Crane发送任务数据前，Fork上检测到盒子
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "向Crane发送任务数据前，Fork上检测到盒子");
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                            modeState = CraneModeState.Idle;
                                        }
                                    }
                                    else
                                    {
                                        //记录Crane日志：启动Crane任务成功
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");

                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                        CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                        taskStep++;

                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 30, 10);
                                    }
                                }
                                break;
                            case 2:
                                {
                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    if (craneTaskStep.iStep >= 30)
                                    {
                                        //重置Crane任务启动信号
                                        CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                        HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneActive);
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step3");
                                        taskStep++;
                                    }
                                }
                                break;
                            case 3:
                                {

                                    //仿真专用
                                    if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 40, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10);
                                    }

                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    //if (craneTaskStep.iStep > 0 && craneTaskStep.iStep < 40)
                                    //{
                                    //    //Crane正在向目的地移动
                                    //    return;
                                    //}
                                    if (craneTaskStep.iStep < 40)
                                    {
                                        if (CraneDev.Instance.CheckCraneE84Alarms(craneTag))
                                        {
                                            resultCode = ResultCode.EqInterLockNG;
                                            if (CraneDev.Instance.CraneTaskPause(craneTag))
                                            {
                                                CraneDev.Instance.CraneTaskAbort(craneTag);
                                                TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Aborting, ResultCode.EqInterLockNG);
                                            }
                                            else
                                            {
                                                currCommand.resultCode = ResultCode.EqInterLockNG;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                endCurrentCommand(TransferState.None, ResultCode.EqInterLockNG);
                                                modeState = CraneModeState.Idle;
                                            }
                                            return;
                                        }
                                        if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                        {
                                            if (iLastPlcStep == 40 && (iLastPlcAction == 20 || iLastPlcAction == 30))
                                            {
                                                if (bDoubleStorage = CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                                {
                                                    //盒子还在货叉上，发生了DoubleStorage
                                                    taskStep = 20;
                                                }
                                                else
                                                {
                                                    if (currCommand.strDestZone.Equals("BSSTKS03M_OUT01"))
                                                    {
                                                        if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "MR2"))
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("MR2出库", 0, "WritePortOPOUTCarrierID Success!", currCommand.strCarrierID);
                                                        }
                                                        else
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("MR2出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                        }
                                                    }
                                                    if (currCommand.strDestZone.Equals("BSSTKS03A_OUT01"))
                                                    {
                                                        if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "OHT2"))
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("OHT2出库", 0, "WritePortOPOUTCarrierID", currCommand.strCarrierID);
                                                        }
                                                        else
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("OHT2出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                        }
                                                    }
                                                    if (currCommand.strDestZone.Equals("BSSTKS03A_OUT02"))
                                                    {
                                                        if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "OHT4"))
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("OHT4出库", 0, "WritePortOPOUTCarrierID", currCommand.strCarrierID);
                                                        }
                                                        else
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog("OHT4出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                        }
                                                    }
                                                    CarrierMng.Instance.CranePlaceCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealDest);
                                                    strCarrierID = "";
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                                        $"CranePlaceCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strDest} ");

                                                    CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Compeleted);

                                                    if (TransferType.InPort2Shelf == currCommand.transferType)
                                                    {
                                                        HostIF.Instance.PostZoneCapacityChangeEvent(currCommand.strCarrierID, EqpEvent.ZoneCapacityChange);
                                                    }

                                                    taskStep++;
                                                    bAction1Started = false;
                                                    bAction2Started = false;
                                                    bAction3Started = false;
                                                    bAction4Started = false;
                                                    bAction9Started = false;
                                                }
                                            }
                                            else
                                            {
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, 10001, "StepError",
                                                        "Get set error, taskstep=5");
                                            }
                                        }
                                        return;
                                    }
                                    //Crane开始放下盒子
                                    if (craneTaskStep.iStep == 40)
                                    {
                                        if (CraneDev.Instance.GetCraneWorkingStatus(craneTag) == CraneWorkingStatus.Paused
                                            || Proj.CacheData.GlobalData.Instance.gbSimulation)
                                        {
                                            if (CraneDev.Instance.IsDoubleStorage(craneTag))
                                            {
                                                if (CraneDev.Instance.CraneTaskPause(craneTag))
                                                {
                                                    if (GlobalData.Instance.gbRetryCount == 3)
                                                    {
                                                        GlobalData.Instance.gbRetryCount = 0;
                                                        bDoubleStorage = true;
                                                        taskStep = 20;  //DoubleStorage的处理
                                                    }
                                                    else
                                                    {
                                                        GlobalData.Instance.gbRetryCount += 1;
                                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GlobalData.Instance.gbRetryCount, "doubleStorage Retry" + GlobalData.Instance.gbRetryCount.ToString(), "");

                                                        HostIF.Instance.PostAlarm((uint)AlarmCode.DoubleStorage);
                                                        HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "DestOccupied"+ currCommand.strStockerCraneID, GlobalData.Instance.gbStockerCraneID, "RETRY, ABORT", (int)AlarmCode.DoubleStorage, EqpEvent.AlarmSet);
                                                        Thread.Sleep(100);
                                                        CraneMng.Instance.ResetCraneAlarmWithEmptyOrDouble(craneName);
                                                        AlarmController.Instance.ClearAlarmByCode((int)AlarmCode.DoubleStorage, GlobalData.Instance.gbStockerCraneID);
                                                        Thread.Sleep(50);
                                                        HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "DestOccupied" + currCommand.strStockerCraneID, GlobalData.Instance.gbStockerCraneID, "RETRY, ABORT", (int)AlarmCode.DoubleStorage, EqpEvent.AlarmCleared);
                                                        HostIF.Instance.ClearAlarm((uint)AlarmCode.DoubleStorage);

                                                        if (ControlState.OnlineRemote != Proj.CacheData.GlobalData.Instance.gbControlState
                                                            || GlobalData.Instance.gbMCSCommunication != MCSCommunication.Communicating
                                                            || GlobalData.Instance.gbHSMSState != HSMSState.Connected)
                                                        {
                                                            CraneDev.Instance.CraneTaskResume(craneTag);
                                                            Thread.Sleep(100);
                                                            uiIsRetryFlag = 1;
                                                        }
                                                    }
                                                }

                                                break;
                                            }
                                        }
                                        if (bAction2Started == false && craneTaskStep.iAction == 20)
                                        {
                                            iLastPlcStep = 40;
                                            iLastPlcAction = 20;
                                            //Crane开始伸Fork
                                            bAction2Started = true;
                                            HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkingStarted);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action2");
                                        }
                                        else if (bAction3Started == false && craneTaskStep.iAction == 30)
                                        {
                                            iLastPlcStep = 40;
                                            iLastPlcAction = 30;
                                            //Crane的Fork开始下降
                                            bAction3Started = true;
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action3");

                                        }
                                        else if (bAction4Started == false && craneTaskStep.iAction == 40)
                                        {
                                            //Crane的Fork开始缩回(ForkDowned)
                                            HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkDowned);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step4, Action4");

                                            //DoubleStorage检查
                                            if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                            {
                                                bDoubleStorage = true;
                                                //盒子还在货叉上，发生了DoubleStorage
                                                taskStep = 20;
                                            }
                                            else
                                            {
                                                if (currCommand.strDestZone.Equals("BSSTKS03M_OUT01"))
                                                {
                                                    if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "MR2"))
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("MR2出库", 0, "WritePortOPOUTCarrierID Success!", currCommand.strCarrierID);
                                                    }
                                                    else
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("MR2出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                    }
                                                }
                                                if (currCommand.strDestZone.Equals("BSSTKS03A_OUT01"))
                                                {
                                                    if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "OHT2"))
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("OHT2出库", 0, "WritePortOPOUTCarrierID", currCommand.strCarrierID);
                                                    }
                                                    else
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("OHT2出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                    }
                                                }
                                                if (currCommand.strDestZone.Equals("BSSTKS03A_OUT02"))
                                                {
                                                    if (CraneDev.Instance.WritePortOPOUTCarrierID(currCommand.strCarrierID, "OHT4"))
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("OHT4出库", 0, "WritePortOPOUTCarrierID", currCommand.strCarrierID);
                                                    }
                                                    else
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog("OHT4出库", 0, "WritePortOPOUTCarrierID Failed!", currCommand.strCarrierID);
                                                    }
                                                }

                                                CarrierMng.Instance.CranePlaceCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealDest);
                                                strCarrierID = "";
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                                    $"CranePlaceCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strDest} ");

                                                CarrierMng.Instance.UpdateCarrierState(currCommand.strCarrierID, CarrierState.Compeleted);

                                                if (TransferType.InPort2Shelf == currCommand.transferType)
                                                {
                                                    HostIF.Instance.PostZoneCapacityChangeEvent(currCommand.strCarrierID, EqpEvent.ZoneCapacityChange);
                                                }

                                                taskStep++;
                                            }

                                            bAction2Started = false;
                                            bAction3Started = false;
                                        }
                                    }
                                    else
                                    {
                                        taskStep++;
                                    }
                                }
                                break;
                            case 4:
                                {
                                    //Crane的Fork正在缩回，等待Crane任务完成信号
                                    if (!CraneDev.Instance.IsCraneTaskComplete(craneTag)
                                        && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        return;
                                    }

                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Task Complete");
                                    //Crane任务完成，答复完成信号
                                    CraneDev.Instance.CraneTaskCompleteAck(craneTag);
                                    //CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);

                                    if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                    {
                                        bDoubleStorage = true;
                                        resultCode = ResultCode.OtherError;
                                    }
                                    else
                                    {
                                        resultCode = ResultCode.Success;
                                    }

                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                    taskStep++;
                                }
                                break;
                            case 5:
                                {
                                    //Crane任务完成信号复位
                                    if (CraneDev.Instance.IsCraneTaskComplete(craneTag)
                                        && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        //重置Crane任务启动信号
                                        CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                        //Crane任务完成，答复完成信号
                                        CraneDev.Instance.CraneTaskCompleteAck(craneTag);//PLC没有将Complete信号置成0，STKC重新Ack
                                        return;
                                    }

                                    //复位答复完成信号
                                    CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);

                                    modeState = CraneModeState.Idle;
                                    HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneIdle);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneIdle, EqpEvent.CraneIdle.ToString(), "Crane Idle");

                                    //重置Crane任务启动信号
                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);

                                    TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, resultCode);
                                    //LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);
                                    LocationMng.Instance.UnReserveLocation(currCommand.strRealDest);
                                    if(IsHaveIDR)
                                    {
                                        switch (resultCode)
                                        {
                                            case ResultCode.Success:
                                                {
                                                    currCommand.resultCode = ResultCode.Success;
                                                    if (TransferType.Crane2Shelf == currCommand.transferType)
                                                    {
                                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                        HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierStored);
                                                    }
                                                    else
                                                    {
                                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                    }
                                                }
                                                break;
                                            case ResultCode.IDReadFail:
                                                {
                                                    //任务完成，错误码？
                                                    currCommand.resultCode = ResultCode.IDReadFail;
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);

                                                    //1.删除原CarrierID的Carrier信息
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);
                                                    //2.添加UnKnownID的Carrier信息
                                                    string strUnKnownID = CarrierMng.Instance.GetIDRFailCarrierID(currCommand.strRealSource);
                                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);
                                                }
                                                break;
                                            case ResultCode.DuplicateID:
                                                {
                                                    //任务完成，错误码？
                                                    currCommand.resultCode = ResultCode.DuplicateID;
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);

                                                    //1.删除DuplicateID的盒子信息
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(idrResult.strCarrierID);

                                                    //2.在DuplicateID的位置处添加UnKnownID的Carrier信息
                                                    string strUnKnownID = CarrierMng.Instance.GenDulplicateCarrierID(idrResult.strCarrierID);
                                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, dupCarrierRealLoc);
                                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);

                                                    //3.删除指令中CarrierID位置处的Carrier信息
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                                    //4.在指令中的位置处安装读到的盒子信息
                                                    CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                                }
                                                break;
                                            case ResultCode.MismatchID:
                                                {
                                                    //任务完成，错误码？
                                                    currCommand.resultCode = ResultCode.MismatchID;
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);

                                                    //1.删除指令中CarrierID位置处的Carrier信息
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                                    //2.在指令中的位置处安装读到的盒子信息
                                                    CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                                }
                                                break;
                                            case ResultCode.TypeMismatch:
                                            case ResultCode.ShelfZoneFull:
                                            case ResultCode.OtherError:
                                            default:
                                                break;
                                        }
                                    }
                                    else
                                    {
                                        currCommand.resultCode = ResultCode.Success;
                                        if (TransferType.Crane2Shelf == currCommand.transferType)
                                        {
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierStored);
                                        }
                                        else
                                        {
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                        }
                                    }

                                    //if (TransferType.Crane2OutPort == currCommand.transferType)
                                    //{
                                    //    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoved);
                                    //}

                                    // 更新画面
                                    CraneMng.Instance.SendCraneActionToUI(strCraneName, "PlaceComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                    //结束Crane的当前任务
                                    endCurrentCommand(TransferState.None, resultCode);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand");
                                }
                                break;
                            case 10:  //EmptyRetrival的处理
                                {
                                    //EmptyRetrival对应的AlarmID
                                    HostIF.Instance.PostAlarm((uint)AlarmCode.SourceEmpty);
                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty"+ currCommand.strStockerCraneID, currCommand.strStockerCraneID, "RETRY, ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmSet);
                                    taskStep++;
                                }
                                break;
                            case 11:
                                {
                                    //如果收到了Retry指令,回到Step3重新执行
                                    //if ()
                                    //{
                                    //    CraneDev.Instance.CraneTaskResume();
                                    //    taskStep = 3;
                                    //}
                                }
                                break;
                            case 20:  //DoubleStorage的处理
                                {
                                    //DoubleStorage的处理对应的AlarmID
                                    HostIF.Instance.PostAlarm((uint)AlarmCode.DoubleStorage);
                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "DestOccupied"+ currCommand.strStockerCraneID, GlobalData.Instance.gbStockerCraneID, "RETRY, ABORT", (int)AlarmCode.DoubleStorage, EqpEvent.AlarmSet);
                                    taskStep++;
                                }
                                break;
                            case 21:
                                {
                                    if (bDoubleStorage)
                                    {
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "doubleStorage start");

                                        taskStep = 40;
                                    }
                                }
                                break;
                            case 30: //Abort流程的处理
                                break;
                        }
                    }
                    break;
                case TransferState.Aborting:
                    {
                        AbortSchedule();
                    }
                    break;
                case TransferState.Canceling:
                    break;
                case TransferState.Paused:
                    break;
                case TransferState.None:   //不应该在此出现
                    {
                        //结束Crane的当前任务
                        resultCode = ResultCode.OtherError;
                        endCurrentCommand(TransferState.None, resultCode);
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand-TransferState.None");
                    }
                    break;
            }
        }

        private void PickCommand()
        {
            switch (currCommand.transferState)
            {
                case TransferState.Queue:
                    {
                        taskStep = 1;
                        TransferMng.Instance.UpdateTransferCrane(currCommand.strCommandID, strCraneName);
                        TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Transferring, ResultCode.None);
                        HistoryWriter.Instance.CraneTransferStart(currCommand.strCommandID, currCommand.iFirstSecendCrane, strCraneName);
                    }
                    break;
                case TransferState.Transferring:
                    {
                        switch (taskStep)
                        {
                            case 1:
                                {
                                    if (!Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        CraneDev.Instance.CraneServoOn(craneTag);
                                        //检查Fork上没有盒子，向Crane发生任务数据，启动任务
                                        if (!CraneDev.Instance.IsCraneForkHasCarrier(craneTag))
                                        {
                                            if (IsHaveIDR)
                                            {
                                                //如果Shelf2Shelf设置了IDRead bypass，告诉Crane 不读码
                                                if (bReadBypass)
                                                {
                                                    CraneDev.Instance.IDReadBypass(craneTag, true);
                                                }
                                                else
                                                {
                                                    CraneDev.Instance.IDReadBypass(craneTag, false);
                                                }
                                            }

                                            CraneTaskData taskData = new CraneTaskData();
                                            taskData.taskType = CraneTaskType.Pick;
                                            taskData.strCarrierID = currCommand.strCarrierID;
                                            taskData.strSourceAddr = currCommand.strRealSource;
                                            taskData.strDestAddr = currCommand.strRealDest;
                                            //向Crane发生任务数据
                                            if (CraneDev.Instance.SendCraneCommand(craneTag, taskData))
                                            {
                                                //记录Crane日志：成功向Crane发生任务数据
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                                    , $"发送成功:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");

                                                //Crane任务启动
                                                if (CraneDev.Instance.CraneTaskStart(craneTag))
                                                {
                                                    //记录Crane日志：启动Crane任务成功
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");

                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                                    CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                                    taskStep++;
                                                }
                                                else
                                                {
                                                    //记录Crane日志：启动Crane任务失败
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动失败");
                                                    CraneDev.Instance.CleanCraneCommand(craneTag);
                                                    TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, ResultCode.OtherError);
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                }
                                            }
                                            else  //SendCraneCommand失败
                                            {
                                                //记录Crane日志：向Crane发生任务数据失败
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString()
                                                    , $"发送失败:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");
                                                if (++iFailCount == 3)
                                                {
                                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                    endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                                    modeState = CraneModeState.Idle;
                                                }
                                            }
                                        }
                                        else //检查Fork上有盒子
                                        {
                                            //记录Crane日志：向Crane发送任务数据前，Fork上检测到盒子
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "向Crane发送任务数据前，Fork上检测到盒子");
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                            endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                            modeState = CraneModeState.Idle;
                                        }
                                    }
                                    else
                                    {
                                        //记录Crane日志：启动Crane任务成功
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), "Crane启动成功");

                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferInitiated);
                                        CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                        taskStep++;

                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 10, 10);
                                    }
                                }
                                break;
                            case 2:
                                {
                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    //Crane还未向源地址移动
                                    //if (craneTaskStep.iStep < 10)
                                    //{
                                    //    return;
                                    //}
                                    if (craneTaskStep.iStep < 10 && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        if (CraneDev.Instance.CheckCraneE84Alarms(craneTag))
                                        {
                                            resultCode = ResultCode.EqInterLockNG;
                                            if (CraneDev.Instance.CraneTaskPause(craneTag))
                                            {
                                                CraneDev.Instance.CraneTaskAbort(craneTag);
                                                TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Aborting, ResultCode.EqInterLockNG);
                                            }
                                            else
                                            {
                                                currCommand.resultCode = ResultCode.EqInterLockNG;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                endCurrentCommand(TransferState.None, ResultCode.EqInterLockNG);
                                                modeState = CraneModeState.Idle;
                                            }
                                            return;
                                        }
                                        //}
                                        //向取Carrier的高度移动中
                                        if (IsHaveIDR && CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                        {
                                            DealReadCardWithTransfer();
                                        }
                                        return;
                                    }
                                    else if (craneTaskStep.iStep == 10)
                                    {
                                        if (IsHaveIDR && !bReadBypass)
                                        {
                                            //重置Crane任务启动信号
                                            CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                            //Crane开始向源地址移动
                                            modeState = CraneModeState.Active;
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.CraneActive);
                                            //记录Crane日志：Crane开始向源地址移动
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane开始向源地址移动");
                                            taskStep++;

                                            //仿真专用
                                            if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                            {
                                                CraneDev.Instance.setCraneTaskInfo(craneTag, 20, 10);
                                            }
                                        }
                                        else //IC Stocker无IDR
                                        {
                                            if (bAction1Started == false && craneTaskStep.iAction == 10)
                                            {
                                                //向目标位检测Shelf上有无Carrier高度移动
                                                bAction1Started = true;
                                                CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                                //Crane开始向源地址移动
                                                modeState = CraneModeState.Active;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.CraneActive);
                                                //记录Crane日志：Crane开始向源地址移动
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane Step1, Action1");

                                                //仿真专用
                                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                {
                                                    CraneDev.Instance.setCraneTaskInfo(craneTag, 10, 20);
                                                }
                                            }
                                            else if (bAction2Started == false && craneTaskStep.iAction == 20)
                                            {
                                                //开始向取Carrier的高度移动
                                                bAction1Started = false;
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferInitiated, EqpEvent.CraneActive.ToString(), "Crane Step1, Action2");
                                                if (CraneDev.Instance.IsCraneDetectedCarrier(craneTag)
                                                    || Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                {
                                                    taskStep++;

                                                    //仿真专用
                                                    if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                    {
                                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 20, 10);
                                                    }
                                                }
                                                else
                                                {
                                                    bEmptyRetrieval = true;
                                                    taskStep = 10;  //EmptyRetrieval的处理
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        taskStep++;
                                        //重置Crane任务启动信号
                                        CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                    }
                                }
                                break;
                            case 3:
                                {
                                    //仿真专用
                                    if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        CraneDev.Instance.setCraneTaskInfo(craneTag, 20, CraneDev.Instance.GetCraneTaskStep(craneTag).iAction + 10);
                                    }

                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep(craneTag);
                                    //if (craneTaskStep.iStep < 20)
                                    //{
                                    //    //向取Carrier的高度移动中
                                    //    return;
                                    //}
                                    if (craneTaskStep.iStep < 20)
                                    {
                                        if (CraneDev.Instance.CheckCraneE84Alarms(craneTag))
                                        {
                                            resultCode = ResultCode.EqInterLockNG;
                                            if (CraneDev.Instance.CraneTaskPause(craneTag))
                                            {
                                                CraneDev.Instance.CraneTaskAbort(craneTag);
                                                TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Aborting, ResultCode.EqInterLockNG);
                                            }
                                            else
                                            {
                                                currCommand.resultCode = ResultCode.EqInterLockNG;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                                endCurrentCommand(TransferState.None, ResultCode.EqInterLockNG);
                                                modeState = CraneModeState.Idle;
                                            }
                                            return;
                                        }
                                        //}
                                        //向取Carrier的高度移动中
                                        if (IsHaveIDR && CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                        {
                                            DealReadCardWithTransfer();
                                        }
                                        return;
                                    }
                                    //Crane开始取货
                                    if (craneTaskStep.iStep == 20)
                                    {
                                        if (CraneDev.Instance.IsEmptySource(craneTag))
                                        {
                                            if (CraneDev.Instance.CraneTaskPause(craneTag))
                                            {
                                                if (GlobalData.Instance.gbRetryCount == 3)
                                                {
                                                    GlobalData.Instance.gbRetryCount = 0;
                                                    //Source Empty
                                                    bEmptyRetrieval = true;
                                                    taskStep = 10;
                                                    bAction1Started = false;
                                                    bAction2Started = false;
                                                    bAction3Started = false;
                                                    bAction4Started = false;
                                                    bAction9Started = false;
                                                }
                                                else if (uiIsRetryFlag != 2)
                                                {
                                                    uiIsRetryFlag = 2;
                                                    GlobalData.Instance.gbRetryCount += 1;

                                                    HostIF.Instance.PostAlarm((uint)AlarmCode.SourceEmpty);
                                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty" + currCommand.strStockerCraneID, currCommand.strStockerCraneID, "RETRY, ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmSet);

                                                    Thread.Sleep(100);
                                                    CraneMng.Instance.ResetCraneAlarmWithEmptyOrDouble(craneName);
                                                    AlarmController.Instance.ClearAlarmByCode((int)AlarmCode.SourceEmpty, GlobalData.Instance.gbStockerCraneID);
                                                    Thread.Sleep(50);
                                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty" + currCommand.strStockerCraneID, currCommand.strStockerCraneID, "RETRY, ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmCleared);
                                                    HostIF.Instance.ClearAlarm((uint)AlarmCode.SourceEmpty);

                                                    if (ControlState.OnlineRemote != Proj.CacheData.GlobalData.Instance.gbControlState
                                                        || GlobalData.Instance.gbMCSCommunication != MCSCommunication.Communicating
                                                        || GlobalData.Instance.gbHSMSState != HSMSState.Connected)
                                                    {
                                                        CraneDev.Instance.CraneTaskResume(craneTag);
                                                        Thread.Sleep(100);
                                                        uiIsRetryFlag = 1;
                                                    }
                                                }
                                            }
                                        }
                                        else 
                                        if (bAction2Started == false && craneTaskStep.iAction == 20)
                                        {
                                            //Crane开始伸Fork
                                            bAction2Started = true;
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.ForkingStarted);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action2");
                                        }
                                        else if (bAction3Started == false && craneTaskStep.iAction == 30)
                                        {
                                            //Crane开始抬起Fork
                                            bAction3Started = true;
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action3");
                                        }
                                        else if (bAction4Started == false && craneTaskStep.iAction == 40)
                                        {
                                            //Crane开始缩回Fork
                                            bAction4Started = true;
                                            HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.ForkRised);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Step2, Action4");

                                            if (IsHaveIDR && !bReadBypass)
                                            {
                                                //处理读码结果（什么情况不读码，要有可配置的控制逻辑）lzt
                                                idrResult = CraneDev.Instance.GetIDRResult(craneTag);
                                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                {
                                                    idrResult.iResultCode = 1;
                                                    idrResult.strCarrierID = "CAR001";
                                                }
                                                switch ((IDRResultCode)idrResult.iResultCode)
                                                {
                                                    case IDRResultCode.Success:
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead Success:{idrResult.strCarrierID}");
                                                            resultCode = ResultCode.Success;
                                                        }
                                                        break;
                                                    case IDRResultCode.Mismatch: //进行Duplicate的检查
                                                        {
                                                            if (CarrierMng.Instance.IsCarrierDuplicate(idrResult.strCarrierID, currCommand.strRealSource, ref dupCarrierRealLoc))
                                                            {
                                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                                    , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead DuplicateID:{idrResult.strCarrierID}");
                                                                resultCode = ResultCode.DuplicateID;
                                                            }
                                                            else
                                                            {
                                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                                    , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead MismatchID:{idrResult.strCarrierID}");
                                                                resultCode = ResultCode.MismatchID;
                                                            }
                                                        }
                                                        break;
                                                    case IDRResultCode.Failed:
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead IDReadFail");
                                                            resultCode = ResultCode.IDReadFail;
                                                            if (++iReadDFailCounter == SysSettings.Instance.CraneIDRReadRules.iMaxReadFailCount)
                                                            {
                                                                iReadDFailCounter = 0;
                                                                if (SysSettings.Instance.CraneIDRReadRules.bShowIDRBreakDown)
                                                                {
                                                                    //报警：IDR Break Down
                                                                    string strCommandID = currCommand == null ? "" : currCommand.strCommandID;
                                                                    HostIF.Instance.PostAlarmEvent(strCommandID, "", strCraneName, "ABORT", 1000, EqpEvent.AlarmSet);
                                                                    AlarmController.Instance.SetAlarm(1000, "IDR", "Crane IDR Break Down");
                                                                    //HostIF.Instance.PostUnitAlarmEvent(strCraneName, 1000, "", EqpEvent.UnitAlarmSet);
                                                                }
                                                            }
                                                        }
                                                        break;
                                                    case IDRResultCode.NoCarrier:
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CarrierIDRead
                                                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead NoCarrier");
                                                            resultCode = ResultCode.OtherError;
                                                            bEmptyRetrieval = true;
                                                            taskStep = 10;
                                                        }
                                                        break;
                                                    case IDRResultCode.None:
                                                    default:
                                                        resultCode = ResultCode.OtherError;
                                                        break;
                                                }
                                                if (resultCode != ResultCode.Success)
                                                {
                                                    //任务结束的处理
                                                    taskStep = 6;
                                                    //读码有问题的情况下才报告读码事件
                                                    HostIF.Instance.PostIDREvent(currCommand.strCarrierID, currCommand.strRealSource, IDReadStatus.Success, EqpEvent.CarrierIDRead);
                                                }
                                                else
                                                {
                                                    //更新盒子位置
                                                    CarrierMng.Instance.CranePickCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierTransferring);
                                                    if (TransferType.Shelf2OutPort == currCommand.transferType)
                                                    {
                                                        HostIF.Instance.PostZoneCapacityChangeEvent(currCommand.strCarrierID, EqpEvent.ZoneCapacityChange);
                                                    }

                                                    taskStep++;
                                                }
                                            }
                                            else
                                            {
                                                //Thread.Sleep(100);
                                                bool bCranePickCarrier = CraneDev.Instance.IsCraneForkHasCarrier(craneTag);
                                                //仿真专用
                                                if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                                {
                                                    bCranePickCarrier = true;
                                                }

                                                if (bCranePickCarrier)
                                                {
                                                    resultCode = ResultCode.Success;
                                                    //更新盒子位置
                                                    CarrierMng.Instance.CranePickCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealSource);
                                                    strCarrierID = currCommand.strCarrierID;
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(),
                                                        $"CranePickCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strRealSource}");
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierTransferring);
                                                    if (TransferType.Shelf2OutPort == currCommand.transferType)
                                                    {
                                                        HostIF.Instance.PostZoneCapacityChangeEvent(currCommand.strCarrierID, EqpEvent.ZoneCapacityChange);
                                                    }

                                                    taskStep++;
                                                }
                                                else //没有取到盒子
                                                {
                                                    //Source Empty
                                                    bEmptyRetrieval = true;
                                                    taskStep = 10;
                                                }
                                            }
                                            bAction2Started = false;
                                            bAction3Started = false;
                                            bAction4Started = false;
                                            bAction9Started = false;
                                        }
                                    }
                                    else
                                    {
                                        taskStep++;
                                    }
                                }
                                break;
                            case 4:
                                {
                                    taskStep++;
                                }
                                break;
                            case 5:
                                {
                                    //Crane的Fork正在缩回，等待Crane任务完成信号
                                    if (!CraneDev.Instance.IsCraneTaskComplete(craneTag)
                                        && !Proj.CacheData.GlobalData.Instance.gbSimulation)
                                    {
                                        return;
                                    }

                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), "Crane Task Complete");
                                    //Crane任务完成，答复完成信号
                                    CraneDev.Instance.CraneTaskCompleteAck(craneTag);

                                    CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                    taskStep++;
                                }
                                break;
                            case 6:
                                {
                                    //Crane任务完成信号复位
                                    if (CraneDev.Instance.IsCraneTaskComplete(craneTag))
                                    {
                                        //重置Crane任务启动信号
                                        CraneDev.Instance.ResetCraneTaskStart(craneTag);
                                        //Crane任务完成，答复完成信号
                                        CraneDev.Instance.CraneTaskCompleteAck(craneTag);//PLC没有将Complete信号置成0，STKC重新Ack
                                        return;
                                    }

                                    //复位答复完成信号
                                    CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);

                                    modeState = CraneModeState.Idle;
                                    HostIF.Instance.PostCraneEvent(strCraneName, EqpEvent.CraneIdle);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.CraneIdle, EqpEvent.CraneIdle.ToString(), "Crane Idle");

                                    CraneMng.Instance.SendCraneActionToUI(strCraneName, "PickComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);

                                    TransferMng.Instance.UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, resultCode);
                                    //LocationMng.Instance.UnReserveLocation(currCommand.strRealSource);
                                    LocationMng.Instance.UnReserveLocation(currCommand.strRealDest);
                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, EqpEvent.TransferCompleted);
                                    if(IsHaveIDR)
                                    {
                                        switch (resultCode)
                                        {
                                            case ResultCode.Success:
                                                {

                                                }
                                                break;
                                            case ResultCode.IDReadFail:
                                                {
                                                    //1.删除原CarrierID的Carrier信息
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                                    //2.添加UnKnownID的Carrier信息
                                                    string strUnKnownID = CarrierMng.Instance.GetIDRFailCarrierID(currCommand.strRealSource);
                                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);
                                                }
                                                break;
                                            case ResultCode.DuplicateID:
                                                {
                                                    //1.删除DuplicateID的盒子信息
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(idrResult.strCarrierID);

                                                    //2.在DuplicateID的位置处添加UnKnownID的Carrier信息
                                                    string strUnKnownID = CarrierMng.Instance.GenDulplicateCarrierID(idrResult.strCarrierID);
                                                    CarrierMng.Instance.AddCarrierInfo(strUnKnownID, dupCarrierRealLoc);
                                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);

                                                    //3.删除指令中CarrierID位置处的Carrier信息
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                                    //4.在指令中的位置处安装读到的盒子信息
                                                    CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                                }
                                                break;
                                            case ResultCode.MismatchID:
                                                {
                                                    //1.删除指令中CarrierID位置处的Carrier信息
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                    CarrierMng.Instance.DeleteCarrierInfo(currCommand.strCarrierID);

                                                    //2.在指令中的位置处安装读到的盒子信息
                                                    CarrierMng.Instance.AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierInstallCompleted);
                                                }
                                                break;
                                            case ResultCode.TypeMismatch:
                                            case ResultCode.ShelfZoneFull:
                                            case ResultCode.OtherError:
                                            default:
                                                break;
                                        }
                                    }
                                   
                                    //结束Crane的当前任务
                                    endCurrentCommand(TransferState.None, resultCode);

                                    //2OutPort时,其余过程放到PortObj中执行
                                    //TransferChange2Port(currCommand.strRealDest, currCommand);
                                }
                                break;
                            case 10:  //EmptyRetrival的处理
                                {
                                    //EmptyRetrival对应的AlarmID
                                    HostIF.Instance.PostAlarm((uint)AlarmCode.SourceEmpty);
                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "SourceEmpty"+ currCommand.strStockerCraneID, currCommand.strStockerCraneID, "RETRY, ABORT", (int)AlarmCode.SourceEmpty, EqpEvent.AlarmSet);
                                    taskStep++;
                                }
                                break;
                            case 11:
                                {
                                    //如果收到了Retry指令,回到Step3重新执行
                                    //if ()
                                    //{
                                    //    CraneDev.Instance.CraneTaskResume();
                                    //    taskStep = 3;
                                    //}
                                }
                                break;
                            case 30: //Abort流程的处理
                                break;
                        }
                    }
                    break;
                case TransferState.Aborting:
                    {
                        AbortSchedule();
                    }
                    break;
                case TransferState.Canceling:
                    break;
                case TransferState.Paused:
                    break;
                case TransferState.None:   //如果为None，说明任务已经结束
                    {
                        //结束Crane的当前任务
                        resultCode = ResultCode.OtherError;
                        endCurrentCommand(TransferState.None, resultCode);
                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)EqpEvent.TransferCompleted, EqpEvent.CraneIdle.ToString(), "endCurrentCommand-TransferState.None");
                    }
                    break;
            }
        }

        public void Start()
        {
            try
            {
                string strRes = "";

                taskExecuteThread = new ThreadBaseModel(10 + iCraneNo, "TaskExecuteThreadFunc "); //Crane的线程ID从10开始
                taskExecuteThread.LoopInterval = 200;
                taskExecuteThread.SetThreadRoutine(taskExecuteThreadFunc);
                taskExecuteThread.TaskInit(ref strRes);
               // if(Proj.CacheData.GlobalData.Instance.gbSimulation == false)
                {
                    taskExecuteThread.Start(ref strRes);
                }

                //alarm线程
                string strResAlarm = "";

                taskExecuteThreadAlarm = new ThreadBaseModel(100 + iCraneNo, "TaskExecuteThreadAlarmFunc "); //Crane的线程ID从10开始
                taskExecuteThreadAlarm.LoopInterval = 1000;
                taskExecuteThreadAlarm.SetThreadRoutine(taskExecuteThreadAlarmFunc);
                taskExecuteThreadAlarm.TaskInit(ref strResAlarm);
                taskExecuteThreadAlarm.Start(ref strResAlarm);
#if false
                string strResCheckStepAction = "";
                taskExecuteCheckStepAction = new ThreadBaseModel(110, "TaskExecuteThreadFuncCheckStepAction ");
                taskExecuteCheckStepAction.LoopInterval = 200;
                taskExecuteCheckStepAction.SetThreadRoutine(TaskExecuteThreadFuncCheckStepAction);
                taskExecuteCheckStepAction.TaskInit(ref strResCheckStepAction);
                taskExecuteCheckStepAction.Start(ref strResCheckStepAction);
#endif
                //更新Crane状态信息
                UpdateCraneXAxisDistance();
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("CraneObj.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }

        public void Stop()
        {
            string strRes = "";
            taskExecuteThread.TaskExit(ref strRes);
        }

        public void AddTask(EnhancedTransferCommand task)
        {
            //if(task.bCraneComplete)
            //{
            //    return;
            //}
            currCommand = task;
            UpdateCraneXAxisDistance();
        }
        
        private void AbortCurrentTask()
        {

        }

        public void Pause()
        {
            CraneDev.Instance.CraneTaskPause(craneTag);
        }

        public void Resume()
        {
            CraneDev.Instance.CraneTaskResume(craneTag);
        }

        public void Abort()
        {
            CraneDev.Instance.CraneTaskAbort(craneTag);
        }

        public void CleanPLCTask()
        {
            CraneDev.Instance.CraneTaskAbort(craneTag);
            Thread.Sleep(200);
            CraneDev.Instance.CraneTaskCompleteAck(craneTag);
            CraneDev.Instance.CraneAbortCompleteAck(craneTag);
            Thread.Sleep(200);
            CraneDev.Instance.ResetCraneTaskCompleteAck(craneTag);
            CraneDev.Instance.ResetCraneAbortCompleteAck(craneTag);
            CraneDev.Instance.ResetCraneTaskStart(craneTag);
            CraneDev.Instance.CleanCraneCommand(craneTag);
            endCurrentCommand();
            modeState = CraneModeState.Idle;
        }

        public bool SetCraneSpeed(int emptySpeed, int storageSpeed, int xSpeed = 100, int ySpeed = 100, int zSpeed = 100, int tSpeed = 100)
        {
            return CraneDev.Instance.SetCraneSpeed(craneTag, emptySpeed, storageSpeed, xSpeed, ySpeed, zSpeed, tSpeed);
        }

        public void setUiIsRetryFlag(uint ui)//0=初始化； 1=Isretry； 2=not retry
        {
            uiIsRetryFlag = ui;
        }
       
        public void WriteMachineStateToDB()
        {
            TntKeyvalueconfig tntKeyvalueconfig = TntKeyvalueconfig.GetByLambda(x => x.Key == "Machine State");
            if (tntKeyvalueconfig != null)
            {
                tntKeyvalueconfig.Vlaue = Proj.CacheData.GlobalData.Instance.gbMachineState.ToString();
                tntKeyvalueconfig.Save();
            }
        }

        public void UpdateCraneXAxisDistance()
        {
            //object objDistance = PlcComm.Instance.GetIOValue("Crane.s2hXAxisUsageCrane1.DriveDistance");
            //if(objDistance != null)
            //{
            //    DB.DbCrane.Instance.UpdateUpdateCraneXAxisDistance(objDistance.ToString());
            //}
        }
    }
}
