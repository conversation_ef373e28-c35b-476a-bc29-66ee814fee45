# Stocker Service 测试脚本
param(
    [string]$BaseUrl = "http://localhost:9900"
)

Write-Host "=== Stocker Service API 测试 ===" -ForegroundColor Green
Write-Host "测试地址: $BaseUrl"
Write-Host ""

# 测试函数
function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Body = $null
    )
    
    Write-Host "测试: $Name" -ForegroundColor Yellow
    Write-Host "URL: $Url"
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            UseBasicParsing = $true
            TimeoutSec = 10
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json)
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        
        Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "响应: $($response.Content.Substring(0, [Math]::Min(200, $response.Content.Length)))"
        if ($response.Content.Length -gt 200) {
            Write-Host "..."
        }
        Write-Host ""
        return $true
    } catch {
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        return $false
    }
}

# 等待服务启动
Write-Host "等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

$testResults = @()

# 测试健康检查
$testResults += Test-Endpoint -Name "健康检查" -Url "$BaseUrl/health"

# 测试连接测试
$testResults += Test-Endpoint -Name "连接测试" -Url "$BaseUrl/api/stocker/test"

# 测试获取所有状态
$testResults += Test-Endpoint -Name "获取所有状态" -Url "$BaseUrl/api/stocker/states"

# 测试发送消息
$messageBody = @{
    Function = "ConnectTest"
    Parameters = @{}
}
$testResults += Test-Endpoint -Name "发送消息" -Url "$BaseUrl/api/stocker/message" -Method "POST" -Body $messageBody

# 测试获取状态
$stateBody = @{
    Name = "HSMS State"
}
$testResults += Test-Endpoint -Name "获取状态" -Url "$BaseUrl/api/stocker/state" -Method "POST" -Body $stateBody

# 测试获取标签值
$tagBody = @{
    TagName = "SystemStatus"
}
$testResults += Test-Endpoint -Name "获取标签值" -Url "$BaseUrl/api/stocker/tag" -Method "POST" -Body $tagBody

# 测试获取客户端列表
$testResults += Test-Endpoint -Name "获取客户端列表" -Url "$BaseUrl/api/stocker/clients"

# 统计结果
$passedTests = ($testResults | Where-Object { $_ -eq $true }).Count
$totalTests = $testResults.Count

Write-Host "=== 测试结果 ===" -ForegroundColor Green
Write-Host "通过: $passedTests/$totalTests" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })

if ($passedTests -eq $totalTests) {
    Write-Host "所有测试通过! ✅" -ForegroundColor Green
} else {
    Write-Host "部分测试失败 ⚠️" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "可以访问以下地址进行进一步测试:"
Write-Host "- Swagger UI: $BaseUrl"
Write-Host "- 健康检查: $BaseUrl/health"
Write-Host "- API文档: $BaseUrl/swagger"
