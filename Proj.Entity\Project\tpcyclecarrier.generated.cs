﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpCyclecarrier and List
    [Serializable]
    [Description("循环测试盒子表")]
    [LinqToDB.Mapping.Table("TP_CYCLECARRIER")]
    public partial class TpCyclecarrier : GEntity<TpCyclecarrier>
    {
        #region Contructor(s)

        private TpCyclecarrier()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CarrierId = RegisterProperty<String>(p => p.CarrierId);
        private static readonly PropertyInfo<Int32?> pty_CycleTimes = RegisterProperty<Int32?>(p => p.CycleTimes);
        private static readonly PropertyInfo<String> pty_PortName = RegisterProperty<String>(p => p.PortName);
        private static readonly PropertyInfo<Int32> pty_UsePort = RegisterProperty<Int32>(p => p.UsePort);
        #endregion

        /// <summary>
        /// Carrier_ID
        /// </summary>
        [Description("CarrierID")]
        [LinqToDB.Mapping.Column("Carrier_ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CarrierId
        {
            get { return GetProperty(pty_CarrierId); }
            set { SetProperty(pty_CarrierId, value); }
        }
        /// <summary>
        /// Cycle_Times
        /// </summary>
        [Description("CycleTimes")]
        [LinqToDB.Mapping.Column("Cycle_Times")]
        public Int32? CycleTimes
        {
            get { return GetProperty(pty_CycleTimes); }
            set { SetProperty(pty_CycleTimes, value); }
        }
        /// <summary>
        /// Port_Name
        /// </summary>
        [Description("PortName")]
        [LinqToDB.Mapping.Column("Port_Name")]
        public String PortName
        {
            get { return GetProperty(pty_PortName); }
            set { SetProperty(pty_PortName, value); }
        }
        /// <summary>
        /// Use_Port
        /// </summary>
        [Description("UsePort")]
        [LinqToDB.Mapping.Column("Use_Port")]
        public Int32 UsePort
        {
            get { return GetProperty(pty_UsePort); }
            set { SetProperty(pty_UsePort, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CarrierId, "Carrier_ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CarrierId, 64, "Carrier_ID不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_PortName, 64, "Port_Name不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_UsePort, "Use_Port是必填项"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CarrierId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TpCyclecarrierList : GEntityList<TpCyclecarrierList, TpCyclecarrier>
    {
        private TpCyclecarrierList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
