﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpCarrier and List
    [Serializable]
    [Description("Carrier数据表")]
    [LinqToDB.Mapping.Table("TP_CARRIER")]
    public partial class TpCarrier : GEntity<TpCarrier>
    {
        #region Contructor(s)

        private TpCarrier()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_Id = RegisterProperty<String>(p => p.Id);
        private static readonly PropertyInfo<String> pty_Location = RegisterProperty<String>(p => p.Location);
        private static readonly PropertyInfo<String> pty_State = RegisterProperty<String>(p => p.State);
        private static readonly PropertyInfo<String> pty_IdReadStatus = RegisterProperty<String>(p => p.IdReadStatus);
        private static readonly PropertyInfo<DateTime?> pty_InstallTime = RegisterProperty<DateTime?>(p => p.InstallTime);
        private static readonly PropertyInfo<String> pty_Comment = RegisterProperty<String>(p => p.Comment);
        #endregion

        /// <summary>
        /// CarrierID
        /// </summary>
        [Description("CarrierID")]
        [LinqToDB.Mapping.Column("Id")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String Id
        {
            get { return GetProperty(pty_Id); }
            set { SetProperty(pty_Id, value); }
        }
        /// <summary>
        /// Location
        /// </summary>
        [Description("Location")]
        [LinqToDB.Mapping.Column("Location")]
        public String Location
        {
            get { return GetProperty(pty_Location); }
            set { SetProperty(pty_Location, value); }
        }
        /// <summary>
        /// State
        /// </summary>
        [Description("State")]
        [LinqToDB.Mapping.Column("State")]
        public String State
        {
            get { return GetProperty(pty_State); }
            set { SetProperty(pty_State, value); }
        }
        /// <summary>
        /// IDReadStatus
        /// </summary>
        [Description("IDReadStatus")]
        [LinqToDB.Mapping.Column("ID_Read_Status")]
        public String IdReadStatus
        {
            get { return GetProperty(pty_IdReadStatus); }
            set { SetProperty(pty_IdReadStatus, value); }
        }
        /// <summary>
        /// InstallTime
        /// </summary>
        [Description("InstallTime")]
        [LinqToDB.Mapping.Column("Install_Time")]
        public DateTime? InstallTime
        {
            get { return GetProperty(pty_InstallTime); }
            set { SetProperty(pty_InstallTime, value); }
        }
        /// <summary>
        /// Comment
        /// </summary>
        [Description("Comment")]
        [LinqToDB.Mapping.Column("Comment")]
        public String Comment
        {
            get { return GetProperty(pty_Comment); }
            set { SetProperty(pty_Comment, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Id, "CarrierID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Id, 64, "CarrierID不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Location, 5, "Location不能超过5个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_State, 1, "State不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_IdReadStatus, 1, "IDReadStatus不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Comment, 255, "Comment不能超过255个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Id; }
        #endregion
    }       
        
    [Serializable]
    public partial class TpCarrierList : GEntityList<TpCarrierList, TpCarrier>
    {
        private TpCarrierList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
