﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region ThOperation and List
    [Serializable]
    [Description("操作历史")]
    [LinqToDB.Mapping.Table("TH_OPERATION")]
    public partial class ThOperation : GEntity<ThOperation>
    {
        #region Contructor(s)

        private ThOperation()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_Client = RegisterProperty<String>(p => p.Client);
        private static readonly PropertyInfo<String> pty_FunctionData = RegisterProperty<String>(p => p.FunctionData);
        private static readonly PropertyInfo<String> pty_FunctionName = RegisterProperty<String>(p => p.FunctionName);
        private static readonly PropertyInfo<Int64> pty_Id = RegisterProperty<Int64>(p => p.Id);
        private static readonly PropertyInfo<DateTime?> pty_Time = RegisterProperty<DateTime?>(p => p.Time);
        private static readonly PropertyInfo<String> pty_UiName = RegisterProperty<String>(p => p.UiName);
        private static readonly PropertyInfo<String> pty_User = RegisterProperty<String>(p => p.User);
        #endregion

        /// <summary>
        /// ClientName
        /// </summary>
        [Description("ClientName")]
        [LinqToDB.Mapping.Column("Client")]
        public String Client
        {
            get { return GetProperty(pty_Client); }
            set { SetProperty(pty_Client, value); }
        }
        /// <summary>
        /// FunctionData
        /// </summary>
        [Description("FunctionData")]
        [LinqToDB.Mapping.Column("Function_Data")]
        public String FunctionData
        {
            get { return GetProperty(pty_FunctionData); }
            set { SetProperty(pty_FunctionData, value); }
        }
        /// <summary>
        /// FunctionName
        /// </summary>
        [Description("FunctionName")]
        [LinqToDB.Mapping.Column("Function_Name")]
        public String FunctionName
        {
            get { return GetProperty(pty_FunctionName); }
            set { SetProperty(pty_FunctionName, value); }
        }
        /// <summary>
        /// PrimaryKey
        /// </summary>
        [Description("PrimaryKey")]
        [LinqToDB.Mapping.Column("ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int64 Id
        {
            get { return GetProperty(pty_Id); }
            set { SetProperty(pty_Id, value); }
        }
        /// <summary>
        /// Time
        /// </summary>
        [Description("Time")]
        [LinqToDB.Mapping.Column("Time")]
        public DateTime? Time
        {
            get { return GetProperty(pty_Time); }
            set { SetProperty(pty_Time, value); }
        }
        /// <summary>
        /// UIName
        /// </summary>
        [Description("UIName")]
        [LinqToDB.Mapping.Column("UI_Name")]
        public String UiName
        {
            get { return GetProperty(pty_UiName); }
            set { SetProperty(pty_UiName, value); }
        }
        /// <summary>
        /// User
        /// </summary>
        [Description("User")]
        [LinqToDB.Mapping.Column("User")]
        public String User
        {
            get { return GetProperty(pty_User); }
            set { SetProperty(pty_User, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Client, 32, "ClientName不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_FunctionData, 255, "FunctionData不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_FunctionName, 64, "FunctionName不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Id, "PrimaryKey是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_UiName, 64, "UIName不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_User, 32, "User不能超过32个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Id.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class ThOperationList : GEntityList<ThOperationList, ThOperation>
    {
        private ThOperationList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
