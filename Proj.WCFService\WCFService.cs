﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.ServiceModel;
using Proj.WCF;
using System.ServiceModel.Channels;
using Proj.CacheData;

namespace Proj.WCF
{
    public delegate bool WCFClientSendMessage(string strFunction, Dictionary<string, object> dicParameter);
    public delegate object WCFClientGetTagValue(string strTagName);

    /// <summary>
    ///  WCF服务的功能实现类，进程中仅允许有一个实例，支持多线程访问(一定要加锁)
    /// </summary>
    [ServiceBehavior(InstanceContextMode = InstanceContextMode.Single, ConcurrencyMode = ConcurrencyMode.Multiple)]
    public class WCFService : IWCFService
    {
        private static WCFService m_Instanse;
        private static readonly object mSyncObject = new object();
        public  List<ICallBackServices> ListClient = new List<ICallBackServices>();
        public event WCFClientSendMessage eventWCFClientSendMessage;
        public event WCFClientGetTagValue eventWCFClientGetTagValue;

        public WCFService()
        {
            m_Instanse = this;
        }

        public static WCFService Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new WCFService();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        /// <summary>
        /// 客户端注册
        /// </summary>
        public void Register()
        {
            lock (mSyncObject)
            {
                ICallBackServices client = OperationContext.Current.GetCallbackChannel<ICallBackServices>();
                string sessionid = OperationContext.Current.SessionId;//获取当前机器Sessionid--------------------------如果多个客户端在同一台机器，就使用此信息。
                string ClientHostName = OperationContext.Current.Channel.RemoteAddress.Uri.Host;//获取当前机器名称-----多个客户端不在同一台机器上，就使用此信息。
                string RemoteAddress = OperationContext.Current.Channel.RemoteAddress.ToString();
                string LocalAddress = OperationContext.Current.Channel.LocalAddress.ToString();
                ListClient.Add(client);
                OperationContext.Current.Channel.Closed += new EventHandler(Channel_Closed);
            }
        }

        /// <summary>
        /// 客户端发送消息
        /// </summary>
        /// <param name="Parameter"></param>
        public object ClientSendMessage(string strFunction, Dictionary<string, object> dicParameter)
        {
            ICallBackServices client = OperationContext.Current.GetCallbackChannel<ICallBackServices>();
            RemoteEndpointMessageProperty clientEndpoint = OperationContext.Current.IncomingMessageProperties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;

            string strLog = "Receive From " + clientEndpoint.Address + ": " + strFunction;
            foreach (string strKey in dicParameter.Keys)
            {
                strLog = strLog + ", " + strKey + ": " + dicParameter[strKey];
            }
            Log.Logger.Instance.WcfLog(strLog);

            if (strFunction == "ConnectTest")
            {
                return true;
            }
            else if (strFunction == "GetState")
            {
                string strStateName = dicParameter["NAME"].ToString();
                string strStateValue = "";
                switch (strStateName)
                {
                    case "HSMS State":
                        strStateValue = Proj.CacheData.GlobalData.Instance.gbHSMSState.ToString();
                        break;
                    case "MCS Communication":
                        strStateValue = Proj.CacheData.GlobalData.Instance.gbMCSCommunication.ToString();
                        break;
                    case "Control State":
                        strStateValue = Proj.CacheData.GlobalData.Instance.gbControlState.ToString();
                        break;
                    case "SC State":
                        strStateValue = Proj.CacheData.GlobalData.Instance.gbSCState.ToString();
                        break;
                    case "SC Mode":
                        strStateValue = Proj.CacheData.GlobalData.Instance.gbSCMode.ToString();
                        break;
                    case "Machine State":
                        strStateValue = Proj.CacheData.GlobalData.Instance.gbMachineState.ToString();
                        break;
                }
                return strStateValue;
            }
            else if (strFunction == "GetAllState")
            {
                string strStates = "";
                strStates += "HSMS State" + "," + GlobalData.Instance.gbHSMSState.ToString() + ";";
                strStates += "MCS Communication" + "," + GlobalData.Instance.gbMCSCommunication.ToString() + ";";
                strStates += "Control State" + "," + GlobalData.Instance.gbControlState.ToString() + ";";
                strStates += "SC State" + "," + GlobalData.Instance.gbSCState.ToString() + ";";
                strStates += "SC Mode" + "," + GlobalData.Instance.gbSCMode.ToString() + ";";
                strStates += "Machine State" + "," + GlobalData.Instance.gbMachineState.ToString();
                return strStates;
            }
            else if(strFunction == "GetTagValue")
            {
                string strTagName = dicParameter["TAGNAME"].ToString();
                if (eventWCFClientGetTagValue != null)
                {
                    return eventWCFClientGetTagValue(strTagName);
                }
            }
            else
            {
                if (eventWCFClientSendMessage != null)
                {
                    return eventWCFClientSendMessage(strFunction, dicParameter);
                }
            }

            return false;
        }

        /// <summary>
        /// 客户端关闭事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Channel_Closed(object sender, EventArgs e)
        {
            lock (mSyncObject)
            {
                ICallBackServices client = sender as ICallBackServices;
                ListClient.Remove(client);
            }
        }

        /// <summary>
        /// 服务器向可户端发送消息
        /// </summary>
        /// <param name="Parameter"></param>
        public bool ServerSendMessage(string strFunction, Dictionary<string, object> dicParameter)
        {
            string strLog = "Send: " + strFunction;
            foreach (string strKey in dicParameter.Keys)
            {
                strLog = strLog + ", " + strKey + ": " + dicParameter[strKey];
            }
            Log.Logger.Instance.WcfLog(strLog);

            try
            {
                lock (mSyncObject)
                {
                    foreach (ICallBackServices client in ListClient)
                    {
                        client.ServerSendMessage(strFunction, dicParameter);
                    }
                }
                return true;
            }
            catch(Exception ex)
            {
                string strEx = "Server Send Message Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Log.Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }
    }
}
