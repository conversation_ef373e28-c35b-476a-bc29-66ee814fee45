using Proj.Service.Hubs;
using Proj.Service.Interfaces;
using Proj.Service.Services;
using Proj.Log;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddSignalR();

// Add API documentation
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Stocker Service API", Version = "v1" });
    c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Proj.Service.xml"), true);
});

// Register custom services
builder.Services.AddSingleton<IStockerService, StockerService>();
builder.Services.AddSingleton<IClientManagerService, ClientManagerService>();

// Configure CORS for SignalR
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Initialize WCF compatibility service
var clientManager = app.Services.GetRequiredService<IClientManagerService>();
WCFCompatibilityService.Instance.Initialize(clientManager);

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Stocker Service API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

app.UseRouting();
app.MapControllers();
app.MapHub<StockerHub>("/stockerhub");

// Add a simple health check endpoint
app.MapGet("/health", () => new { Status = "Healthy", Timestamp = DateTime.Now });

// Log startup information
var logger = Logger.Instance;
logger.Info("Stocker Service (.NET 8.0) starting...");

app.Run();
