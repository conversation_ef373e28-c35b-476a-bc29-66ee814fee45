using Proj.Service.Hubs;
using Proj.Service.Interfaces;
using Proj.Service.Services;
using Proj.Log;

Console.WriteLine("=== Starting Stocker Service (.NET 8.0) ===");

try
{
    var builder = WebApplication.CreateBuilder(args);
    Console.WriteLine("?7?7 WebApplication builder created");

    // Add services to the container
    builder.Services.AddControllers();
    Console.WriteLine("?7?7 Controllers added");

    builder.Services.AddSignalR();
    Console.WriteLine("?7?7 SignalR added");

    // Add API documentation
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new() { Title = "Stocker Service API", Version = "v1" });
        // Skip XML comments for now to avoid potential issues
        // c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Proj.Service.xml"), true);
    });
    Console.WriteLine("?7?7 Swagger added");

    // Register custom services
    Console.WriteLine("Registering custom services...");
    builder.Services.AddScoped<IStockerService, StockerService>();
    Console.WriteLine("?7?7 StockerService registered");

    builder.Services.AddSingleton<IClientManagerService, ClientManagerService>();
    Console.WriteLine("?7?7 ClientManagerService registered");

    // Configure CORS for SignalR
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowAll", policy =>
        {
            policy.AllowAnyOrigin()
                  .AllowAnyMethod()
                  .AllowAnyHeader();
        });
    });
    Console.WriteLine("?7?7 CORS configured");

    // Configure Kestrel to listen on specific port
    builder.WebHost.UseUrls("http://localhost:9900");
    Console.WriteLine("?7?7 URL configured: http://localhost:9900");

    Console.WriteLine("Building application...");
    var app = builder.Build();
    Console.WriteLine("?7?7 Application built successfully");

    try
    {
        // Initialize WCF compatibility service
        Console.WriteLine("Initializing WCF Compatibility Service...");
        var clientManager = app.Services.GetRequiredService<IClientManagerService>();
        WCFCompatibilityService.Instance.Initialize(clientManager);
        Console.WriteLine("?7?7 WCF Compatibility Service initialized successfully");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"?7?4 Error initializing WCF Compatibility Service: {ex.Message}");
        Console.WriteLine($"Stack trace: {ex.StackTrace}");
        throw;
    }

    // Configure the HTTP request pipeline
    Console.WriteLine("Configuring HTTP request pipeline...");

    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v1/swagger.json", "Stocker Service API v1");
            c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
        });
        Console.WriteLine("?7?7 Swagger UI configured");
    }

    // Skip HTTPS redirection for local development
    // app.UseHttpsRedirection();
    Console.WriteLine("? HTTPS redirection skipped for local development");

    app.UseCors("AllowAll");
    Console.WriteLine("?7?7 CORS configured");

    app.UseRouting();
    Console.WriteLine("?7?7 Routing configured");

    app.MapControllers();
    Console.WriteLine("?7?7 Controllers mapped");

    app.MapHub<StockerHub>("/stockerhub");
    Console.WriteLine("?7?7 SignalR hub mapped to /stockerhub");

    // Add a simple health check endpoint
    app.MapGet("/health", () => new { Status = "Healthy", Timestamp = DateTime.Now });
    Console.WriteLine("?7?7 Health check endpoint mapped to /health");

    // Log startup information
    try
    {
        var logger = Logger.Instance;
        logger.Info("Stocker Service (.NET 8.0) starting...");
        Console.WriteLine("?7?7 Logger initialized");
        Console.WriteLine("?0?4 Service will be available at: http://localhost:9900");
        Console.WriteLine("?9?6 Swagger UI available at: http://localhost:9900");
        Console.WriteLine("?7?8?1?5  Health check available at: http://localhost:9900/health");
        Console.WriteLine("?9?2 SignalR hub available at: http://localhost:9900/stockerhub");
        Console.WriteLine("");
        Console.WriteLine("Starting application...");

        app.Run();
    }
    catch (Exception ex)
    {
        Console.WriteLine($"?7?4 Fatal error starting service: {ex.Message}");
        Console.WriteLine($"Stack trace: {ex.StackTrace}");
        throw;
    }
}
catch (Exception ex)
{
    Console.WriteLine($"?7?4 Fatal error during initialization: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    throw;
}
