﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region ThEqp and List
    [Serializable]
    [Description("设备事件历史")]
    [LinqToDB.Mapping.Table("TH_EQP")]
    public partial class ThEqp : GEntity<ThEqp>
    {
        #region Contructor(s)

        private ThEqp()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<Int32?> pty_EventId = RegisterProperty<Int32?>(p => p.EventId);
        private static readonly PropertyInfo<String> pty_EventName = RegisterProperty<String>(p => p.EventName);
        private static readonly PropertyInfo<Int64> pty_Id = RegisterProperty<Int64>(p => p.Id);
        private static readonly PropertyInfo<String> pty_Text = RegisterProperty<String>(p => p.Text);
        private static readonly PropertyInfo<DateTime?> pty_Time = RegisterProperty<DateTime?>(p => p.Time);
        private static readonly PropertyInfo<String> pty_Unit = RegisterProperty<String>(p => p.Unit);
        #endregion

        /// <summary>
        /// EventID
        /// </summary>
        [Description("EventID")]
        [LinqToDB.Mapping.Column("Event_ID")]
        public Int32? EventId
        {
            get { return GetProperty(pty_EventId); }
            set { SetProperty(pty_EventId, value); }
        }
        /// <summary>
        /// EventName
        /// </summary>
        [Description("EventName")]
        [LinqToDB.Mapping.Column("Event_Name")]
        public String EventName
        {
            get { return GetProperty(pty_EventName); }
            set { SetProperty(pty_EventName, value); }
        }
        /// <summary>
        /// PrimaryKey
        /// </summary>
        [Description("PrimaryKey")]
        [LinqToDB.Mapping.Column("ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int64 Id
        {
            get { return GetProperty(pty_Id); }
            set { SetProperty(pty_Id, value); }
        }
        /// <summary>
        /// Text
        /// </summary>
        [Description("Text")]
        [LinqToDB.Mapping.Column("Text")]
        public String Text
        {
            get { return GetProperty(pty_Text); }
            set { SetProperty(pty_Text, value); }
        }
        /// <summary>
        /// Time
        /// </summary>
        [Description("Time")]
        [LinqToDB.Mapping.Column("Time")]
        public DateTime? Time
        {
            get { return GetProperty(pty_Time); }
            set { SetProperty(pty_Time, value); }
        }
        /// <summary>
        /// DevUnit
        /// </summary>
        [Description("DevUnit")]
        [LinqToDB.Mapping.Column("Unit")]
        public String Unit
        {
            get { return GetProperty(pty_Unit); }
            set { SetProperty(pty_Unit, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_EventName, 64, "EventName不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Id, "PrimaryKey是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Text, 255, "Text不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Unit, 64, "DevUnit不能超过32个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Id.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class ThEqpList : GEntityList<ThEqpList, ThEqp>
    {
        private ThEqpList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
