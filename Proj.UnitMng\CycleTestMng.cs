﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Proj.Entity;
using Proj.DB;
using Proj.CacheData;
using Proj.DataTypeDef;
namespace Proj.UnitMng
{
    public class CycleTestMng
    {
        private static CycleTestMng m_Instanse;
        private static readonly object mSyncObject = new object();
        private CycleTestMng() { }
        public static CycleTestMng Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new CycleTestMng();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        private bool bTestStart = false;  //循环测试是否开始
        private int iCurrCycleTimes = 0;

        public bool Initialize()
        {
            bool bRes = false;
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    SetCycleTestInfo();
                    bRes = true;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("CarrierMng.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return bRes;
        }

        public bool SaveCycleTestInfo(CycleTestInfo cycleInfo)
        {
            if (DbTrans.Instance.SaveCycleTestInfo(cycleInfo))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    GlobalData.Instance.gbCycleTestInfo = cycleInfo;
                }
                return true;
            }
            return false;
        }

        public void SetCycleTestInfo()
        {
            try
            {
            CycleTestInfo cycInfo = new CycleTestInfo();
            TpCyclecarrier cycleCarrier = TpCyclecarrierList.GetAll().First();
            if (cycleCarrier != null)
            {
                cycInfo.strCarrierID = cycleCarrier.CarrierId;
                cycInfo.iCycleTimes = int.Parse(cycleCarrier.CycleTimes.ToString());
                cycInfo.bUsePort = cycleCarrier.UsePort == 1 ? true : false;
                cycInfo.strPortName = cycleCarrier.PortName;
                cycInfo.Locations = new List<string>();
                TpCyclelocationList cyclelocationList = TpCyclelocationList.GetAll();
                foreach (TpCyclelocation cyclelocation in cyclelocationList)
                {
                    cycInfo.Locations.Add(cyclelocation.Location);
                }
                GlobalData.Instance.gbCycleTestInfo = cycInfo;
                }
            }
            catch (Exception ex)
            {
                Proj.Log.Logger.Instance.ExceptionLog("CycleTestMng.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }

        public CycleTestInfo GetCycleTestInfo()
        {
            lock (GlobalData.Instance.objRWLock)
            {
                return GlobalData.Instance.gbCycleTestInfo;
            }
        }

        public bool StartCyleTest()
        {
            if(CycleTestMng.Instance.Initialize() == false)
            {
                return false;
            }

            currentIndex = 0;
            startIndex = 0;
            currentSourceLoc = "";
            currentDestLoc = "";
            isFirstCommand = true;
            iCurrCycleTimes = 0;
            bTestStart = true;
            return true;
        }

        public void StopCycleTest()
        {
            bTestStart = false;
        }

        public bool IsCycleStart()
        {
            return bTestStart;
        }

        public int GetRemainCycleTimes()
        {
            lock (GlobalData.Instance.objRWLock)
            {
                return GlobalData.Instance.gbCycleTestInfo.iCycleTimes - iCurrCycleTimes;
            }
        }

        
        private int currentIndex = 0;//当前需要执行指令索引
        private int startIndex = 0;//第一次开始执行指令索引
        private string currentSourceLoc = "";//当前指令原位置
        private string currentDestLoc = "";//当前指令目标位置
        //LocationType currentLocType;
        private bool currentIsPort;//执行当前指令时，盒子是否在Port上
        private bool isFirstCommand;//是否第一条指令标识
        public EnhancedTransferCommand GenNextCycleCommand()
        {
            EnhancedTransferCommand enhancedTransferCommand = new EnhancedTransferCommand();
            CycleTestInfo cInfo = GlobalData.Instance.gbCycleTestInfo;

            LocationType sourceLocType;
            LocationType destLocType;
            TransferType transType;
            //盒子信息 测试时注释
            //EnhancedCarrierInfo carrierInfo = CarrierMng.Instance.GetEnhancedCarrierInfo(cInfo.strCarrierID);

            //looprun的第一条指令
            if (isFirstCommand)
            {
                //找到盒子的所在位置
                currentSourceLoc = CarrierMng.Instance.GetCarrierLocation(cInfo.strCarrierID);
                //测试时注释
                //LocationType currentLocType = LocationMng.Instance.GetLocationType(carrierInfo.strCarrierLoc);
                //测试数据，假定carrer再shelf上
                LocationType currentLocType = LocationType.Shelf;

                //carrier在shelf上
                if (currentLocType == LocationType.Shelf)
                {
                    //找到满足条件的第一个起始位置，目标地址不能和carrier当前位置重合
                    foreach (string loc in cInfo.Locations)
                    {
                        if (loc == currentSourceLoc)
                        {
                            startIndex++;
                        }
                        else
                        {
                            break;
                        }
                    }
                    currentIndex = startIndex;
                    currentIsPort = false;
                }
                //carrier在port上
                else
                {
                    currentIsPort = true;
                }

                isFirstCommand = false;
            }
            else
            {
                //源地址等于上一次的目标地址
                currentSourceLoc = currentDestLoc;
            }


            //是否使用port进行looprun
            if (cInfo.bUsePort)
            {
                //当前盒子在port上
                if (currentIsPort)
                {
                    //目标地址为shelf，从looprun集合中获取
                    currentDestLoc = cInfo.Locations[currentIndex];
                    currentIndex++;

                    sourceLocType = LocationType.IoPort;
                    destLocType = LocationType.Shelf;
                    transType = TransferType.InPort2Shelf;
                }
                else
                {
                    //目标地址为port
                    currentDestLoc = cInfo.strPortName;

                    sourceLocType = LocationType.Shelf;
                    destLocType = LocationType.IoPort;
                    transType = TransferType.Shelf2OutPort;
                }
                //port和 shelf类型转换
                currentIsPort = !currentIsPort;
            }
            else
            {
                currentDestLoc = cInfo.Locations[currentIndex];
                currentIndex++;

                sourceLocType = LocationType.Shelf;
                destLocType = LocationType.Shelf;
                transType = TransferType.Shelf2Shelf;
            }

            //遍历到集合末尾后，重新开始
            if (currentIndex == cInfo.Locations.Count)
            {
                //当前循环圈数
                iCurrCycleTimes++;
                //目标地址索引
                currentIndex = 0;

            }

            //looprun执行完成预定的圈数
            if (GetRemainCycleTimes() == 0)
            {
                StopCycleTest();
            }
            
            //生成指令
            enhancedTransferCommand.strCommandName = "TRANSFER";
            enhancedTransferCommand.strCommandID = TransferMng.Instance.GenScanCommandID();
            enhancedTransferCommand.u2Priority = 99;
            enhancedTransferCommand.strCarrierID = cInfo.strCarrierID;
            enhancedTransferCommand.strCarrierLoc = currentSourceLoc;
            enhancedTransferCommand.strDest = currentDestLoc;
            enhancedTransferCommand.strStockerCraneID = GlobalData.Instance.gbStockerCraneID;
            enhancedTransferCommand.transferState = TransferState.Queue;

            enhancedTransferCommand.strRealSource = currentSourceLoc;
            enhancedTransferCommand.strRealDest = currentDestLoc;
            enhancedTransferCommand.strSourceZone = LocationMng.Instance.GetLocationZone(currentSourceLoc);
            enhancedTransferCommand.strDestZone = LocationMng.Instance.GetLocationZone(currentDestLoc);

            enhancedTransferCommand.sourceLocType = sourceLocType;
            enhancedTransferCommand.destLocType = destLocType;
            enhancedTransferCommand.transferType = transType;

            return enhancedTransferCommand;
        }
    }
}
