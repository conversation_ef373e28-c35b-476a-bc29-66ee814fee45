﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Proj.Entity;
using Proj.DataTypeDef;
using Proj.CacheData;
using Proj.WCF;

namespace Proj.DB
{
    public class DbTrans
    {
        private static DbTrans m_Instanse;
        private static readonly object mSyncObject = new object();
        private DbTrans() { }
        public static DbTrans Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new DbTrans();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public bool Transaction()
        {
            try
            {
                using (var trans = new Csla.Transaction.TransactionScope())
                {
                    //多表操作

                    //事物提交
                    trans.Complete();
                    return true;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTrans.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool AddCarrier(string strCarrierID, string strCarrierLoc)
        {
            try
            {
                TpCarrier findCarrier = TpCarrier.GetByLambda(x => x.Id == strCarrierID);
                if(findCarrier != null)//Carrier 已存在
                {
                    return false;
                }
                //Location表更新位置占用信息
                TpLocation tpCarrierLocation = TpLocation.GetByLambda(x => x.Address == strCarrierLoc);
                if (tpCarrierLocation.CarrierId != "")
                {
                    DeleteCarrier(tpCarrierLocation.CarrierId);
                }

                EnhancedCarrierInfo eCarrierInfo = new EnhancedCarrierInfo();
                //using (var trans = new Csla.Transaction.TransactionScope())
                {
                    eCarrierInfo.strCarrierID = strCarrierID;
                    eCarrierInfo.strCarrierLoc = strCarrierLoc;

                    //Location表更新位置占用信息
                    TpLocation tpLocation = TpLocation.GetByLambda(x => x.Address == strCarrierLoc);
                    tpLocation.CarrierId = strCarrierID;
                    tpLocation.IsOccupied = 1;
                    eCarrierInfo.strCarrierZoneName = tpLocation.ZoneName;
                    eCarrierInfo.strLocationName = tpLocation.Name;
                    eCarrierInfo.locType = (LocationType)Convert.ToInt32(tpLocation.Type);
                    tpLocation.Save();

                    //Carrier表添加盒子数据
                    TpCarrier tpCarrier = TpCarrier.New();
                    tpCarrier.Id = strCarrierID;
                    tpCarrier.Location = tpLocation.Address;
                    tpCarrier.State = ((int)(eCarrierInfo.carrierState = CarrierState.Installed)).ToString(); 
                    if(strCarrierID.Contains("UNKNOWN"))
                    {
                        tpCarrier.IdReadStatus = ((int)IDReadStatus.Failure).ToString();
                    }
                    else
                    {
                        tpCarrier.IdReadStatus = ((int)IDReadStatus.Success).ToString();
                    }
                    eCarrierInfo.strInstallTime = (tpCarrier.InstallTime = DateTime.Now)?.ToString("yyyyMMddHHmmssff");
                    tpCarrier.Comment = "";
                    tpCarrier.Save();

                    //Zone表更新容量信息
                    TpZone tpZone = TpZone.GetById(eCarrierInfo.strCarrierZoneName);
                    if (tpZone != null)
                    {
                        if((ZoneType)uint.Parse(tpZone.Type) == ZoneType.Shelf)
                        {
                            if(1 >= tpZone.Capacity)
                            {
                                tpZone.Capacity = 0;
                            }
                            else
                            {
                                tpZone.Capacity -= 1;
                            }
                        }
                        else
                        {
                            tpZone.Capacity = 0;
                        }
                    }
                    tpZone.Save(); 
                    
                    //事物提交
                    //trans.Complete();
                }

                lock (GlobalData.Instance.objRWLock)
                {
                    GlobalData.Instance.gbEnhancedCarriers.Add(eCarrierInfo);

                    foreach (LocationInfo locationInfo in GlobalData.Instance.gbLocations)
                    {
                        if (locationInfo.strAddress == strCarrierLoc)
                        {
                            locationInfo.strCarrierID = strCarrierID;
                            locationInfo.IsOccupied = true;
                            break;
                        }
                    }
                }

                //通知界面更新
                //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                //dicParams.Add("ADDRESS", strCarrierLoc);
                //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTrans.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool UpdateCarrier(string strCarrierID, string strCarrierLoc)
        {
            try
            {
                string locOld = "";
                string locNew = "";
                string zoneOld = "";
                string zoneNew = "";
                string locName = "";
                string locType = "";
                //using (var trans = new Csla.Transaction.TransactionScope())
                {
                    //Carrier表更新位置信息
                    TpCarrier tpCarrier = TpCarrier.GetById(strCarrierID);
                    locOld = tpCarrier.Location;
                    locNew = strCarrierLoc;
                    if (locOld == locNew)
                    {
                        //盒子的位置信息没有改变，直接返回
                        return true;
                    }
                    TpLocation tpLocOld = TpLocation.GetById(tpCarrier.Location);
                    TpLocation tpLocNew = TpLocation.GetById(strCarrierLoc);
                    zoneOld = tpLocOld.ZoneName;
                    zoneNew = tpLocNew.ZoneName;
                    locName = tpLocNew.Name;
                    locType = tpLocNew.Type;

                    tpCarrier.Location = strCarrierLoc;
                    tpCarrier.Save();

                    //位置变更新2个位置的占用信息
                    tpLocOld.CarrierId = "";
                    tpLocOld.IsOccupied = 0;
                    tpLocOld.Save();
                    tpLocNew.CarrierId = strCarrierID;
                    tpLocNew.IsOccupied = 1;
                    tpLocNew.Save();

                    //Zone表更新容量信息
                    if (zoneOld != zoneNew)
                    {
                        TpZone tpZoneOld = TpZone.GetById(zoneOld);
                        if ((ZoneType)uint.Parse(tpZoneOld.Type) == ZoneType.Shelf)
                        {
                            tpZoneOld.Capacity += 1;
                        }
                        else
                        {
                            tpZoneOld.Capacity = 1;
                        }
                        tpZoneOld.Save();

                        TpZone tpZoneNew = TpZone.GetById(zoneNew);
                        if ((ZoneType)uint.Parse(tpZoneNew.Type) == ZoneType.Shelf)
                        {
                            if (1 >= tpZoneNew.Capacity)
                            {
                                tpZoneNew.Capacity = 0;
                            }
                            else
                            {
                                tpZoneNew.Capacity -= 1;
                            }
                        }
                        else
                        {
                            tpZoneNew.Capacity = 0;
                        }
                        tpZoneNew.Save();
                    }

                    //事物提交
                    //trans.Complete();
                }

                lock(GlobalData.Instance.objRWLock)
                {
                    int iCount = GlobalData.Instance.gbEnhancedCarriers.Count;
                    for (int i = 0; i < iCount; i++)
                    {
                        if (GlobalData.Instance.gbEnhancedCarriers[i].strCarrierID == strCarrierID)
                        {
                            EnhancedCarrierInfo eCarrier = GlobalData.Instance.gbEnhancedCarriers[i];
                            eCarrier.strCarrierLoc = locNew;
                            eCarrier.strLocationName = locName;
                            eCarrier.strCarrierZoneName = zoneNew;
                            eCarrier.locType = (LocationType)Convert.ToInt32(locType);
                            //GlobalData.Instance.gbEnhancedCarriers[i].strCarrierLoc = locNew;
                            break;
                        }
                    }

                    foreach (LocationInfo locationInfo in GlobalData.Instance.gbLocations)
                    {
                        if (locationInfo.strAddress == locOld)
                        {
                            locationInfo.strCarrierID = "";
                            locationInfo.IsOccupied = false;
                        }
                        else if (locationInfo.strAddress == locNew)
                        {
                            locationInfo.strCarrierID = strCarrierID;
                            locationInfo.IsOccupied = true;
                        }
                    }
                }

                //通知界面更新
                //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                //dicParams.Add("ADDRESS", strCarrierLoc);
                //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTrans.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool DeleteCarrier(string strCarrierID)
        {
            try
            {
                string strCarrierLoc = "";
                //using (var trans = new Csla.Transaction.TransactionScope())
                {
                    TpLocation tpLocation = TpLocation.GetByLambda(x => x.CarrierId == strCarrierID);
                    TpCarrier tpCar = TpCarrier.GetByLambda(x => x.Id == strCarrierID);
                    if (tpCar != null)
                    {
                        tpLocation = TpLocation.GetByLambda(x => x.Address == tpCar.Location);
                    }
                                        
                    //Carrier表删除盒子信息
                    TpCarrierList.DeleteByCriteria(x => x.Id == strCarrierID);

                    //Location表更新位置占用信息
                   // TpLocation tpLocation = TpLocation.GetByLambda(x => x.CarrierId == strCarrierID);
                    if (tpLocation != null)
                    {
                        strCarrierLoc = tpLocation.Address;
                        tpLocation.CarrierId = "";
                        tpLocation.IsOccupied = 0;
                        tpLocation.Save();

                        //Zone表更新容量信息
                        TpZone tpZone = TpZone.GetById(tpLocation.ZoneName);
                        if ((ZoneType)uint.Parse(tpZone.Type) == ZoneType.Shelf)
                        {
                            tpZone.Capacity += 1;
                        }
                        else
                        {
                            tpZone.Capacity = 1;
                        }
                        tpZone.Save();                 
                    }
                    //事物提交
                    //trans.Complete();
                }

                lock (GlobalData.Instance.objRWLock)
                {
                    int iCount = GlobalData.Instance.gbEnhancedCarriers.Count;
                    int iIndex = 0;
                    for (iIndex = 0; iIndex < iCount; iIndex++)
                    {
                        if (GlobalData.Instance.gbEnhancedCarriers[iIndex].strCarrierID == strCarrierID)
                        {
                            GlobalData.Instance.gbEnhancedCarriers.RemoveAt(iIndex);
                            break;
                        }
                    }      

                    foreach (LocationInfo locationInfo in GlobalData.Instance.gbLocations)
                    {
                        if (locationInfo.strAddress == strCarrierLoc)
                        {
                            locationInfo.strCarrierID = "";
                            locationInfo.IsOccupied = false;
                            break;
                        }
                    }
                }

                //通知界面更新
                //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                //dicParams.Add("ADDRESS", strCarrierLoc);
                //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTrans.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        
        public bool AddTransferCommand(string cmdSource, ref EnhancedTransferCommand command)
        {
            try
            {
                //using (var trans = new Csla.Transaction.TransactionScope())
                {
                    //添加指令
                    TpTransfer tpTransfer = TpTransfer.New();
                    tpTransfer.CmdSource = cmdSource;
                    tpTransfer.Id = command.strCommandID;
                    tpTransfer.CarrierId = command.strCarrierID;
                    tpTransfer.SourceLocation = command.strRealSource;
                    tpTransfer.DestLocation = command.strRealDest;
                    tpTransfer.Priority = (int)command.u2Priority;
                    tpTransfer.CalcPriority = (int)command.u2Priority;
                    tpTransfer.CraneName = command.strStockerCraneID;
                    tpTransfer.CommandType = command.strCommandName;
                    command.createTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                    tpTransfer.CreateTime = DateTime.Now;
                    tpTransfer.State = ((uint)command.transferState).ToString();
                    tpTransfer.TransferType = ((uint)command.transferType).ToString();
                    tpTransfer.Save();

                    //事物提交
                    //trans.Complete();

                    ////通知界面更新
                    //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                    //dicParams.Add("ADDRESS", command.strRealDest);
                    //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                    return true;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTrans.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        
        public bool UpdateTransferCrane(string strCommandID, string strCraneID)
        {
            try
            {
                //using (var trans = new Csla.Transaction.TransactionScope())
                {
                    TpTransfer tpTransfer = TpTransfer.GetById(strCommandID);
                    tpTransfer.CraneName = strCraneID;
                    tpTransfer.Save();

                    TpCrane tpCrane = TpCrane.GetById(TpCraneList.GetByLambda(x => x.Name == strCraneID).First().No);
                    tpCrane.CommandId = strCommandID;
                    tpCrane.Save();                    

                    //事物提交
                    //trans.Complete();
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTrans.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        
        public bool CranePickCarrier(string craneID, string carrierID, string sourceLocation)
        {
            try
            {
                //using (var trans = new Csla.Transaction.TransactionScope())
                {
                    TpLocation tpLocation = TpLocation.GetById(sourceLocation);
                    tpLocation.CarrierId = "";
                    tpLocation.IsOccupied = 0;
                    tpLocation.Save();

                    TpZone tpZone = TpZone.GetById(tpLocation.ZoneName);
                    if (tpZone != null)
                    {
                        if ((ZoneType)uint.Parse(tpZone.Type) == ZoneType.Shelf)
                        {
                            tpZone.Capacity += 1;
                        }
                        else
                        {
                            tpZone.Capacity = 1;
                        }
                    }
                    tpZone.Save();

                    TpLocation tpLocation1 = TpLocation.GetByLambda(x => x.Name == craneID);
                    tpLocation1.CarrierId = carrierID;
                    tpLocation1.IsOccupied = 1;
                    tpLocation1.Save();

                    TpCarrier tpCarrier = TpCarrier.GetById(carrierID);
                    tpCarrier.Location = tpLocation1.Address;
                    tpCarrier.Save();

                    //事物提交
                    //trans.Complete();

                    //通知界面更新
                    //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                    //dicParams.Add("ADDRESS", sourceLocation);
                    //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                    return true;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTrans.cs: " + craneID + "  " + carrierID + "  " + sourceLocation 
                    + ",  " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool CranePlaceCarrier(string craneID, string carrierID, string destLocation)
        {
            try
            {
                //using (var trans = new Csla.Transaction.TransactionScope())
                {
                    TpLocation tpLocation = TpLocation.GetById(destLocation);
                    tpLocation.CarrierId = carrierID;
                    tpLocation.IsOccupied = 1;
                    tpLocation.Save();

                    TpZone tpZone = TpZone.GetById(tpLocation.ZoneName);
                    if (tpZone != null)
                    {
                        if ((ZoneType)uint.Parse(tpZone.Type) == ZoneType.Shelf)
                        {
                            if (1 >= tpZone.Capacity)
                            {
                                tpZone.Capacity = 0;
                            }
                            else
                            {
                                tpZone.Capacity -= 1;
                            }
                        }
                        else
                        {
                            tpZone.Capacity = 0;
                        }
                    }
                    tpZone.Save();

                    TpLocation tpLocation1 = TpLocation.GetByLambda(x => x.Name == craneID);
                    tpLocation1.CarrierId = "";
                    tpLocation1.IsOccupied = 0;
                    tpLocation1.Save();

                    TpCarrier tpCarrier = TpCarrier.GetById(carrierID);
                    tpCarrier.Location = tpLocation.Address;
                    tpCarrier.Save();

                    //事物提交
                    //trans.Complete();

                    //通知界面更新
                    //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                    //dicParams.Add("ADDRESS", destLocation);
                    //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                    return true;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTrans.cs: " + craneID + "  " + carrierID + "  " + destLocation
                    + ",  " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }


        public bool SaveCycleTestInfo(CycleTestInfo cycleInfo)
        {
            try
            {
                //using (var trans = new Csla.Transaction.TransactionScope())
                {
                    //多表操作

                    //TpCyclecarrier tpCycleCarrier = TpCyclecarrier..GetById(cycleInfo.strCarrierID);
                    //if (tpCycleCarrier == null)
                    //{
                    //tpCycleCarrier = TpCyclecarrier.New();
                    //tpCycleCarrier.CarrierId = cycleInfo.strCarrierID;
                    //}
                    //清除数据
                    TpCyclecarrierList.GetBySQL("delete from tp_cyclecarrier");
                    //looprun数据只有一条
                    TpCyclecarrier tpCycleCarrier = TpCyclecarrier.New();
                    tpCycleCarrier.CarrierId = cycleInfo.strCarrierID;
                    tpCycleCarrier.CycleTimes = cycleInfo.iCycleTimes;
                    tpCycleCarrier.PortName = cycleInfo.strPortName;
                    tpCycleCarrier.UsePort = cycleInfo.bUsePort ? 1 : 0;//1:使用prot 0:不使用port
                    tpCycleCarrier.Save();

                    //清除数据
                    TpCyclelocationList.GetBySQL("Delete from tp_cyclelocation");
                    int nIndex = 0;
                    foreach (string strLocation in cycleInfo.Locations)
                    {
                        nIndex++;
                        TpCyclelocation tpCycleLocation = TpCyclelocation.New();
                        tpCycleLocation.Location = strLocation;
                        tpCycleLocation.No = nIndex;
                        tpCycleLocation.Save();
                    }

                    //事物提交
                    //trans.Complete();
                    return true;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTrans.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public List<string> GetCycleLocationList()
        {
            List<string> LoopList = new List<string>();
            foreach (TpCyclelocation cycLoc in TpCyclelocationList.GetAll().ToList())
            {
                LoopList.Add(cycLoc.Location);
            }

            return LoopList;
        }

    }
}
