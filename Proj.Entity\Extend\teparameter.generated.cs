﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TeParameter and List
    [Serializable]
    [Description("设备参数")]
    [LinqToDB.Mapping.Table("TE_PARAMETER")]
    public partial class TeParameter : GEntity<TeParameter>
    {
        #region Contructor(s)

        private TeParameter()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CParameterId = RegisterProperty<String>(p => p.CParameterId);
        private static readonly PropertyInfo<String> pty_CParameterName = RegisterProperty<String>(p => p.CParameterName);
        private static readonly PropertyInfo<String> pty_CParametervalue = RegisterProperty<String>(p => p.CParametervalue);
        private static readonly PropertyInfo<String> pty_CParameterunit = RegisterProperty<String>(p => p.CParameterunit);
        private static readonly PropertyInfo<String> pty_CParameterup = RegisterProperty<String>(p => p.CParameterup);
        private static readonly PropertyInfo<String> pty_CParameterlow = RegisterProperty<String>(p => p.CParameterlow);
        private static readonly PropertyInfo<String> pty_CParameterCode = RegisterProperty<String>(p => p.CParameterCode);
        private static readonly PropertyInfo<String> pty_CDeviceId = RegisterProperty<String>(p => p.CDeviceId);
        private static readonly PropertyInfo<String> pty_CSpc = RegisterProperty<String>(p => p.CSpc);
        private static readonly PropertyInfo<String> pty_CMsa = RegisterProperty<String>(p => p.CMsa);
        private static readonly PropertyInfo<String> pty_COee = RegisterProperty<String>(p => p.COee);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion

        /// <summary>
        /// 编号
        /// </summary>
        [Description("编号")]
        [LinqToDB.Mapping.Column("C_PARAMETERID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CParameterId
        {
            get { return GetProperty(pty_CParameterId); }
            set { SetProperty(pty_CParameterId, value); }
        }
        /// <summary>
        /// 参数名称
        /// </summary>
        [Description("参数名称")]
        [LinqToDB.Mapping.Column("C_PARAMETERNAME")]
        public String CParameterName
        {
            get { return GetProperty(pty_CParameterName); }
            set { SetProperty(pty_CParameterName, value); }
        }
        /// <summary>
        /// 参数值
        /// </summary>
        [Description("参数值")]
        [LinqToDB.Mapping.Column("C_PARAMETERVALUE")]
        public String CParametervalue
        {
            get { return GetProperty(pty_CParametervalue); }
            set { SetProperty(pty_CParametervalue, value); }
        }
        /// <summary>
        /// 参数单位
        /// </summary>
        [Description("参数单位")]
        [LinqToDB.Mapping.Column("C_PARAMETERUNIT")]
        public String CParameterunit
        {
            get { return GetProperty(pty_CParameterunit); }
            set { SetProperty(pty_CParameterunit, value); }
        }
        /// <summary>
        /// 参数上限
        /// </summary>
        [Description("参数上限")]
        [LinqToDB.Mapping.Column("C_PARAMETERUP")]
        public String CParameterup
        {
            get { return GetProperty(pty_CParameterup); }
            set { SetProperty(pty_CParameterup, value); }
        }
        /// <summary>
        /// 参数下限
        /// </summary>
        [Description("参数下限")]
        [LinqToDB.Mapping.Column("C_PARAMETERLOW")]
        public String CParameterlow
        {
            get { return GetProperty(pty_CParameterlow); }
            set { SetProperty(pty_CParameterlow, value); }
        }
        /// <summary>
        /// 参数编码
        /// </summary>
        [Description("参数编码")]
        [LinqToDB.Mapping.Column("C_PARAMETERCODE")]
        public String CParameterCode
        {
            get { return GetProperty(pty_CParameterCode); }
            set { SetProperty(pty_CParameterCode, value); }
        }
        /// <summary>
        /// 设备主键
        /// </summary>
        [Description("设备主键")]
        [LinqToDB.Mapping.Column("C_DEVICEID")]
        public String CDeviceId
        {
            get { return GetProperty(pty_CDeviceId); }
            set { SetProperty(pty_CDeviceId, value); }
        }
        /// <summary>
        /// 是否SPC参数
        /// </summary>
        [Description("是否SPC参数")]
        [LinqToDB.Mapping.Column("C_SPC")]
        public String CSpc
        {
            get { return GetProperty(pty_CSpc); }
            set { SetProperty(pty_CSpc, value); }
        }
        /// <summary>
        /// 是否MSA参数
        /// </summary>
        [Description("是否MSA参数")]
        [LinqToDB.Mapping.Column("C_MSA")]
        public String CMsa
        {
            get { return GetProperty(pty_CMsa); }
            set { SetProperty(pty_CMsa, value); }
        }
        /// <summary>
        /// 是否OEE参数
        /// </summary>
        [Description("是否OEE参数")]
        [LinqToDB.Mapping.Column("C_OEE")]
        public String COee
        {
            get { return GetProperty(pty_COee); }
            set { SetProperty(pty_COee, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime? CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("C_SW01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("C_SW02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("C_SW03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CParameterId, "编号是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterId, 36, "编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterName, 80, "参数名称不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParametervalue, 40, "参数值不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterunit, 40, "参数单位不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterup, 40, "参数上限不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterlow, 40, "参数下限不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterCode, 30, "参数编码不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeviceId, 30, "设备主键不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSpc, 1, "是否SPC参数不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMsa, 1, "是否MSA参数不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_COee, 1, "是否OEE参数不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CParameterId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TeParameterList : GEntityList<TeParameterList, TeParameter>
    {
        private TeParameterList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
