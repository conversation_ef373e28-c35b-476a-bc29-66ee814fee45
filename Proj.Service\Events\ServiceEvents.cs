namespace Proj.Service.Events
{
    /// <summary>
    /// 客户端发送消息事件委托
    /// </summary>
    /// <param name="function">功能名称</param>
    /// <param name="parameters">参数字典</param>
    /// <returns>处理结果</returns>
    public delegate object? ClientSendMessageHandler(string function, Dictionary<string, object> parameters);

    /// <summary>
    /// 客户端获取标签值事件委托
    /// </summary>
    /// <param name="tagName">标签名称</param>
    /// <returns>标签值</returns>
    public delegate object? ClientGetTagValueHandler(string tagName);

    /// <summary>
    /// 服务事件管理器
    /// </summary>
    public class ServiceEventManager
    {
        private static ServiceEventManager? _instance;
        private static readonly object _lock = new();

        /// <summary>
        /// 单例实例
        /// </summary>
        public static ServiceEventManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new ServiceEventManager();
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 客户端发送消息事件
        /// </summary>
        public event ClientSendMessageHandler? ClientSendMessage;

        /// <summary>
        /// 客户端获取标签值事件
        /// </summary>
        public event ClientGetTagValueHandler? ClientGetTagValue;

        /// <summary>
        /// 触发客户端发送消息事件
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>处理结果</returns>
        public object? OnClientSendMessage(string function, Dictionary<string, object> parameters)
        {
            return ClientSendMessage?.Invoke(function, parameters);
        }

        /// <summary>
        /// 触发客户端获取标签值事件
        /// </summary>
        /// <param name="tagName">标签名称</param>
        /// <returns>标签值</returns>
        public object? OnClientGetTagValue(string tagName)
        {
            return ClientGetTagValue?.Invoke(tagName);
        }
    }
}
