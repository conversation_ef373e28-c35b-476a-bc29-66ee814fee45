﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntNotifyconfig and List
    [Serializable]
    [Description("通知配置表")]
    [LinqToDB.Mapping.Table("Tnt_NotifyConfig")]
    public partial class TntNotifyconfig : GEntity<TntNotifyconfig>, ITimestamp
    {
        #region Contructor(s)

        private TntNotifyconfig()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CNotifyType = RegisterProperty<String>(p => p.CNotifyType);
        private static readonly PropertyInfo<String> pty_CNotifyobject = RegisterProperty<String>(p => p.CNotifyobject);
        private static readonly PropertyInfo<String> pty_CNotifyparam = RegisterProperty<String>(p => p.CNotifyparam);
        private static readonly PropertyInfo<String> pty_CDescription = RegisterProperty<String>(p => p.CDescription);
        private static readonly PropertyInfo<String> pty_CSendcountId = RegisterProperty<String>(p => p.CSendcountId);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        private static readonly PropertyInfo<String> pty_CReceivecounts = RegisterProperty<String>(p => p.CReceivecounts);
        private static readonly PropertyInfo<String> pty_CConfig1 = RegisterProperty<String>(p => p.CConfig1);
        private static readonly PropertyInfo<String> pty_CConfig2 = RegisterProperty<String>(p => p.CConfig2);
        private static readonly PropertyInfo<String> pty_CContent = RegisterProperty<String>(p => p.CContent);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 通知类型
        /// </summary>
        [Description("通知类型")]
        [LinqToDB.Mapping.Column("c_NotifyType")]
        public String CNotifyType
        {
            get { return GetProperty(pty_CNotifyType); }
            set { SetProperty(pty_CNotifyType, value); }
        }
        /// <summary>
        /// 通知类别
        /// </summary>
        [Description("通知类别")]
        [LinqToDB.Mapping.Column("c_NotifyObject")]
        public String CNotifyobject
        {
            get { return GetProperty(pty_CNotifyobject); }
            set { SetProperty(pty_CNotifyobject, value); }
        }
        /// <summary>
        /// 通知参数
        /// </summary>
        [Description("通知参数")]
        [LinqToDB.Mapping.Column("c_NotifyParam")]
        public String CNotifyparam
        {
            get { return GetProperty(pty_CNotifyparam); }
            set { SetProperty(pty_CNotifyparam, value); }
        }
        /// <summary>
        /// 描述
        /// </summary>
        [Description("描述")]
        [LinqToDB.Mapping.Column("c_Description")]
        public String CDescription
        {
            get { return GetProperty(pty_CDescription); }
            set { SetProperty(pty_CDescription, value); }
        }
        /// <summary>
        /// 通知信息ID
        /// </summary>
        [Description("通知信息ID")]
        [LinqToDB.Mapping.Column("c_SendCountId")]
        public String CSendcountId
        {
            get { return GetProperty(pty_CSendcountId); }
            set { SetProperty(pty_CSendcountId, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
        /// <summary>
        /// c_ReceiveCounts
        /// </summary>
        [Description("c_ReceiveCounts")]
        [LinqToDB.Mapping.Column("c_ReceiveCounts")]
        public String CReceivecounts
        {
            get { return GetProperty(pty_CReceivecounts); }
            set { SetProperty(pty_CReceivecounts, value); }
        }
        /// <summary>
        /// c_Config1
        /// </summary>
        [Description("c_Config1")]
        [LinqToDB.Mapping.Column("c_Config1")]
        public String CConfig1
        {
            get { return GetProperty(pty_CConfig1); }
            set { SetProperty(pty_CConfig1, value); }
        }
        /// <summary>
        /// c_Config2
        /// </summary>
        [Description("c_Config2")]
        [LinqToDB.Mapping.Column("c_Config2")]
        public String CConfig2
        {
            get { return GetProperty(pty_CConfig2); }
            set { SetProperty(pty_CConfig2, value); }
        }
        /// <summary>
        /// c_Content
        /// </summary>
        [Description("c_Content")]
        [LinqToDB.Mapping.Column("c_Content")]
        public String CContent
        {
            get { return GetProperty(pty_CContent); }
            set { SetProperty(pty_CContent, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 36, "主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CNotifyType, 2, "通知类型不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CNotifyobject, 2, "通知类别不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CNotifyparam, 300, "通知参数不能超过300个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDescription, 400, "描述不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSendcountId, 50, "通知信息ID不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CReceivecounts, 50, "c_ReceiveCounts不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CConfig1, 20, "c_Config1不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CConfig2, 20, "c_Config2不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CContent, 200, "c_Content不能超过200个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntNotifyconfigList : GEntityList<TntNotifyconfigList, TntNotifyconfig>
    {
        private TntNotifyconfigList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
