using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Proj.Log;

namespace Proj.DevComm
{
    /// <summary>
    /// Tag API 适配器 - 内存变量管理
    /// </summary>
    public static class TagAPI
    {
        private static readonly ConcurrentDictionary<string, TagValue> _tags = new();
        private static readonly ConcurrentDictionary<string, List<TagValue>> _historyData = new();
        private static bool _isRecording = false;
        private static int _historyDays = 30;
        private static Timer _historyTimer;
        private static readonly object _lockObj = new object();

        public static void Initialize()
        {
            Logger.Instance.EventLog("TagAPI.Initialize: 标签API初始化完成");
        }

        public static void Shutdown()
        {
            lock (_lockObj)
            {
                _historyTimer?.Dispose();
                _isRecording = false;
                Logger.Instance.EventLog("TagAPI.Shutdown: 标签API已关闭");
            }
        }

        /// <summary>
        /// 获取标签值
        /// </summary>
        /// <param name="tagName">标签名</param>
        /// <returns>标签值</returns>
        public static object GetValue(string tagName)
        {
            try
            {
                if (_tags.TryGetValue(tagName, out var tagValue))
                {
                    return tagValue.Value;
                }

                // 如果标签不存在，返回默认值
                Logger.Instance.ExceptionLog($"TagAPI.GetValue: 标签不存在 - {tagName}");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"TagAPI.GetValue: 获取标签值失败 - {tagName}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 设置标签值
        /// </summary>
        /// <param name="tagName">标签名</param>
        /// <param name="value">标签值</param>
        /// <returns>是否成功</returns>
        public static bool SetValue(string tagName, object value)
        {
            try
            {
                var tagValue = new TagValue
                {
                    Name = tagName,
                    Value = value,
                    Timestamp = DateTime.Now,
                    Quality = TagQuality.Good
                };

                _tags.AddOrUpdate(tagName, tagValue, (key, oldValue) => tagValue);

                // 如果正在记录历史数据，添加到历史记录
                if (_isRecording)
                {
                    RecordHistoryValue(tagName, tagValue);
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"TagAPI.SetValue: 设置标签值失败 - {tagName}, 错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 批量获取标签值
        /// </summary>
        /// <param name="tagNames">标签名列表</param>
        /// <returns>标签值字典</returns>
        public static Dictionary<string, object> GetValues(IEnumerable<string> tagNames)
        {
            var result = new Dictionary<string, object>();
            
            foreach (var tagName in tagNames)
            {
                result[tagName] = GetValue(tagName);
            }

            return result;
        }

        /// <summary>
        /// 批量设置标签值
        /// </summary>
        /// <param name="tagValues">标签值字典</param>
        /// <returns>是否全部成功</returns>
        public static bool SetValues(Dictionary<string, object> tagValues)
        {
            bool allSuccess = true;

            foreach (var kvp in tagValues)
            {
                if (!SetValue(kvp.Key, kvp.Value))
                {
                    allSuccess = false;
                }
            }

            return allSuccess;
        }

        /// <summary>
        /// 设置历史数据保存天数
        /// </summary>
        /// <param name="days">保存天数</param>
        public static void SetHistoryLength(int days)
        {
            _historyDays = days;
            Logger.Instance.EventLog($"TagAPI.SetHistoryLength: 历史数据保存天数设置为 {days} 天");
        }

        /// <summary>
        /// 开始记录历史数据
        /// </summary>
        /// <returns>是否成功</returns>
        public static bool StartRecord()
        {
            lock (_lockObj)
            {
                try
                {
                    if (_isRecording)
                        return true;

                    _isRecording = true;
                    
                    // 启动定时器，每分钟清理一次过期的历史数据
                    _historyTimer = new Timer(CleanupHistoryData, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
                    
                    Logger.Instance.EventLog("TagAPI.StartRecord: 历史数据记录已启动");
                    return true;
                }
                catch (Exception ex)
                {
                    Logger.Instance.ExceptionLog($"TagAPI.StartRecord: 启动历史数据记录失败 - {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// 停止记录历史数据
        /// </summary>
        public static void StopRecord()
        {
            lock (_lockObj)
            {
                try
                {
                    if (!_isRecording)
                        return;

                    _isRecording = false;
                    _historyTimer?.Dispose();
                    _historyTimer = null;
                    
                    Logger.Instance.EventLog("TagAPI.StopRecord: 历史数据记录已停止");
                }
                catch (Exception ex)
                {
                    Logger.Instance.ExceptionLog($"TagAPI.StopRecord: 停止历史数据记录失败 - {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取标签的历史数据
        /// </summary>
        /// <param name="tagName">标签名</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>历史数据列表</returns>
        public static List<TagValue> GetHistoryData(string tagName, DateTime startTime, DateTime endTime)
        {
            try
            {
                if (!_historyData.TryGetValue(tagName, out var historyList))
                {
                    return new List<TagValue>();
                }

                var result = new List<TagValue>();
                foreach (var tagValue in historyList)
                {
                    if (tagValue.Timestamp >= startTime && tagValue.Timestamp <= endTime)
                    {
                        result.Add(tagValue);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"TagAPI.GetHistoryData: 获取历史数据失败 - {tagName}, 错误: {ex.Message}");
                return new List<TagValue>();
            }
        }

        /// <summary>
        /// 获取所有标签名
        /// </summary>
        /// <returns>标签名列表</returns>
        public static List<string> GetAllTagNames()
        {
            return new List<string>(_tags.Keys);
        }

        /// <summary>
        /// 检查标签是否存在
        /// </summary>
        /// <param name="tagName">标签名</param>
        /// <returns>是否存在</returns>
        public static bool TagExists(string tagName)
        {
            return _tags.ContainsKey(tagName);
        }

        /// <summary>
        /// 删除标签
        /// </summary>
        /// <param name="tagName">标签名</param>
        /// <returns>是否成功</returns>
        public static bool RemoveTag(string tagName)
        {
            try
            {
                bool removed = _tags.TryRemove(tagName, out _);
                if (removed)
                {
                    _historyData.TryRemove(tagName, out _);
                    Logger.Instance.EventLog($"TagAPI.RemoveTag: 标签已删除 - {tagName}");
                }
                return removed;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"TagAPI.RemoveTag: 删除标签失败 - {tagName}, 错误: {ex.Message}");
                return false;
            }
        }

        private static void RecordHistoryValue(string tagName, TagValue tagValue)
        {
            try
            {
                _historyData.AddOrUpdate(tagName, 
                    new List<TagValue> { tagValue },
                    (key, existingList) =>
                    {
                        existingList.Add(tagValue);
                        return existingList;
                    });
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"TagAPI.RecordHistoryValue: 记录历史数据失败 - {tagName}, 错误: {ex.Message}");
            }
        }

        private static void CleanupHistoryData(object state)
        {
            try
            {
                var cutoffTime = DateTime.Now.AddDays(-_historyDays);
                
                foreach (var kvp in _historyData)
                {
                    var tagName = kvp.Key;
                    var historyList = kvp.Value;
                    
                    // 移除过期的历史数据
                    historyList.RemoveAll(tv => tv.Timestamp < cutoffTime);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"TagAPI.CleanupHistoryData: 清理历史数据失败 - {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 标签值
    /// </summary>
    public class TagValue
    {
        public string Name { get; set; }
        public object Value { get; set; }
        public DateTime Timestamp { get; set; }
        public TagQuality Quality { get; set; }
    }

    /// <summary>
    /// 标签质量枚举
    /// </summary>
    public enum TagQuality
    {
        Good,
        Bad,
        Uncertain
    }
}
