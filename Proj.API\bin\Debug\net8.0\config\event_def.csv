EventId,EventName,Enabled,Description
1,"EquipmentOffline",true,"Equipment goes offline"
2,"ControlStateLocal",true,"Control state changed to local"
3,"ControlStateRemote",true,"Control state changed to remote"
4,"AlarmSet",true,"Alarm condition detected"
5,"AlarmCleared",true,"Alarm condition cleared"
6,"PPChanged",true,"Process program changed"
7,"VariableChanged",true,"Variable value changed"
100,"CarrierArrived",true,"Carrier arrived at port"
101,"CarrierDeparted",true,"Carrier departed from port"
102,"CarrierInstalled",true,"Carrier installed in stocker"
103,"CarrierRemoved",true,"Carrier removed from stocker"
104,"TransferStarted",true,"Transfer operation started"
105,"TransferCompleted",true,"Transfer operation completed"
106,"TransferAborted",true,"Transfer operation aborted"
200,"CraneStarted",true,"Crane operation started"
201,"CraneCompleted",true,"Crane operation completed"
202,"CraneError",true,"Crane operation error"
203,"CraneIdle",true,"<PERSON> became idle"
204,"CraneBusy",true,"<PERSON> became busy"
300,"PortOpened",true,"Port opened"
301,"PortClosed",true,"Port closed"
302,"PortError",true,"Port error detected"
303,"PortMaintenance",true,"Port in maintenance mode"
400,"LocationOccupied",true,"Storage location occupied"
401,"LocationEmpty",true,"Storage location emptied"
402,"LocationBlocked",true,"Storage location blocked"
403,"LocationUnblocked",true,"Storage location unblocked"
500,"SystemStarted",true,"System startup completed"
501,"SystemStopped",true,"System shutdown initiated"
502,"SystemPaused",true,"System paused"
503,"SystemResumed",true,"System resumed"
504,"SystemError",true,"System error detected"
505,"SystemRecovered",true,"System recovered from error"
600,"MaintenanceStarted",true,"Maintenance mode started"
601,"MaintenanceCompleted",true,"Maintenance mode completed"
602,"CalibrationStarted",true,"Calibration started"
603,"CalibrationCompleted",true,"Calibration completed"
700,"UserLogin",true,"User logged in"
701,"UserLogout",true,"User logged out"
702,"UserAccessDenied",true,"User access denied"
800,"ConfigurationChanged",true,"Configuration changed"
801,"ParameterChanged",true,"Parameter value changed"
802,"RecipeLoaded",true,"Recipe loaded"
803,"RecipeChanged",true,"Recipe changed"
900,"CommunicationEstablished",true,"Communication established"
901,"CommunicationLost",true,"Communication lost"
902,"MessageReceived",true,"Message received"
903,"MessageSent",true,"Message sent"
904,"MessageError",true,"Message error"
1000,"CustomEvent1",false,"Custom event 1"
1001,"CustomEvent2",false,"Custom event 2"
1002,"CustomEvent3",false,"Custom event 3"
