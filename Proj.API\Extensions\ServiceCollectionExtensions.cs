using Microsoft.Extensions.DependencyInjection;
using Proj.API.Services;
using Secs4Net;

namespace Proj.API.Extensions
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加 SECS/GEM 服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSecsGemServices(this IServiceCollection services)
        {
            // 注册配置服务
            services.AddSingleton<ISecsGemConfigService, SecsGemConfigService>();
            
            // 注册连接工厂
            services.AddSingleton<ISecsConnectionFactory, SecsConnectionFactory>();
            
            // 注册日志记录器
            services.AddSingleton<ISecsGemLogger, SecsLogger>();
            
            // 注册 GEM 设备实现
            services.AddTransient<IGemEquipment, GemEquipmentImpl>();
            
            return services;
        }

        /// <summary>
        /// 添加 SECS/GEM 服务 (自定义日志记录器)
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="loggerImplementation">自定义日志记录器实现</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSecsGemServices<TLogger>(this IServiceCollection services)
            where TLogger : class, ISecsGemLogger
        {
            // 注册配置服务
            services.AddSingleton<ISecsGemConfigService, SecsGemConfigService>();
            
            // 注册连接工厂
            services.AddSingleton<ISecsConnectionFactory, SecsConnectionFactory>();
            
            // 注册自定义日志记录器
            services.AddSingleton<ISecsGemLogger, TLogger>();
            
            // 注册 GEM 设备实现
            services.AddTransient<IGemEquipment, GemEquipmentImpl>();
            
            return services;
        }

        /// <summary>
        /// 添加 SECS/GEM 服务 (完全自定义)
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configureServices">服务配置委托</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSecsGemServices(
            this IServiceCollection services,
            Action<IServiceCollection> configureServices)
        {
            // 执行自定义配置
            configureServices(services);
            
            // 确保 GEM 设备实现已注册
            var hasGemEquipment = false;
            foreach (var service in services)
            {
                if (service.ServiceType == typeof(IGemEquipment))
                {
                    hasGemEquipment = true;
                    break;
                }
            }

            if (!hasGemEquipment)
            {
                services.AddTransient<IGemEquipment, GemEquipmentImpl>();
            }
            
            return services;
        }
    }
}
