﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region ThHost and List
    [Serializable]
    [Description("与HOST通讯历史")]
    [LinqToDB.Mapping.Table("TH_HOST")]
    public partial class ThHost : GEntity<ThHost>
    {
        #region Contructor(s)

        private ThHost()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_Direction = RegisterProperty<String>(p => p.Direction);
        private static readonly PropertyInfo<Int64> pty_Id = RegisterProperty<Int64>(p => p.Id);
        private static readonly PropertyInfo<String> pty_MsgName = RegisterProperty<String>(p => p.MsgName);
        private static readonly PropertyInfo<String> pty_MsgType = RegisterProperty<String>(p => p.MsgType);
        private static readonly PropertyInfo<String> pty_StreamFunction = RegisterProperty<String>(p => p.StreamFunction);
        private static readonly PropertyInfo<String> pty_Text = RegisterProperty<String>(p => p.Text);
        private static readonly PropertyInfo<DateTime?> pty_Time = RegisterProperty<DateTime?>(p => p.Time);
        #endregion

        /// <summary>
        /// Direction
        /// </summary>
        [Description("Direction")]
        [LinqToDB.Mapping.Column("Direction")]
        public String Direction
        {
            get { return GetProperty(pty_Direction); }
            set { SetProperty(pty_Direction, value); }
        }
        /// <summary>
        /// PrimaryKey
        /// </summary>
        [Description("PrimaryKey")]
        [LinqToDB.Mapping.Column("ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int64 Id
        {
            get { return GetProperty(pty_Id); }
            set { SetProperty(pty_Id, value); }
        }
        /// <summary>
        /// MsgName
        /// </summary>
        [Description("MsgName")]
        [LinqToDB.Mapping.Column("Msg_Name")]
        public String MsgName
        {
            get { return GetProperty(pty_MsgName); }
            set { SetProperty(pty_MsgName, value); }
        }
        /// <summary>
        /// MsgType
        /// </summary>
        [Description("MsgType")]
        [LinqToDB.Mapping.Column("Msg_Type")]
        public String MsgType
        {
            get { return GetProperty(pty_MsgType); }
            set { SetProperty(pty_MsgType, value); }
        }
        /// <summary>
        /// StreamFunction
        /// </summary>
        [Description("StreamFunction")]
        [LinqToDB.Mapping.Column("Stream_Function")]
        public String StreamFunction
        {
            get { return GetProperty(pty_StreamFunction); }
            set { SetProperty(pty_StreamFunction, value); }
        }
        /// <summary>
        /// Text
        /// </summary>
        [Description("Text")]
        [LinqToDB.Mapping.Column("Text")]
        public String Text
        {
            get { return GetProperty(pty_Text); }
            set { SetProperty(pty_Text, value); }
        }
        /// <summary>
        /// Time
        /// </summary>
        [Description("Time")]
        [LinqToDB.Mapping.Column("Time")]
        public DateTime? Time
        {
            get { return GetProperty(pty_Time); }
            set { SetProperty(pty_Time, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Direction, 32, "Direction不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Id, "PrimaryKey是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_MsgName, 64, "MsgName不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_MsgType, 32, "MsgType不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_StreamFunction, 8, "StreamFunction不能超过8个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Text, 4000, "Text不能超过4000个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Id.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class ThHostList : GEntityList<ThHostList, ThHost>
    {
        private ThHostList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
