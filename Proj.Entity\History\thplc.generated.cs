﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region ThPlc and List
    [Serializable]
    [Description("PLC读写历史")]
    [LinqToDB.Mapping.Table("TH_PLC")]
    public partial class ThPlc : GEntity<ThPlc>
    {
        #region Contructor(s)

        private ThPlc()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<Int64> pty_Id = RegisterProperty<Int64>(p => p.Id);
        private static readonly PropertyInfo<String> pty_IoName = RegisterProperty<String>(p => p.IoName);
        private static readonly PropertyInfo<String> pty_IoValue = RegisterProperty<String>(p => p.IoValue);
        private static readonly PropertyInfo<DateTime?> pty_Time = RegisterProperty<DateTime?>(p => p.Time);
        private static readonly PropertyInfo<String> pty_Type = RegisterProperty<String>(p => p.Type);
        private static readonly PropertyInfo<String> pty_Unit = RegisterProperty<String>(p => p.Unit);
        #endregion

        /// <summary>
        /// PrimaryKey
        /// </summary>
        [Description("PrimaryKey")]
        [LinqToDB.Mapping.Column("ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int64 Id
        {
            get { return GetProperty(pty_Id); }
            set { SetProperty(pty_Id, value); }
        }
        /// <summary>
        /// IOName
        /// </summary>
        [Description("IOName")]
        [LinqToDB.Mapping.Column("IO_Name")]
        public String IoName
        {
            get { return GetProperty(pty_IoName); }
            set { SetProperty(pty_IoName, value); }
        }
        /// <summary>
        /// IOValue
        /// </summary>
        [Description("IOValue")]
        [LinqToDB.Mapping.Column("IO_Value")]
        public String IoValue
        {
            get { return GetProperty(pty_IoValue); }
            set { SetProperty(pty_IoValue, value); }
        }
        /// <summary>
        /// Time
        /// </summary>
        [Description("Time")]
        [LinqToDB.Mapping.Column("Time")]
        public DateTime? Time
        {
            get { return GetProperty(pty_Time); }
            set { SetProperty(pty_Time, value); }
        }
        /// <summary>
        /// Type
        /// </summary>
        [Description("Type")]
        [LinqToDB.Mapping.Column("Type")]
        public String Type
        {
            get { return GetProperty(pty_Type); }
            set { SetProperty(pty_Type, value); }
        }
        /// <summary>
        /// DevUnit
        /// </summary>
        [Description("DevUnit")]
        [LinqToDB.Mapping.Column("Unit")]
        public String Unit
        {
            get { return GetProperty(pty_Unit); }
            set { SetProperty(pty_Unit, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Id, "PrimaryKey是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_IoName, 64, "IOName不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_IoValue, 64, "IOValue不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Type, 32, "Type不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Unit, 32, "DevUnit不能超过32个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Id.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class ThPlcList : GEntityList<ThPlcList, ThPlc>
    {
        private ThPlcList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
