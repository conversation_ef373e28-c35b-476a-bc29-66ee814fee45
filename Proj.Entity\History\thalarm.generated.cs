﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region ThAlarm and List
    [Serializable]
    [Description("报警历史")]
    [LinqToDB.Mapping.Table("TH_ALARM")]
    public partial class ThAlarm : GEntity<ThAlarm>
    {
        #region Contructor(s)

        private ThAlarm()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<DateTime?> pty_ClearTime = RegisterProperty<DateTime?>(p => p.ClearTime);
        private static readonly PropertyInfo<Int32?> pty_Code = RegisterProperty<Int32?>(p => p.Code);
        private static readonly PropertyInfo<String> pty_Comment = RegisterProperty<String>(p => p.Comment);
        private static readonly PropertyInfo<Int64> pty_Id = RegisterProperty<Int64>(p => p.Id);
        private static readonly PropertyInfo<DateTime?> pty_StartTime = RegisterProperty<DateTime?>(p => p.StartTime);
        private static readonly PropertyInfo<String> pty_Unit = RegisterProperty<String>(p => p.Unit);
        #endregion

        /// <summary>
        /// ClearTime
        /// </summary>
        [Description("ClearTime")]
        [LinqToDB.Mapping.Column("Clear_Time")]
        public DateTime? ClearTime
        {
            get { return GetProperty(pty_ClearTime); }
            set { SetProperty(pty_ClearTime, value); }
        }
        /// <summary>
        /// AlarmCode
        /// </summary>
        [Description("AlarmCode")]
        [LinqToDB.Mapping.Column("Code")]
        public Int32? Code
        {
            get { return GetProperty(pty_Code); }
            set { SetProperty(pty_Code, value); }
        }
        /// <summary>
        /// Comment
        /// </summary>
        [Description("Comment")]
        [LinqToDB.Mapping.Column("Comment")]
        public String Comment
        {
            get { return GetProperty(pty_Comment); }
            set { SetProperty(pty_Comment, value); }
        }
        /// <summary>
        /// PrimaryKey
        /// </summary>
        [Description("PrimaryKey")]
        [LinqToDB.Mapping.Column("ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int64 Id
        {
            get { return GetProperty(pty_Id); }
            set { SetProperty(pty_Id, value); }
        }
        /// <summary>
        /// StartTime
        /// </summary>
        [Description("StartTime")]
        [LinqToDB.Mapping.Column("Start_Time")]
        public DateTime? StartTime
        {
            get { return GetProperty(pty_StartTime); }
            set { SetProperty(pty_StartTime, value); }
        }
        /// <summary>
        /// AlarmUnit
        /// </summary>
        [Description("AlarmUnit")]
        [LinqToDB.Mapping.Column("Unit")]
        public String Unit
        {
            get { return GetProperty(pty_Unit); }
            set { SetProperty(pty_Unit, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Comment, 255, "Comment不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Id, "PrimaryKey是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Unit, 32, "AlarmUnit不能超过32个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Id.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class ThAlarmList : GEntityList<ThAlarmList, ThAlarm>
    {
        private ThAlarmList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
