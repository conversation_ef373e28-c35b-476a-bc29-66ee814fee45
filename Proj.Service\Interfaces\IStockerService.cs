using Proj.Service.Models;

namespace Proj.Service.Interfaces
{
    /// <summary>
    /// Stocker服务接口，替代原WCF服务
    /// </summary>
    public interface IStockerService
    {
        /// <summary>
        /// 处理客户端发送的消息
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>处理结果</returns>
        Task<object?> ProcessClientMessageAsync(string function, Dictionary<string, object> parameters);

        /// <summary>
        /// 获取系统状态
        /// </summary>
        /// <param name="stateName">状态名称</param>
        /// <returns>状态值</returns>
        Task<string> GetStateAsync(string stateName);

        /// <summary>
        /// 获取所有系统状态
        /// </summary>
        /// <returns>所有状态信息</returns>
        Task<SystemStateInfo> GetAllStatesAsync();

        /// <summary>
        /// 获取标签值
        /// </summary>
        /// <param name="tagName">标签名称</param>
        /// <returns>标签值</returns>
        Task<object?> GetTagValueAsync(string tagName);

        /// <summary>
        /// 连接测试
        /// </summary>
        /// <returns>连接是否正常</returns>
        Task<bool> TestConnectionAsync();
    }

    /// <summary>
    /// 客户端管理服务接口
    /// </summary>
    public interface IClientManagerService
    {
        /// <summary>
        /// 注册客户端连接
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        /// <param name="ipAddress">客户端IP地址</param>
        void RegisterClient(string connectionId, string ipAddress);

        /// <summary>
        /// 移除客户端连接
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        void RemoveClient(string connectionId);

        /// <summary>
        /// 获取所有连接的客户端
        /// </summary>
        /// <returns>客户端连接信息列表</returns>
        IEnumerable<ClientConnectionInfo> GetConnectedClients();

        /// <summary>
        /// 向所有客户端发送消息
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>发送是否成功</returns>
        Task<bool> SendMessageToAllClientsAsync(string function, Dictionary<string, object> parameters);

        /// <summary>
        /// 向指定客户端发送消息
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>发送是否成功</returns>
        Task<bool> SendMessageToClientAsync(string connectionId, string function, Dictionary<string, object> parameters);

        /// <summary>
        /// 更新客户端活动时间
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        void UpdateClientActivity(string connectionId);
    }
}
