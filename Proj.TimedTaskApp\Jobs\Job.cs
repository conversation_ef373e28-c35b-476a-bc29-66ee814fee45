﻿using System;
using System.IO;
using System.Reflection;
using Proj.Business.DeviceGather;
using Proj.Entity;

namespace Proj.TimedTaskApp.Base
{
    /// <summary>
    /// 工作项
    /// 每项任务必须包含一个工作类并继承至Base.ServiceJob。
    /// 抽像方法Start()与Stop()必须在子类中实现。
    /// </summary>
    public class Job
    {
        //配置对象
        private TntJobsgroup mConfigObject;
        //下次运行时间
        private DateTime mNextTime;
        //任务是否在运行中
        protected bool mIsRunning;
        // 值变化监视类
        /// <summary>
        /// 构造函数
        /// </summary>
        public Job(TntJobsgroup ConfigObject)
        {
            //变量初始化
            this.mNextTime = DateTime.Now;
            this.mIsRunning = false;
            this.mConfigObject = ConfigObject;
        }

        /// <summary>
        /// 配置对象
        /// </summary>
        public TntJobsgroup ConfigObject
        {
            get { return this.mConfigObject; }
            set { this.mConfigObject = value; }
        }

        /// <summary>
        /// 开始工作
        /// </summary>
        public void StartJob()
        {
            if (this.mConfigObject != null)
            {
                try
                {
                    TntJobsexcute jobExe = TntJobsexcute.GetByLambda(x => x.CJobgroupCode == this.mConfigObject.CJobgroupCode && x.CEnable == "1");
                    if (jobExe != null)
                    {
                        this.mNextTime = (DateTime)jobExe.CNexttime;


                        if (DateTime.Now > this.mNextTime.AddMinutes(1))
                        {
                            this.mNextTime = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        }
                        if (DateTime.Now >= this.mNextTime && DateTime.Now < this.mNextTime.AddMinutes(1))
                        {
                            this.Start(jobExe);
                        }
                    }
                }
                catch(Exception e)
                {
                    Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + "\\" + "Error.txt", e.Message);
                }
            }

        }

        /// <summary>
        /// 停止工作
        /// </summary>
        public void StopJob()
        {
            this.mConfigObject = null;
            this.mNextTime = DateTime.Now;
            this.mIsRunning = false;
            this.Stop();
        }

        /// <summary>
        /// 执行逻辑
        /// </summary>
        private void ExecuteLogic()
        {
            string nameEntity = this.ConfigObject.CClass;
            string methodName = this.ConfigObject.CMethod;

            //null CycleMonitor.MethodBatch   getMaxNum  null
            Assembly assembly = Assembly.LoadFile(AppDomain.CurrentDomain.BaseDirectory + "Proj.Business.dll");
            Type T = assembly.GetType(nameEntity);
            MethodInfo method = T.GetMethod(methodName);

            // 3.调用的实例化方法（非静态方法）需要创建类型的一个实例
            Object obj = Activator.CreateInstance(T);
            //4.方法需要传入的参数
            string[] args = new string[] { this.ConfigObject.CJobgroupCode, this.ConfigObject.CJobgroupName };
            object[] parameters = new object[] { args };
            // 5.调用方法，如果调用的是一个静态方法，就不需要第3步（创建类型的实例）
            // 相应地调用静态方法时，Invoke的第一个参数为null
            var a = method.Invoke(obj, parameters);
        }

        #region 子类必需实现的抽象成员

        /// <summary>
        /// 任务开始
        /// </summary>
        protected void Start(TntJobsexcute exe)
        {
            try
            {
                //Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + "\\" + "AppLog.txt", this.ConfigObject.CJobgroupName);

                //运行中
                this.mIsRunning = true;
                //执行工作项
                this.ExecuteLogic();
            }
            catch (Exception error)
            {
                //异常日志
                Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + @"\" + "Error.txt", error.ToString());
                //发生异常时停止服务程序
                Base.ServiceTools.WindowsServiceStop("TimedTaskService");
            }
            finally
            {
                //空闲
                this.mIsRunning = false;

                #region 更新任务执行表
                exe.CExcutetime = DateTime.Now;
                exe.CExcutecount += 1;
                // 如果是按周期执行
                if(this.ConfigObject.CMode == "0")
                {
                    // 如果只执行一次，或执行固定次数，本次执行后即失效
                    if(this.ConfigObject.CExcutetimes == exe.CExcutecount)
                    {
                        exe.CEnable = "0";
                    }
                    // 如果按固定频率执行
                    else
                    {
                        // 如果有开始时间 按开始时间计算,如果没有开始时间 按当前执行时间计算
                        if (Convert.ToDateTime(ConfigObject.CStart).Year != 1)
                        {
                            this.mNextTime = (DateTime)exe.CNexttime;
                        }
                        // 计算下次执行时间   秒
                        if (this.ConfigObject.CUnit == "0")
                        {
                            exe.CNexttime = this.mNextTime.AddSeconds((double)this.ConfigObject.CInterval);
                        }
                        // 分
                        if (this.ConfigObject.CUnit == "1")
                        {
                            exe.CNexttime = this.mNextTime.AddMinutes((double)this.ConfigObject.CInterval);
                        }
                        // 小时
                        if (this.ConfigObject.CUnit == "2")
                        {
                            exe.CNexttime = this.mNextTime.AddHours((double)this.ConfigObject.CInterval);
                        }
                        // 天
                        if (this.ConfigObject.CUnit == "3")
                        {
                            exe.CNexttime = this.mNextTime.AddDays((double)this.ConfigObject.CInterval);
                        }
                        // 周
                        if (this.ConfigObject.CUnit == "4")
                        {
                            exe.CNexttime = this.mNextTime.AddDays((double)this.ConfigObject.CInterval * 7);
                        }                     
                    }
                }
                // 如果是按公式定点执行
                else
                {
                    exe.CNexttime = DateExpression.GetNextExcuteTime((DateTime)exe.CNexttime, this.ConfigObject.CExpression);
                }
                // 如果有结束时间，根据下次执行时间判断
                if (Convert.ToDateTime(ConfigObject.CEnd).Year != 1 && this.ConfigObject.CEnd < exe.CNexttime)
                {
                    // 下次执行失效
                    exe.CEnable = "0";
                }
                try
                {
                exe.Save();
                }
                catch (Exception ex)
                {
                    Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + @"\" + "Error.txt", ex.ToString());
                }
                #endregion
            }
        }

        /// <summary>
        /// 任务停止
        /// </summary>
        protected void Stop()
        {
            this.mIsRunning = false;
        }

        #endregion
    }
}

