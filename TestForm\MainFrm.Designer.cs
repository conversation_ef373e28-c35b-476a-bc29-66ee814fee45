﻿namespace TestForm
{
    partial class MainFrm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnTransfer = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.txtTaskCarrierID = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.txtTaskSourceAddr = new System.Windows.Forms.TextBox();
            this.txtTaskDestAddr = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tpTransferCmd = new System.Windows.Forms.TabPage();
            this.start = new System.Windows.Forms.Button();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.tabPage6 = new System.Windows.Forms.TabPage();
            this.gbPlcTaskData = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.txtPlcCmdType = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPlcDestAddr = new System.Windows.Forms.TextBox();
            this.txtPlcSourceAddr = new System.Windows.Forms.TextBox();
            this.txtPlcCarrierID = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.txtStructBoolW = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.txtStructFloatW = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.txtStructIntW = new System.Windows.Forms.TextBox();
            this.txtArrayInt0W = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.txtArrayInt1W = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.txtStructStringW = new System.Windows.Forms.TextBox();
            this.label13 = new System.Windows.Forms.Label();
            this.ArrayInt2 = new System.Windows.Forms.Label();
            this.txtArrayInt2W = new System.Windows.Forms.TextBox();
            this.txtTaskDoubleW = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.txtTaskBoolW = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.txtTaskStringW = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.txtTaskFloatW = new System.Windows.Forms.TextBox();
            this.label17 = new System.Windows.Forms.Label();
            this.txtTaskInt2W = new System.Windows.Forms.TextBox();
            this.label18 = new System.Windows.Forms.Label();
            this.txtTaskDintW = new System.Windows.Forms.TextBox();
            this.label19 = new System.Windows.Forms.Label();
            this.txtTaskIntW = new System.Windows.Forms.TextBox();
            this.label20 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.txtStructBoolR = new System.Windows.Forms.TextBox();
            this.label22 = new System.Windows.Forms.Label();
            this.txtStructFloatR = new System.Windows.Forms.TextBox();
            this.label23 = new System.Windows.Forms.Label();
            this.txtStructIntR = new System.Windows.Forms.TextBox();
            this.label24 = new System.Windows.Forms.Label();
            this.txtStructStringR = new System.Windows.Forms.TextBox();
            this.label25 = new System.Windows.Forms.Label();
            this.txtArrayInt1R = new System.Windows.Forms.TextBox();
            this.label26 = new System.Windows.Forms.Label();
            this.txtArrayInt2R = new System.Windows.Forms.TextBox();
            this.label27 = new System.Windows.Forms.Label();
            this.txtArrayInt0R = new System.Windows.Forms.TextBox();
            this.label28 = new System.Windows.Forms.Label();
            this.txtTaskIntR = new System.Windows.Forms.TextBox();
            this.label29 = new System.Windows.Forms.Label();
            this.txtTaskDintR = new System.Windows.Forms.TextBox();
            this.label30 = new System.Windows.Forms.Label();
            this.txtTaskInt2R = new System.Windows.Forms.TextBox();
            this.label31 = new System.Windows.Forms.Label();
            this.txtTaskFloatR = new System.Windows.Forms.TextBox();
            this.label32 = new System.Windows.Forms.Label();
            this.txtTaskStringR = new System.Windows.Forms.TextBox();
            this.label33 = new System.Windows.Forms.Label();
            this.txtTaskBoolR = new System.Windows.Forms.TextBox();
            this.label34 = new System.Windows.Forms.Label();
            this.txtTaskDoubleR = new System.Windows.Forms.TextBox();
            this.btnWriteData = new System.Windows.Forms.Button();
            this.btnDbTest = new System.Windows.Forms.Button();
            this.btnHsmsStart = new System.Windows.Forms.Button();
            this.tabControl1.SuspendLayout();
            this.tpTransferCmd.SuspendLayout();
            this.gbPlcTaskData.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnTransfer
            // 
            this.btnTransfer.Location = new System.Drawing.Point(111, 134);
            this.btnTransfer.Name = "btnTransfer";
            this.btnTransfer.Size = new System.Drawing.Size(82, 28);
            this.btnTransfer.TabIndex = 0;
            this.btnTransfer.Text = "Transfer";
            this.btnTransfer.UseVisualStyleBackColor = true;
            this.btnTransfer.Click += new System.EventHandler(this.btnTransfer_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(10, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(95, 15);
            this.label1.TabIndex = 1;
            this.label1.Text = "Carrier ID:";
            // 
            // txtTaskCarrierID
            // 
            this.txtTaskCarrierID.Location = new System.Drawing.Point(111, 22);
            this.txtTaskCarrierID.Name = "txtTaskCarrierID";
            this.txtTaskCarrierID.Size = new System.Drawing.Size(124, 25);
            this.txtTaskCarrierID.TabIndex = 2;
            this.txtTaskCarrierID.Text = "ABC123";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(41, 61);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(63, 15);
            this.label2.TabIndex = 1;
            this.label2.Text = "Source:";
            // 
            // txtTaskSourceAddr
            // 
            this.txtTaskSourceAddr.Location = new System.Drawing.Point(111, 58);
            this.txtTaskSourceAddr.Name = "txtTaskSourceAddr";
            this.txtTaskSourceAddr.Size = new System.Drawing.Size(124, 25);
            this.txtTaskSourceAddr.TabIndex = 2;
            this.txtTaskSourceAddr.Text = "20202";
            // 
            // txtTaskDestAddr
            // 
            this.txtTaskDestAddr.Location = new System.Drawing.Point(111, 94);
            this.txtTaskDestAddr.Name = "txtTaskDestAddr";
            this.txtTaskDestAddr.Size = new System.Drawing.Size(124, 25);
            this.txtTaskDestAddr.TabIndex = 7;
            this.txtTaskDestAddr.Text = "20206";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(57, 97);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(47, 15);
            this.label7.TabIndex = 4;
            this.label7.Text = "Dest:";
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tpTransferCmd);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Controls.Add(this.tabPage5);
            this.tabControl1.Controls.Add(this.tabPage6);
            this.tabControl1.Location = new System.Drawing.Point(12, 12);
            this.tabControl1.Multiline = true;
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(333, 215);
            this.tabControl1.TabIndex = 10;
            // 
            // tpTransferCmd
            // 
            this.tpTransferCmd.Controls.Add(this.start);
            this.tpTransferCmd.Controls.Add(this.btnTransfer);
            this.tpTransferCmd.Controls.Add(this.label1);
            this.tpTransferCmd.Controls.Add(this.label2);
            this.tpTransferCmd.Controls.Add(this.txtTaskDestAddr);
            this.tpTransferCmd.Controls.Add(this.txtTaskCarrierID);
            this.tpTransferCmd.Controls.Add(this.label7);
            this.tpTransferCmd.Controls.Add(this.txtTaskSourceAddr);
            this.tpTransferCmd.Location = new System.Drawing.Point(4, 46);
            this.tpTransferCmd.Name = "tpTransferCmd";
            this.tpTransferCmd.Padding = new System.Windows.Forms.Padding(3);
            this.tpTransferCmd.Size = new System.Drawing.Size(325, 165);
            this.tpTransferCmd.TabIndex = 0;
            this.tpTransferCmd.Text = "Transfer";
            this.tpTransferCmd.UseVisualStyleBackColor = true;
            // 
            // start
            // 
            this.start.Location = new System.Drawing.Point(228, 134);
            this.start.Name = "start";
            this.start.Size = new System.Drawing.Size(82, 28);
            this.start.TabIndex = 0;
            this.start.Text = "启动";
            this.start.UseVisualStyleBackColor = true;
            this.start.Click += new System.EventHandler(this.start_Click);
            // 
            // tabPage2
            // 
            this.tabPage2.Location = new System.Drawing.Point(4, 46);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(325, 165);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "tabPage2";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // tabPage3
            // 
            this.tabPage3.Location = new System.Drawing.Point(4, 46);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage3.Size = new System.Drawing.Size(325, 165);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "tabPage3";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // tabPage4
            // 
            this.tabPage4.Location = new System.Drawing.Point(4, 46);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(325, 165);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "tabPage4";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // tabPage5
            // 
            this.tabPage5.Location = new System.Drawing.Point(4, 46);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Size = new System.Drawing.Size(325, 165);
            this.tabPage5.TabIndex = 4;
            this.tabPage5.Text = "tabPage5";
            this.tabPage5.UseVisualStyleBackColor = true;
            // 
            // tabPage6
            // 
            this.tabPage6.Location = new System.Drawing.Point(4, 46);
            this.tabPage6.Name = "tabPage6";
            this.tabPage6.Size = new System.Drawing.Size(325, 165);
            this.tabPage6.TabIndex = 5;
            this.tabPage6.Text = "tabPage6";
            this.tabPage6.UseVisualStyleBackColor = true;
            // 
            // gbPlcTaskData
            // 
            this.gbPlcTaskData.Controls.Add(this.label3);
            this.gbPlcTaskData.Controls.Add(this.label6);
            this.gbPlcTaskData.Controls.Add(this.txtPlcCmdType);
            this.gbPlcTaskData.Controls.Add(this.label5);
            this.gbPlcTaskData.Controls.Add(this.txtPlcDestAddr);
            this.gbPlcTaskData.Controls.Add(this.txtPlcSourceAddr);
            this.gbPlcTaskData.Controls.Add(this.txtPlcCarrierID);
            this.gbPlcTaskData.Controls.Add(this.label4);
            this.gbPlcTaskData.Location = new System.Drawing.Point(351, 36);
            this.gbPlcTaskData.Name = "gbPlcTaskData";
            this.gbPlcTaskData.Size = new System.Drawing.Size(273, 191);
            this.gbPlcTaskData.TabIndex = 11;
            this.gbPlcTaskData.TabStop = false;
            this.gbPlcTaskData.Text = "PlcTaskData";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(31, 42);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(79, 15);
            this.label3.TabIndex = 1;
            this.label3.Text = "CMD Type:";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(16, 84);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(95, 15);
            this.label6.TabIndex = 1;
            this.label6.Text = "Carrier ID:";
            // 
            // txtPlcCmdType
            // 
            this.txtPlcCmdType.Location = new System.Drawing.Point(117, 39);
            this.txtPlcCmdType.Name = "txtPlcCmdType";
            this.txtPlcCmdType.ReadOnly = true;
            this.txtPlcCmdType.Size = new System.Drawing.Size(124, 25);
            this.txtPlcCmdType.TabIndex = 2;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(47, 120);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(63, 15);
            this.label5.TabIndex = 1;
            this.label5.Text = "Source:";
            // 
            // txtPlcDestAddr
            // 
            this.txtPlcDestAddr.Location = new System.Drawing.Point(117, 153);
            this.txtPlcDestAddr.Name = "txtPlcDestAddr";
            this.txtPlcDestAddr.ReadOnly = true;
            this.txtPlcDestAddr.Size = new System.Drawing.Size(124, 25);
            this.txtPlcDestAddr.TabIndex = 7;
            // 
            // txtPlcSourceAddr
            // 
            this.txtPlcSourceAddr.Location = new System.Drawing.Point(117, 117);
            this.txtPlcSourceAddr.Name = "txtPlcSourceAddr";
            this.txtPlcSourceAddr.ReadOnly = true;
            this.txtPlcSourceAddr.Size = new System.Drawing.Size(124, 25);
            this.txtPlcSourceAddr.TabIndex = 2;
            // 
            // txtPlcCarrierID
            // 
            this.txtPlcCarrierID.Location = new System.Drawing.Point(117, 81);
            this.txtPlcCarrierID.Name = "txtPlcCarrierID";
            this.txtPlcCarrierID.ReadOnly = true;
            this.txtPlcCarrierID.Size = new System.Drawing.Size(124, 25);
            this.txtPlcCarrierID.TabIndex = 2;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(63, 156);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(47, 15);
            this.label4.TabIndex = 4;
            this.label4.Text = "Dest:";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(14, 257);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(95, 15);
            this.label8.TabIndex = 4;
            this.label8.Text = "StructBool:";
            // 
            // txtStructBoolW
            // 
            this.txtStructBoolW.Location = new System.Drawing.Point(112, 254);
            this.txtStructBoolW.Name = "txtStructBoolW";
            this.txtStructBoolW.Size = new System.Drawing.Size(124, 25);
            this.txtStructBoolW.TabIndex = 7;
            this.txtStructBoolW.Text = "true";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(6, 315);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(103, 15);
            this.label9.TabIndex = 4;
            this.label9.Text = "StructFloat:";
            // 
            // txtStructFloatW
            // 
            this.txtStructFloatW.Location = new System.Drawing.Point(112, 312);
            this.txtStructFloatW.Name = "txtStructFloatW";
            this.txtStructFloatW.Size = new System.Drawing.Size(124, 25);
            this.txtStructFloatW.TabIndex = 7;
            this.txtStructFloatW.Text = "3.14";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(22, 286);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(87, 15);
            this.label10.TabIndex = 4;
            this.label10.Text = "StructInt:";
            // 
            // txtStructIntW
            // 
            this.txtStructIntW.Location = new System.Drawing.Point(112, 283);
            this.txtStructIntW.Name = "txtStructIntW";
            this.txtStructIntW.Size = new System.Drawing.Size(124, 25);
            this.txtStructIntW.TabIndex = 7;
            this.txtStructIntW.Text = "724";
            // 
            // txtArrayInt0W
            // 
            this.txtArrayInt0W.Location = new System.Drawing.Point(112, 383);
            this.txtArrayInt0W.Name = "txtArrayInt0W";
            this.txtArrayInt0W.Size = new System.Drawing.Size(124, 25);
            this.txtArrayInt0W.TabIndex = 15;
            this.txtArrayInt0W.Text = "1";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(22, 386);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(87, 15);
            this.label11.TabIndex = 12;
            this.label11.Text = "ArrayInt0:";
            // 
            // txtArrayInt1W
            // 
            this.txtArrayInt1W.Location = new System.Drawing.Point(112, 412);
            this.txtArrayInt1W.Name = "txtArrayInt1W";
            this.txtArrayInt1W.Size = new System.Drawing.Size(124, 25);
            this.txtArrayInt1W.TabIndex = 16;
            this.txtArrayInt1W.Text = "3";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(22, 415);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(87, 15);
            this.label12.TabIndex = 13;
            this.label12.Text = "ArrayInt1:";
            // 
            // txtStructStringW
            // 
            this.txtStructStringW.Location = new System.Drawing.Point(112, 342);
            this.txtStructStringW.Name = "txtStructStringW";
            this.txtStructStringW.Size = new System.Drawing.Size(124, 25);
            this.txtStructStringW.TabIndex = 17;
            this.txtStructStringW.Text = "Test123";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(-2, 345);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(111, 15);
            this.label13.TabIndex = 14;
            this.label13.Text = "StructString:";
            // 
            // ArrayInt2
            // 
            this.ArrayInt2.AutoSize = true;
            this.ArrayInt2.Location = new System.Drawing.Point(22, 446);
            this.ArrayInt2.Name = "ArrayInt2";
            this.ArrayInt2.Size = new System.Drawing.Size(87, 15);
            this.ArrayInt2.TabIndex = 13;
            this.ArrayInt2.Text = "ArrayInt2:";
            // 
            // txtArrayInt2W
            // 
            this.txtArrayInt2W.Location = new System.Drawing.Point(112, 443);
            this.txtArrayInt2W.Name = "txtArrayInt2W";
            this.txtArrayInt2W.Size = new System.Drawing.Size(124, 25);
            this.txtArrayInt2W.TabIndex = 16;
            this.txtArrayInt2W.Text = "5";
            // 
            // txtTaskDoubleW
            // 
            this.txtTaskDoubleW.Location = new System.Drawing.Point(351, 383);
            this.txtTaskDoubleW.Name = "txtTaskDoubleW";
            this.txtTaskDoubleW.Size = new System.Drawing.Size(124, 25);
            this.txtTaskDoubleW.TabIndex = 28;
            this.txtTaskDoubleW.Text = "123456.32";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(251, 386);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(95, 15);
            this.label14.TabIndex = 24;
            this.label14.Text = "TaskDouble:";
            // 
            // txtTaskBoolW
            // 
            this.txtTaskBoolW.Location = new System.Drawing.Point(351, 443);
            this.txtTaskBoolW.Name = "txtTaskBoolW";
            this.txtTaskBoolW.Size = new System.Drawing.Size(124, 25);
            this.txtTaskBoolW.TabIndex = 29;
            this.txtTaskBoolW.Text = "false";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(267, 446);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(79, 15);
            this.label15.TabIndex = 25;
            this.label15.Text = "TaskBool:";
            // 
            // txtTaskStringW
            // 
            this.txtTaskStringW.Location = new System.Drawing.Point(351, 412);
            this.txtTaskStringW.Name = "txtTaskStringW";
            this.txtTaskStringW.Size = new System.Drawing.Size(124, 25);
            this.txtTaskStringW.TabIndex = 30;
            this.txtTaskStringW.Text = "abcdef";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(251, 415);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(95, 15);
            this.label16.TabIndex = 26;
            this.label16.Text = "TaskString:";
            // 
            // txtTaskFloatW
            // 
            this.txtTaskFloatW.Location = new System.Drawing.Point(351, 342);
            this.txtTaskFloatW.Name = "txtTaskFloatW";
            this.txtTaskFloatW.Size = new System.Drawing.Size(124, 25);
            this.txtTaskFloatW.TabIndex = 31;
            this.txtTaskFloatW.Text = "2.76";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(259, 345);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(87, 15);
            this.label17.TabIndex = 27;
            this.label17.Text = "TaskFloat:";
            // 
            // txtTaskInt2W
            // 
            this.txtTaskInt2W.Location = new System.Drawing.Point(351, 283);
            this.txtTaskInt2W.Name = "txtTaskInt2W";
            this.txtTaskInt2W.Size = new System.Drawing.Size(124, 25);
            this.txtTaskInt2W.TabIndex = 21;
            this.txtTaskInt2W.Text = "246";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(267, 286);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(79, 15);
            this.label18.TabIndex = 18;
            this.label18.Text = "TaskInt2:";
            // 
            // txtTaskDintW
            // 
            this.txtTaskDintW.Location = new System.Drawing.Point(351, 312);
            this.txtTaskDintW.Name = "txtTaskDintW";
            this.txtTaskDintW.Size = new System.Drawing.Size(124, 25);
            this.txtTaskDintW.TabIndex = 22;
            this.txtTaskDintW.Text = "65535";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(267, 315);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(79, 15);
            this.label19.TabIndex = 19;
            this.label19.Text = "TaskDint:";
            // 
            // txtTaskIntW
            // 
            this.txtTaskIntW.Location = new System.Drawing.Point(351, 254);
            this.txtTaskIntW.Name = "txtTaskIntW";
            this.txtTaskIntW.Size = new System.Drawing.Size(124, 25);
            this.txtTaskIntW.TabIndex = 23;
            this.txtTaskIntW.Text = "563";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(275, 257);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(71, 15);
            this.label20.TabIndex = 20;
            this.label20.Text = "TaskInt:";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(531, 260);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(95, 15);
            this.label21.TabIndex = 4;
            this.label21.Text = "StructBool:";
            // 
            // txtStructBoolR
            // 
            this.txtStructBoolR.Location = new System.Drawing.Point(629, 257);
            this.txtStructBoolR.Name = "txtStructBoolR";
            this.txtStructBoolR.ReadOnly = true;
            this.txtStructBoolR.Size = new System.Drawing.Size(124, 25);
            this.txtStructBoolR.TabIndex = 7;
            this.txtStructBoolR.Text = " ";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(523, 318);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(103, 15);
            this.label22.TabIndex = 4;
            this.label22.Text = "StructFloat:";
            // 
            // txtStructFloatR
            // 
            this.txtStructFloatR.Location = new System.Drawing.Point(629, 315);
            this.txtStructFloatR.Name = "txtStructFloatR";
            this.txtStructFloatR.ReadOnly = true;
            this.txtStructFloatR.Size = new System.Drawing.Size(124, 25);
            this.txtStructFloatR.TabIndex = 7;
            this.txtStructFloatR.Text = " ";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(539, 289);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(87, 15);
            this.label23.TabIndex = 4;
            this.label23.Text = "StructInt:";
            // 
            // txtStructIntR
            // 
            this.txtStructIntR.Location = new System.Drawing.Point(629, 286);
            this.txtStructIntR.Name = "txtStructIntR";
            this.txtStructIntR.ReadOnly = true;
            this.txtStructIntR.Size = new System.Drawing.Size(124, 25);
            this.txtStructIntR.TabIndex = 7;
            this.txtStructIntR.Text = " ";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(515, 348);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(111, 15);
            this.label24.TabIndex = 14;
            this.label24.Text = "StructString:";
            // 
            // txtStructStringR
            // 
            this.txtStructStringR.Location = new System.Drawing.Point(629, 345);
            this.txtStructStringR.Name = "txtStructStringR";
            this.txtStructStringR.ReadOnly = true;
            this.txtStructStringR.Size = new System.Drawing.Size(124, 25);
            this.txtStructStringR.TabIndex = 17;
            this.txtStructStringR.Text = " ";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(539, 418);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(87, 15);
            this.label25.TabIndex = 13;
            this.label25.Text = "ArrayInt1:";
            // 
            // txtArrayInt1R
            // 
            this.txtArrayInt1R.Location = new System.Drawing.Point(629, 415);
            this.txtArrayInt1R.Name = "txtArrayInt1R";
            this.txtArrayInt1R.ReadOnly = true;
            this.txtArrayInt1R.Size = new System.Drawing.Size(124, 25);
            this.txtArrayInt1R.TabIndex = 16;
            this.txtArrayInt1R.Text = " ";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(539, 449);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(87, 15);
            this.label26.TabIndex = 13;
            this.label26.Text = "ArrayInt2:";
            // 
            // txtArrayInt2R
            // 
            this.txtArrayInt2R.Location = new System.Drawing.Point(629, 446);
            this.txtArrayInt2R.Name = "txtArrayInt2R";
            this.txtArrayInt2R.ReadOnly = true;
            this.txtArrayInt2R.Size = new System.Drawing.Size(124, 25);
            this.txtArrayInt2R.TabIndex = 16;
            this.txtArrayInt2R.Text = " ";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(539, 389);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(87, 15);
            this.label27.TabIndex = 12;
            this.label27.Text = "ArrayInt0:";
            // 
            // txtArrayInt0R
            // 
            this.txtArrayInt0R.Location = new System.Drawing.Point(629, 386);
            this.txtArrayInt0R.Name = "txtArrayInt0R";
            this.txtArrayInt0R.ReadOnly = true;
            this.txtArrayInt0R.Size = new System.Drawing.Size(124, 25);
            this.txtArrayInt0R.TabIndex = 15;
            this.txtArrayInt0R.Text = " ";
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(792, 260);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(71, 15);
            this.label28.TabIndex = 20;
            this.label28.Text = "TaskInt:";
            // 
            // txtTaskIntR
            // 
            this.txtTaskIntR.Location = new System.Drawing.Point(868, 257);
            this.txtTaskIntR.Name = "txtTaskIntR";
            this.txtTaskIntR.ReadOnly = true;
            this.txtTaskIntR.Size = new System.Drawing.Size(124, 25);
            this.txtTaskIntR.TabIndex = 23;
            this.txtTaskIntR.Text = " ";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(784, 318);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(79, 15);
            this.label29.TabIndex = 19;
            this.label29.Text = "TaskDint:";
            // 
            // txtTaskDintR
            // 
            this.txtTaskDintR.Location = new System.Drawing.Point(868, 315);
            this.txtTaskDintR.Name = "txtTaskDintR";
            this.txtTaskDintR.ReadOnly = true;
            this.txtTaskDintR.Size = new System.Drawing.Size(124, 25);
            this.txtTaskDintR.TabIndex = 22;
            this.txtTaskDintR.Text = " ";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(784, 289);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(79, 15);
            this.label30.TabIndex = 18;
            this.label30.Text = "TaskInt2:";
            // 
            // txtTaskInt2R
            // 
            this.txtTaskInt2R.Location = new System.Drawing.Point(868, 286);
            this.txtTaskInt2R.Name = "txtTaskInt2R";
            this.txtTaskInt2R.ReadOnly = true;
            this.txtTaskInt2R.Size = new System.Drawing.Size(124, 25);
            this.txtTaskInt2R.TabIndex = 21;
            this.txtTaskInt2R.Text = " ";
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(776, 348);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(87, 15);
            this.label31.TabIndex = 27;
            this.label31.Text = "TaskFloat:";
            // 
            // txtTaskFloatR
            // 
            this.txtTaskFloatR.Location = new System.Drawing.Point(868, 345);
            this.txtTaskFloatR.Name = "txtTaskFloatR";
            this.txtTaskFloatR.ReadOnly = true;
            this.txtTaskFloatR.Size = new System.Drawing.Size(124, 25);
            this.txtTaskFloatR.TabIndex = 31;
            this.txtTaskFloatR.Text = " ";
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(768, 418);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(95, 15);
            this.label32.TabIndex = 26;
            this.label32.Text = "TaskString:";
            // 
            // txtTaskStringR
            // 
            this.txtTaskStringR.Location = new System.Drawing.Point(868, 415);
            this.txtTaskStringR.Name = "txtTaskStringR";
            this.txtTaskStringR.ReadOnly = true;
            this.txtTaskStringR.Size = new System.Drawing.Size(124, 25);
            this.txtTaskStringR.TabIndex = 30;
            this.txtTaskStringR.Text = " ";
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(784, 449);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(79, 15);
            this.label33.TabIndex = 25;
            this.label33.Text = "TaskBool:";
            // 
            // txtTaskBoolR
            // 
            this.txtTaskBoolR.Location = new System.Drawing.Point(868, 446);
            this.txtTaskBoolR.Name = "txtTaskBoolR";
            this.txtTaskBoolR.ReadOnly = true;
            this.txtTaskBoolR.Size = new System.Drawing.Size(124, 25);
            this.txtTaskBoolR.TabIndex = 29;
            this.txtTaskBoolR.Text = " ";
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(768, 389);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(95, 15);
            this.label34.TabIndex = 24;
            this.label34.Text = "TaskDouble:";
            // 
            // txtTaskDoubleR
            // 
            this.txtTaskDoubleR.Location = new System.Drawing.Point(868, 386);
            this.txtTaskDoubleR.Name = "txtTaskDoubleR";
            this.txtTaskDoubleR.ReadOnly = true;
            this.txtTaskDoubleR.Size = new System.Drawing.Size(124, 25);
            this.txtTaskDoubleR.TabIndex = 28;
            this.txtTaskDoubleR.Text = " ";
            // 
            // btnWriteData
            // 
            this.btnWriteData.Location = new System.Drawing.Point(215, 504);
            this.btnWriteData.Name = "btnWriteData";
            this.btnWriteData.Size = new System.Drawing.Size(75, 23);
            this.btnWriteData.TabIndex = 32;
            this.btnWriteData.Text = "写值";
            this.btnWriteData.UseVisualStyleBackColor = true;
            this.btnWriteData.Click += new System.EventHandler(this.btnWriteData_Click);
            // 
            // btnDbTest
            // 
            this.btnDbTest.Location = new System.Drawing.Point(771, 36);
            this.btnDbTest.Name = "btnDbTest";
            this.btnDbTest.Size = new System.Drawing.Size(96, 37);
            this.btnDbTest.TabIndex = 33;
            this.btnDbTest.Text = "DbTest";
            this.btnDbTest.UseVisualStyleBackColor = true;
            this.btnDbTest.Click += new System.EventHandler(this.btnDbTest_Click);
            // 
            // btnHsmsStart
            // 
            this.btnHsmsStart.Location = new System.Drawing.Point(771, 145);
            this.btnHsmsStart.Name = "btnHsmsStart";
            this.btnHsmsStart.Size = new System.Drawing.Size(96, 37);
            this.btnHsmsStart.TabIndex = 33;
            this.btnHsmsStart.Text = "HSMS Start";
            this.btnHsmsStart.UseVisualStyleBackColor = true;
            this.btnHsmsStart.Click += new System.EventHandler(this.btnHsmsStart_Click);
            // 
            // MainFrm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1028, 581);
            this.Controls.Add(this.btnHsmsStart);
            this.Controls.Add(this.btnDbTest);
            this.Controls.Add(this.btnWriteData);
            this.Controls.Add(this.txtTaskDoubleR);
            this.Controls.Add(this.txtTaskDoubleW);
            this.Controls.Add(this.label34);
            this.Controls.Add(this.label14);
            this.Controls.Add(this.txtTaskBoolR);
            this.Controls.Add(this.txtTaskBoolW);
            this.Controls.Add(this.label33);
            this.Controls.Add(this.label15);
            this.Controls.Add(this.txtTaskStringR);
            this.Controls.Add(this.txtTaskStringW);
            this.Controls.Add(this.label32);
            this.Controls.Add(this.label16);
            this.Controls.Add(this.txtTaskFloatR);
            this.Controls.Add(this.txtTaskFloatW);
            this.Controls.Add(this.label31);
            this.Controls.Add(this.label17);
            this.Controls.Add(this.txtTaskInt2R);
            this.Controls.Add(this.txtTaskInt2W);
            this.Controls.Add(this.label30);
            this.Controls.Add(this.label18);
            this.Controls.Add(this.txtTaskDintR);
            this.Controls.Add(this.txtTaskDintW);
            this.Controls.Add(this.label29);
            this.Controls.Add(this.label19);
            this.Controls.Add(this.txtTaskIntR);
            this.Controls.Add(this.txtTaskIntW);
            this.Controls.Add(this.label28);
            this.Controls.Add(this.label20);
            this.Controls.Add(this.txtArrayInt0R);
            this.Controls.Add(this.txtArrayInt0W);
            this.Controls.Add(this.label27);
            this.Controls.Add(this.label11);
            this.Controls.Add(this.txtArrayInt2R);
            this.Controls.Add(this.txtArrayInt2W);
            this.Controls.Add(this.label26);
            this.Controls.Add(this.ArrayInt2);
            this.Controls.Add(this.txtArrayInt1R);
            this.Controls.Add(this.txtArrayInt1W);
            this.Controls.Add(this.label25);
            this.Controls.Add(this.label12);
            this.Controls.Add(this.txtStructStringR);
            this.Controls.Add(this.txtStructStringW);
            this.Controls.Add(this.label24);
            this.Controls.Add(this.label13);
            this.Controls.Add(this.gbPlcTaskData);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.txtStructIntR);
            this.Controls.Add(this.txtStructIntW);
            this.Controls.Add(this.label23);
            this.Controls.Add(this.label10);
            this.Controls.Add(this.txtStructFloatR);
            this.Controls.Add(this.txtStructFloatW);
            this.Controls.Add(this.label22);
            this.Controls.Add(this.label9);
            this.Controls.Add(this.txtStructBoolR);
            this.Controls.Add(this.label21);
            this.Controls.Add(this.txtStructBoolW);
            this.Controls.Add(this.label8);
            this.Name = "MainFrm";
            this.Text = "TestFrom";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainFrm_FormClosing);
            this.Load += new System.EventHandler(this.MainFrm_Load);
            this.tabControl1.ResumeLayout(false);
            this.tpTransferCmd.ResumeLayout(false);
            this.tpTransferCmd.PerformLayout();
            this.gbPlcTaskData.ResumeLayout(false);
            this.gbPlcTaskData.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnTransfer;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtTaskCarrierID;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtTaskSourceAddr;
        private System.Windows.Forms.TextBox txtTaskDestAddr;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tpTransferCmd;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.TabPage tabPage5;
        private System.Windows.Forms.TabPage tabPage6;
        private System.Windows.Forms.GroupBox gbPlcTaskData;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox txtPlcCmdType;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPlcDestAddr;
        private System.Windows.Forms.TextBox txtPlcSourceAddr;
        private System.Windows.Forms.TextBox txtPlcCarrierID;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox txtStructBoolW;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox txtStructFloatW;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox txtStructIntW;
        private System.Windows.Forms.TextBox txtArrayInt0W;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox txtArrayInt1W;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox txtStructStringW;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label ArrayInt2;
        private System.Windows.Forms.TextBox txtArrayInt2W;
        private System.Windows.Forms.TextBox txtTaskDoubleW;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox txtTaskBoolW;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox txtTaskStringW;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.TextBox txtTaskFloatW;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.TextBox txtTaskInt2W;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.TextBox txtTaskDintW;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.TextBox txtTaskIntW;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.TextBox txtStructBoolR;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.TextBox txtStructFloatR;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.TextBox txtStructIntR;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.TextBox txtStructStringR;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.TextBox txtArrayInt1R;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.TextBox txtArrayInt2R;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.TextBox txtArrayInt0R;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.TextBox txtTaskIntR;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.TextBox txtTaskDintR;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.TextBox txtTaskInt2R;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.TextBox txtTaskFloatR;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.TextBox txtTaskStringR;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.TextBox txtTaskBoolR;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.TextBox txtTaskDoubleR;
        private System.Windows.Forms.Button btnWriteData;
        private System.Windows.Forms.Button btnDbTest;
        private System.Windows.Forms.Button btnHsmsStart;
        private System.Windows.Forms.Button start;
    }
}

