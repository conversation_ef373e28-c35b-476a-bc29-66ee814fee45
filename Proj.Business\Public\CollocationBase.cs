﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Proj.Business
{
    public class CollocationBase
    {
        private int color = 0;
        public CollocationBase()
        {
        }
        //报警信息赋值
        public string getBJValue()
        {
            color = 1;
            return "冷却液不足，温度超过预警，请及时处理。";
        }
        //报警信息颜色
        public int getBJColor()
        {
          
            return color;
        }
        //第一条链接状态赋值
        public string getXTValue()
        {
            return "Hellow XT";
        }
        //第二条链接状态赋值
        public string getKZValue()
        {
            return "Hellow KZ";
        }
        //第一条链接状态图片代码
        public int getState1()
        {
            return 1;
        }
        //第二条链接状态图片代码
        public int getState2()
        {
            return 1;
        }
        //指示红灯赋值：1为亮
        public int setRed()
        {
            return 1;
        }
        //指示黄灯赋值：1为亮
        public int setYellow()
        {
            return 1;
        }
        //指示蓝灯赋值：1为亮
        public int setBlue()
        {
            return 1;
        }
        //指示绿灯赋值：1为亮
        public int setGreen()
        {
            return 1;
        }
       

    }
}
