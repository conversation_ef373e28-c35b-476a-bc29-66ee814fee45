﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Proj.Entity;
using Proj.DB;
using Proj.CacheData;
using Proj.DataTypeDef;

namespace Proj.UnitMng
{
    public class PortMng
    {
        private static PortMng m_Instanse;
        private static readonly object mSyncObject = new object();
        private PortMng() { }
        public static PortMng Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new PortMng();
                        }
                    }
                }
                return m_Instanse;
            }
        }
        private List<PortObj> portObjList = new List<PortObj>();

        public bool Initialize()
        {
            bool bRes = false;
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    TpPortViewList tpPortViewList = DbPort.Instance.GetDbPortViewList();
                    //TpPortList portList = DbPort.Instance.GetDbPortList();
                    GlobalData.Instance.gbCurrentPortStates.Clear();
                    GlobalData.Instance.gbCurrentEqPortStatus.Clear();
                    //foreach (TpPort port in portList)
                    foreach (TpPortView port in tpPortViewList)
                    {
                        PortObj portObj = new PortObj(port.No, port.Name, port.ZoneName, (PortUnitType)int.Parse(port.UnitType)
                            , port.IsMport == 1, port.IsIdr == 1, port.Priority, port.EqNumber, port.TagName);
                        //IOPort
                        if (port.EqNumber == 0)
                        {
                            PortInfo portInfo = new PortInfo();
                            portInfo.strPortID = port.Name;

                            //仿真专用
                            if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                            {
                                portInfo.transferState = PortTransferState.InService; 
                            }
                            else
                            {
                                portInfo.transferState = portObj.GetPortTransferState(); //改为从PLC获取数据，使读取准确
                            }
                                
                            GlobalData.Instance.gbCurrentPortStates.Add(portInfo);

                            PortTypeInfo typeInfo = new PortTypeInfo();
                            typeInfo.strPortID = port.Name;

                            //仿真专用
                            if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                            {
                                typeInfo.portUnitType = PortUnitType.None;
                            }
                            else
                            {
                                typeInfo.portUnitType = portObj.GetIOPortUnitType();
                            }
                                
                            GlobalData.Instance.gbPortTypes.Add(typeInfo);

                            if (true != port.TagName.Contains("MR"))
                            {
                                TpPort tpPort = TpPort.GetById(TpPortList.GetByLambda(x => x.Name == port.Name).First().No);
                                SendPortSpeed(port.Name, tpPort.SpeedEmpty, tpPort.SpeedLoad);
                            }
                        }
                        else //EqPort
                        {
                            EqPortInfo eqPortInfo = portObj.GetEqPortInfo();
                            //eqPortInfo.strPortID = port.Name;
                            //eqPortInfo.eqReqStatus = EqReqStatus.ReqOff;                //先默认，需要根据实际的情况进行更新
                            //eqPortInfo.eqPresenceStatus = EqPresenceStatus.NoPresence;  //先默认，需要根据实际的情况进行更新
                            GlobalData.Instance.gbCurrentEqPortStatus.Add(eqPortInfo);
                        }
                        portObjList.Add(portObj);
                        portObj.Start();
                    }
                    bRes = true;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("PortMng.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return bRes;
        }

        public bool UnInit()
        {
            foreach (PortObj port in portObjList)
            {
                port.Stop();
            }
            return true;
        }

        public bool IsPortExit(string strPortID)
        {
            TpPort tpPort = DbPort.Instance.GetPortDbInfoByName(strPortID);
            if (null != tpPort)
            {
                return true;
            }

            return false;
        }

        public void UpdateIOPortTransferState(string portID, PortTransferState state)
        {
            try
            {
                if (DbPort.Instance.UpdatePortTransferState(portID, state))
                {
                    lock (GlobalData.Instance.objRWLock)
                    {
                        foreach (PortInfo port in GlobalData.Instance.gbCurrentPortStates)
                        {
                            if (portID == port.strPortID)
                            {
                                PortInfo portInfo = port;
                                portInfo.transferState = state;
                                return;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("PortMng.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }

        public void UpdateEqPortReqStatus(string portID, EqReqStatus status)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EqPortInfo eqPort in GlobalData.Instance.gbCurrentEqPortStatus)
                {
                    if (portID == eqPort.strPortID)
                    {
                        EqPortInfo eqPortInfo = eqPort;
                        eqPort.eqReqStatus = status;
                        return;
                    }
                }
            }
        }

        public void UpdateEqPortPresenceStatus(string portID, EqPresenceStatus status)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EqPortInfo eqPort in GlobalData.Instance.gbCurrentEqPortStatus)
                {
                    if (portID == eqPort.strPortID)
                    {
                        EqPortInfo eqPortInfo = eqPort;
                        eqPort.eqPresenceStatus = status;
                        return;
                    }
                }
            }
        }

        public void AddPortTypeChgCommand(string portID, PortUnitType unitType)
        {
            foreach (PortObj portObj in portObjList)
            {
                if (portObj.Name == portID)
                {
                    portObj.AddPortTypeChgTask(portID, unitType);
                }
            }
        }

        public IDRResult GetPortIDRResult(string portID)
        {
            IDRResult idrResult = new IDRResult();
            foreach (PortObj portObj in portObjList)
            {
                if (portObj.Name == portID)
                {
                    return portObj.GetPortIDRResult();
                }
            }
            return idrResult;
        }

        public IOPortWorkingStatus GetIOPortWorkingState(string portID)
        {
            foreach (PortObj portObj in portObjList)
            {
                if (portObj.Name == portID)
                {
                    return portObj.PortWorkingState;
                }
            }
            return IOPortWorkingStatus.Init;
        }

        public string GetIOPortNameByAddress(string portAddress)
        {
            foreach (PortObj portObj in portObjList)
            {
                if (portObj.Location == portAddress)
                {
                    return portObj.Name;
                }
            }
            return "";
        }
        public string GetIOAddressByPortName(string PortName)
        {
            foreach (PortObj portObj in portObjList)
            {
                if (portObj.Name == PortName)
                {
                    return portObj.Location;
                }
            }
            return "";
        }
        public string GetIOPortTagNameByAddress(string portAddress)
        {
            foreach (PortObj portObj in portObjList)
            {
                if (portObj.Location == portAddress)
                {
                    return portObj.TagName;
                }
            }
            return "";
        }
        public EqPortInfo GetEQPortInfo(string portID)
        {
            foreach (PortObj portObj in portObjList)
            {
                if (portObj.Name == portID)
                {
                    return portObj.GetEqPortInfo();
                }
            }
            return null;
        }

        public PortUnitType GetIOPortUntType(string portID)
        {
            foreach (PortObj portObj in portObjList)
            {
                if (portObj.Name == portID)
                {
                    return portObj.PortUnitType;
                }
            }
            return PortUnitType.None;
        }

        public bool SendPortSpeed(string portID, int emptySpeed, int storageSpeed)
        {
            try
            {
            int iPortCount = portObjList.Count;
            for (int i = 0; i < iPortCount; i++)
            {
                if (portObjList[i].Name == portID)
                {
                    bool bRet = portObjList[i].SendPortSpeed(emptySpeed, storageSpeed);
                    if (bRet)
                    {
                        TpPort tpPort = TpPort.GetById(TpPortList.GetByLambda(x => x.Name == portID).First().No);
                        tpPort.SpeedEmpty = emptySpeed;
                        tpPort.SpeedLoad = storageSpeed;
                        tpPort.Save();
						return bRet;
                    }
                }
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("PortMng.cs:SendPortSpeed " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }

            return false;
        }

        public bool ResetPortAlarm(string strPortName)
        {
            bool bResult = false;
            int iPortCount = portObjList.Count;
            for (int i = 0; i < iPortCount; i++)
            {
                if (portObjList[i].Name == strPortName)
                {
                    bResult = portObjList[i].ResetAlarm();
                }
            }
            if (bResult)
            {
                Alarm.AlarmController.Instance.ToMCSClearAlarm("", strPortName);
                return Alarm.AlarmController.Instance.ClearAlarmByUnit(strPortName);
            }
            return false;
        }

        public bool EnablePort(string strPortName)
        {
            int iPortCount = portObjList.Count;
            for (int i = 0; i < iPortCount; i++)
            {
                if (portObjList[i].Name == strPortName)
                {
                    portObjList[i].PortInService();
                    return true;
                }
            }
            return false;
        }

        public bool DisablePort(string strPortName)
        {
            int iPortCount = portObjList.Count;
            for (int i = 0; i < iPortCount; i++)
            {
                if (portObjList[i].Name == strPortName)
                {
                    portObjList[i].PortOutOfService();
                    return true;
                }
            }
            return false;
        }

        public TpPortList GetAllInPortList()
        {
            TpPortList portList = DbPort.Instance.GetDbPortList();
            foreach(TpPort tpPort in DbPort.Instance.GetDbPortList())
            {
                if(!tpPort.UnitType.Equals("0"))
                {
                    portList.Remove(tpPort);
                }
            }

            return portList;
        }

        public InPortCSTIDTime GetInPortCSTIDTime(string strPortName)
        {
            InPortCSTIDTime stInPortCSTIDTime = new InPortCSTIDTime();

            int iPortCount = portObjList.Count;
            for (int i = 0; i < iPortCount; i++)
            {
                if (portObjList[i].Name == strPortName)
                {
                    stInPortCSTIDTime = portObjList[i].stInPortCSTIDTime;
                }
            }

            return stInPortCSTIDTime;
        }

        public bool SetInPortCSTIDTime(string strPortName)
        {
            int iPortCount = portObjList.Count;
            for (int i = 0; i < iPortCount; i++)
            {
                if (portObjList[i].Name == strPortName)
                {
                    portObjList[i].stInPortCSTIDTime.bInputFlag = false;
                    portObjList[i].stInPortCSTIDTime.strCasstileID = null;
                    portObjList[i].stInPortCSTIDTime.bIsReadSuccess = true;
    }
            }

            return true;
        }
    }
}
