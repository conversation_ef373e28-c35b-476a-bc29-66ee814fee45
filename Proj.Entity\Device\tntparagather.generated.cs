﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntParagather and List
    [Serializable]
    [Description("设备变量配置")]
    [LinqToDB.Mapping.Table("Tnt_ParaGather")]
    public partial class TntParagather : GEntity<TntParagather>, ITimestamp
    {
        #region Contructor(s)

        private TntParagather()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CParagatherId = RegisterProperty<String>(p => p.CParagatherId);
        private static readonly PropertyInfo<String> pty_CParameterCode = RegisterProperty<String>(p => p.CParameterCode);
        private static readonly PropertyInfo<String> pty_CParameterName = RegisterProperty<String>(p => p.CParameterName);
        private static readonly PropertyInfo<String> pty_CParameterType = RegisterProperty<String>(p => p.CParameterType);
        private static readonly PropertyInfo<String> pty_CParameterup = RegisterProperty<String>(p => p.CParameterup);
        private static readonly PropertyInfo<String> pty_CParameterlow = RegisterProperty<String>(p => p.CParameterlow);
        private static readonly PropertyInfo<String> pty_CParameterdef = RegisterProperty<String>(p => p.CParameterdef);
        private static readonly PropertyInfo<String> pty_CTransmode = RegisterProperty<String>(p => p.CTransmode);
        private static readonly PropertyInfo<String> pty_CTransdate = RegisterProperty<String>(p => p.CTransdate);
        private static readonly PropertyInfo<String> pty_CDeviceId = RegisterProperty<String>(p => p.CDeviceId);
        private static readonly PropertyInfo<String> pty_CDescribe = RegisterProperty<String>(p => p.CDescribe);
        private static readonly PropertyInfo<String> pty_CAccessType = RegisterProperty<String>(p => p.CAccessType);
        private static readonly PropertyInfo<String> pty_CNote = RegisterProperty<String>(p => p.CNote);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion


        /// <summary>
        /// 变量主键
        /// </summary>
        [Description("变量主键")]
        [LinqToDB.Mapping.Column("c_ParaGatherId")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CParagatherId
        {
            get { return GetProperty(pty_CParagatherId); }
            set { SetProperty(pty_CParagatherId, value); }
        }
        /// <summary>
        /// 变量编码
        /// </summary>
        [Description("变量编码")]
        [LinqToDB.Mapping.Column("c_ParaMeterCode")]
        public String CParameterCode
        {
            get { return GetProperty(pty_CParameterCode); }
            set { SetProperty(pty_CParameterCode, value); }
        }
        /// <summary>
        /// 变量名称
        /// </summary>
        [Description("变量名称")]
        [LinqToDB.Mapping.Column("c_ParaMeterName")]
        public String CParameterName
        {
            get { return GetProperty(pty_CParameterName); }
            set { SetProperty(pty_CParameterName, value); }
        }
        /// <summary>
        /// 变量类型
        /// </summary>
        [Description("变量类型")]
        [LinqToDB.Mapping.Column("c_ParaMeterType")]
        public String CParameterType
        {
            get { return GetProperty(pty_CParameterType); }
            set { SetProperty(pty_CParameterType, value); }
        }
        /// <summary>
        /// 变量上限
        /// </summary>
        [Description("变量上限")]
        [LinqToDB.Mapping.Column("c_ParaMeterUp")]
        public String CParameterup
        {
            get { return GetProperty(pty_CParameterup); }
            set { SetProperty(pty_CParameterup, value); }
        }
        /// <summary>
        /// 变量下限
        /// </summary>
        [Description("变量下限")]
        [LinqToDB.Mapping.Column("c_ParaMeterLow")]
        public String CParameterlow
        {
            get { return GetProperty(pty_CParameterlow); }
            set { SetProperty(pty_CParameterlow, value); }
        }
        /// <summary>
        /// 变量默认值
        /// </summary>
        [Description("变量默认值")]
        [LinqToDB.Mapping.Column("c_ParaMeterDef")]
        public String CParameterdef
        {
            get { return GetProperty(pty_CParameterdef); }
            set { SetProperty(pty_CParameterdef, value); }
        }
        /// <summary>
        /// 传输方式
        /// </summary>
        [Description("传输方式")]
        [LinqToDB.Mapping.Column("c_TransMode")]
        public String CTransmode
        {
            get { return GetProperty(pty_CTransmode); }
            set { SetProperty(pty_CTransmode, value); }
        }
        /// <summary>
        /// 传输周期
        /// </summary>
        [Description("传输周期")]
        [LinqToDB.Mapping.Column("c_TransDate")]
        public String CTransdate
        {
            get { return GetProperty(pty_CTransdate); }
            set { SetProperty(pty_CTransdate, value); }
        }
        /// <summary>
        /// 设备ID
        /// </summary>
        [Description("设备ID")]
        [LinqToDB.Mapping.Column("c_DeviceId")]
        public String CDeviceId
        {
            get { return GetProperty(pty_CDeviceId); }
            set { SetProperty(pty_CDeviceId, value); }
        }
        /// <summary>
        /// 描述
        /// </summary>
        [Description("描述")]
        [LinqToDB.Mapping.Column("c_Describe")]
        public String CDescribe
        {
            get { return GetProperty(pty_CDescribe); }
            set { SetProperty(pty_CDescribe, value); }
        }
        /// <summary>
        /// 访问类型
        /// </summary>
        [Description("访问类型")]
        [LinqToDB.Mapping.Column("c_AccessType")]
        public String CAccessType
        {
            get { return GetProperty(pty_CAccessType); }
            set { SetProperty(pty_CAccessType, value); }
        }
        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        [LinqToDB.Mapping.Column("c_Note")]
        public String CNote
        {
            get { return GetProperty(pty_CNote); }
            set { SetProperty(pty_CNote, value); }
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CParagatherId, "变量主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParagatherId, 36, "变量主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterCode, 30, "变量编码不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterName, 40, "变量名称不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterType, 40, "变量类型不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterup, 40, "变量上限不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterlow, 40, "变量下限不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParameterdef, 40, "变量默认值不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CTransmode, 2, "传输方式不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CTransdate, 10, "传输周期不能超过10个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeviceId, 30, "设备ID不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDescribe, 100, "描述不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CAccessType, 2, "访问类型不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CNote, 100, "备注不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CParagatherId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntParagatherList : GEntityList<TntParagatherList, TntParagather>
    {
        private TntParagatherList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
