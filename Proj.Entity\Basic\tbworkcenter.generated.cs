﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbWorkcenter and List
    [Serializable]
    [Description("工作中心信息表")]
    [LinqToDB.Mapping.Table("TB_WORKCENTER")]
    public partial class TbWorkcenter : GEntity<TbWorkcenter>, ITimestamp
    {
        #region Contructor(s)

        private TbWorkcenter()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CWorkcenterId = RegisterProperty<String>(p => p.CWorkcenterId);
        private static readonly PropertyInfo<String> pty_CWorkcenterName = RegisterProperty<String>(p => p.CWorkcenterName);
        private static readonly PropertyInfo<String> pty_CWorkcenterDes = RegisterProperty<String>(p => p.CWorkcenterDes);
        private static readonly PropertyInfo<String> pty_CWorkernum = RegisterProperty<String>(p => p.CWorkernum);
        private static readonly PropertyInfo<String> pty_CWorkhours = RegisterProperty<String>(p => p.CWorkhours);
        private static readonly PropertyInfo<String> pty_CWorkType = RegisterProperty<String>(p => p.CWorkType);
        private static readonly PropertyInfo<String> pty_CProductionlineId = RegisterProperty<String>(p => p.CProductionlineId);
        private static readonly PropertyInfo<String> pty_CProductionlineName = RegisterProperty<String>(p => p.CProductionlineName);
        private static readonly PropertyInfo<String> pty_CWorkshopId = RegisterProperty<String>(p => p.CWorkshopId);
        private static readonly PropertyInfo<String> pty_CWorkshopName = RegisterProperty<String>(p => p.CWorkshopName);
        private static readonly PropertyInfo<String> pty_CFactoryId = RegisterProperty<String>(p => p.CFactoryId);
        private static readonly PropertyInfo<String> pty_CFactoryDes = RegisterProperty<String>(p => p.CFactoryDes);
        private static readonly PropertyInfo<String> pty_CWorkshopType = RegisterProperty<String>(p => p.CWorkshopType);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        private static readonly PropertyInfo<String> pty_CExtendfieldd = RegisterProperty<String>(p => p.CExtendfieldd);
        #endregion

        /// <summary>
        /// 工作中心ID
        /// </summary>
        [Description("工作中心ID")]
        [LinqToDB.Mapping.Column("C_WORKCENTERID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CWorkcenterId
        {
            get { return GetProperty(pty_CWorkcenterId); }
            set { SetProperty(pty_CWorkcenterId, value); }
        }
        /// <summary>
        /// 工作中心名称
        /// </summary>
        [Description("工作中心名称")]
        [LinqToDB.Mapping.Column("C_WORKCENTERNAME")]
        public String CWorkcenterName
        {
            get { return GetProperty(pty_CWorkcenterName); }
            set { SetProperty(pty_CWorkcenterName, value); }
        }
        /// <summary>
        /// 工作中心描述
        /// </summary>
        [Description("工作中心描述")]
        [LinqToDB.Mapping.Column("C_WORKCENTERDES")]
        public String CWorkcenterDes
        {
            get { return GetProperty(pty_CWorkcenterDes); }
            set { SetProperty(pty_CWorkcenterDes, value); }
        }
        /// <summary>
        /// 作业人员数量
        /// </summary>
        [Description("作业人员数量")]
        [LinqToDB.Mapping.Column("C_WORKERNUM")]
        public String CWorkernum
        {
            get { return GetProperty(pty_CWorkernum); }
            set { SetProperty(pty_CWorkernum, value); }
        }
        /// <summary>
        /// 标准工时
        /// </summary>
        [Description("标准工时")]
        [LinqToDB.Mapping.Column("C_WORKHOURS")]
        public String CWorkhours
        {
            get { return GetProperty(pty_CWorkhours); }
            set { SetProperty(pty_CWorkhours, value); }
        }
        /// <summary>
        /// 生产类型
        /// </summary>
        [Description("生产类型")]
        [LinqToDB.Mapping.Column("C_WORKTYPE")]
        public String CWorkType
        {
            get { return GetProperty(pty_CWorkType); }
            set { SetProperty(pty_CWorkType, value); }
        }
        /// <summary>
        /// 所属产线ID
        /// </summary>
        [Description("所属产线ID")]
        [LinqToDB.Mapping.Column("C_PRODUCTIONLINEID")]
        public String CProductionlineId
        {
            get { return GetProperty(pty_CProductionlineId); }
            set { SetProperty(pty_CProductionlineId, value); }
        }
        /// <summary>
        /// 所属产线名称
        /// </summary>
        [Description("所属产线名称")]
        [LinqToDB.Mapping.Column("C_PRODUCTIONLINENAME")]
        public String CProductionlineName
        {
            get { return GetProperty(pty_CProductionlineName); }
            set { SetProperty(pty_CProductionlineName, value); }
        }
        /// <summary>
        /// 所属车间ID
        /// </summary>
        [Description("所属车间ID")]
        [LinqToDB.Mapping.Column("C_WORKSHOPID")]
        public String CWorkshopId
        {
            get { return GetProperty(pty_CWorkshopId); }
            set { SetProperty(pty_CWorkshopId, value); }
        }
        /// <summary>
        /// 所属车间名称
        /// </summary>
        [Description("所属车间名称")]
        [LinqToDB.Mapping.Column("C_WORKSHOPNAME")]
        public String CWorkshopName
        {
            get { return GetProperty(pty_CWorkshopName); }
            set { SetProperty(pty_CWorkshopName, value); }
        }
        /// <summary>
        /// 所属工厂ID
        /// </summary>
        [Description("所属工厂ID")]
        [LinqToDB.Mapping.Column("C_FACTORYID")]
        public String CFactoryId
        {
            get { return GetProperty(pty_CFactoryId); }
            set { SetProperty(pty_CFactoryId, value); }
        }
        /// <summary>
        /// 所属工厂名称
        /// </summary>
        [Description("所属工厂名称")]
        [LinqToDB.Mapping.Column("C_FACTORYDES")]
        public String CFactoryDes
        {
            get { return GetProperty(pty_CFactoryDes); }
            set { SetProperty(pty_CFactoryDes, value); }
        }
        /// <summary>
        /// 所属车间类别
        /// </summary>
        [Description("所属车间类别")]
        [LinqToDB.Mapping.Column("C_WORKSHOP_TYPE")]
        public String CWorkshopType
        {
            get { return GetProperty(pty_CWorkshopType); }
            set { SetProperty(pty_CWorkshopType, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        /// <summary>
        /// 扩展字段D
        /// </summary>
        [Description("扩展字段D")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDD")]
        public String CExtendfieldd
        {
            get { return GetProperty(pty_CExtendfieldd); }
            set { SetProperty(pty_CExtendfieldd, value); }
        }
        #endregion


        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CWorkcenterId, "工作中心ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkcenterId, 40, "工作中心ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkcenterName, 160, "工作中心名称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkcenterDes, 400, "工作中心描述不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkernum, 10, "作业人员数量不能超过10个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkhours, 10, "标准工时不能超过10个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkType, 2, "生产类型不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProductionlineId, 40, "所属产线ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProductionlineName, 160, "所属产线名称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkshopId, 40, "所属车间ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkshopName, 160, "所属车间名称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryId, 20, "所属工厂ID不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryDes, 200, "所属工厂名称不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkshopType, 2, "所属车间类别不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldd, 80, "扩展字段D不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CWorkcenterId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbWorkcenterList : GEntityList<TbWorkcenterList, TbWorkcenter>
    {
        private TbWorkcenterList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
