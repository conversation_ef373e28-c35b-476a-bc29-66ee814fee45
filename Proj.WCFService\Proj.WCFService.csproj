﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Proj.CacheData\Proj.CacheData.csproj" />
    <ProjectReference Include="..\Proj.Common\Proj.Common.csproj" />
    <ProjectReference Include="..\Proj.DataTypeDef\Proj.DataTypeDef.csproj" />
    <ProjectReference Include="..\Proj.Log\Proj.Log.csproj" />
  </ItemGroup>

</Project>
