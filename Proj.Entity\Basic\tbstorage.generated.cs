﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;


 
namespace Proj.Entity
{
    #region TbStorage and List
    [Serializable]
    [Description("库房信息表")]
    [LinqToDB.Mapping.Table("TB_STORAGE")]
    public partial class TbStorage : GEntity<TbStorage>, ITimestamp
    {
        #region Contructor(s)

        private TbStorage()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CStorageId = RegisterProperty<String>(p => p.CStorageId);
        private static readonly PropertyInfo<String> pty_CStorageDes = RegisterProperty<String>(p => p.CStorageDes);
        private static readonly PropertyInfo<String> pty_CFactoryId = RegisterProperty<String>(p => p.CFactoryId);
        private static readonly PropertyInfo<String> pty_CFactoryDes = RegisterProperty<String>(p => p.CFactoryDes);
        private static readonly PropertyInfo<String> pty_CStorageType = RegisterProperty<String>(p => p.CStorageType);
        private static readonly PropertyInfo<String> pty_CNegativeflag = RegisterProperty<String>(p => p.CNegativeflag);
        private static readonly PropertyInfo<String> pty_CManageflag = RegisterProperty<String>(p => p.CManageflag);
        private static readonly PropertyInfo<String> pty_CLockflag = RegisterProperty<String>(p => p.CLockflag);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 库房ID
        /// </summary>
        [Description("库房ID")]
        [LinqToDB.Mapping.Column("C_STORAGEID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CStorageId
        {
            get { return GetProperty(pty_CStorageId); }
            set { SetProperty(pty_CStorageId, value); }
        }
        /// <summary>
        /// 库房描述
        /// </summary>
        [Description("库房描述")]
        [LinqToDB.Mapping.Column("C_STORAGEDES")]
        public String CStorageDes
        {
            get { return GetProperty(pty_CStorageDes); }
            set { SetProperty(pty_CStorageDes, value); }
        }
        /// <summary>
        /// 工厂ID
        /// </summary>
        [Description("工厂ID")]
        [LinqToDB.Mapping.Column("C_FACTORYID")]
        public String CFactoryId
        {
            get { return GetProperty(pty_CFactoryId); }
            set { SetProperty(pty_CFactoryId, value); }
        }
        /// <summary>
        /// 工厂描述
        /// </summary>
        [Description("工厂描述")]
        [LinqToDB.Mapping.Column("C_FACTORYDES")]
        public String CFactoryDes
        {
            get { return GetProperty(pty_CFactoryDes); }
            set { SetProperty(pty_CFactoryDes, value); }
        }
        /// <summary>
        /// 库房类别
        /// </summary>
        [Description("库房类别")]
        [LinqToDB.Mapping.Column("C_STORAGE_TYPE")]
        public String CStorageType
        {
            get { return GetProperty(pty_CStorageType); }
            set { SetProperty(pty_CStorageType, value); }
        }
        /// <summary>
        /// 允许负库存
        /// </summary>
        [Description("允许负库存")]
        [LinqToDB.Mapping.Column("C_NEGATIVEFLAG")]
        public String CNegativeflag
        {
            get { return GetProperty(pty_CNegativeflag); }
            set { SetProperty(pty_CNegativeflag, value); }
        }
        /// <summary>
        /// 管理库存标识
        /// </summary>
        [Description("管理库存标识")]
        [LinqToDB.Mapping.Column("C_MANAGEFLAG")]
        public String CManageflag
        {
            get { return GetProperty(pty_CManageflag); }
            set { SetProperty(pty_CManageflag, value); }
        }
        /// <summary>
        /// 锁库标识
        /// </summary>
        [Description("锁库标识")]
        [LinqToDB.Mapping.Column("C_LOCKFLAG")]
        public String CLockflag
        {
            get { return GetProperty(pty_CLockflag); }
            set { SetProperty(pty_CLockflag, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }

      
     
        
        #endregion 
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CStorageId, "库房ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CStorageId, 40, "库房ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CStorageDes, 160, "库房描述不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryId, 40, "工厂ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryDes, 160, "工厂描述不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CStorageType, 2, "库房类别不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CNegativeflag, 2, "允许负库存不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CManageflag, 2, "管理库存标识不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLockflag, 2, "锁库标识不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CStorageId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbStorageList : GEntityList<TbStorageList, TbStorage>
    {
        private TbStorageList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
