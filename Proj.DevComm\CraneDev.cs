﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Proj.DataTypeDef;
using Proj.History;
using System.Threading;

namespace Proj.DevComm
{
    public class CraneDev
    {
        private static CraneDev m_Instanse;
        private static readonly object mSyncObject = new object();
        private CraneDev() { }
        public static CraneDev Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new CraneDev();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        private string preSetCraneTag = "Crane.h2sCraneSet";
        private string preCraneStatus = "Crane.s2hCraneStatus";
        private string preAxisUsage = "Crane.s2hAxisUsage";
        private string preAxisStatus = "Crane.s2hAxisStatus";
        private string preAxisServo = "Crane.s2hAxisServo";
        /// <summary>
        /// 向PLC发送Crane控制命令
        /// </summary>
        /// <param name="taskData">TaskType:1：Transfer, 2：Scan, 3：Pick，4：Place，5：Move，6：Home</param>
        /// <param name="taskData">CarrierID:盒子ID</param>
        /// <param name="taskData">SourceAddress:源位置</param>
        /// <param name="taskData">DestAddress:目的地址</param>
        /// <returns></returns>
        public bool SendCraneCommand(string strCraneTag, CraneTaskData taskData)
        {
            //任务类型值检查
            if (taskData.taskType < CraneTaskType.Transfer || taskData.taskType > CraneTaskType.DoubleCheck)
            {
                //记录Crane日志：SendCraneCommand Failed: TaskType = 
                return false;
            }

            string craneTag = preSetCraneTag + strCraneTag;

            //根据任务类型进行参数的检查，填充任务参数
            List<KeyValuePair<string, object>> keyValues = new List<KeyValuePair<string, object>>();
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".TaskType", (int)taskData.taskType));
            switch (taskData.taskType)
            {
                case CraneTaskType.Transfer: //任务参数：CarrierID，SourceAddr，DestAddr
                    {
                        if (string.IsNullOrEmpty(taskData.strCarrierID))
                        {
                            //记录Crane日志：SendCraneCommand Failed: CarrierID IsNullOrEmpty in Transfer Cmd
                            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommandFailed", "CarrierID IsNullOrEmpty in Transfer Cmd");
                            return false;
                        }
                        keyValues.Add(new KeyValuePair<string, object>(craneTag + ".CSTID", taskData.strCarrierID));
                        if (string.IsNullOrEmpty(taskData.strSourceAddr))
                        {
                            //记录Crane日志：SendCraneCommand Failed: SourceAddr IsNullOrEmpty in Transfer Cmd
                            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommandFailed", "SourceAddr IsNullOrEmpty in Transfer Cmd");
                            return false;
                        }
                        if (string.IsNullOrEmpty(taskData.strDestAddr))
                        {
                            //记录Crane日志：SendCraneCommand Failed: DestAddr IsNullOrEmpty in Transfer Cmd
                            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommandFailed", "DestAddr IsNullOrEmpty in Transfer Cmd");
                            return false;
                        }

                        try
                        {
                            int[] srcAddr = new int[3] 
                            { 
                                int.Parse(taskData.strSourceAddr.Substring(0, 1)), 
                                int.Parse(taskData.strSourceAddr.Substring(1, 2)), 
                                int.Parse(taskData.strSourceAddr.Substring(3, 2))
                            };
                            int[] destAddr = new int[3]
                            { 
                                int.Parse(taskData.strDestAddr.Substring(0, 1)), 
                                int.Parse(taskData.strDestAddr.Substring(1, 2)), 
                                int.Parse(taskData.strDestAddr.Substring(3, 2))
                            };
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceBank", srcAddr[0]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceBay", srcAddr[1]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceLevel", srcAddr[2]));

                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestBank", destAddr[0]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestBay", destAddr[1]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestLevel", destAddr[2]));
                        }
                        catch(Exception ex)
                        {
                            //记录程序异常日志:SendCraneCommand Failed: {ex.Message}
                            Log.Logger.Instance.ExceptionLog("CraneDev.cs:" + ex.Message + ", Stack: " + ex.StackTrace);
                            return false;
                        }
                    }
                    break;
                case CraneTaskType.Scan: //任务参数：CarrierID，SourceAddr
                    {
                        if (!string.IsNullOrEmpty(taskData.strCarrierID))
                        {
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".CSTID", taskData.strCarrierID));
                        }

                        if (string.IsNullOrEmpty(taskData.strSourceAddr))
                        {
                            //记录Crane日志：SendCraneCommand Failed: SourceAddr IsNullOrEmpty in Scan Cmd
                            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommandFailed", "SourceAddr IsNullOrEmpty in Scan Cmd");
                            return false;
                        }

                        try
                        {
                            int[] srcAddr = new int[3] 
                            { 
                                int.Parse(taskData.strSourceAddr.Substring(0, 1)), 
                                int.Parse(taskData.strSourceAddr.Substring(1, 2)), 
                                int.Parse(taskData.strSourceAddr.Substring(3, 2))
                            };
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceBank", srcAddr[0]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceBay", srcAddr[1]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceLevel", srcAddr[2]));
                            int[] destAddr = new int[3]
                            {
                                int.Parse(taskData.strDestAddr.Substring(0, 1)),
                                int.Parse(taskData.strDestAddr.Substring(1, 2)),
                                int.Parse(taskData.strDestAddr.Substring(3, 2))
                             };
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestBank", destAddr[0]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestBay", destAddr[1]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestLevel", destAddr[2]));
                        }
                        catch (Exception ex)
                        {
                            //记录程序异常日志:SendCraneCommand Failed: {ex.Message}
                            Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                            return false;
                        }
                    }
                    break;
                case CraneTaskType.Pick: //任务参数：CarrierID，SourceAddr
                    {
                        if (string.IsNullOrEmpty(taskData.strCarrierID))
                        {
                            //记录Crane日志：SendCraneCommand Failed: CarrierID IsNullOrEmpty in Pick Cmd
                            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommandFailed", "CarrierID IsNullOrEmpty in Pick Cmd");
                            return false;
                        }
                        keyValues.Add(new KeyValuePair<string, object>(craneTag + ".CSTID", taskData.strCarrierID));

                        if (string.IsNullOrEmpty(taskData.strSourceAddr))
                        {
                            //记录Crane日志：SendCraneCommand Failed: SourceAddr IsNullOrEmpty in Pick Cmd
                            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommandFailed", "SourceAddr IsNullOrEmpty in Pick Cmd");
                            return false;
                        }

                        try
                        {
                            int[] srcAddr = new int[3] 
                            { 
                                int.Parse(taskData.strSourceAddr.Substring(0, 1)), 
                                int.Parse(taskData.strSourceAddr.Substring(1, 2)), 
                                int.Parse(taskData.strSourceAddr.Substring(3, 2))
                            };
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceBank", srcAddr[0]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceBay", srcAddr[1]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceLevel", srcAddr[2]));
                        }
                        catch (Exception ex)
                        {
                            //记录程序异常日志:SendCraneCommand Failed: {ex.Message}
                            Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                            return false;
                        }
                    }
                    break;
                case CraneTaskType.Place: //任务参数：CarrierID，DestAddr
                case CraneTaskType.DoubleCheck:
                    {
                        //CarrierID是否有用？ lzt
                        if (string.IsNullOrEmpty(taskData.strCarrierID))
                        {
                            //记录Crane日志：SendCraneCommand Failed: CarrierID IsNullOrEmpty in Place Cmd
                            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommandFailed", "CarrierID IsNullOrEmpty in Place Cmd");
                            return false;
                        }
                        keyValues.Add(new KeyValuePair<string, object>(craneTag + ".CSTID", taskData.strCarrierID));

                        if (string.IsNullOrEmpty(taskData.strDestAddr))
                        {
                            //记录Crane日志：SendCraneCommand Failed: DestAddr IsNullOrEmpty in Place Cmd
                            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommandFailed", "DestAddr IsNullOrEmpty in Place Cmd");
                            return false;
                        }

                        try
                        {
                            int[] destAddr = new int[3]
                            { 
                                int.Parse(taskData.strDestAddr.Substring(0, 1)), 
                                int.Parse(taskData.strDestAddr.Substring(1, 2)), 
                                int.Parse(taskData.strDestAddr.Substring(3, 2))
                            };

                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestBank", destAddr[0]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestBay", destAddr[1]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestLevel", destAddr[2]));
                        }
                        catch (Exception ex)
                        {
                            //记录程序异常日志:SendCraneCommand Failed: {ex.Message}
                            Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                            return false;
                        }
                    }
                    break;
                case CraneTaskType.Move: //任务参数：SourceAddr
                    {
                        if (string.IsNullOrEmpty(taskData.strSourceAddr))
                        {
                            //记录Crane日志：SendCraneCommand Failed: SourceAddr IsNullOrEmpty in Move Cmd
                            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommandFailed", "SourceAddr IsNullOrEmpty in Move Cmd");
                            return false;
                        }

                        try
                        {
                            int[] srcAddr = new int[3] 
                            { 
                                int.Parse(taskData.strSourceAddr.Substring(0, 1)), 
                                int.Parse(taskData.strSourceAddr.Substring(1, 2)), 
                                int.Parse(taskData.strSourceAddr.Substring(3, 2))
                            };
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceBank", srcAddr[0]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceBay", srcAddr[1]));
                            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceLevel", srcAddr[2]));
                        }
                        catch (Exception ex)
                        {
                            //记录程序异常日志:SendCraneCommand Failed: {ex.Message}
                            Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                            return false;
                        }
                    }
                    break;
                case CraneTaskType.Home: //任务参数：无
                    break;
                default:
                    {
                        //记录Crane日志：SendCraneCommand Failed: TaskType Undefined
                        History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommandFailed", "TaskType Undefined");
                        return false;
                    }
                    //break;
            }
            bool bRes = false;

            CraneServoOn(strCraneTag);
            
            //bRes = PlcComm.Instance.WriteIOValue(keyValues);
            bRes = PlcComm.Instance.WriteTagValue(keyValues);
            //记录Crane日志：Is SendCraneCommand Succeeded：{bRes}，TaskData:{taskData.taskType, taskData.strCarrierID, taskData.strSourceAddr, taskData.strDestAddr}
            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "SendCraneCommand", 
                $"Is SendCraneCommand Succeeded：{bRes}，TaskData:{taskData.taskType}, {taskData.strCarrierID}, {taskData.strSourceAddr}, {taskData.strDestAddr}");

            if (bRes && CraneTaskType.Scan != taskData.taskType)
            {
                int[] srcAddr = new int[3]
                           {
                                int.Parse(taskData.strSourceAddr.Substring(0, 1)),
                                int.Parse(taskData.strSourceAddr.Substring(1, 2)),
                                int.Parse(taskData.strSourceAddr.Substring(3, 2))
                           };
                int[] destAddr = new int[3]
                {
                                int.Parse(taskData.strDestAddr.Substring(0, 1)),
                                int.Parse(taskData.strDestAddr.Substring(1, 2)),
                                int.Parse(taskData.strDestAddr.Substring(3, 2))
                };

                for (int iLoop = 0; iLoop < 10; iLoop++)
                {
                    Thread.Sleep(200);
                    //CraneTaskData outTaskData = GetCraneCurrTaskData(strCraneTag);

                    int[] srcOutAddr = new int[3]
                           {
                                int.Parse(taskData.strSourceAddr.Substring(0, 1)),
                                int.Parse(taskData.strSourceAddr.Substring(1, 2)),
                                int.Parse(taskData.strSourceAddr.Substring(3, 2))
                           };
                    int[] destOutAddr = new int[3]
                    {
                                int.Parse(taskData.strDestAddr.Substring(0, 1)),
                                int.Parse(taskData.strDestAddr.Substring(1, 2)),
                                int.Parse(taskData.strDestAddr.Substring(3, 2))
                    };

                    switch (taskData.taskType)
                    {
                        case CraneTaskType.Transfer: //任务参数：CarrierID，SourceAddr，DestAddr
                            {
                                if (srcAddr[0] != srcOutAddr[0] || srcAddr[1] != srcOutAddr[1] || srcAddr[2] != srcOutAddr[2] ||
                                    destAddr[0] != destOutAddr[0] || destAddr[1] != destOutAddr[1] ||destAddr[2] != destOutAddr[2])
                                {
                                    if (9 == iLoop)
                                    {
                                        return false;
                                    }
                                    else
                                    {
                                        continue;
                                    }
                                }
                                else
                                {
                                    break;
                                }
                            }
                        case CraneTaskType.Scan: //任务参数：CarrierID，SourceAddr
                        case CraneTaskType.Move: //任务参数：SourceAddr
                        case CraneTaskType.Pick: //任务参数：CarrierID，SourceAddr
                            {
                                if (srcAddr[0] != srcOutAddr[0] || srcAddr[1] != srcOutAddr[1] || srcAddr[2] != srcOutAddr[2])
                                {
                                    if (9 == iLoop)
                                    {
                                        return false;
                                    }
                                    else
                                    {
                                        continue;
                                    }
                                }
                                else
                                {
                                    break;
                                }
                            }
                        case CraneTaskType.Place: //任务参数：CarrierID，DestAddr
                            {
                                if (destAddr[0] != destOutAddr[0] || destAddr[1] != destOutAddr[1] || destAddr[2] != destOutAddr[2])
                                {
                                    if (9 == iLoop)
                                    {
                                        return false;
                                    }
                                    else
                                    {
                                        continue;
                                    }
                                }

                                else
                                {
                                    break;
                                }
                            }
                        default:
                            {
                                if (9 == iLoop)
                                {
                                    return false;
                                }
                                else
                                {
                                    continue;
                                }
                            }
                    }
                }
            }
            return bRes;
        }
        /// <summary>
        /// 清空PLC中Crane的任务数据
        /// </summary>
        /// <returns></returns>
        public bool CleanCraneCommand(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            List<KeyValuePair<string, object>> keyValues = new List<KeyValuePair<string, object>>();

            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".TaskType", 0));
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".CSTID", ""));

            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceBank", 0));
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceBay", 0));
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".SourceLevel", 0));

            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestBank", 0));
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestBay", 0));
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".DestLevel", 0));

            bool bRes = false;
            //bRes = PlcComm.Instance.WriteIOValue(keyValues);
            bRes = PlcComm.Instance.WriteTagValue(keyValues);
            //记录Crane日志：Is CleanCraneCommand Succeeded：{bRes}
            History.HistoryWriter.Instance.EqpEventLog("Crane", 0, "CleanCraneCommand", $"Is CleanCraneCommand Succeeded：{bRes}");
            return bRes;
        }
        /// <summary>
        /// 对Crane任务的中止完成进行答复
        /// </summary>
        /// <returns></returns>
        public bool CraneAbortCompleteAck(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".AbortCompleteAck", 1);
            if (bRes)
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "AbortCompleteAck", "AbortCompleteAck 1 success");
            }
            else
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "AbortCompleteAck", "AbortCompleteAck 1 failed");
            }
            return bRes;
        }
        /// <summary>
        /// 对Crane任务的中止完成的答复进行复位
        /// </summary>
        /// <returns></returns>
        public bool ResetCraneAbortCompleteAck(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".AbortCompleteAck", 0);
            if (bRes)
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "AbortCompleteAck", "AbortCompleteAck 0 success");
            }
            else
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "AbortCompleteAck", "AbortCompleteAck 0 failed");
            }
            return bRes;
        }
        /// <summary>
        /// 对Crane任务的正常完成进行答复
        /// </summary>
        /// <returns></returns>
        public bool CraneTaskCompleteAck(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".TaskCompleteAck", 1);
            if(bRes)
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "TaskCompleteAck", "TaskCompleteAck 1 success");
            }
            else
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "TaskCompleteAck", "TaskCompleteAck 1 failed");
            }
            //记录Crane日志：Is AckCraneTaskComplete Succeeded：{bRes}
            return bRes;
        }
        /// <summary>
        /// 对Crane任务的正常完成的答复进行复位
        /// </summary>
        /// <returns></returns>
        public bool ResetCraneTaskCompleteAck(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".TaskCompleteAck", 0);
            if (bRes)
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "TaskCompleteAck", "TaskCompleteAck 0 success");
            }
            else
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "TaskCompleteAck", "TaskCompleteAck 0 failed");
            }
            return bRes;
        }
        public int GetTaskCompleteAck(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            int iRes = (int)PlcComm.Instance.GetTagValue(craneTag + ".TaskCompleteAck");
            return iRes;
        }
        /// <summary>
        /// 控制Crane开始执行任务
        /// </summary>
        /// <returns></returns>
        public bool CraneTaskStart(string strCraneTag)
        {
            //bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".TaskStart", 1);
            bool bRes = ControlCraneTaskAction(strCraneTag, CraneAction.Start);
            //记录Crane日志：Is StartCraneTask Succeeded：{bRes}
            return bRes;
        }
        /// <summary>
        /// 复位控制Crane开始执行任务
        /// </summary>
        /// <returns></returns>
        public bool ResetCraneTaskStart(string strCraneTag)
        {
            //bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".TaskStart", 0);
            bool bRes = ControlCraneTaskAction(strCraneTag, CraneAction.None);
            //记录Crane日志：Is StartCraneTask Succeeded：{bRes}
            return bRes;
        }
        /// <summary>
        /// 控制Crane动作的继续、暂停和终止
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        private bool ControlCraneTaskAction(string strCraneTag, CraneAction action)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".Control", (int)action);
            if (bRes)
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "Control", "Control " + (int)action + " success");
            }
            else
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "Control", "Control " + (int)action + "failed");
            }
            return bRes;
        }

        public int GetControlValue(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            int iRes = (int)PlcComm.Instance.GetTagValue(craneTag + ".Control");
            return iRes;
        }
        /// <summary>
        /// 控制Crane继续任务动作
        /// </summary>
        /// <returns></returns>
        public bool CraneTaskResume(string strCraneTag)
        {
            bool bRes = ControlCraneTaskAction(strCraneTag, CraneAction.Resume);
            //记录Crane日志：Is CraneTaskResume Succeeded：{bRes}

            return bRes;
        }
        /// <summary>
        /// 控制Crane暂停任务动作
        /// </summary>
        /// <returns></returns>
        public bool CraneTaskPause(string strCraneTag)
        {
            bool bRes = ControlCraneTaskAction(strCraneTag, CraneAction.Pause);
            //记录Crane日志：Is CraneTaskResume Succeeded：{bRes}

            return bRes;
        }
        /// <summary>
        /// 控制Crane终止任务动作
        /// </summary>
        /// <returns></returns>
        public bool CraneTaskAbort(string strCraneTag)
        {
            bool bRes = ControlCraneTaskAction(strCraneTag, CraneAction.Abort);
            //记录Crane日志：Is CraneTaskResume Succeeded：{bRes}

            return bRes;
        }
        /// <summary>
        /// 复位Crane任务动作
        /// </summary>
        /// <returns></returns>
        public bool ResetCraneTaskAction(string strCraneTag)
        {
            bool bRes = ControlCraneTaskAction(strCraneTag, CraneAction.None);
            //记录Crane日志：Is ResetCraneTaskAction Succeeded：{bRes}

            return bRes;
        }
        /// <summary>
        /// 设置Crane的运行速度
        /// </summary>
        /// <param name="emptySpeed"></param>
        /// <param name="storageSpeed"></param>
        /// <param name="xSpeed"></param>
        /// <param name="ySpeed"></param>
        /// <param name="zSpeed"></param>
        /// <param name="tSpeed"></param>
        /// <returns></returns>
        public bool SetCraneSpeed(string strCraneTag, int emptySpeed, int storageSpeed, int xSpeed = 100, int ySpeed = 100, int zSpeed = 100, int tSpeed = 100)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            List<KeyValuePair<string, object>> keyValues = new List<KeyValuePair<string, object>>();

            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".EmptySpeed", emptySpeed));
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".StorageSpeed", storageSpeed));
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".xAxisSpeed", xSpeed));
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".yAxisSpeed", ySpeed));
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".zAxisSpeed", zSpeed));
            keyValues.Add(new KeyValuePair<string, object>(craneTag + ".tAxisSpeed", tSpeed));

            bool bRes = false;
            //bRes = PlcComm.Instance.WriteIOValue(keyValues);
            bRes = PlcComm.Instance.WriteTagValue(keyValues);
            //记录Crane日志：Is SetCraneSpeed Succeeded：{bRes}, emptySpeed, storageSpeed, xSpeed = , ySpeed = , zSpeed = , tSpeed = 

            return bRes;
        }

        public bool CheckCraneSpeedZero(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            object objEmptySpeed = PlcComm.Instance.GetTagValue(craneTag + ".EmptySpeed");
            object objStorageSpeed = PlcComm.Instance.GetTagValue(craneTag + ".StorageSpeed");
            try
            {
                if (Convert.ToInt32(objEmptySpeed) == 0 && Convert.ToInt32(objStorageSpeed) == 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                return true;
            }
        }

        /// <summary>
        /// 清除Crane报警
        /// </summary>
        /// <returns></returns>
        public bool CraneAlarmClear(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".AlarmReset", 1);
            if (bRes)
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "AlarmReset", "AlarmReset 1 success");
            }
            else
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "AlarmReset", "AlarmReset 1 failed");
            }
            return bRes;
        }
        /// <summary>
        /// 复位Crane报警清除
        /// </summary>
        /// <returns></returns>
        public bool ResetCraneAlarmClear(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".AlarmReset", 0);
            if (bRes)
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "AlarmReset", "AlarmReset 0 success");
            }
            else
            {
                HistoryWriter.Instance.EqpEventLog(strCraneTag, 201, "AlarmReset", "AlarmReset 0 failed");
            }
            return bRes;
        }

        public bool CraneServoOn(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".ServoOn", 1);
            //记录Crane日志：Is CraneServoOn Succeeded：{bRes}

            //if(CheckCraneSpeedZero(strCraneTag))
            //{
            //    SetCraneSpeed(strCraneTag, 50, 50);
            //}

            return bRes;
        }

        public bool CraneServoOff(string strCraneTag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".ServoOn", 0);
            //记录Crane日志：Is CraneServoOff Succeeded：{bRes}
            return bRes;
        }

        public bool IDReadBypass(string strCraneTag, bool flag)
        {
            string craneTag = preSetCraneTag + strCraneTag;
            bool bRes = PlcComm.Instance.WriteTagValue(craneTag + ".BCRBaypass", flag);
            //记录Crane日志：Is CraneServoOff Succeeded：{bRes}
            return bRes;
        }

        /// <summary>
        /// 获取Crane的当前任务
        /// </summary>
        /// <returns></returns>
        public CraneTaskData GetCraneCurrTaskData(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;

            CraneTaskData taskData = new CraneTaskData();

            object objTaskType = PlcComm.Instance.GetTagValue(craneTag + ".TaskType");
            object objCarrierID = PlcComm.Instance.GetTagValue(craneTag + ".CSTID");

            object objSrcAddr0 = PlcComm.Instance.GetTagValue(craneTag + ".SourceBank");
            object objSrcAddr1 = PlcComm.Instance.GetTagValue(craneTag + ".SourceBay");
            object objSrcAddr2 = PlcComm.Instance.GetTagValue(craneTag + ".SourceLevel");
            object objDestAddr0 = PlcComm.Instance.GetTagValue(craneTag + ".DestBank");
            object objDestAddr1 = PlcComm.Instance.GetTagValue(craneTag + ".DestBay");
            object objDestAddr2 = PlcComm.Instance.GetTagValue(craneTag + ".DestLevel");

            if (null == objTaskType || null == objCarrierID
                || null == objSrcAddr0 || null == objSrcAddr1 || null == objSrcAddr2
                || null == objDestAddr0 || null == objDestAddr1 || null == objDestAddr2)
            {
                return taskData;
            }

            try
            {
                taskData.taskType = (CraneTaskType)Convert.ToInt32(objTaskType);
                taskData.strCarrierID = objCarrierID.ToString();
                taskData.strSourceAddr = objSrcAddr0.ToString()
                    + (objSrcAddr1.ToString().Length == 1 ? "0" + objSrcAddr1.ToString() : objSrcAddr1.ToString())
                    + (objSrcAddr2.ToString().Length == 1 ? "0" + objSrcAddr2.ToString() : objSrcAddr2.ToString());

                taskData.strDestAddr = objDestAddr0.ToString()
                    + (objDestAddr1.ToString().Length == 1 ? "0" + objDestAddr1.ToString() : objDestAddr1.ToString())
                    + (objDestAddr2.ToString().Length == 1 ? "0" + objDestAddr2.ToString() : objDestAddr2.ToString());

            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetCraneCurrTaskData Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return taskData;
        }
        /// <summary>
        /// 查询Crane是否存在报警
        /// </summary>
        /// <returns></returns>
        public bool IsCraneHasAlarm(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;

            bool bRes = false;
            object objHasAlarm = PlcComm.Instance.GetTagValue(craneTag + ".Alarm");
            try
            {
                bRes = Convert.ToBoolean(objHasAlarm);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneHasAlarm Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        /// <summary>
        /// 查询Crane任务是否终止完成
        /// </summary>
        /// <returns></returns>
        public bool IsCraneAbortComplete(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;

            bool bRes = false;
            object objAbortComplete = PlcComm.Instance.GetTagValue(craneTag + ".AbortComplete");
            try
            {
                bRes = Convert.ToBoolean(objAbortComplete);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneAbortComplete Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }
        /// <summary>
        /// 查询Crane是否在Home位置
        /// </summary>
        /// <returns></returns>
        public bool IsCraneAtHome(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            bool bRes = false;
            object objAtHome = PlcComm.Instance.GetTagValue(craneTag + ".CraneAtHome");
            try
            {
                bRes = Convert.ToBoolean(objAtHome);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneAtHome Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }
        /// <summary>
        /// 查询Crane货叉是否在Home位置
        /// </summary>
        /// <returns></returns>
        public bool IsCraneForkAtHome(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            bool bRes = false;
            object objForkAtHome = PlcComm.Instance.GetTagValue(craneTag + ".ForkAtHome");
            try
            {
                bRes = Convert.ToBoolean(objForkAtHome);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneForkAtHome Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }
        /// <summary>
        /// 查询Crane货叉上是否存在盒子
        /// </summary>
        /// <returns></returns>
        public bool IsCraneForkHasCarrier(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            bool bRes = false;
            object objForkHasCarrier = PlcComm.Instance.GetTagValue(craneTag + ".CSTStorge");
            try
            {
                bRes = Convert.ToBoolean(objForkHasCarrier);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneForkHasCarrier Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        //需要电气添加信号
        public bool IsCraneDetectedCarrier(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            bool bRes = false;
            object objForkHasCarrier = PlcComm.Instance.GetTagValue(craneTag + ".ShelfCSTDetect");
            try
            {
                bRes = Convert.ToBoolean(objForkHasCarrier);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneForkHasCarrier Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public bool IsEmptySource(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            bool bRes = false;
            object objEmptySource = PlcComm.Instance.GetTagValue("Crane.s2hCraneCstAlarmsCrane1.EmptySource");
            try
            {
                bRes = Convert.ToBoolean(objEmptySource);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneForkHasCarrier Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public bool IsDoubleStorage(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            bool bRes = false;
            object objDoubleStorage = PlcComm.Instance.GetTagValue("Crane.s2hCraneCstAlarmsCrane1.DoubleStorage");
            try
            {
                bRes = Convert.ToBoolean(objDoubleStorage);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneForkHasCarrier Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        /// <summary>
        /// 查询Crane是否处于就绪状态
        /// </summary>
        /// <returns></returns>
        public bool IsCraneReady(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            bool bRes = false;
            object objReady = PlcComm.Instance.GetTagValue(craneTag + ".Ready");
            try
            {
                bRes = Convert.ToBoolean(objReady);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneReady Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }
        /// <summary>
        /// 查询Crane是否全部私服上电
        /// </summary>
        /// <returns></returns>
        public bool IsCraneServoEnable(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            bool bRes = false;
            object objServoEnable = PlcComm.Instance.GetTagValue(craneTag + ".ServoEnable");
            try
            {
                bRes = Convert.ToBoolean(objServoEnable);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneServoEnable Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }
        /// <summary>
        /// 查询Crane的任务是否正常完成
        /// </summary>
        /// <returns></returns>
        public bool IsCraneTaskComplete(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            bool bRes = false;
            object objTaskComplete = PlcComm.Instance.GetTagValue(craneTag + ".TaskComplete");
            try
            {
                bRes = Convert.ToBoolean(objTaskComplete);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCraneTaskComplete Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public bool IsCarrierDetected(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            bool bRes = false;
            object objCarrierDetected = PlcComm.Instance.GetTagValue(craneTag + ".ShelfCSTDetect");
            try
            {
                bRes = Convert.ToBoolean(objCarrierDetected);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsCarrierDetected Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public IDRResult GetIDRResult(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            IDRResult idrResult = new IDRResult();

            object objIDRReadState = PlcComm.Instance.GetTagValue(craneTag + ".CSTIDReadState");
            object objIDRReadCode = PlcComm.Instance.GetTagValue(craneTag + ".CSTIDReadCode");
            try
            {
                idrResult.iResultCode = Convert.ToInt32(objIDRReadState);
                idrResult.strCarrierID = objIDRReadCode.ToString();
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIDRResult Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return idrResult;
        }
        ///
        public void setCraneTaskInfo(string strCraneTag, int nStep, int nAction)
        {
            string craneTag = preCraneStatus + strCraneTag;
            object objStep = Robot.TagAPI.SetValue("Tag." + craneTag + ".Step", nStep);
            object objAction = Robot.TagAPI.SetValue("Tag." + craneTag + ".Action", nAction); 
        }

        /// <summary>
        /// 获取Crane执行任务的步骤信息
        /// </summary>
        /// <returns></returns>
        public CraneTaskStep GetCraneTaskStep(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            CraneTaskStep taskStep = new CraneTaskStep();
            object objStep = PlcComm.Instance.GetTagValue(craneTag + ".Step");
            object objAction = PlcComm.Instance.GetTagValue(craneTag + ".Action");
            try 
            {
                taskStep.iStep = Convert.ToInt32(objStep);
                taskStep.iAction = Convert.ToInt32(objAction);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetCraneTaskStep Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return taskStep;
        }
        /// <summary>
        /// 获取Crane的示教状态信息
        /// </summary>
        public void GetCraneTeachState(string strCraneTag)
        {
            string craneTag = "s2hCraneTeachState" + strCraneTag;
            bool[] teachState = new bool[210];
            object objState= null;
            for (int i = 0; i < 64; i++)
            {
                objState = PlcComm.Instance.GetTagValue("s2hCraneTeachStates[" + i + "]");
                try
                {
                    teachState[i] = Convert.ToBoolean(objState);
                }
                catch (Exception ex)
                {
                    //记录程序异常日志:GetCraneTeachState Failed: {ex.Message}
                    Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                    return;
                }
            }      
        }
        /// <summary>
        /// 获取Crane的工作状态：空闲、运行中、暂停
        /// </summary>
        /// <returns></returns>
        public CraneWorkingStatus GetCraneWorkingStatus(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            int iStatus = 0;
            object objStatus = PlcComm.Instance.GetTagValue(craneTag + ".WorkingStatus");
            try
            {
                iStatus = Convert.ToInt32(objStatus);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetCraneWorkingStatus Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return (CraneWorkingStatus)iStatus;
        }

        public OperMod GetCraneOperMode(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            OperMod operMode = OperMod.Manual;
            object objOperMoe = PlcComm.Instance.GetTagValue(craneTag + ".OperationMod");
            try
            {
                operMode = (OperMod)Convert.ToInt32(objOperMoe);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortOperMode Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return operMode;
        }

        public string GetCraneCarrierId(string strCraneTag)
        {
            string craneTag = preCraneStatus + strCraneTag;
            object objOperMoe = PlcComm.Instance.GetTagValue(craneTag + ".CSTID");
            try
            {
                 return (string)Convert.ToString(objOperMoe);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortOperMode Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return "";
        }


        /// <summary>
        /// 获取Crane的IO数据
        /// </summary>
        public void GetCraneIOs(string strCraneTag)
        {
            string craneTag = "s2hCraneIOs" + strCraneTag;
            bool[] ios = new bool[64];
            object objIO = null;
            for (int i = 0; i < 64; i++)
            {
                objIO = PlcComm.Instance.GetTagValue(craneTag + "[" + i + "]");
                try
                {
                    ios[i] = Convert.ToBoolean(objIO);
                }
                catch (Exception ex)
                {
                    //记录程序异常日志:GetCraneIOs Failed: {ex.Message}
                    Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                    return;
                }
            }
        }
        /// <summary>
        /// 获取Crane的报警ID
        /// </summary>
        /// <returns></returns>
        public List<int> GetCraneAlarms(string strCraneTag)
        {
            string craneTag = "Crane.s2hCraneAlarms" + strCraneTag;
            List<int> alarmIDs = new List<int>();
            object objAlarm = null;
            bool bAlarmed = false;
            for (int i = 0; i < 320; i++)
            {
                objAlarm = PlcComm.Instance.GetTagValue(craneTag + "[" + i + "]");
                try
                {
                    bAlarmed = Convert.ToBoolean(objAlarm);
                    if (bAlarmed)
                    {
                        alarmIDs.Add(1001 + i);
                    }
                }
                catch (Exception ex)
                {
                    //记录程序异常日志:GetCraneAlarms Failed: {ex.Message}
                    Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                    return null;
                }
            }
            return alarmIDs;
        }

        /// <summary>
        /// Crane是否发生E84报警
        /// </summary>
        /// <returns></returns>
        public bool CheckCraneE84Alarms(string strCraneTag)
        {
            return false;

            string craneTag = "s2hCraneAlarms" + strCraneTag;
            List<int> alarmIDs = new List<int>();
            object objAlarm = null;
            bool bAlarmed = false;
            for (int i = 16; i < 21; i++)
            {
                objAlarm = PlcComm.Instance.GetIOValue(craneTag + "[" + i + "]");
                try
                {
                    bAlarmed = Convert.ToBoolean(objAlarm);
                    if (bAlarmed)
                    {
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    //记录程序异常日志:GetCraneAlarms Failed: {ex.Message}
                    Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                    return false;
                }
            }
            for (int i = 164; i < 165; i++)
            {
                objAlarm = PlcComm.Instance.GetIOValue(craneTag + "[" + i + "]");
                try
                {
                    bAlarmed = Convert.ToBoolean(objAlarm);
                    if (bAlarmed)
                    {
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    //记录程序异常日志:GetCraneAlarms Failed: {ex.Message}
                    Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取Crane的轴伺服数据
        /// </summary>
        /// <returns></returns>
        public AxisServoData GetCraneAxisServoData(string strCraneTag, string strAxisName)
        {
            string craneTag = preAxisServo + strCraneTag + strAxisName;

            AxisServoData xAxisDriveData = new AxisServoData();
            object objErrorCode = PlcComm.Instance.GetTagValue(craneTag + ".ErrorCode");
            object objCurrPos = PlcComm.Instance.GetTagValue(craneTag + "CurrPos");
            object objCmdPos = PlcComm.Instance.GetTagValue(craneTag + "CmdPos");
            object objCurrSpeed = PlcComm.Instance.GetTagValue(craneTag + "CurrSpeed");
            object objTorque = PlcComm.Instance.GetTagValue(craneTag + "Torque");

            try
            {
                xAxisDriveData.ErrorCode = Convert.ToInt32(objErrorCode);
                xAxisDriveData.CurrPos = Convert.ToSingle(objCurrPos);
                xAxisDriveData.CmdPos = Convert.ToSingle(objCmdPos);
                xAxisDriveData.CurrSpeed = Convert.ToSingle(objCurrSpeed);
                xAxisDriveData.Torque = Convert.ToSingle(objTorque);
            }
            catch (Exception ex)
            {
                //记录Crane日志：GetCraneXAxisDriveData Failed: Covert Data Error
                //记录程序异常日志:GetCraneXAxisDriveData Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return xAxisDriveData;
        }
        /// <summary>
        /// 获取Crane的轴使用数据
        /// </summary>
        /// <returns></returns>
        public AxisUsage GetCraneAxisUsage(string strCraneTag, string strAxisName)
        {
            string craneTag = preAxisUsage + strCraneTag + strAxisName;

            AxisUsage xAxisUsage = new AxisUsage();
            object objDriveCount = PlcComm.Instance.GetTagValue(craneTag + ".DriveCount");
            object objDriveDistance = PlcComm.Instance.GetTagValue(craneTag + ".DriveDistance");
            object objDriveTime = PlcComm.Instance.GetTagValue(craneTag + ".DriveTime");
            try
            {
                xAxisUsage.DriveCount = Convert.ToInt32(objDriveCount);
                xAxisUsage.DriveDistance = Convert.ToInt32(objDriveDistance);
                xAxisUsage.DriveTime = Convert.ToInt32(objDriveTime);
            }
            catch (Exception ex)
            {
                //记录Crane日志：GetXAxisUsage Failed: Covert Data Error
                //记录程序异常日志:GetXAxisUsage Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return xAxisUsage;
        }
        /// <summary>
        /// 获取Crane的轴状态
        /// </summary>
        /// <returns></returns>
        public AxisStaus GetCraneAxisStatus(string strCraneTag, string strAxisName)
        {
            string craneTag = preAxisStatus + strCraneTag + strAxisName;
            AxisStaus xAxisStaus = new AxisStaus();
            object objServoEnable = PlcComm.Instance.GetTagValue(craneTag + ".ServoEnable");
            object objAlarm = PlcComm.Instance.GetTagValue(craneTag + ".Alarm");
            object objWarning = PlcComm.Instance.GetTagValue(craneTag + ".Warning");
            object objStandStill = PlcComm.Instance.GetTagValue(craneTag + ".StandStill");
            object objSynchronizing = PlcComm.Instance.GetTagValue(craneTag + ".Synchronizing");
            try
            {
                xAxisStaus.ServoEnable = Convert.ToBoolean(objServoEnable);
                xAxisStaus.Alarm = Convert.ToBoolean(objAlarm);
                xAxisStaus.Warning = Convert.ToBoolean(objWarning);
                xAxisStaus.StandStill = Convert.ToBoolean(objStandStill);
                xAxisStaus.Synchronizing = Convert.ToBoolean(objSynchronizing);
            }
            catch (Exception ex)
            {
                //记录Crane日志：GetCraneXAxisStaus Failed: Covert Data Error
                //记录程序异常日志:GetCraneXAxisStaus Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return xAxisStaus;
        }

        public bool WritePortOPOUTCarrierID(string strCarrierID, string strOHT)
        {
            string strPath = "Port.s2hIOPortStatus" + strOHT + ".StageCSTID";
            bool bRes = PlcComm.Instance.WriteTagValue(strPath, strCarrierID);

            return bRes;
        }
    }
}
