﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Proj.DataTypeDef;

namespace Proj.DevComm
{
    public class PortDev
    {
        private static PortDev m_Instanse;
        private static readonly object mSyncObject = new object();

        private PortDev() { }
        public static PortDev Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new PortDev();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        private string preIOPortSet = "Port.h2sIOPortSet";
        private string preIOPortStatus = "Port.s2hIOPortStatus";
        private string preEQPortStatus = "Port.s2h";
        private string preAxisUsage = "Port.s2hAxisUsage";
        private string preAxisStatus = "Port.s2hAxisStatus";
        private string preAxisServo = "Port.s2hAxisServo";

        public bool SetIOPortSpeed(string portTag, int emptySpeed, int storageSpeed, int xSpeed = 100, int zSpeed = 100, int tSpeed = 100)
        {
            string ioPortTag = preIOPortSet + portTag;
            if (portTag.Contains("MR"))
            {
                return true;
            }
                List<KeyValuePair<string, object>> keyValues = new List<KeyValuePair<string, object>>();
            keyValues.Add(new KeyValuePair<string, object>(ioPortTag + ".EmptySpeed", emptySpeed));
            keyValues.Add(new KeyValuePair<string, object>(ioPortTag + ".StorgeSpeed", storageSpeed));
            if (portTag.Contains("CV") || portTag.Contains("OHT"))
            {
                keyValues.Add(new KeyValuePair<string, object>(ioPortTag + ".xAxisSpeed", xSpeed));
                keyValues.Add(new KeyValuePair<string, object>(ioPortTag + ".zAxisSpeed", zSpeed));
            }
           
            if (!portTag.Contains("OHT"))
            {
                 keyValues.Add(new KeyValuePair<string, object>(ioPortTag + ".tAxisSpeed", tSpeed));
            }
           
            bool bRes = PlcComm.Instance.WriteTagValue(keyValues);
            //记录Port日志：Is SetIOPortSpeed Succeeded：{bRes}, emptySpeed, storageSpeed, xSpeed = , ySpeed = , zSpeed = 

            return bRes;
        }

        public bool CheckIOPortSpeedZero(string portTag)
        {
            string ioPortTag = preIOPortSet + portTag;
            object objEmptySpeed = PlcComm.Instance.GetTagValue(ioPortTag + ".EmptySpeed");
            object objStorageSpeed = PlcComm.Instance.GetTagValue(ioPortTag + ".StorgeSpeed");
            try
            {
                if (Convert.ToInt32(objEmptySpeed) == 0 && Convert.ToInt32(objStorageSpeed) == 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                return true;
            }
        }

        //public bool IOPortEnable(string portTag)
        //{
        //    string ioPortTag = preIOPortSet + portTag;
        //    bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".PortDisable", 0);
        //    //记录Port日志：Is IOPortEnable Succeeded：{bRes}
        //    return bRes;
        //}

        //public bool IOPortDisable(string portTag)
        //{
        //    string ioPortTag = preIOPortSet + portTag;
        //    bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".PortDisable", 1);
        //    //记录Port日志：Is IOPortDisable Succeeded：{bRes}
        //    return bRes;
        //}
        //public bool IOPortIDREnable(string portTag)
        //{
        //    string ioPortTag = preIOPortSet + portTag;
        //    bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".BCRBaypass", 0);
        //    //记录Port日志：Is IOPortIDREnable Succeeded：{bRes}
        //    return bRes;
        //}

        //public bool IOPortIDRDisable(string portTag)
        //{
        //    string ioPortTag = preIOPortSet + portTag;
        //    bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".BCRBaypass", 1);
        //    //记录Port日志：Is IOPortIDRDisable Succeeded：{bRes}
        //    return bRes;
        //}

        //public bool IOPortGratingEnable(string portTag)
        //{
        //    string ioPortTag = preIOPortSet + portTag;
        //    bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".LC1Mutting", 0);
        //    //记录Port日志：Is IOPortGratingEnable Succeeded：{bRes}
        //    return bRes;
        //}
        //public bool IOPortGratingDisable(string portTag)
        //{
        //    string ioPortTag = preIOPortSet + portTag;
        //    bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".LC1Mutting", 1);
        //    //记录Port日志：Is IOPortGratingDisable Succeeded：{bRes}
        //    return bRes;
        //}

        public bool SetIOPortInputMode(string portTag)
        {
            string ioPortTag = preIOPortSet + portTag;
            //bool bRes = PlcComm.Instance.WriteIOValue(ioPortTag + ".Type", 1);
            bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".TypeChange", 1);
            //记录Port日志：Is SetIOPortInputMode Succeeded：{bRes}
            return bRes;
        }

        public bool SetIOPortOutputMode(string portTag)
        {
            string ioPortTag = preIOPortSet + portTag;
            //bool bRes = PlcComm.Instance.WriteIOValue(ioPortTag + ".Type", 2);
            bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".TypeChange", 2);
            //记录Port日志：Is SetIOPortOutputMode Succeeded：{bRes}
            return bRes;
        }

        public bool ReseIOPort(string portTag)
        {
            string ioPortTag = preIOPortSet + portTag;
            //bool bRes = PlcComm.Instance.WriteIOValue(ioPortTag + ".Type", 1);
            bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".TypeChange", 0);
            //记录Port日志：Is SetIOPortInputMode Succeeded：{bRes}
            return bRes;
        }

        //public bool IOPortAlarmClear(string portTag)
        //{
        //    string ioPortTag = preIOPortSet + portTag;
        //    bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".AlarmReset", 1);
        //    //记录Port日志：Is PGVPortAlarmClear Succeeded：{bRes}
        //    return bRes;
        //}
        //public bool ResetIOPortAlarmClear(string portTag)
        //{
        //    string ioPortTag = preIOPortSet + portTag;
        //    bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".AlarmReset", 0);
        //    //记录Port日志：Is PGVPortAlarmClear Succeeded：{bRes}
        //    return bRes;
        //}

        public bool IOPortServoOn(string portTag)
        {
            string ioPortTag = preIOPortSet + portTag;
            bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".ServoOn", 1);
            //记录Port日志：Is IOPortServoOn Succeeded：{bRes}

            //if (PortDev.Instance.CheckIOPortSpeedZero(portTag))
            //{
            //    PortDev.Instance.SetIOPortSpeed(portTag, 50, 50);
            //}
            return bRes;
        }
        public bool IOPortServoOff(string portTag)
        {
            string ioPortTag = preIOPortSet + portTag;
            bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".ServoOn", 0);
            //记录Port日志：Is IOPortServoOff Succeeded：{bRes}
            return bRes;
        }

        public bool BufferHandoffCompleteAck(string portTag)
        {
            string ioPortTag = preIOPortSet + portTag;
            if (portTag.Contains("CV") || portTag.Contains("OHT"))
            {
                bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".BufferCompleteAck", 1);
                return bRes;
            }
            else
            {
                bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".HandoffCompleteAck", 1);
                return bRes;
            }
        }

        public bool HandoffCompleteAck(string portTag)
        {
            string ioPortTag = preIOPortSet + portTag;
            if (portTag.Contains("CV") || portTag.Contains("OHT"))
            {
                bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".StageCompleteAck", 1);
                return bRes;
            }
            else
            {
                bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".HandoffCompleteAck", 1);
                return bRes;
            }
        }

        public bool ResetHandoffCompleteAck(string portTag)
        {
            string ioPortTag = preIOPortSet + portTag;
            if (portTag.Contains("CV") || portTag.Contains("OHT"))
            {
                bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".StageCompleteAck", 0);
                return bRes;
            }
            else
            {
                bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".HandoffCompleteAck", 0);
                return bRes;
            }
        }

        public bool ResetBufferHandoffCompleteAck(string portTag)
        {
            string ioPortTag = preIOPortSet + portTag;
            if (portTag.Contains("CV") || portTag.Contains("OHT"))
            {
                bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".BufferCompleteAck", 0);
                return bRes;
            }
            else
            {
                bool bRes = PlcComm.Instance.WriteTagValue(ioPortTag + ".HandoffCompleteAck", 0);
                return bRes;
            }
        }

        public bool IsIOPortHasAlarm(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            bool bRes = false;
            object objAlarm = PlcComm.Instance.GetTagValue(ioPortTag + ".Alarm");
            try
            {
                bRes = Convert.ToBoolean(objAlarm);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsIOPortHasAlarm Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public bool IsIOPortHasFoupErrorAlarm(string portTag)
        {
            string ioPortTag = "Port.s2hFoupError" + portTag;
            bool bRes = false;
            object objAlarm = PlcComm.Instance.GetTagValue(ioPortTag);
            try
            {
                bRes = Convert.ToBoolean(objAlarm);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsIOPortHasAlarm Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public bool setPortFoupErrorFlag(string portTag, int i)
        {
            bool bRes = PlcComm.Instance.WriteTagValue("Port.h2sFoupErrorSetMR2", i);
           
            return bRes;
        }

        public bool IsIOPortTypeChgPermit(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            bool bRes = false;
            object objTypeChgPermit = PlcComm.Instance.GetTagValue(ioPortTag + ".TypeChangePermit");
            try
            {
                bRes = Convert.ToBoolean(objTypeChgPermit);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsIOPortTypeChgPermit Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        //public bool IsIOPortDisabled(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    bool bRes = false;
        //    object objDisabled = PlcComm.Instance.GetTagValue(ioPortTag + ".PortDisable");
        //    try
        //    {
        //        bRes = Convert.ToBoolean(objDisabled);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:IsIOPortDisabled Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return bRes;
        //}

        //public bool IsIOPortIDRByPass(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    bool bRes = false;
        //    object objIDRDisabled = PlcComm.Instance.GetTagValue(ioPortTag + ".BCRBaypass");
        //    try
        //    {
        //        bRes = Convert.ToBoolean(objIDRDisabled);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:Is IOPortIDRByPass Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return bRes;
        //}

        //public bool IsIOPortLoadReq(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    bool bRes = false;
        //    object objLReq = PlcComm.Instance.GetTagValue(ioPortTag + ".LReq");
        //    try
        //    {
        //        bRes = Convert.ToBoolean(objLReq);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:Is IOPortLoadReq Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return bRes;
        //}
        //public bool IsIOPortUnloadReq(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    bool bRes = false;
        //    object objUReq = PlcComm.Instance.GetTagValue(ioPortTag + ".UReq");
        //    try
        //    {
        //        bRes = Convert.ToBoolean(objUReq);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:Is IOPortUnloadReq Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return bRes;
        //}

        public IDRResult GetIOPortIDRResult(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            IDRResult idrResult = new IDRResult();
            idrResult.iResultCode = 3;
            object objReadState = PlcComm.Instance.GetTagValue(ioPortTag + ".CSTIDReadState");
            object objReadCode = PlcComm.Instance.GetTagValue(ioPortTag + ".CSTIDReadCode"); 
            try
            {
                if (objReadState != null)
                {
                    idrResult.iResultCode = Convert.ToInt32(objReadState);
                }
                if (objReadCode != null)
                {
                    idrResult.strCarrierID = objReadCode.ToString();
                }                    
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortIDRResult Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return idrResult;
        }

        public string GetIOPortStageCSTID(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            string strCarrierID = "";
            object objReadCode = PlcComm.Instance.GetTagValue(ioPortTag + ".StageCSTID");
            try
            {
                if (objReadCode != null)
                {
                    strCarrierID = objReadCode.ToString();
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return strCarrierID;
        }

        public string GetIOPortVehicleCSTID(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            string strCarrierID = "";
            object objReadCode = PlcComm.Instance.GetTagValue(ioPortTag + ".VehicleCSTID");
            try
            {
                if (objReadCode != null)
                {
                    strCarrierID = objReadCode.ToString();
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return strCarrierID;
        }
        public string GetIOPortBufferCSTID(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            string strCarrierID = "";
            object objReadCode = PlcComm.Instance.GetTagValue(ioPortTag + ".BufferCSTID");
            try
            {
                if (objReadCode != null)
                {
                    strCarrierID = objReadCode.ToString();
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return strCarrierID;
        }

        public IDRResult GetCVPortIDRResult(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            IDRResult idrResult = new IDRResult();
            idrResult.iResultCode = -1;
            object objReadCode = PlcComm.Instance.GetTagValue(ioPortTag + ".StageCSTID");
            try
            {
                if (objReadCode != null)
                {
                    idrResult.strCarrierID = objReadCode.ToString();
                    if(idrResult.strCarrierID != "" && !idrResult.strCarrierID.StartsWith("/0") && !idrResult.strCarrierID.StartsWith("\0"))
                    {
                        idrResult.iResultCode = 1;
                    }
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortIDRResult Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return idrResult;
        }

        //public bool IsIOPortReady(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    bool bRes = false;
        //    object objReady = PlcComm.Instance.GetTagValue(ioPortTag + ".Ready");
        //    try
        //    {
        //        bRes = Convert.ToBoolean(objReady);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:IsIOPortReady Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return bRes;
        //}

        //public bool IsIOPortServoOn(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    bool bRes = false;
        //    object objServoOn = PlcComm.Instance.GetTagValue(ioPortTag + ".ServoEnable");
        //    try
        //    {
        //        bRes = Convert.ToBoolean(objServoOn);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:IsIOPortServoOn Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return bRes;
        //}

        //public bool IsIOPortHasCarrier(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    bool bRes = false;
        //    object objHasCarrier = PlcComm.Instance.GetTagValue(".CSTStorge");
        //    try
        //    {
        //        bRes = Convert.ToBoolean(objHasCarrier);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:IsInPortHasCarrier Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return bRes;
        //}


        //public bool IsIOPortOperMode(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    bool bRes = false;
        //    object objHasCarrier = PlcComm.Instance.GetTagValue(".CSTStorge");
        //    try
        //    {
        //        bRes = Convert.ToBoolean(objHasCarrier);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:IsInPortHasCarrier Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return bRes;
        //}

        //public bool IsIOPortAtLP(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    bool bRes = false;
        //    object objAtLP = PlcComm.Instance.GetTagValue(ioPortTag + ".AtLP");
        //    try
        //    {
        //        bRes = Convert.ToBoolean(objAtLP);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:IsIOPortAtLP Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return bRes;
        //}

        //public bool IsIOPortAtOP(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    bool bRes = false;
        //    object objAtLP = PlcComm.Instance.GetTagValue(ioPortTag + ".AtOP");
        //    try
        //    {
        //        bRes = Convert.ToBoolean(objAtLP);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:IsIOPortAtOP Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return bRes;
        //}

        //public PortWorkingStatus GetPortWorkingState(string portTag)
        //{
        //    string ioPortTag = preIOPortStatus + portTag;
        //    PortWorkingStatus state = PortWorkingStatus.None;
        //    object objInPortWorkStatus = PlcComm.Instance.GetTagValue(ioPortTag + ".State");
        //    try
        //    {
        //        state = (PortWorkingStatus)Convert.ToInt32(objInPortWorkStatus);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志:GetPortWorkingState Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return state;
        //}

        public PortUnitType GetIOPortUnitType(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            PortUnitType unitType = PortUnitType.None;

            object objIOPortPortUnitType = PlcComm.Instance.GetTagValue(ioPortTag + ".Type");
            try
            {
                if(Convert.ToInt32(objIOPortPortUnitType) == 1)
                {
                    unitType = PortUnitType.Input;
                }
                if (Convert.ToInt32(objIOPortPortUnitType) == 2)
                {
                    unitType = PortUnitType.Output;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortUnitType Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return unitType;
        }

        public IOPortWorkingStatus GetIOPortWorkingState(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            if(portTag.Contains("CV"))
            {
                ioPortTag = ioPortTag + ".StageState";
            }
            else
            {
                ioPortTag = ioPortTag + ".State";
            }
            IOPortWorkingStatus state = IOPortWorkingStatus.Init;
            object objIOPortWorkStatus = PlcComm.Instance.GetTagValue(ioPortTag);
            try
            {
                state = (IOPortWorkingStatus)Convert.ToInt32(objIOPortWorkStatus);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortWorkingState Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return state;
        }
        public IOPortWorkingStatus GetBufferWorkingState(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            ioPortTag = ioPortTag + ".BufferState";
            IOPortWorkingStatus state = IOPortWorkingStatus.Init;
            object objIOPortWorkStatus = PlcComm.Instance.GetTagValue(ioPortTag);
            try
            {
                state = (IOPortWorkingStatus)Convert.ToInt32(objIOPortWorkStatus);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortWorkingState Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return state;
        }


        public CVVehicleWorkingStatus GetCVVehicleWorkingState(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            CVVehicleWorkingStatus state = CVVehicleWorkingStatus.Init;
            object objCVVehicleWorkStatus = PlcComm.Instance.GetTagValue(ioPortTag + ".VehicleState");
            try
            {
                state = (CVVehicleWorkingStatus)Convert.ToInt32(objCVVehicleWorkStatus);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortWorkingState Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return state;
        }

        /// <summary>
        /// OP上是否有CST
        /// </summary>
        public bool IsOPHasCST(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            object objCST = PlcComm.Instance.GetTagValue(ioPortTag + ".CSTPresence_Inside");
            try
            {
                return Convert.ToBoolean(objCST);
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// CV车上是否有CST
        /// </summary>
        public bool IsCVVehicleHasCST(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            object objCST = PlcComm.Instance.GetTagValue(ioPortTag + ".VehicleCSTPrencence");
            try
            {
                return Convert.ToBoolean(objCST);
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public MGVPortOperMod GetMGVPortOperMode(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            MGVPortOperMod operMode = MGVPortOperMod.Manual;
            object objOperMoe = PlcComm.Instance.GetTagValue(ioPortTag + ".OperationMod");
            try
            {
                operMode = (MGVPortOperMod)Convert.ToInt32(objOperMoe);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortOperMode Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return operMode;
        }

        public CVPortOperMod GetCVPortOperMode(string portTag)
        {
            string ioPortTag = preIOPortStatus + portTag;
            CVPortOperMod operMode = CVPortOperMod.Manual;
            object objOperMoe = PlcComm.Instance.GetTagValue(ioPortTag + ".OperationMod");
            try
            {
                operMode = (CVPortOperMod)Convert.ToInt32(objOperMoe);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortOperMode Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return operMode;
        }

        /// <summary>
        /// 判断MGV或CV Port是否处于Auto模式
        /// </summary>
        public bool IsIOPortAuto(string portTag)
        {
            int iOperMode = 0;
            string ioPortTag = preIOPortStatus + portTag;
            object objOperMoe = PlcComm.Instance.GetTagValue(ioPortTag + ".OperationMod");
            try
            {
                iOperMode = Convert.ToInt32(objOperMoe);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetIOPortOperMode Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            if(portTag.Contains("CV") && iOperMode == 3)
            {
                return true;
            }
            else if (portTag.Contains("MGV") && iOperMode == 2)
            {
                return true;
            }
            else if (iOperMode == 2)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 获取Crane的IO数据
        /// </summary>
        //public void GetIOPortIOs(string strCraneTag)
        //{
        //    string ioPortTag = preIOPortStatus + strCraneTag;
        //    bool[] ios = new bool[64];
        //    object objIO = null;
        //    for (int i = 0; i < 64; i++)
        //    {
        //        objIO = PlcComm.Instance.GetTagValue(ioPortTag + "[" + i + "]");
        //        try
        //        {
        //            ios[i] = Convert.ToBoolean(objIO);
        //        }
        //        catch (Exception ex)
        //        {
        //            //记录程序异常日志:GetCraneIOs Failed: {ex.Message}
        //            Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //            return;
        //        }
        //    }
        //}
        /// <summary>
        /// 获取Crane的报警ID
        /// </summary>
        /// <returns></returns>
        public List<int> GetIOPortAlarms(string strPortTag)
        {
            string ioPortTag;
            if(strPortTag.Contains("MGV"))
            {
                ioPortTag = "s2hMGVPortAlarms" + strPortTag;
            }
            else
            {
                ioPortTag = "s2hCVPortAlarms" + strPortTag;
            }
            List<int> alarmIDs = new List<int>();
            object objAlarm = null;
            bool bAlarmed = false;

            int iLoop = 96;
            if (strPortTag == "CV1")
            {
                iLoop = 320;
            }
            else if (strPortTag.Contains("OHT") )
            {
                iLoop = 96;
                ioPortTag = "s2hAlarms" + strPortTag;
            }
            else if (strPortTag.Contains("MR"))
            {
                iLoop = 96;
                ioPortTag = "s2hAlarms" + strPortTag;
            }

            for (int i = 0; i < iLoop; i++)
            {
                objAlarm = PlcComm.Instance.GetTagValue("Port." + ioPortTag + "[" + i + "]");
                try
                {
                    bAlarmed = Convert.ToBoolean(objAlarm);
                    if (bAlarmed)
                    {
                        if(strPortTag == "MGV1")
                        {
                            alarmIDs.Add(2001 + i);
                        }
                        else if (strPortTag == "MGV2")
                        {
                            alarmIDs.Add(2101 + i);
                        }
                        else if (strPortTag == "CV1")
                        {
                            if (i < 144)
                            {
                                alarmIDs.Add(5001 + i);
                            }
                            else if(i >= 160 && i < 304)
                            {
                                alarmIDs.Add(4001 + i - 160);
                            }
                        }
                        else if (strPortTag.Contains("OHT"))
                        {
                            if (strPortTag == "OHT1")
                            {
                                alarmIDs.Add(2001 + i);
                            }
                            else if (strPortTag == "OHT2")
                            {
                                alarmIDs.Add(2101 + i);
                            }
                            else if (strPortTag == "OHT3")
                            {
                                alarmIDs.Add(2201 + i);
                            }
                            else if (strPortTag == "OHT4")
                            {
                                alarmIDs.Add(2301 + i);
                            }
                        }
                        else if (strPortTag == "MR1")
                        {
                            alarmIDs.Add(3001 + i);
                        }
                        else if (strPortTag == "MR2")
                        {
                            alarmIDs.Add(3101 + i);
                        }
                        
                        else
                        {
                            alarmIDs.Add(1 + i);
                        }
                    }
                }
                catch (Exception ex)
                {
                    //记录程序异常日志:GetCraneAlarms Failed: {ex.Message}
                    Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                    return null;
                }
            }
            return alarmIDs;
        }


        /// <summary>
        /// 获取IOPort轴伺服数据
        /// </summary>
        /// <returns></returns>
        //public AxisServoData GetIOPortAxisServoData(string portTag, string axisName)
        //{
        //    string craneTag = preAxisServo + portTag + axisName;

        //    AxisServoData xAxisDriveData = new AxisServoData();
        //    object objErrorCode = PlcComm.Instance.GetTagValue(craneTag + ".ErrorCode");
        //    object objCurrPos = PlcComm.Instance.GetTagValue(craneTag + "CurrPos");
        //    object objCmdPos = PlcComm.Instance.GetTagValue(craneTag + "CmdPos");
        //    object objCurrSpeed = PlcComm.Instance.GetTagValue(craneTag + "CurrSpeed");
        //    object objTorque = PlcComm.Instance.GetTagValue(craneTag + "Torque");

        //    try
        //    {
        //        xAxisDriveData.ErrorCode = Convert.ToInt32(objErrorCode);
        //        xAxisDriveData.CurrPos = Convert.ToSingle(objCurrPos);
        //        xAxisDriveData.CmdPos = Convert.ToSingle(objCmdPos);
        //        xAxisDriveData.CurrSpeed = Convert.ToSingle(objCurrSpeed);
        //        xAxisDriveData.Torque = Convert.ToSingle(objTorque);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录Crane日志：GetCraneXAxisDriveData Failed: Covert Data Error
        //        //记录程序异常日志:GetCraneXAxisDriveData Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return xAxisDriveData;
        //}
        /// <summary>
        /// 获取IOPort轴使用数据
        /// </summary>
        /// <returns></returns>
        //public AxisUsage GetIOPortAxisUsage(string portTag, string axisName)
        //{
        //    string craneTag = preAxisServo + portTag + axisName;

        //    AxisUsage xAxisUsage = new AxisUsage();
        //    object objDriveCount = PlcComm.Instance.GetTagValue(craneTag + ".DriveCount");
        //    object objDriveDistance = PlcComm.Instance.GetTagValue(craneTag + ".DriveDistance");
        //    object objDriveTime = PlcComm.Instance.GetTagValue(craneTag + ".DriveTime");
        //    try
        //    {
        //        xAxisUsage.DriveCount = Convert.ToInt32(objDriveCount);
        //        xAxisUsage.DriveDistance = Convert.ToInt32(objDriveDistance);
        //        xAxisUsage.DriveTime = Convert.ToInt32(objDriveTime);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录Crane日志：GetXAxisUsage Failed: Covert Data Error
        //        //记录程序异常日志:GetXAxisUsage Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }

        //    return xAxisUsage;
        //}
        /// <summary>
        /// 获取IOPort轴状态
        /// </summary>
        /// <returns></returns>
        //public AxisStaus GetIOPortAxisStatus(string portTag, string axisName)
        //{
        //    string craneTag = preAxisServo + portTag + axisName;
        //    AxisStaus xAxisStaus = new AxisStaus();
        //    object objServoEnable = PlcComm.Instance.GetTagValue(craneTag + ".ServoEnable");
        //    object objAlarm = PlcComm.Instance.GetTagValue(craneTag + ".Alarm");
        //    object objWarning = PlcComm.Instance.GetTagValue(craneTag + ".Warning");
        //    object objStandStill = PlcComm.Instance.GetTagValue(craneTag + ".StandStill");
        //    object objSynchronizing = PlcComm.Instance.GetTagValue(craneTag + ".Synchronizing");
        //    try
        //    {
        //        xAxisStaus.ServoEnable = Convert.ToBoolean(objServoEnable);
        //        xAxisStaus.Alarm = Convert.ToBoolean(objAlarm);
        //        xAxisStaus.Warning = Convert.ToBoolean(objWarning);
        //        xAxisStaus.StandStill = Convert.ToBoolean(objStandStill);
        //        xAxisStaus.Synchronizing = Convert.ToBoolean(objSynchronizing);
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录Crane日志：GetCraneXAxisStaus Failed: Covert Data Error
        //        //记录程序异常日志:GetCraneXAxisStaus Failed: {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //    }
        //    return xAxisStaus;
        //}

        public EqPortStatus GetEqPortRequest(string portTag)
        {
            string eqPortTag = "s2hEQPortStatus" + portTag;
            EqPortStatus eqPortStatus = new EqPortStatus();
            object objLReq = PlcComm.Instance.GetIOValue(eqPortTag + ".P_L_REQ");
            object objUReq = PlcComm.Instance.GetIOValue(eqPortTag + ".P_U_REQ");
            eqPortStatus.pLReq = Convert.ToBoolean(objLReq);
            eqPortStatus.pUReq = Convert.ToBoolean(objUReq);
            return eqPortStatus;
        }

        public bool IsEQPortHasAlarm(string portTag)
        {
            string ioPortTag = "s2hEQPortStatus" + portTag;
            bool bRes = false;
            object objAlarm = PlcComm.Instance.GetIOValue(ioPortTag + ".P_Abnormal");
            try
            {
                bRes = Convert.ToBoolean(objAlarm);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsEQPortHasAlarm Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("PortDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }
        /// <summary>
        /// 清除Port报警
        /// </summary>
        /// <returns></returns>
        public bool PortAlarmClear(string strPortTag)
        {
            string portTag = preIOPortSet + strPortTag;
            bool bRes = PlcComm.Instance.WriteTagValue(portTag + ".AlarmReset", 1);
            //记录Crane日志：Is CraneAlarmClear Succeeded：{bRes}
            return bRes;
        }

        /// <summary>
        /// 复位Port报警清除
        /// </summary>
        /// <returns></returns>
        public bool ResetPortAlarmClear(string strPortTag)
        {
            string portTag = preIOPortSet + strPortTag;
            bool bRes = PlcComm.Instance.WriteTagValue(portTag + ".AlarmReset", 0);
            //记录Crane日志：Is ResetCraneAlarmClear Succeeded：{bRes}
            return bRes;
        }

        public bool EqPortHasCST(string strPortTag)
        {
            string eqPortTag = preEQPortStatus + strPortTag;
            object objCST = PlcComm.Instance.GetTagValue(eqPortTag + ".P_CSTContain");
            if (objCST == null)
            {
                return false;
            }
            return Convert.ToBoolean(objCST);
        }

        public bool WritePortLPInCarrierID(string strCarrierID, string strPortTag)
        {
            string strPath = "Port.s2hIOPortStatus" + strPortTag + ".BufferCSTID";
            bool bRes = PlcComm.Instance.WriteTagValue(strPath, strCarrierID);

            return bRes;
        }
        ///////////////////////////////添加Port口所有PIO变量读取 start/////////////////////////////////////////////////////
        public PortPIOStatus GetPortAllPIO(string portPath, string portTag)
        {
            string strPortTag = portPath + portTag;

            PortPIOStatus PIOPortStatus = new PortPIOStatus();
            object objLReq = PlcComm.Instance.GetIOValue(strPortTag + ".P_L_REQ");
            object objUReq = PlcComm.Instance.GetIOValue(strPortTag + ".P_U_REQ");
            object objPReady = PlcComm.Instance.GetIOValue(strPortTag + ".P_Ready");
            object objPAbnormal = PlcComm.Instance.GetIOValue(strPortTag + ".P_Abnormal");
            object objPInterLock = PlcComm.Instance.GetIOValue(strPortTag + ".P_InterLock");
            object objPCSTContain = PlcComm.Instance.GetIOValue(strPortTag + ".P_CSTContain");

            object objATR_REQ = PlcComm.Instance.GetIOValue(strPortTag + ".A_TR_REQ");
            object objABusy = PlcComm.Instance.GetIOValue(strPortTag + ".A_Busy");
            object objACOMPT = PlcComm.Instance.GetIOValue(strPortTag + ".A_COMPT");
            object objAAbnormal = PlcComm.Instance.GetIOValue(strPortTag + ".A_Abnormal");
            object objAInterLock = PlcComm.Instance.GetIOValue(strPortTag + ".A_InterLock");
            object objACSTContain = PlcComm.Instance.GetIOValue(strPortTag + ".A_CSTContain");

            PIOPortStatus.P_L_REQ = Convert.ToBoolean(objLReq);
            PIOPortStatus.P_U_REQ = Convert.ToBoolean(objUReq);
            PIOPortStatus.P_Ready = Convert.ToBoolean(objPReady);
            PIOPortStatus.P_Abnormal = Convert.ToBoolean(objPAbnormal);
            PIOPortStatus.P_InterLock = Convert.ToBoolean(objPInterLock);
            PIOPortStatus.P_CSTContain = Convert.ToBoolean(objPCSTContain);
            PIOPortStatus.A_TR_REQ = Convert.ToBoolean(objATR_REQ);
            PIOPortStatus.A_Busy = Convert.ToBoolean(objABusy);
            PIOPortStatus.A_COMPT = Convert.ToBoolean(objACOMPT);
            PIOPortStatus.A_Abnormal = Convert.ToBoolean(objAAbnormal);
            PIOPortStatus.A_InterLock = Convert.ToBoolean(objAInterLock);
            PIOPortStatus.A_CSTContain = Convert.ToBoolean(objACSTContain);

            return PIOPortStatus;
        }

        ///////////////////////////////添加Port口所有PIO变量读取 end/////////////////////////////////////////////////////
    }
}
