﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpStockerStatusList and List
    [Serializable]
    [Description("设备配置")]
    [LinqToDB.Mapping.Table("TP_STOCKERSTATUS")]
    public partial class TpStockerStatus : GEntity<TpStockerStatus>
    {
        #region Contructor(s)

        private TpStockerStatus()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_Name = RegisterProperty<String>(p => p.Name);
        private static readonly PropertyInfo<String> pty_Value = RegisterProperty<String>(p => p.Value);
        #endregion

        /// <summary>
        /// Name
        /// </summary>
        [Description("Name")]
        [LinqToDB.Mapping.Column("Name")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String Name
        {
            get { return GetProperty(pty_Name); }
            set { SetProperty(pty_Name, value); }
        }
        /// <summary>
        /// Value
        /// </summary>
        [Description("Value")]
        [LinqToDB.Mapping.Column("Value")]
        public String Value
        {
            get { return GetProperty(pty_Value); }
            set { SetProperty(pty_Value, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Name, "Name是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Name, 64, "Name不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Value, "Value是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Value, 64, "Value不能超过64个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Name; }
        #endregion
    }       
        
    [Serializable]
    public partial class TpStockerStatusList : GEntityList<TpStockerStatusList, TpStockerStatus>
    {
        private TpStockerStatusList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
