﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntLogbackconfig and List
    [Serializable]
    [Description("日志备份配置表")]
    [LinqToDB.Mapping.Table("Tnt_LogBackConfig")]
    public partial class TntLogbackconfig : GEntity<TntLogbackconfig>, ITimestamp
    {
        #region Contructor(s)

        private TntLogbackconfig()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CSaveType = RegisterProperty<String>(p => p.CSaveType);
        private static readonly PropertyInfo<Int64> pty_CSavedays = RegisterProperty<Int64>(p => p.CSavedays);
        private static readonly PropertyInfo<String> pty_CProvider = RegisterProperty<String>(p => p.CProvider);
        private static readonly PropertyInfo<String> pty_CConnectstr = RegisterProperty<String>(p => p.CConnectstr);
        private static readonly PropertyInfo<String> pty_CValId = RegisterProperty<String>(p => p.CValId);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        private static readonly PropertyInfo<String> pty_CSw04 = RegisterProperty<String>(p => p.CSw04);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 保存方式
        /// </summary>
        [Description("保存方式")]
        [LinqToDB.Mapping.Column("c_SaveType")]
        public String CSaveType
        {
            get { return GetProperty(pty_CSaveType); }
            set { SetProperty(pty_CSaveType, value); }
        }
        /// <summary>
        /// 保存天数
        /// </summary>
        [Description("保存天数")]
        [LinqToDB.Mapping.Column("c_SaveDays")]
        public Int64 CSavedays
        {
            get { return GetProperty(pty_CSavedays); }
            set { SetProperty(pty_CSavedays, value); }
        }
        /// <summary>
        /// 连接数据库
        /// </summary>
        [Description("连接数据库")]
        [LinqToDB.Mapping.Column("c_Provider")]
        public String CProvider
        {
            get { return GetProperty(pty_CProvider); }
            set { SetProperty(pty_CProvider, value); }
        }
        /// <summary>
        /// 连接字符串
        /// </summary>
        [Description("连接字符串")]
        [LinqToDB.Mapping.Column("c_ConnectStr")]
        public String CConnectstr
        {
            get { return GetProperty(pty_CConnectstr); }
            set { SetProperty(pty_CConnectstr, value); }
        }
        /// <summary>
        /// 有效标识
        /// </summary>
        [Description("有效标识")]
        [LinqToDB.Mapping.Column("c_Valid")]
        public String CValId
        {
            get { return GetProperty(pty_CValId); }
            set { SetProperty(pty_CValId, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
        /// <summary>
        /// 扩展4
        /// </summary>
        [Description("扩展4")]
        [LinqToDB.Mapping.Column("c_Sw04")]
        public String CSw04
        {
            get { return GetProperty(pty_CSw04); }
            set { SetProperty(pty_CSw04, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 400, "主键不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSaveType, 2, "保存方式不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProvider, 200, "连接数据库不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CConnectstr, 200, "连接字符串不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CValId, 4, "有效标识不能超过4个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 80, "扩展1不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 80, "扩展2不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 80, "扩展3不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw04, 80, "扩展4不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntLogbackconfigList : GEntityList<TntLogbackconfigList, TntLogbackconfig>
    {
        private TntLogbackconfigList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
