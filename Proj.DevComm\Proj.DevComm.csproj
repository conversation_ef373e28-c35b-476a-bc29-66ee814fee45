﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IoTClient" Version="1.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Proj.DataTypeDef\Proj.DataTypeDef.csproj" />
    <ProjectReference Include="..\Proj.DB\Proj.DB.csproj" />
    <ProjectReference Include="..\Proj.Entity\Proj.Entity.csproj" />
    <ProjectReference Include="..\Proj.History\Proj.History.csproj" />
  </ItemGroup>

</Project>
