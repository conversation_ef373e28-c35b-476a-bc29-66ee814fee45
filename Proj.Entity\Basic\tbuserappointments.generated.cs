﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbUserAppointments and List
    [Serializable]
    [Description("TB_USER_APPOINTMENTS")]
    [LinqToDB.Mapping.Table("TB_USER_APPOINTMENTS")]
    public partial class TbUserAppointments : GEntity<TbUserAppointments>
    {
        #region Contructor(s)

        private TbUserAppointments()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_UniqueId = RegisterProperty<String>(p => p.UniqueId);
        private static readonly PropertyInfo<Int64?> pty_Type = RegisterProperty<Int64?>(p => p.Type);
        private static readonly PropertyInfo<String> pty_Startdate = RegisterProperty<String>(p => p.Startdate);
        private static readonly PropertyInfo<String> pty_Enddate = RegisterProperty<String>(p => p.Enddate);
        private static readonly PropertyInfo<Int16?> pty_Allday = RegisterProperty<Int16?>(p => p.Allday);
        private static readonly PropertyInfo<String> pty_Subject = RegisterProperty<String>(p => p.Subject);
        private static readonly PropertyInfo<String> pty_Location = RegisterProperty<String>(p => p.Location);
        private static readonly PropertyInfo<String> pty_Description = RegisterProperty<String>(p => p.Description);
        private static readonly PropertyInfo<Int64?> pty_Status = RegisterProperty<Int64?>(p => p.Status);
        private static readonly PropertyInfo<Int64?> pty_Label = RegisterProperty<Int64?>(p => p.Label);
        private static readonly PropertyInfo<Int64?> pty_ResourceId = RegisterProperty<Int64?>(p => p.ResourceId);
        private static readonly PropertyInfo<String> pty_Resourceids = RegisterProperty<String>(p => p.Resourceids);
        private static readonly PropertyInfo<String> pty_Reminderinfo = RegisterProperty<String>(p => p.Reminderinfo);
        private static readonly PropertyInfo<String> pty_Recurrenceinfo = RegisterProperty<String>(p => p.Recurrenceinfo);
        private static readonly PropertyInfo<String> pty_Customfield1 = RegisterProperty<String>(p => p.Customfield1);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("UNIQUEID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String UniqueId
        {
            get { return GetProperty(pty_UniqueId); }
            set { SetProperty(pty_UniqueId, value); }
        }
        /// <summary>
        /// 类型
        /// </summary>
        [Description("类型")]
        [LinqToDB.Mapping.Column("TYPE")]
        public Int64? Type
        {
            get { return GetProperty(pty_Type); }
            set { SetProperty(pty_Type, value); }
        }
        /// <summary>
        /// 开始时间
        /// </summary>
        [Description("开始时间")]
        [LinqToDB.Mapping.Column("STARTDATE")]
        public String Startdate
        {
            get { return GetProperty(pty_Startdate); }
            set { SetProperty(pty_Startdate, value); }
        }
        /// <summary>
        /// 结束时间
        /// </summary>
        [Description("结束时间")]
        [LinqToDB.Mapping.Column("ENDDATE")]
        public String Enddate
        {
            get { return GetProperty(pty_Enddate); }
            set { SetProperty(pty_Enddate, value); }
        }
        /// <summary>
        /// 全天事件
        /// </summary>
        [Description("全天事件")]
        [LinqToDB.Mapping.Column("ALLDAY")]
        public Int16? Allday
        {
            get { return GetProperty(pty_Allday); }
            set { SetProperty(pty_Allday, value); }
        }
        /// <summary>
        /// 主题
        /// </summary>
        [Description("主题")]
        [LinqToDB.Mapping.Column("SUBJECT")]
        public String Subject
        {
            get { return GetProperty(pty_Subject); }
            set { SetProperty(pty_Subject, value); }
        }
        /// <summary>
        /// 地点
        /// </summary>
        [Description("地点")]
        [LinqToDB.Mapping.Column("LOCATION")]
        public String Location
        {
            get { return GetProperty(pty_Location); }
            set { SetProperty(pty_Location, value); }
        }
        /// <summary>
        /// 事件描述
        /// </summary>
        [Description("事件描述")]
        [LinqToDB.Mapping.Column("DESCRIPTION")]
        public String Description
        {
            get { return GetProperty(pty_Description); }
            set { SetProperty(pty_Description, value); }
        }
        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        [LinqToDB.Mapping.Column("STATUS")]
        public Int64? Status
        {
            get { return GetProperty(pty_Status); }
            set { SetProperty(pty_Status, value); }
        }
        /// <summary>
        /// 标签
        /// </summary>
        [Description("标签")]
        [LinqToDB.Mapping.Column("LABEL")]
        public Int64? Label
        {
            get { return GetProperty(pty_Label); }
            set { SetProperty(pty_Label, value); }
        }
        /// <summary>
        /// 资源
        /// </summary>
        [Description("资源")]
        [LinqToDB.Mapping.Column("RESOURCEID")]
        public Int64? ResourceId
        {
            get { return GetProperty(pty_ResourceId); }
            set { SetProperty(pty_ResourceId, value); }
        }
        /// <summary>
        /// 多人资源
        /// </summary>
        [Description("多人资源")]
        [LinqToDB.Mapping.Column("RESOURCEIDS")]
        public String Resourceids
        {
            get { return GetProperty(pty_Resourceids); }
            set { SetProperty(pty_Resourceids, value); }
        }
        /// <summary>
        /// 提醒信息
        /// </summary>
        [Description("提醒信息")]
        [LinqToDB.Mapping.Column("REMINDERINFO")]
        public String Reminderinfo
        {
            get { return GetProperty(pty_Reminderinfo); }
            set { SetProperty(pty_Reminderinfo, value); }
        }
        /// <summary>
        /// 日程重复信息
        /// </summary>
        [Description("日程重复信息")]
        [LinqToDB.Mapping.Column("RECURRENCEINFO")]
        public String Recurrenceinfo
        {
            get { return GetProperty(pty_Recurrenceinfo); }
            set { SetProperty(pty_Recurrenceinfo, value); }
        }
        /// <summary>
        /// 自定义
        /// </summary>
        [Description("自定义")]
        [LinqToDB.Mapping.Column("CUSTOMFIELD1")]
        public String Customfield1
        {
            get { return GetProperty(pty_Customfield1); }
            set { SetProperty(pty_Customfield1, value); }
        }
        #endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_UniqueId, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_UniqueId, 36, "主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Startdate, 76, "开始时间不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Enddate, 76, "结束时间不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Subject, 50, "主题不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Location, 50, "地点不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Description, 2000, "事件描述不能超过2000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Resourceids, 2000, "多人资源不能超过2000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Reminderinfo, 2000, "提醒信息不能超过2000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Recurrenceinfo, 2000, "日程重复信息不能超过2000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Customfield1, 2000, "自定义不能超过2000个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.UniqueId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbUserAppointmentsList : GEntityList<TbUserAppointmentsList, TbUserAppointments>
    {
        private TbUserAppointmentsList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
