using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Proj.API.Extensions;

namespace Proj.API.Examples
{
    /// <summary>
    /// 依赖注入使用示例
    /// </summary>
    public class DIUsageExample
    {
        /// <summary>
        /// 示例1: 基本依赖注入使用
        /// </summary>
        public static void BasicDIExample()
        {
            // 创建服务容器
            var services = new ServiceCollection();
            
            // 添加 SECS/GEM 服务
            services.AddSecsGemServices();
            
            // 构建服务提供者
            var serviceProvider = services.BuildServiceProvider();
            
            // 获取 GEM 设备实例
            var gemEquipment = serviceProvider.GetRequiredService<IGemEquipment>();
            
            Console.WriteLine("GEM 设备实例已通过依赖注入创建");
            
            // 使用设备
            // gemEquipment.ConnectAsync();
        }

        /// <summary>
        /// 示例2: 在 Host 应用中使用
        /// </summary>
        public static void HostApplicationExample()
        {
            // 注意: 这个示例需要在实际的 Host 应用中使用
            // var host = Host.CreateDefaultBuilder()
            //     .ConfigureServices((context, services) =>
            //     {
            //         services.AddSecsGemServices();
            //     })
            //     .Build();
            // await host.RunAsync();

            Console.WriteLine("Host 应用示例 - 请在实际项目中实现");
        }

        /// <summary>
        /// 示例3: 自定义服务配置
        /// </summary>
        public static void CustomServiceExample()
        {
            var services = new ServiceCollection();
            
            // 自定义服务配置
            services.AddSecsGemServices(serviceCollection =>
            {
                // 可以在这里添加自定义的服务实现
                // serviceCollection.AddSingleton<ISecsGemLogger, CustomLogger>();
                // serviceCollection.AddSingleton<ISecsGemConfigService, CustomConfigService>();
            });
            
            var serviceProvider = services.BuildServiceProvider();
            var gemEquipment = serviceProvider.GetRequiredService<IGemEquipment>();
        }
    }

    /// <summary>
    /// SECS/GEM 托管服务示例 (注释掉以避免编译错误)
    /// </summary>
    /*
    public class SecsGemHostedService : BackgroundService
    {
        private readonly IGemEquipment _gemEquipment;

        public SecsGemHostedService(IGemEquipment gemEquipment)
        {
            _gemEquipment = gemEquipment;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await _gemEquipment.ConnectAsync();

            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }
        }
    }
    */
}
