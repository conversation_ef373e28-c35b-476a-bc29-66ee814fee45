using System.Text.Json;

namespace Proj.API
{
    /// <summary>
    /// SECS/GEM 配置类
    /// </summary>
    public class SecsGemConfig
    {
        /// <summary>
        /// 是否为主动模式
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// IP地址
        /// </summary>
        public string IpAddress { get; set; } = "127.0.0.1";

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port { get; set; } = 5555;

        /// <summary>
        /// Socket接收缓冲区大小
        /// </summary>
        public int SocketReceiveBufferSize { get; set; } = 8192;

        /// <summary>
        /// 设备ID
        /// </summary>
        public ushort DeviceId { get; set; } = 1;

        /// <summary>
        /// T3 - Reply Timeout (回复超时时间，毫秒)
        /// SEMI E5 标准推荐值: 45秒
        /// </summary>
        public int T3 { get; set; } = 45000;

        /// <summary>
        /// T5 - Connect Separation Time (连接分离时间，毫秒)
        /// SEMI E5 标准推荐值: 10秒
        /// </summary>
        public int T5 { get; set; } = 10000;

        /// <summary>
        /// T6 - Control Transaction Timeout (控制事务超时，毫秒)
        /// SEMI E5 标准推荐值: 5秒
        /// </summary>
        public int T6 { get; set; } = 5000;

        /// <summary>
        /// T7 - Not Selected Timeout (未选择超时，毫秒)
        /// SEMI E5 标准推荐值: 10秒
        /// </summary>
        public int T7 { get; set; } = 10000;

        /// <summary>
        /// T8 - Network Intercharacter Timeout (网络字符间超时，毫秒)
        /// SEMI E5 标准推荐值: 5秒
        /// </summary>
        public int T8 { get; set; } = 5000;

        /// <summary>
        /// 从JSON文件加载配置
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        /// <returns>配置对象</returns>
        public static SecsGemConfig LoadFromFile(string configFilePath)
        {
            try
            {
                if (!File.Exists(configFilePath))
                {
                    Console.WriteLine($"[SecsGemConfig] 配置文件不存在: {configFilePath}，使用默认配置");
                    return new SecsGemConfig();
                }

                var jsonContent = File.ReadAllText(configFilePath);
                var config = JsonSerializer.Deserialize<SecsGemConfig>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    ReadCommentHandling = JsonCommentHandling.Skip,
                    AllowTrailingCommas = true
                });

                Console.WriteLine($"[SecsGemConfig] 成功加载配置文件: {configFilePath}");
                return config ?? new SecsGemConfig();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SecsGemConfig] 加载配置文件失败: {ex.Message}，使用默认配置");
                return new SecsGemConfig();
            }
        }

        /// <summary>
        /// 保存配置到JSON文件
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        public void SaveToFile(string configFilePath)
        {
            try
            {
                var directory = Path.GetDirectoryName(configFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 创建带有详细注释的JSON内容
                var jsonContent = CreateDetailedJsonContent();

                File.WriteAllText(configFilePath, jsonContent);
                Console.WriteLine($"[SecsGemConfig] 配置已保存到: {configFilePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SecsGemConfig] 保存配置文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建带有详细注释的JSON配置内容
        /// </summary>
        /// <returns>格式化的JSON字符串</returns>
        private string CreateDetailedJsonContent()
        {
            var content = @"{
  // ========================================
  // SECS/GEM 通信配置文件
  // ========================================

  // 基本连接配置
  ""isActive"": " + IsActive.ToString().ToLower() + @",           // 是否为主动模式 (true: 设备主动连接Host, false: 被动等待连接)
  ""ipAddress"": """ + IpAddress + @""",       // IP地址 (Host的IP地址或本地回环地址)
  ""port"": " + Port + @",                     // 端口号 (SECS/GEM标准端口通常为5555)
  ""socketReceiveBufferSize"": " + SocketReceiveBufferSize + @", // Socket接收缓冲区大小 (字节)
  ""deviceId"": " + DeviceId + @",                   // 设备ID (通常为1，用于标识设备)

  // ========================================
  // SECS/GEM 超时配置 (单位: 毫秒)
  // 根据 SEMI E5 标准设置
  // ========================================
  ""t3"": " + T3 + @",      // Reply Timeout - 回复超时时间 (等待回复消息的最大时间)
  ""t5"": " + T5 + @",      // Connect Separation Time - 连接分离时间 (连接尝试之间的间隔)
  ""t6"": " + T6 + @",       // Control Transaction Timeout - 控制事务超时 (控制消息处理超时)
  ""t7"": " + T7 + @",      // Not Selected Timeout - 未选择超时 (等待选择状态的超时)
  ""t8"": " + T8 + @"       // Network Intercharacter Timeout - 网络字符间超时 (网络传输字符间隔超时)

  // ========================================
  // 配置说明:
  // 1. isActive=true: 设备作为客户端主动连接到Host
  // 2. isActive=false: 设备作为服务器等待Host连接
  // 3. 超时时间根据实际网络环境和设备响应速度调整
  // 4. 生产环境建议适当增加超时时间以提高稳定性
  // ========================================
}";
            return content;
        }
    }
}
