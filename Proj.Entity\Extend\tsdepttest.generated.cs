﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;
 
namespace Proj.Entity
{
    #region TsDepttest and List
    [Serializable]
    [Description("部门表1")]
    [LinqToDB.Mapping.Table("TS_DEPTTEST")]
    public partial class TsDepttest : GEntity<TsDepttest>
    {
        #region Contructor(s)

        private TsDepttest()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CDeptId = RegisterProperty<String>(p => p.CDeptId);
        private static readonly PropertyInfo<String> pty_CDeptPId = RegisterProperty<String>(p => p.CDeptPId);
        private static readonly PropertyInfo<String> pty_CDeptName = RegisterProperty<String>(p => p.CDeptName);
        private static readonly PropertyInfo<String> pty_CDeptDesc = RegisterProperty<String>(p => p.CDeptDesc);
        private static readonly PropertyInfo<String> pty_CCompany = RegisterProperty<String>(p => p.CCompany);
        private static readonly PropertyInfo<String> pty_CClassify = RegisterProperty<String>(p => p.CClassify);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        private static readonly PropertyInfo<String> pty_CSw04 = RegisterProperty<String>(p => p.CSw04);
        private static readonly PropertyInfo<String> pty_CSw05 = RegisterProperty<String>(p => p.CSw05);
        #endregion

        /// <summary>
        /// 部门
        /// </summary>
        [Description("部门")]
        [LinqToDB.Mapping.Column("C_DEPT_ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CDeptId
        {
            get { return GetProperty(pty_CDeptId); }
            set { SetProperty(pty_CDeptId, value); }
        }
        /// <summary>
        /// 上级部门
        /// </summary>
        [Description("上级部门")]
        [LinqToDB.Mapping.Column("C_DEPT_PID")]
        public String CDeptPId
        {
            get { return GetProperty(pty_CDeptPId); }
            set { SetProperty(pty_CDeptPId, value); }
        }
        /// <summary>
        /// 短名称
        /// </summary>
        [Description("短名称")]
        [LinqToDB.Mapping.Column("C_DEPT_NAME")]
        public String CDeptName
        {
            get { return GetProperty(pty_CDeptName); }
            set { SetProperty(pty_CDeptName, value); }
        }
        /// <summary>
        /// 描述
        /// </summary>
        [Description("描述")]
        [LinqToDB.Mapping.Column("C_DEPT_DESC")]
        public String CDeptDesc
        {
            get { return GetProperty(pty_CDeptDesc); }
            set { SetProperty(pty_CDeptDesc, value); }
        }
        /// <summary>
        /// 集团
        /// </summary>
        [Description("集团")]
        [LinqToDB.Mapping.Column("C_COMPANY")]
        public String CCompany
        {
            get { return GetProperty(pty_CCompany); }
            set { SetProperty(pty_CCompany, value); }
        }
        /// <summary>
        /// 分组
        /// </summary>
        [Description("分组")]
        [LinqToDB.Mapping.Column("C_CLASSIFY")]
        public String CClassify
        {
            get { return GetProperty(pty_CClassify); }
            set { SetProperty(pty_CClassify, value); }
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("C_SW01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("C_SW02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("C_SW03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
        /// <summary>
        /// 扩展4
        /// </summary>
        [Description("扩展4")]
        [LinqToDB.Mapping.Column("C_SW04")]
        public String CSw04
        {
            get { return GetProperty(pty_CSw04); }
            set { SetProperty(pty_CSw04, value); }
        }
        /// <summary>
        /// 扩展5
        /// </summary>
        [Description("扩展5")]
        [LinqToDB.Mapping.Column("C_SW05")]
        public String CSw05
        {
            get { return GetProperty(pty_CSw05); }
            set { SetProperty(pty_CSw05, value); }
        }

        
        
        #endregion 
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CDeptId, "部门是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeptId, 400, "部门不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeptPId, 400, "上级部门不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeptName, 200, "短名称不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeptDesc, 2000, "描述不能超过2000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCompany, 200, "集团不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CClassify, 80, "分组不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 80, "扩展1不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 80, "扩展2不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 80, "扩展3不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw04, 80, "扩展4不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw05, 80, "扩展5不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CDeptId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TsDepttestList : GEntityList<TsDepttestList, TsDepttest>
    {
        private TsDepttestList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
