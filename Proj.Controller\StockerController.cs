﻿using System;
using System.Collections.Generic;

//using System.Threading;
using SecsLite;
using Proj.HostComm;
using Proj.CacheData;
using Proj.UnitMng;
using Proj.Common;
using Proj.DataTypeDef;
using Proj.DevComm;
using Proj.WCF;
using Proj.History;
using System.Xml;
using System.Threading;
using Proj.Entity;
using Proj.Business.TimedTask;

namespace Proj.Controller
{
    public class StockerController
    {
        private static StockerController m_Instanse;
        private static readonly object mSyncObject = new object();

        private static int iCheckCount = 0;

        private StockerController()
        {
            GlobalData.Instance.gbEqpName = "STKC"; //需要读取EC并赋值
            WCFService.Instance.eventWCFClientSendMessage += Instance_eventWCFClientSendMessage;
            WCFService.Instance.eventWCFClientGetTagValue += Instance_eventWCFClientGetTagValue;
            HostIF.Instance.EventHostCommand += Instance_EventHostCommand;
            HostIF.Instance.EventStateChange += Instance_EventStateChange;
            CraneMng.Instance.NoticeCraneAction += SendCraneActionToUI;
            CraneMng.Instance.AutoAddTransferCommand += AutoAddTransferCommand;
        }

        private ThreadBaseModel immediateCmdThread = null; // new ThreadBaseModel(1, "ImmediateExecuteCommandThread ");
        private ThreadBaseModel cmdScheduleThread = null;  // new ThreadBaseModel(2, "CommandScheduleThread ");
        private ThreadBaseModel calcServerRunCountThread = null;//记录Server连续运行计数
        private Queue<ImmediateCommand> immediateCommandQueue = new Queue<ImmediateCommand>();
        private ImmediateCommand currImmediateCommand = null;
        private object objImmediateCommandLock = new object();
        private string strIdleCrane = "";
        //private ManualResetEvent opcStartWriteEvent = new ManualResetEvent(false);

        private ThreadBaseModel LogBackUpCmdThread = null;
        LogBacker g_logBack;

        private OperMod LastOperMod = OperMod.Manual;

        private int Compare(EnhancedTransferCommand x, EnhancedTransferCommand y)
        {
            return -(x.u2Priority.CompareTo(y.u2Priority));
        }
        private int iCompareWithCommand(EnhancedTransferCommand a1, EnhancedTransferCommand a2)
        {
            if (a1.u2Priority.CompareTo(a2.u2Priority) != 0)
                return -(a1.u2Priority.CompareTo(a2.u2Priority));
            else if (a1.createTime.CompareTo(a2.createTime) != 0)
                return (a1.createTime.CompareTo(a2.createTime));
            else
                return 1;
        }

        private void immediateCmdThreadFunc()
        {
            if (this.currImmediateCommand == null || this.currImmediateCommand.State == ImmediateCommandState.Completed)
            {
                if (this.immediateCommandQueue.Count == 0)
                {
                    return;
                }
                lock (objImmediateCommandLock)
                {
                    this.currImmediateCommand = immediateCommandQueue.Dequeue();
                    this.currImmediateCommand.State = ImmediateCommandState.Active;
                }
            }
            this.currImmediateCommand.Execute();
        }

        private void calcServerRunCountThreadFunc()
        {
            try
            {
                TntKeyvalueconfig tntKeyvalueconfig = TntKeyvalueconfig.GetByLambda(x => x.Key == "ServerTime");
                if(tntKeyvalueconfig != null)
                {
                    uint uiServerRunCount = Convert.ToUInt32(tntKeyvalueconfig.Vlaue);
                    uiServerRunCount += 1;
                    if (uiServerRunCount > 65535)
                    {
                        uiServerRunCount = 1;
                    }
                    tntKeyvalueconfig.Vlaue = uiServerRunCount.ToString();
                    tntKeyvalueconfig.Save();
                }
            }
            catch(Exception ex)
            {
                HistoryWriter.Instance.ExceptionLog("StockerController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }
        //*****Crane
        private void cmdScheduleThreadFunc()
        {
            //此处可以添加任务优先级的动态计算
            //...  lzt
            switch (GlobalData.Instance.gbSCState)
            {
                case SCState.Auto:
                    {
                        switch (GlobalData.Instance.gbSCMode)
                        {
                            case SCMode.Normal:
                                {
                                    //指令分派(双Crane时，需要重写)
                                    strIdleCrane = CraneMng.Instance.GetIdleCrane();
                                    if (string.IsNullOrEmpty(strIdleCrane))
                                    {
                                        //没有空闲的Crane
                                        return;
                                    }
                                    else
                                    {
                                        //if(CraneMng.Instance.GetCranePlaceCount() >= 100)
                                        //{
                                        //    AutoAddDoubleCheckCommand();
                                        //    return;
                                        //}

                                        //检查Crane上是否有数据残留
                                        OperMod CurOperMod = CraneDev.Instance.GetCraneOperMode("Crane1");
                                        if (CurOperMod == OperMod.Auto)
                                        {
                                            if (LastOperMod != CurOperMod &&
                                                !CraneDev.Instance.IsCraneForkHasCarrier("Crane1"))
                                            {
                                                try
                                                {
                                                    string strCraneName = CraneMng.Instance.GetCraneName();
                                                    TpLocation tpPortLocation = TpLocation.GetByLambda(x => x.Name == strCraneName);
                                                    TpCarrierList tpCarrierList = TpCarrierList.GetByLambda(x => x.Location == tpPortLocation.Address);
                                                    foreach (TpCarrier tpCarrier in tpCarrierList)
                                                    {
                                                        if (tpCarrier.Id != "")
                                                        {
                                                            HostIF.Instance.PostCarrierEvent(tpCarrier.Id, EqpEvent.CarrierRemoveCompleted);
                                                            HistoryWriter.Instance.EqpEventLog("Crane1", 10004, "异常处理", $"删除残帐CarrierID: {tpCarrier.Id}");
                                                            CarrierMng.Instance.DeleteCarrierInfo(tpCarrier.Id);
                                                        }
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    HistoryWriter.Instance.ExceptionLog("cmdScheduleThreadFunc.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                                                }
                                            }
                                            LastOperMod = OperMod.Auto;
                                        }
                                        else
                                        {
                                            LastOperMod = OperMod.Manual;
                                        }
                                        
                                        if (GlobalData.Instance.gbInPortReadFailed && GlobalData.Instance.gbInPorttrName != "")
                                        {
                                            AutoAddInPortReadFailedOutCommand(GlobalData.Instance.gbInPorttrName);
                                            GlobalData.Instance.gbInPortReadFailed = false;
                                            GlobalData.Instance.gbInPorttrName = "";
                                            return;
                                        }

                                       
                                        if(ControlState.OnlineRemote != Proj.CacheData.GlobalData.Instance.gbControlState)
                                        {
                                            foreach (TpPort tpPort in PortMng.Instance.GetAllInPortList())
                                            {
                                                InPortCSTIDTime stInPortCSTIDTime = PortMng.Instance.GetInPortCSTIDTime(tpPort.Name);

                                                if (stInPortCSTIDTime.bInputFlag)
                                                {
                                                    //Add Command
                                                    AutoAddPortInStockerCommand(tpPort.Location, stInPortCSTIDTime.strCasstileID);

                                                    PortMng.Instance.SetInPortCSTIDTime(tpPort.Name);   
                                                }

                                            }
                                        }
                                        foreach (TpPort tpPort in PortMng.Instance.GetAllInPortList())
                                        {
                                            InPortCSTIDTime stInPortCSTIDTime = PortMng.Instance.GetInPortCSTIDTime(tpPort.Name);

                                            if (stInPortCSTIDTime.bInputFlag)
                                            {
                                                TimeSpan time = DateTime.Now - stInPortCSTIDTime.timeInPut;
                                                int iTotalSeconds = Convert.ToInt32(time.TotalSeconds);
                                                if (iTotalSeconds > 32)
                                                {
                                                    //Add Command
                                                    AutoAddPortInStockerCommand(tpPort.Location, stInPortCSTIDTime.strCasstileID);

                                                    PortMng.Instance.SetInPortCSTIDTime(tpPort.Name);
                                                }
                                            }

                                        }

                                        //指令优先级排序
                                        GlobalData.Instance.gbEnhancedTransfers.Sort(iCompareWithCommand);

                                        EnhancedTransferCommand enhancedTransferCommand = TransferMng.Instance.GetNextExecuteCommand(strIdleCrane);
                                        if (null == enhancedTransferCommand)
                                        {
                                            //没有等待执行的指令
                                            return;
                                        }

                                        if (strIdleCrane.Length == 0)
                                        {
                                            //没有空闲的Crane
                                            return;
                                        }
           
                                        CraneMng.Instance.AddCommand(strIdleCrane, enhancedTransferCommand);
                                        Log.Logger.Instance.SCLog("SCMode.Normal:添加任务");
                                    }
                                }
                                break;
                            case SCMode.Test:
                                {
                                    if (CycleTestMng.Instance.IsCycleStart() && CycleTestMng.Instance.GetRemainCycleTimes() >= 0)
                                    {
                                        strIdleCrane = CraneMng.Instance.GetIdleCrane();
                                        if (string.IsNullOrEmpty(strIdleCrane))
                                        {
                                            //没有空闲的Crane
                                            return;
                                        }
                                        else
                                        {
                                            EnhancedTransferCommand enhancedTransferCommand = CycleTestMng.Instance.GenNextCycleCommand();
                                            if (null == enhancedTransferCommand)
                                            {
                                                //没有等待执行的指令
                                                return;
                                            }
                                            if (enhancedTransferCommand.sourceLocType == LocationType.IoPort)
                                            {
                                                string strPortName = LocationMng.Instance.GetLocationNameByAddress(enhancedTransferCommand.strRealSource);
                                                PortMng.Instance.AddPortTypeChgCommand(strPortName, PortUnitType.Input);
                                            }
                                            if (enhancedTransferCommand.destLocType == LocationType.IoPort)
                                            {
                                                string strPortName = LocationMng.Instance.GetLocationNameByAddress(enhancedTransferCommand.strRealDest);
                                                PortMng.Instance.AddPortTypeChgCommand(strPortName, PortUnitType.Output);
                                            }
                                            TransferMng.Instance.AddTransferCommandInfo(((int)CommandSource.SC).ToString(), enhancedTransferCommand);
                                            CraneMng.Instance.AddCommand(strIdleCrane, enhancedTransferCommand);
                                        }
                                    }
                                }
                                break;
                            case SCMode.Maintenance:
                                break;
                            case SCMode.Simulation:
                                break;
                        }
                    }
                    break;
                case SCState.Pausing:
                    {
                        //如果没有正在执行的指令
                        if (true)
                        {
                            SCStateMng.Instance.SetState(SCState.Paused);
                            HostIF.Instance.PostScEvent(EqpEvent.SCPauseCompleted);
                        }
                        else
                        {

                        }
                    }
                    break;
                case SCState.SCInit:
                    SCStateMng.Instance.SetState(SCState.SCInit);
                    break;
                case SCState.Paused:
                    SCStateMng.Instance.SetState(SCState.Paused);
                    break;
                default:              //不处理
                    break;
            }
        }

        private void dealLogBackUpCmdThreadFunc()
        {
            //日志压缩备份
            string now = DateTime.Now.ToString("HH");
            if(now.Equals("23"))
            {
                if(g_logBack == null)
                {
                    g_logBack = new LogBacker();
                }
                g_logBack.ZipBackDeteleFile();
            }
        }
        private HCACK Instance_EventHostCommand(string RCMD, Dictionary<string, object> dicParameter)
        {
            HCACK res = HCACK.CommandNotExist;
            switch (RCMD.ToUpper())
            {
                case "INSTALL":
                    res = AnalyzeInstallCommand(dicParameter);
                    break;
                case "REMOVE":
                    res = AnalyzeRemoveCommand(dicParameter);
                    break;
                case "RESUME":
                    res = AnalyzeResumeCommand(dicParameter);
                    break;
                case "ABORT":
                    res = AnalyzeAbortCommand(dicParameter);
                    break;
                case "RETRY":
                    res = AnalyzeRetryCommand(dicParameter);
                    break;
                case "CANCEL":
                    res = AnalyzeCancelCommand(dicParameter);
                    break;
                case "PAUSE":
                    res = AnalyzePauseCommand(dicParameter);
                    break;
                case "LOCATE":
                    res = AnalyzeLocateCommand(dicParameter);
                    break;
                case "PORTTYPECHG":
                    res = AnalyzePortTypeChgCommand(dicParameter);
                    break;
                case "SCAN":
                    res = AnalyzeScanCommand(dicParameter);
                    break;
                case "TRANSFER":
                    res = AnalyzeTransferCommand(dicParameter);
                    break;
            }

            return res;
        }

        private bool Instance_eventWCFClientSendMessage(string strMsgName, Dictionary<string, object> dicParameter)
        {
            HCACK res = HCACK.CommandNotExist;
            switch (strMsgName.ToUpper())
            {
                case "INSTALL":
                    res = AnalyzeInstallCommand(dicParameter);
                    break;
                case "REMOVE":
                    res = AnalyzeRemoveCommand(dicParameter);
                    break;
                case "RESUME":
                    res = AnalyzeResumeCommand(dicParameter);
                    break;
                case "ABORT":
                    {
                        dicParameter.Add("COMMANDSOURCE", ((int)CommandSource.GUI).ToString());
                        res = AnalyzeAbortCommand(dicParameter);
                        break;
                    }
                case "CANCEL":
                    {
                        dicParameter.Add("COMMANDSOURCE", ((int)CommandSource.GUI).ToString());
                        res = AnalyzeCancelCommand(dicParameter);
                    }
                    break;
                case "PAUSE":
                    res = AnalyzePauseCommand(dicParameter);
                    break;
                case "CLEANPLCTASK":
                    res = AnalyzeCleanPLCTaskCommand(dicParameter);
                    break;
                //case "LOCATE":
                //    res = AnalyzeLocateCommand(dicParameter);
                //    break;
                case "PORTTYPECHG":
                    res = AnalyzePortTypeChgCommand(dicParameter);
                    break;
                case "SCAN":
                    {
                            dicParameter.Add("COMMANDSOURCE", ((int)CommandSource.GUI).ToString());
                            res = AnalyzeScanCommand(dicParameter);
                            break;
                    }
                case "MULTISCAN":
                    {
                        res = AnalyzeMultiScanCommand(dicParameter);
                        break;
                    }
                case "TRANSFER":
                    {
                        string strCmdID = TransferMng.Instance.GenManualCommandID();
                        dicParameter.Add("COMMANDID", strCmdID);
                        dicParameter.Add("COMMANDSOURCE", ((int)CommandSource.GUI).ToString());
                        res = AnalyzeTransferCommand(dicParameter);
                        break;
                    }
                case "MOVE":
                    {
                        res = AnalyzeMoveCommand(dicParameter);
                        break;
                    }
                case "SETLOOPTEST":
                    {
                        CycleTestInfo cycInfo = new CycleTestInfo();
                        cycInfo.strCarrierID = dicParameter["CARRIERID"].ToString();
                        cycInfo.bUsePort = (bool)dicParameter["USEPORT"];
                        cycInfo.strPortName = dicParameter["PORTNAME"].ToString();
                        cycInfo.iCycleTimes = (int)dicParameter["CYCLETIMES"];
                        string[] locationArray = dicParameter["LOCATIONS"].ToString().Split(';');
                        cycInfo.Locations = new List<string>();
                        cycInfo.Locations.AddRange(locationArray);
                        return CycleTestMng.Instance.SaveCycleTestInfo(cycInfo);
                    }
                case "STARTLOOPTEST":
                    return CycleTestMng.Instance.StartCyleTest();
                case "STOPLOOPTEST":
                    CycleTestMng.Instance.StopCycleTest();
                    return true;
                case "CLEARUNITALARM":
                    return ClearUnitAlarm(dicParameter);
                case "CHANGESTATE":
                    return UIChangeState(dicParameter["NAME"].ToString(), dicParameter["VALUE"].ToString());
                case "SETCRANESPEED":
                    res = AnalyzeSetCraneSpeedCommand(dicParameter);
                    break;
                case "SETPORTSPEED":
                    res = AnalyzeSetPortSpeedCommand(dicParameter);
                    break;
                case "SETFFUSPEED":
                    res = AnalyzeSetFFUSpeedCommand(dicParameter);
                    break;
                case "SETLOCATIONENABLED":
                    res = AnalyzeSetLocationEnabled(dicParameter);
                    break;
                case "SETMULTILOCATIONSENABLED":
                    res = AnalyzeSetMultiLocationsEnabled(dicParameter);
                    break;
                case "CLEARRESERVED":
                    res = AnalyzeClearReserved(dicParameter);
                    break;
                case "SETSERVERTAGS":
                    res = AnalyzeSetServerTags(dicParameter);
                    break;
                case "UPDATEPRIORITY":
                    res = AnalyzeUpdatePriorityCommand(dicParameter);
                    break;
                case "CHECKABORT":  //询问是否满足Abort条件
                    {
                        if (CheckCraneCanAbort())
                        {
                            res = HCACK.PerformLater;
                        }
                        else
                        {
                            res = HCACK.CantPerformNow;
                        }
                        break;
                    }
            }

            if(res == HCACK.Acknowledge || res == HCACK.PerformLater)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        private object Instance_eventWCFClientGetTagValue(string strTagName)
        {
            return PlcComm.Instance.GetTagValue(strTagName);
        }

        private bool ClearUnitAlarm(Dictionary<string, object> dicParameter)
        {
            string strUnit = dicParameter["UNIT"].ToString();
            string strAddress = LocationMng.Instance.GetLocationAddress(strUnit);
            LocationType locType = LocationMng.Instance.GetLocationType(strAddress);
            if(locType == LocationType.Crane)
            {
                return CraneMng.Instance.ResetCraneAlarm(strUnit);
            }
            else if (locType == LocationType.IoPort)
            {
                return PortMng.Instance.ResetPortAlarm(strUnit);
            }
            else
            {
                return CraneMng.Instance.ResetSTKAlarm();
            }
        }

        private HCACK AnalyzeInstallCommand(Dictionary<string, object> dicParameter)
        {
            //指令参数检查
            string strCarrierID = dicParameter["CARRIERID"].ToString();
            string strAddress = dicParameter["CARRIERLOC"].ToString();

            //参数检查 判空
            if (string.IsNullOrEmpty(strCarrierID))
            {
                //生成Invalid Item,lzt
                return HCACK.ParamInvalid;
            }
            //1.carrierLoc如果为Zone,需要进一步处理,lzt
            //2.检查carrierLoc位置是否存在
            if (string.IsNullOrEmpty(strAddress)) 
            {
                //生成Invalid Item,lzt
                return HCACK.ParamInvalid;
            }

            InstallCommand installCommand = new InstallCommand();
            installCommand.CommandName = "INSTALL";
            installCommand.DicParam = dicParameter;
            installCommand.State = ImmediateCommandState.Queue;
            lock(objImmediateCommandLock)
            {
                immediateCommandQueue.Enqueue(installCommand);
            }
            return HCACK.PerformLater;
        }

        private HCACK AnalyzeRemoveCommand(Dictionary<string, object> dicParameter)
        {
            //指令参数检查
            string strCarrierID = dicParameter["CARRIERID"].ToString();
            //参数检查
            if (string.IsNullOrEmpty(strCarrierID))
            {
                //生成Invalid Item,lzt
                return HCACK.ParamInvalid;
            }

            RemoveCommand removeCommand = new RemoveCommand();
            removeCommand.CommandName = "REMOVE";
            removeCommand.DicParam = dicParameter;
            removeCommand.State = ImmediateCommandState.Queue;
            lock (objImmediateCommandLock)
            {
                immediateCommandQueue.Enqueue(removeCommand);
            }

            return HCACK.PerformLater;
        }

        private HCACK AnalyzeResumeCommand(Dictionary<string, object> dicParameter)
        {
            ResumeCommand resumeCommand = new ResumeCommand();
            resumeCommand.CommandName = "RESUME";
            resumeCommand.DicParam = dicParameter;
            resumeCommand.State = ImmediateCommandState.Queue;
            lock (objImmediateCommandLock)
            {
                immediateCommandQueue.Enqueue(resumeCommand);
            }

            return HCACK.PerformLater;
        }

        private HCACK AnalyzeCleanPLCTaskCommand(Dictionary<string, object> dicParameter)
        {
            ClearPLCTaskCommand cleanCommand = new ClearPLCTaskCommand();
            cleanCommand.CommandName = "CLEANTASK";
            cleanCommand.DicParam = dicParameter;
            cleanCommand.State = ImmediateCommandState.Queue;
            lock (objImmediateCommandLock)
            {
                immediateCommandQueue.Enqueue(cleanCommand);
            }

            return HCACK.PerformLater;
        }

        private HCACK AnalyzeUpdatePriorityCommand(Dictionary<string, object> dicParameter)
        {
            //指令参数检查
            string commandID = dicParameter["COMMANDID"].ToString();
            if (string.IsNullOrEmpty(commandID))
            {
                //生成Invalid Item,lzt
                return HCACK.ParamInvalid;
            }

            int iPriority = 0;
            try
            {
                iPriority = Convert.ToInt32(dicParameter["PRIORITY"]);
            }
            catch
            {
                return HCACK.ParamInvalid;
            }

            bool bHaveCommand = false;
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    if (enhancedTransferCommand.strCommandID == commandID)
                    {
                        if (enhancedTransferCommand.transferState == TransferState.Transferring)
                        {
                            return HCACK.CantPerformNow;
                        }
                        else
                        {
                            TransferMng.Instance.UpdataTransferPriority(enhancedTransferCommand.strCommandID, iPriority);
                        }
                        bHaveCommand = true;
                        break;
                    }
                }
            }
            if (!bHaveCommand)
            {
                return HCACK.NoSuchObjectExists;
            }

            return HCACK.PerformLater;
        }

        private HCACK AnalyzeAbortCommand(Dictionary<string, object> dicParameter)
        {
            //指令参数检查
            string commandID = dicParameter["COMMANDID"].ToString();
            //参数检查
            if (string.IsNullOrEmpty(commandID))
            {
                //生成Invalid Item,lzt
                return HCACK.ParamInvalid;
            }
            //如果是界面手动下发的命令，向MCS上报254事件
            string strCommandSource = dicParameter["COMMANDSOURCE"].ToString();
            if (strCommandSource != "0")
            {
                HostIF.Instance.PostManualAbortEvent(commandID);
            }

            bool bHaveCommand = false;
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    if (enhancedTransferCommand.strCommandID == commandID)
                    {
                        if (enhancedTransferCommand.transferState != TransferState.Transferring)
                        {
                            //生成Invalid Item,lzt
                            return HCACK.CantPerformNow;
                        }
                        else
                        {
                            TransferMng.Instance.UpdataTransferState(enhancedTransferCommand.strCommandID, enhancedTransferCommand.iFirstSecendCrane, TransferState.Aborting, ResultCode.None);
                           // CraneMng.Instance.craneObjList
                           // CraneObj.  SetAbortStepValue(enhancedTransferCommand);
                            enhancedTransferCommand.dealOption = DealExceptionOption.Abort;
                        }
                        bHaveCommand = true;
                        break;
                    }
                }
            }
            if (!bHaveCommand)
            {
                //生成Invalid Item,lzt
                return HCACK.NoSuchObjectExists;
            }

            if (!CheckCraneCanAbort())
            {
                return HCACK.CantPerformNow;
            }
            //通过上面的直接改变指令状态，在执行的执行流程中进行处理，不必再创建一个立即执行的指令
            //ImmediateCommand abortCommand = new ImmediateCommand();
            //abortCommand.CommandName = "ABORT";
            //abortCommand.DicParam = dicParameter;
            //abortCommand.State = ImmediateCommandState.Queue;
            //lock (objImmediateCommandLock)
            //{
            //    immediateCommandQueue.Enqueue(abortCommand);
            //}

            return HCACK.PerformLater;
        }

        private HCACK AnalyzeRetryCommand(Dictionary<string, object> dicParameter)
        {
            //指令参数检查
            if (GlobalData.Instance.gbRetryCount > 0 && GlobalData.Instance.gbRetryCount <= 3)
            {
                CraneMng.Instance.Resume();
                Thread.Sleep(50);
                CraneMng.Instance.setUiIsRetryFlag(1);
                return HCACK.PerformLater;
            }

            string commandID = dicParameter["COMMANDID"].ToString();
            //参数检查
            if (string.IsNullOrEmpty(commandID))
            {
                //生成Invalid Item,lzt
                return HCACK.ParamInvalid;
            }

            bool bHaveCommand = false;
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    if (enhancedTransferCommand.strCommandID == commandID)
                    {
                        TransferMng.Instance.UpdataTransferState(enhancedTransferCommand.strCommandID, enhancedTransferCommand.iFirstSecendCrane, TransferState.Transferring, ResultCode.None);
                        enhancedTransferCommand.dealOption = DealExceptionOption.Retry;
                        
                        bHaveCommand = true;
                        break;
                    }
                }
            }
            if (!bHaveCommand)
            {
                //生成Invalid Item,lzt
                return HCACK.NoSuchObjectExists;
            }

            return HCACK.PerformLater;
        }

        private HCACK AnalyzeCancelCommand(Dictionary<string, object> dicParameter)
        {
            //指令参数检查
            string commandID = dicParameter["COMMANDID"].ToString();
            //参数检查
            if (string.IsNullOrEmpty(commandID))
            {
                //生成Invalid Item,lzt
                return HCACK.ParamInvalid;
            }
            //如果是界面手动下发的命令，向MCS上报254事件
            string strCommandSource = dicParameter["COMMANDSOURCE"].ToString();
            if (strCommandSource != "0")
            {
                HostIF.Instance.PostManualCancelEvent(commandID);
            }

            bool bHaveCommand = false;
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    if (enhancedTransferCommand.strCommandID == commandID)
                    {
                        if (enhancedTransferCommand.transferState == TransferState.Transferring)
                        {
                            //生成Invalid Item,lzt
                            return HCACK.CantPerformNow;
                        }
                        bHaveCommand = true;
                        break;
                    }
                }
            }
            if (!bHaveCommand)
            {
                //生成Invalid Item,lzt
                return HCACK.NoSuchObjectExists;
            }
            CancelCommand cancelCommand = new CancelCommand();
            cancelCommand.CommandName = "CANCEL";
            cancelCommand.DicParam = dicParameter;
            cancelCommand.State = ImmediateCommandState.Queue;
            lock (objImmediateCommandLock)
            {
                immediateCommandQueue.Enqueue(cancelCommand);
            }

            return HCACK.PerformLater;
        }

        private HCACK AnalyzePauseCommand(Dictionary<string, object> dicParameter)
        {
            PauseCommand pauseCommand = new PauseCommand();
            pauseCommand.CommandName = "PAUSE";
            pauseCommand.DicParam = dicParameter;
            pauseCommand.State = ImmediateCommandState.Queue;
            lock (objImmediateCommandLock)
            {
                immediateCommandQueue.Enqueue(pauseCommand);
            }

            return HCACK.PerformLater;
        }

        //文档中没有该指令的场景描述，不实现
        private HCACK AnalyzeLocateCommand(Dictionary<string, object> dicParameter)
        {
            return HCACK.CommandNotExist;
        }

        private HCACK AnalyzeMoveCommand(Dictionary<string, object> dicParameter)
        {
            try
            {
                string strAddress = dicParameter["SOURCE"].ToString();
                uint u2Priority = Convert.ToUInt16(dicParameter["PRIORITY"]);
                if (string.IsNullOrEmpty(strAddress))
                {
                    return HCACK.ParamInvalid;
                }
                EnhancedTransferCommand eTransferCmd = new EnhancedTransferCommand();
                eTransferCmd.strCommandName = "MOVE";
                eTransferCmd.strCommandID = TransferMng.Instance.GenScanCommandID();
                eTransferCmd.u2Priority = u2Priority;
                eTransferCmd.strRealSource = strAddress;
                eTransferCmd.strRealDest = strAddress;
                eTransferCmd.transferState = TransferState.Queue;
                TransferMng.Instance.AddTransferCommandInfo(((int)CommandSource.GUI).ToString(), eTransferCmd);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("StockerController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return HCACK.PerformLater;
        }

        private HCACK AnalyzePortTypeChgCommand(Dictionary<string, object> dicParameter)
        {
            try
            {
                string strPortID = dicParameter["PORTID"].ToString();
                if (string.IsNullOrEmpty(strPortID))
                {
                    //生成Invalid Item,lzt
                    return HCACK.ParamInvalid;
                }

                PortUnitType type = PortUnitType.None;
                int iType = Convert.ToInt32(dicParameter["PORTTYPE"].ToString());
                if (iType == 0)
                    type = PortUnitType.Input;
                else if (iType == 1)
                    type = PortUnitType.Output;
                else
                    return HCACK.ParamInvalid;

                PortMng.Instance.AddPortTypeChgCommand(strPortID, type);

            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("StockerController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                return HCACK.CantPerformNow;
            }
            return HCACK.PerformLater;                
        }

        string strScanDestAddress = "11003";
        private HCACK AnalyzeScanCommand(Dictionary<string, object> dicParameter)
        {
            try
            {
                string strCommandSource = dicParameter["COMMANDSOURCE"].ToString();
                string strCarrierID = dicParameter["CARRIERID"].ToString();
                string strAddress = dicParameter["CARRIERLOC"].ToString();
                uint u2Priority = 50;
                if (dicParameter.ContainsKey("PRIORITY"))
                {
                    u2Priority = Convert.ToUInt16(dicParameter["PRIORITY"]);
                }
                if (string.IsNullOrEmpty(strAddress))
                {
                    if(CarrierMng.Instance.Exist(strCarrierID))
                    {
                        strAddress = CarrierMng.Instance.GetCarrierLocation(strCarrierID);
                    }
                    else
                    {
                        return HCACK.ParamInvalid;
                    }
                }
                lock (GlobalData.Instance.objRWLock)
                {
                    foreach (EnhancedTransferCommand cmd in GlobalData.Instance.gbEnhancedTransfers)
                    {
                        if ( strAddress == cmd.strCarrierLoc || strAddress == cmd.strDest)
                        {
                            return HCACK.RejectAlreadyInCondition;
                        }
                    }
                }
                //双Crane时，需要对CarrierLoc所在的Zone以及2个Crane的位置进行判断，并指定执行Scan的Crane（也会出现不能指定Crane的情况，需要在指令分派的时候指定）
                //单Crane不对位置进行判断，也不指定执行的Crane

                //CarrierID可以为空，如果不为空，检查指令队列中是否存在该Carrier的指令
                //if (!string.IsNullOrEmpty(strCarrierID))
                //{
                //    lock (GlobalData.Instance.objRWLock)
                //    {
                //        foreach (EnhancedTransferCommand cmd in GlobalData.Instance.gbEnhancedTransfers)
                //        {
                //            if (cmd.strCarrierID == strCarrierID)
                //            {
                //                return HCACK.RejectAlreadyInCondition;
                //            }
                //        }
                //    }
                //}

                EnhancedTransferCommand eTransferCmd = new EnhancedTransferCommand();
                eTransferCmd.strCommandName = "SCAN";
                eTransferCmd.strCommandID = TransferMng.Instance.GenScanCommandID();
                eTransferCmd.u2Priority = u2Priority;
                eTransferCmd.strRealSource = strAddress;
                eTransferCmd.strRealDest = strScanDestAddress;
                eTransferCmd.strCarrierID = strCarrierID;
                eTransferCmd.strCarrierLoc = strAddress;
                eTransferCmd.transferState = TransferState.Queue;
                eTransferCmd.strSourceZone = LocationMng.Instance.GetLocationZone(strAddress);
                eTransferCmd.strDestZone = LocationMng.Instance.GetLocationZone(strScanDestAddress);
                TransferMng.Instance.AddTransferCommandInfo(strCommandSource, eTransferCmd);

                LocationReservedCommand reservedSrcCommand = new LocationReservedCommand();
                Dictionary<string, object> dicSourceLoc = new Dictionary<string, object>();
                dicSourceLoc.Add("LOCATION", eTransferCmd.strRealSource);
                reservedSrcCommand.CommandName = "LocationReserved";
                reservedSrcCommand.DicParam = dicSourceLoc;
                reservedSrcCommand.State = ImmediateCommandState.Queue;
                lock (objImmediateCommandLock)
                {
                    immediateCommandQueue.Enqueue(reservedSrcCommand);
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("StockerController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return HCACK.PerformLater;
        }

        private HCACK AnalyzeMultiScanCommand(Dictionary<string, object> dicParameter)
        {
            try
            {
                string strCommandSource = ((int)CommandSource.GUI).ToString();

                foreach(string strAddress in dicParameter.Keys)
                {
                    if (string.IsNullOrEmpty(strAddress))
                    {
                        return HCACK.ParamInvalid;
                    }
                    EnhancedTransferCommand eTransferCmd = new EnhancedTransferCommand();
                    eTransferCmd.strCommandName = "SCAN";
                    eTransferCmd.strCommandID = TransferMng.Instance.GenScanCommandID();
                    eTransferCmd.u2Priority = 999;
                    eTransferCmd.strRealSource = strAddress;
                    eTransferCmd.strRealDest = strAddress;
                    eTransferCmd.transferState = TransferState.Queue;
                    TransferMng.Instance.AddTransferCommandInfo(strCommandSource, eTransferCmd);
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("StockerController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return HCACK.PerformLater;
        }

        private HCACK AnalyzeTransferCommand(Dictionary<string, object> dicParameter)
        {
            lock (GlobalData.Instance.objAnalyzeTransferCommandLock)
            {
                try
                {
                    string strCommandSource = dicParameter["COMMANDSOURCE"].ToString();
                    string strCommandID = dicParameter["COMMANDID"].ToString();
                    uint u2Priority = Convert.ToUInt16(dicParameter["PRIORITY"]);
                    string strCarrierID = dicParameter["CARRIERID"].ToString();
                    string strSource = dicParameter["SOURCE"].ToString();
                    string strDest = dicParameter["DEST"].ToString();

                    //参数检查
                    if (string.IsNullOrEmpty(strCommandID))
                    {
                        //生成Invalid Item,lzt
                        //记录程序异常日志
                        return HCACK.ParamInvalid;
                    }

                    if (u2Priority < 0 || (u2Priority > 99 && u2Priority != 999))
                    {
                        //生成Invalid Item,lzt
                        //记录程序异常日志
                        return HCACK.ParamInvalid;
                    }
                
                    if (string.IsNullOrEmpty(strCarrierID))
                    {
                        //生成Invalid Item,lzt
                        //记录程序异常日志
                        return HCACK.ParamInvalid;
                    }
                    else
                    {
                        if (!CarrierMng.Instance.Exist(strCarrierID))
                        {
                            //生成Invalid Item,lzt
                            //记录程序异常日志
                            return HCACK.NoSuchObjectExists;
                        }
                    }
                
                    if (string.IsNullOrEmpty(strDest))
                    {
                        //生成Invalid Item,lzt
                        //记录程序异常日志
                        return HCACK.ParamInvalid;
                    }
                    //盒子ID、源地址、目的地址、传送类型的判断
                    string strRealSource = "";
                    string strRealDest = "";
                    TransferType transferType = TransferType.Unknown;

                    EnhancedCarrierInfo enhancedCarrierInfo = CarrierMng.Instance.GetEnhancedCarrierInfo(strCarrierID);
                    strRealSource = LocationMng.Instance.GetLocationAddress(enhancedCarrierInfo.strLocationName);
                    LocationType carrierLocType = LocationMng.Instance.GetLocationType(strRealSource);
                    if (carrierLocType == LocationType.Unknown)
                    {
                        //记录程序异常日志
                        return HCACK.CantPerformNow;
                    }

                    if (carrierLocType == LocationType.IoPort)
                    {
                        u2Priority = 60;
                        InPortCSTIDTime stInPortCSTIDTime = PortMng.Instance.GetInPortCSTIDTime(enhancedCarrierInfo.strCarrierZoneName);
                        if (stInPortCSTIDTime.bInputFlag)
                        {
                            PortMng.Instance.SetInPortCSTIDTime(enhancedCarrierInfo.strCarrierZoneName);
                        }
                    }

                    //源地址为空，表示源地址是Crane：传送类型为Place
                    if (string.IsNullOrEmpty(strRealSource))
                    {
                        if (carrierLocType != LocationType.Crane)
                        {
                            //生成Invalid Item,lzt
                            return HCACK.CantPerformNow;
                        }
                    }
                    else
                    {
                        //Source检查
                        if (ZoneMng.Instance.Exists(strSource))
                        {
                            //Source参数为Zone
                            if (strSource != enhancedCarrierInfo.strCarrierZoneName)
                            {
                                //盒子没在Source参数所在的Zone
                                return HCACK.CantPerformNow;
                            }
                        }
                        else
                        {
                            //Source为具体位置:Shelf、Port
                            if (strRealSource != enhancedCarrierInfo.strCarrierLoc)
                            {
                                //盒子位置与Source参数不一致
                                return HCACK.CantPerformNow;
                            }
                        }
                        if (LocationMng.Instance.IsAddressProhibited(strRealSource))
                        {
                            return HCACK.CantPerformNow;
                        }
                    }
                    //Dest检查
                    if (ZoneMng.Instance.Exists(strDest))
                    {
                        if(strSource == strDest)
                        {
                            strRealDest = strRealSource;
                        }
                        else
                        {
                            //Dest为Zone，在该Zone中找一个空的位置
                            strRealDest = LocationMng.Instance.GetEmptyLocation(strDest);
                        }
                    }
                    else
                    {
                        //Dest为具体位置
                        strRealDest = strDest;
                        if(strRealDest == "")
                        {
                            return HCACK.CantPerformNow;
                        }
                    }
          
                    if (LocationMng.Instance.IsAddressProhibited(strRealDest))
                    {
                        return HCACK.CantPerformNow;
                    }

                    LocationType destLocType = LocationMng.Instance.GetLocationType(strRealDest);
                    if (destLocType == LocationType.Unknown)
                    {
                        //记录程序异常日志
                        return HCACK.CantPerformNow;
                    }
                    //判断此TransferCommand是否已存在
                    foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                    {
                        if (enhancedTransferCommand.strCarrierID == strCarrierID
                            && enhancedTransferCommand.strSourceZone == LocationMng.Instance.GetLocationZone(strRealSource)
                            /*&& enhancedTransferCommand.strDestZone == LocationMng.Instance.GetLocationZone(strRealDest)*/)
                        {
                            return HCACK.CantPerformNow;
                        }
                    }

                    switch (carrierLocType)
                    {
                        case LocationType.Crane:
                            {
                                switch (destLocType)
                                {
                                    case LocationType.EqPort:
                                        transferType = TransferType.Crane2EqPort;
                                        break;
                                    case LocationType.IoPort:
                                        transferType = TransferType.Crane2OutPort;
                                        break;
                                    case LocationType.Shelf:
                                        transferType = TransferType.Crane2Shelf;
                                        break;
                                    case LocationType.Crane: //不存在Crane->Crane
                                    default:
                                        transferType = TransferType.Unknown;
                                        break;
                                }
                            }
                            break;
                        case LocationType.EqPort:
                            {
                                switch (destLocType)
                                {
                                    case LocationType.EqPort:
                                        transferType = TransferType.EqPort2EqPort;
                                        break;
                                    case LocationType.IoPort:
                                        transferType = TransferType.EqPort2OutPort;
                                        break;
                                    case LocationType.Shelf:
                                        transferType = TransferType.EqPort2Shlef;
                                        break;
                                    case LocationType.Crane:
                                        transferType = TransferType.EqPort2Crane;
                                        break;
                                    default:
                                        break;
                                }
                            }
                            break;
                        case LocationType.IoPort:
                            {
                                switch (destLocType)
                                {
                                    case LocationType.EqPort:
                                        transferType = TransferType.InPort2EqPort;
                                        break;
                                    case LocationType.IoPort:
                                        transferType = TransferType.InPort2OutPort;
                                        break;
                                    case LocationType.Shelf:
                                        transferType = TransferType.InPort2Shelf;
                                        break;
                                    case LocationType.Crane:
                                        transferType = TransferType.InPort2Crane;
                                        break;
                                    default:
                                        transferType = TransferType.Unknown;
                                        break;
                                }
                            }
                            break;
                        case LocationType.Shelf:
                            {
                                switch (destLocType)
                                {
                                    case LocationType.EqPort:
                                        transferType = TransferType.Shelf2EqPort;
                                        break;
                                    case LocationType.IoPort:
                                        transferType = TransferType.Shelf2OutPort;
                                        break;
                                    case LocationType.Shelf:
                                        transferType = TransferType.Shelf2Shelf;
                                        break;
                                    case LocationType.Crane:
                                        transferType = TransferType.Shelf2Crane;
                                        break;
                                    default:
                                        transferType = TransferType.Unknown;
                                        break;
                                }
                            }
                            break;
                    }
                    if (transferType == TransferType.Unknown)
                    {
                        LocationMng.Instance.UnReserveLocation(strRealDest);
                        //记录程序异常日志
                        return HCACK.CantPerformNow;
                    }
                    LocationReservedCommand reservedSrcCommand = new LocationReservedCommand();
                    Dictionary<string, object> dicSourceLoc = new Dictionary<string, object>();
                    dicSourceLoc.Add("LOCATION", strRealSource);
                    reservedSrcCommand.CommandName = "LocationReserved";
                    reservedSrcCommand.DicParam = dicSourceLoc;
                    reservedSrcCommand.State = ImmediateCommandState.Queue;
                    lock (objImmediateCommandLock)
                    {
                        immediateCommandQueue.Enqueue(reservedSrcCommand);
                    }

                    LocationMng.Instance.ReserveLocation(strRealDest);

                    //通过以上的参数检查，添加Transfer指令
                    EnhancedTransferCommand eTransferCommand = new EnhancedTransferCommand();
                    eTransferCommand.strCommandName = "TRANSFER";
                    eTransferCommand.strCommandID = strCommandID;
                    eTransferCommand.u2Priority = u2Priority;
                    eTransferCommand.strCarrierID = strCarrierID;
                    eTransferCommand.strCarrierLoc = strSource;
                    eTransferCommand.strDest = strDest;
                    eTransferCommand.strStockerCraneID = GlobalData.Instance.gbStockerCraneID;  //单Crane时，固定分派
                    eTransferCommand.iFirstSecendCrane = 1;
                    eTransferCommand.transferState = TransferState.Queue;
                    eTransferCommand.strRealSource = strRealSource;
                    eTransferCommand.strRealDest = strRealDest;
                    eTransferCommand.sourceLocType = LocationMng.Instance.GetLocationType(strRealSource); ;
                    eTransferCommand.destLocType = destLocType;
                    eTransferCommand.transferType = transferType;
                    eTransferCommand.strSourceZone = LocationMng.Instance.GetLocationZone(strRealSource);
                    eTransferCommand.strDestZone = LocationMng.Instance.GetLocationZone(strRealDest);
                    TransferMng.Instance.AddTransferCommandInfo(strCommandSource, eTransferCommand);
                    //如果是界面手动下发的命令，向MCS上报254事件
                    if (strCommandSource != "0")
                    {
                        HostIF.Instance.PostManualTransferEvent(strCommandID);
                    }
                }
                catch (Exception ex)
                {
                    Log.Logger.Instance.ExceptionLog("StockerController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                }
                return HCACK.PerformLater;
            }
        }

        private HCACK AnalyzeSetCraneSpeedCommand(Dictionary<string, object> dicParameter)
        {
            //指令参数检查
            string craneID = dicParameter["CRANE"].ToString();
            int emptySpeed = Convert.ToInt32(dicParameter["EMPTYSPEED"]);
            int loadSpeed = Convert.ToInt32(dicParameter["LOADSPEED"]);

            //参数检查
            if (string.IsNullOrEmpty(craneID))
            {
                //生成Invalid Item,lzt
                return HCACK.ParamInvalid;
            }

            CraneSetSpeedCommand command = new CraneSetSpeedCommand();
            command.CommandName = "SETCRANESPEED";
            command.DicParam = dicParameter;
            command.State = ImmediateCommandState.Queue;
            lock (objImmediateCommandLock)
            {
                immediateCommandQueue.Enqueue(command);
            }
            return HCACK.PerformLater;
        }

        private HCACK AnalyzeSetPortSpeedCommand(Dictionary<string, object> dicParameter)
        {
            //指令参数检查
            string portID = dicParameter["PORT"].ToString();
            //int speed = Convert.ToInt32(dicParameter["SPEED"]);
            int emptySpeed = Convert.ToInt32(dicParameter["EMPTYSPEED"]);
            int loadSpeed = Convert.ToInt32(dicParameter["LOADSPEED"]);

            //参数检查
            if (string.IsNullOrEmpty(portID))
            {
                //生成Invalid Item,lzt
                return HCACK.ParamInvalid;
            }


            PortSetSpeedCommand command = new PortSetSpeedCommand();
            command.CommandName = "SETPORTSPEED";
            command.DicParam = dicParameter;
            command.State = ImmediateCommandState.Queue;
            lock (objImmediateCommandLock)
            {
                immediateCommandQueue.Enqueue(command);
            }
            return HCACK.PerformLater;
        }

        private HCACK AnalyzeSetFFUSpeedCommand(Dictionary<string, object> dicParameter)
        {
            //指令参数检查
            string strFFUList = dicParameter["ADDRESS"].ToString();
            int iSpeed = Convert.ToInt32(dicParameter["SPEED"]);

            //参数检查
            if (string.IsNullOrEmpty(strFFUList))
            {
                return HCACK.ParamInvalid;
            }

            FFUSetSpeedCommand command = new FFUSetSpeedCommand();
            command.CommandName = "SETFFUSPEED";
            command.DicParam = dicParameter;
            command.State = ImmediateCommandState.Queue;
            lock (objImmediateCommandLock)
            {
                immediateCommandQueue.Enqueue(command);
            }
            return HCACK.PerformLater;
        }

        private HCACK AnalyzeSetLocationEnabled(Dictionary<string, object> dicParameter)
        {
            //指令参数检查
            string strLocation = dicParameter["LOCATION"].ToString();
            int iEnabled = -1;

            //参数检查
            if (string.IsNullOrEmpty(strLocation))
            {
                return HCACK.ParamInvalid;
            }
            if(Int32.TryParse(dicParameter["ENABLED"].ToString(), out iEnabled) == false)
            {
                return HCACK.ParamInvalid;
            }

            //写数据
            if(iEnabled == 1)
            {
                LocationMng.Instance.EnableLocation(strLocation);
            }
            else if (iEnabled == 0)
            {
                LocationMng.Instance.DisableLocation(strLocation);
            }

            //上报ShelfStateChanged
            if (LocationMng.Instance.GetLocationType(strLocation) == LocationType.Shelf)
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    GlobalData.Instance.gbZoneName = LocationMng.Instance.GetLocationZone(strLocation);
                    GlobalData.Instance.gbZoneCapacity = ZoneMng.Instance.GetZoneCapacity(GlobalData.Instance.gbZoneName);
                    GlobalData.Instance.gbDisabledLocations.Clear();
                    foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                    {
                        if (loc.IsProhibited && loc.locType == LocationType.Shelf)
                        {
                            DisabledLoc disLoc = new DisabledLoc();
                            disLoc.strCarrierId = loc.strCarrierID;
                            disLoc.strCarrierLoc = loc.strAddress;
                            GlobalData.Instance.gbDisabledLocations.Add(disLoc);
                        }
                    }
                    HostIF.Instance.PostEvent((uint)EqpEvent.ShelfStateChanged);
                }
            }

            return HCACK.Acknowledge;
        }

        private HCACK AnalyzeSetMultiLocationsEnabled(Dictionary<string, object> dicParameter)
        {
            foreach(string strLocation in dicParameter.Keys)
            {
                string strEnabled = dicParameter[strLocation].ToString();

                //参数检查
                if (string.IsNullOrEmpty(strLocation))
                {
                    return HCACK.ParamInvalid;
                }
                int iEnabled = -1;
                if (Int32.TryParse(strEnabled, out iEnabled) == false)
                {
                    return HCACK.ParamInvalid;
                }

                //写数据
                if (iEnabled == 1)
                {
                    LocationMng.Instance.EnableLocation(strLocation);
                }
                else if (iEnabled == 0)
                {
                    LocationMng.Instance.DisableLocation(strLocation);
                }
            }
            return HCACK.Acknowledge;
        }

        private HCACK AnalyzeClearReserved(Dictionary<string, object> dicParameter)
        {
            ClearReservedCommand clearCommand = new ClearReservedCommand();
            clearCommand.CommandName = "ClearReserved";
            clearCommand.DicParam = dicParameter;
            clearCommand.State = ImmediateCommandState.Queue;
            lock (objImmediateCommandLock)
            {
                immediateCommandQueue.Enqueue(clearCommand);
            }
            return HCACK.PerformLater;
        }

        private HCACK AnalyzeSetServerTags(Dictionary<string, object> dicParameter)
        {
            List<KeyValuePair<string, object>> keyValues = new List<KeyValuePair<string, object>>();
            foreach(string strKey in dicParameter.Keys)
            {
                keyValues.Add(new KeyValuePair<string, object>(strKey, dicParameter[strKey]));
            }
            if(PlcComm.Instance.WriteTagValue(keyValues))
            {
                return HCACK.Acknowledge;
            }
            else
            {
                return HCACK.RejectAlreadyInCondition;
            }
        }

        public static StockerController Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new StockerController();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public void CheckPLCConnectStatus()
        {
            if (Proj.CacheData.GlobalData.Instance.gbSimulation)
            {
                return;
            }

            while (true)
            {
                Thread.Sleep(1000);
                string craneTag = "Global.PLCCommunicationState";
                object objPLC = PlcComm.Instance.GetTagValue(craneTag);

                int iPLCConnectStatus = Convert.ToInt32(objPLC);
                if (1 != iPLCConnectStatus)
                {
                    Log.Logger.Instance.ExceptionLog("PLC通信断开");
                    if (!PlcComm.Instance.Start())
                    {
                        //记录程序异常日志：PLC通信初始化失败
                        Log.Logger.Instance.ExceptionLog("PLC通信初始化失败");

                        //如果10次链接不成功，通知client端重启
                        iCheckCount++;
                        if (iCheckCount == 10)
                        {
                            iCheckCount = 0;

                            //通过WCF通知GUI弹出报警
                            Dictionary<string, object> dicParams = new Dictionary<string, object>();
                            dicParams.Add("ConnectPLCFailed", "");
                            Proj.WCF.WCFService.Instance.ServerSendMessage("ConnectPLC", dicParams);

                            return;
                        }        
                    }
                }
            }
        }
        public bool Initialize()
        {
            try
            {
                //PLC通信初始化
                if (!PlcComm.Instance.Start())
                {
                    //记录程序异常日志：PLC通信初始化失败
                    Log.Logger.Instance.ExceptionLog("PLC通信初始化失败");
                    return false;
                }

                //数据库数据初始化：Carrier、Port、Location、Zone、Crane
                if (!CarrierMng.Instance.Initialize())
                {
                    //记录程序异常日志：Carrier数据初始化失败
                    Log.Logger.Instance.ExceptionLog("Carrier数据初始化失败");
                    return false;
                }

                if (!PortMng.Instance.Initialize())
                {
                    //记录程序异常日志：Port数据初始化失败
                    Log.Logger.Instance.ExceptionLog("Port数据初始化失败");
                    return false;
                }

                if (!LocationMng.Instance.Initialize())
                {
                    //记录程序异常日志：Location数据初始化失败
                    Log.Logger.Instance.ExceptionLog("Location数据初始化失败");
                    return false;
                }

                if (!ZoneMng.Instance.Initialize())
                {
                    //记录程序异常日志：Zone数据初始化失败
                    Log.Logger.Instance.ExceptionLog("Zone数据初始化失败");
                    return false;
                }

                if (!CraneMng.Instance.Initialize())
                {
                    //记录程序异常日志：Crane数据初始化失败
                    Log.Logger.Instance.ExceptionLog("Crane数据初始化失败");
                    return false;
                }

                if (!TransferMng.Instance.Initialize())
                {
                    //记录程序异常日志：Transfer数据初始化失败
                    Log.Logger.Instance.ExceptionLog("Transfer数据初始化失败");
                    return false;
                }

                //SC内部初始化
                //创建线程监控设备
                string strRes = "";

                cmdScheduleThread = new ThreadBaseModel(2, "CommandScheduleThread ");
                cmdScheduleThread.LoopInterval = 300;
                cmdScheduleThread.SetThreadRoutine(cmdScheduleThreadFunc);
                cmdScheduleThread.TaskInit(ref strRes);
                cmdScheduleThread.Start(ref strRes);

                immediateCmdThread = new ThreadBaseModel(1, "ImmediateExecuteCommandThread ");
                immediateCmdThread.LoopInterval = 200;
                immediateCmdThread.SetThreadRoutine(immediateCmdThreadFunc);
                immediateCmdThread.TaskInit(ref strRes);
                immediateCmdThread.Start(ref strRes);

                calcServerRunCountThread = new ThreadBaseModel(1, "calcServerRunCountThread ");
                calcServerRunCountThread.LoopInterval = 5000;
                calcServerRunCountThread.SetThreadRoutine(calcServerRunCountThreadFunc);
                calcServerRunCountThread.TaskInit(ref strRes);
                calcServerRunCountThread.Start(ref strRes);


                //日志压缩
                LogBackUpCmdThread = new ThreadBaseModel(3501, "LogBackUpCmdThread ");
                LogBackUpCmdThread.LoopInterval = 3500 * 1000;
                LogBackUpCmdThread.SetThreadRoutine(dealLogBackUpCmdThreadFunc);
                LogBackUpCmdThread.TaskInit(ref strRes);
                LogBackUpCmdThread.Start(ref strRes);

                ushort usDeiceID = 1;
                string strIP = "127.0.0.1";
                ushort usPort = 5001;
                XmlDocument xmlConfig = new XmlDocument( );
                string strBinPath = System.IO.Directory.GetCurrentDirectory();
                xmlConfig.Load(strBinPath + @"\config\secs-config.xml");
                foreach (XmlNode node in xmlConfig.SelectNodes("SECSConfig/Host"))
                {
                    usDeiceID = Convert.ToUInt16(node.Attributes["deviceid"].Value);
                    strIP = node.Attributes["ip"].Value;
                    usPort = Convert.ToUInt16(node.Attributes["port"].Value);
                    GlobalData.Instance.gbEqpName = node.Attributes["eqpname"].Value;
                }

                //Host通信
                HsmsConfig cfgPassive = new HsmsConfig()
                {
                    Mode = HsmsMode.Passive,
                    DeviceID = usDeiceID,
                    LocalIP = strIP,
                    LocalPort = usPort,
                    LinkTestInterval = 10000,
                };
                HostIF.Instance.Initialize();
                HostIF.Instance.StartGEM(cfgPassive);
                //检查PLC通信
                Thread CheckPLCThread = new Thread(CheckPLCConnectStatus);
                CheckPLCThread.Start();

                g_logBack = new LogBacker();

                return true;
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("StockerController.cs, Initialize: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool UnInit()
        {
            string strRes = "";
            if(cmdScheduleThread != null)
            {
                cmdScheduleThread.TaskExit(ref strRes);
            }

            if (immediateCmdThread != null)
            {
                immediateCmdThread.TaskExit(ref strRes);
            }
            if(LogBackUpCmdThread != null)
            {
                LogBackUpCmdThread.TaskExit(ref strRes);
            }
            CraneMng.Instance.UnInit();
            PortMng.Instance.UnInit();
            PlcComm.Instance.Stop();
            HostIF.Instance.StopGEM();
            return true;
        }
        
        public void AutoAddTransferCommand(Dictionary<string, object> dicParameter)
        {
            if (!TransferMng.Instance.IsHaveCraneCommand(dicParameter["SOURCE"].ToString(), dicParameter["CARRIERID"].ToString()))
            {
                string strDest = LocationMng.Instance.GetEmptyShelf();
                if (string.IsNullOrEmpty(strDest))
                {
                    HistoryWriter.Instance.EqpEventLog("SC", 0, "AutoAddTransferCommandFailed", "AutoAddTransferCommandFailed: Dest IsNullOrEmpty");
                    return;
                }

                string strCmdID = TransferMng.Instance.GenManualCommandID();
                dicParameter.Add("COMMANDID", strCmdID);
                dicParameter.Add("COMMANDSOURCE", ((int)CommandSource.SC).ToString());
                dicParameter.Add("PRIORITY", 99);

                dicParameter.Add("DEST", strDest);
                AnalyzeTransferCommand(dicParameter);
            }
        }

        public void AutoAddDoubleCheckCommand()
        {
            HistoryWriter.Instance.EqpEventLog("SC", 0, "AutoAddDoubleCheckCommand", "AutoAddDoubleCheckCommand");
            string strDest = LocationMng.Instance.GetDoubleCheckShelf();
            if (string.IsNullOrEmpty(strDest))
            {
                HistoryWriter.Instance.EqpEventLog("SC", 0, "AutoAddDoubleCheckCommandFailed", "AutoAddDoubleCheckCommandFailed: Dest IsNullOrEmpty");
                return;
            }

            //添加Transfer指令
            EnhancedTransferCommand eTransferCommand = new EnhancedTransferCommand();
            eTransferCommand.strCommandName = "DOUBLECHECK";
            eTransferCommand.strCommandID = TransferMng.Instance.GenManualCommandID(); ;
            eTransferCommand.u2Priority = 1;
            eTransferCommand.strCarrierID = "TEST";
            eTransferCommand.strCarrierLoc = strDest;
            eTransferCommand.strDest = strDest;
            eTransferCommand.strStockerCraneID = GlobalData.Instance.gbStockerCraneID;  //单Crane时，固定分派
            eTransferCommand.iFirstSecendCrane = 1;
            eTransferCommand.transferState = TransferState.Queue;
            eTransferCommand.strRealSource = strDest;
            eTransferCommand.strRealDest = strDest;
            eTransferCommand.sourceLocType = LocationType.Shelf; ;
            eTransferCommand.destLocType = LocationType.Shelf;
            eTransferCommand.transferType = TransferType.DoubleCheck;
            eTransferCommand.strSourceZone = LocationMng.Instance.GetLocationZone(strDest);
            eTransferCommand.strDestZone = LocationMng.Instance.GetLocationZone(strDest);
            TransferMng.Instance.AddTransferCommandInfo(((int)CommandSource.SC).ToString(), eTransferCommand);

            //清除搬运计数
            CraneMng.Instance.ResetCranePlaceCount();
        }

        public void AutoAddInPortReadFailedOutCommand(string strInPorttrName)
        {
            EnhancedTransferCommand eTransferCommand = new EnhancedTransferCommand();
            eTransferCommand.strCommandName = "TRANSFER";
            eTransferCommand.strCommandID = TransferMng.Instance.GenManualCommandID();
            eTransferCommand.u2Priority = 999;
            eTransferCommand.strCarrierID = LocationMng.Instance.GetLocationCarrierByAddress(PortMng.Instance.GetIOAddressByPortName(strInPorttrName));
            eTransferCommand.strCarrierLoc = PortMng.Instance.GetIOAddressByPortName(strInPorttrName);
            eTransferCommand.strDest = PortMng.Instance.GetIOAddressByPortName(GlobalData.Instance.gbOutPorttrName);
            eTransferCommand.strStockerCraneID = GlobalData.Instance.gbStockerCraneID;  //单Crane时，固定分派
            eTransferCommand.iFirstSecendCrane = 1;
            eTransferCommand.transferState = TransferState.Queue;
            eTransferCommand.strRealSource = PortMng.Instance.GetIOAddressByPortName(strInPorttrName);
            eTransferCommand.strRealDest = PortMng.Instance.GetIOAddressByPortName(GlobalData.Instance.gbOutPorttrName) ;
            eTransferCommand.sourceLocType = LocationType.IoPort;
            eTransferCommand.destLocType = LocationType.IoPort;
            eTransferCommand.transferType = TransferType.InPort2OutPort;
            eTransferCommand.strSourceZone = LocationMng.Instance.GetLocationZone(eTransferCommand.strRealSource);
            eTransferCommand.strDestZone = LocationMng.Instance.GetLocationZone(eTransferCommand.strRealDest);
            TransferMng.Instance.AddTransferCommandInfo(((int)CommandSource.SC).ToString(), eTransferCommand);

            HostIF.Instance.PostManualTransferEvent(eTransferCommand.strCommandID);
        }


        public void AutoAddPortInStockerCommand(string strPortLocation, string strCarrierId)
        {
            lock(GlobalData.Instance.objAutoInTransferCommandLock)
            {
                HistoryWriter.Instance.EqpEventLog("自动入库", 0, "AutoAddPortInStockerCommand", "AutoAddPortInStockerCommand");

                LocationReservedCommand reservedSrcCommand = new LocationReservedCommand();
                Dictionary<string, object> dicSourceLoc = new Dictionary<string, object>();
                dicSourceLoc.Add("LOCATION", strPortLocation);
                reservedSrcCommand.CommandName = "LocationReserved";
                reservedSrcCommand.DicParam = dicSourceLoc;
                reservedSrcCommand.State = ImmediateCommandState.Queue;
                lock (objImmediateCommandLock)
                {
                    immediateCommandQueue.Enqueue(reservedSrcCommand);
                }

                string strRealDest = LocationMng.Instance.GetEmptyShelf();
                LocationMng.Instance.ReserveLocation(strRealDest);

                EnhancedTransferCommand eTransferCommand = new EnhancedTransferCommand();
                eTransferCommand.strCommandName = "TRANSFER";
                eTransferCommand.strCommandID = TransferMng.Instance.GenManualCommandID();
                eTransferCommand.u2Priority = 60;
                eTransferCommand.strCarrierID = strCarrierId;
                eTransferCommand.strCarrierLoc = strPortLocation;
                eTransferCommand.strDest = strRealDest;
                eTransferCommand.strStockerCraneID = GlobalData.Instance.gbStockerCraneID;  //单Crane时，固定分派
                eTransferCommand.iFirstSecendCrane = 1;
                eTransferCommand.transferState = TransferState.Queue;
                eTransferCommand.strRealSource = strPortLocation;
                eTransferCommand.strRealDest = eTransferCommand.strDest;
                eTransferCommand.sourceLocType = LocationType.IoPort;
                eTransferCommand.destLocType = LocationType.Shelf;
                eTransferCommand.transferType = TransferType.InPort2Shelf;
                eTransferCommand.strSourceZone = LocationMng.Instance.GetLocationZone(eTransferCommand.strRealSource);
                eTransferCommand.strDestZone = LocationMng.Instance.GetLocationZone(eTransferCommand.strRealDest);
                TransferMng.Instance.AddTransferCommandInfo(((int)CommandSource.SC).ToString(), eTransferCommand);

                HostIF.Instance.PostManualTransferEvent(eTransferCommand.strCommandID);
            }
        }


        /// <summary>
        /// 将Crane动作事件发送到UI（请先写数据库，再调用此函数）
        /// </summary>
        /// <param name="strCraneName">Crane名字</param>
        /// <param name="strAction">PickStart/PickComplete/PlaceStart/PlaceComplete</param>
        /// <param name="strCarrierID"></param>
        /// <param name="strSource">源位置，如果是Crane则填写CraneID</param>
        /// <param name="strDest">目标位置，如果是Crane则填写CraneID</param>
        public void SendCraneActionToUI(string strCraneName,string strAction, string strCarrierID, string strSource, string strDest)
        {
            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("CARRIERID", strCarrierID);
            dicParams.Add("SOURCE", strSource);
            dicParams.Add("DEST", strDest);
            WCFService.Instance.ServerSendMessage(strAction, dicParams);
        }

        private void Instance_EventStateChange(string strStateName, string strStateValue)
        {
            try
            {              
                TntKeyvalueconfig tntKeyvalueconfig = TntKeyvalueconfig.GetByLambda(x => x.Key == strStateName);
                if (tntKeyvalueconfig != null)
                {
                    tntKeyvalueconfig.Vlaue = strStateValue;
                    tntKeyvalueconfig.Save();
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("StockerController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            
            SendStateToUI(strStateName, strStateValue);
        }

        private void SendStateToUI(string strStateName, string strStateValue)
        {
            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("NAME", strStateName);
            dicParams.Add("VALUE", strStateValue);
            WCFService.Instance.ServerSendMessage("UpdateState", dicParams);
        }

        private void WriteStockerStatus(string strStateName, string strStateValue)
        {
            try
            {
                if ("SC State" == strStateName)
                {
                    TpStockerStatus tpStockerStatus = TpStockerStatus.GetByLambda(x => x.Name == "STKCStatus");
                    if (GlobalData.Instance.gbSCState == SCState.Auto)
                    {
                        tpStockerStatus.Value = "1";
                    }
                    else
                    {
                        tpStockerStatus.Value = "0";
                    }

                    tpStockerStatus.Save();
                }
            }
            catch (Exception ex)
            {
                HistoryWriter.Instance.ExceptionLog("StockerController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }
        private bool UIChangeState(string strStateName, string strStateValue)
        {
            bool bResult = false;
            lock (GlobalData.Instance.objRWLock)
            {
                switch(strStateName)
                {
                    case "Control State":
                        switch (strStateValue)
                        {
                            case "Offline":
                                if(HostIF.Instance.SwitchOffline() == SecsLite.Gem.GemResult.Success)
                                {
                                    bResult = true;
                                }
                                break;
                            case "Online":
                                if (HostIF.Instance.SwitchOnline() == SecsLite.Gem.GemResult.Success)
                                {
                                    bResult = true;
                                }
                                break;
                            case "OnlineLocal":
                                if(HostIF.Instance.SwitchLocal() == SecsLite.Gem.GemResult.Success)
                                {
                                    bResult = true;
                                }
                                break;
                            case "OnlineRemote":
                                if(HostIF.Instance.SwitchRemote() == SecsLite.Gem.GemResult.Success)
                                {
                                    bResult = true;
                                }
                                break;
                        }
                        break;
                    case "SC State":
                        bResult = Enum.TryParse(strStateValue, true, out GlobalData.Instance.gbSCState);
                        if(GlobalData.Instance.gbSCState == SCState.Auto)
                        {
                            if(CraneMng.Instance.CraneHasAlarm())
                            {
                                //GlobalData.Instance.gbSCState = SCState.Paused;
                               // HostIF.Instance.PostScEvent(EqpEvent.SCPaused);
                                CraneMng.Instance.CraneOperationStateChg(GlobalData.Instance.gbStockerCraneID, CraneOperationState.Down);
                                HostIF.Instance.PostCraneEvent(GlobalData.Instance.gbStockerCraneID, EqpEvent.CraneOutOfService);
                            }
                            else
                            {
                                HostIF.Instance.PostScEvent(EqpEvent.SCAutoComplete);
                                CraneMng.Instance.CraneOperationStateChg(GlobalData.Instance.gbStockerCraneID, CraneOperationState.Normal);
                                HostIF.Instance.PostCraneEvent(GlobalData.Instance.gbStockerCraneID, EqpEvent.CraneInService);
                            }
                        }
                        else if (GlobalData.Instance.gbSCState == SCState.Paused)
                        {
                            HostIF.Instance.PostScEvent(EqpEvent.SCPauseCompleted);
                            if (CraneMng.Instance.CraneHasAlarm())
                            {
                                CraneMng.Instance.CraneOperationStateChg(GlobalData.Instance.gbStockerCraneID, CraneOperationState.Down);
                                HostIF.Instance.PostCraneEvent(GlobalData.Instance.gbStockerCraneID, EqpEvent.CraneOutOfService);
                            }
                        }
                        break;
                    case "SC Mode":
                        bResult = Enum.TryParse(strStateValue, true, out GlobalData.Instance.gbSCMode);
                        break;
                }

                //WriteStockerStatus(strStateName, strStateValue);
            }
            return bResult;
        }

        /// <summary>
        /// 新增，检查Crane是否满足Abort条件
        /// </summary>
        private bool CheckCraneCanAbort()
        {
            CraneTaskStep craneStep = CraneDev.Instance.GetCraneTaskStep("Crane1");
            if ((craneStep.iStep == 20 && craneStep.iAction > 20 && craneStep.iAction < 50) 
             || (craneStep.iStep == 40 && craneStep.iAction > 10))
            {
                return false;
            }
            else
            {
                return true;
            }
        }
    }
}
