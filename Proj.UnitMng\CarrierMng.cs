﻿using System;
using System.Linq;

using Proj.Entity;
using Proj.DB;
using Proj.CacheData;
using Proj.DataTypeDef;
using Proj.History;
using Proj.HostComm;

namespace Proj.UnitMng
{
    public class CarrierMng
    {
        private static CarrierMng m_Instanse;
        private static readonly object mSyncObject = new object();
        private CarrierMng() { }
        public static CarrierMng Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new CarrierMng();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public bool Initialize()
        {
            bool bRes = false;
            string strCarrierID = "";
            string strCarrierLoc = "";
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    // 读取数据库
                    TpLocationList locList = TpLocationList.GetAll();
                    // 更新界面
                    foreach (TpLocation loc in locList)
                    {
                        if (loc.IsOccupied == 1)
                        {
                            strCarrierID = loc.CarrierId;
                            strCarrierLoc = loc.Address;
                            TpCarrier cst = TpCarrier.GetByLambda(x => x.Id == loc.CarrierId);
                            if (cst == null)
                            {
                                AddCarrierInfo(loc.CarrierId, loc.Address);
                            }
                        }
                    }

                    TpCarrierViewList carrierList = DbCarrier.Instance.GetDbCarrierList();
                    GlobalData.Instance.gbEnhancedCarriers.Clear();
                    foreach (TpCarrierView carrier in carrierList)
                    {
                        strCarrierID = carrier.Id;
                        strCarrierLoc = carrier.Location;
                        EnhancedCarrierInfo enCarrier = new EnhancedCarrierInfo();
                        enCarrier.strCarrierID = carrier.Id;
                        enCarrier.strCarrierLoc = carrier.Location; 
                        if(carrier.LocName == null || carrier.LocName == "")
                        {
                            TpCarrierList.DeleteByCriteria(x => x.Id == carrier.Id);
                        }
                        enCarrier.strLocationName = carrier.LocName;
                        enCarrier.strCarrierZoneName = carrier.ZoneName;
                        enCarrier.strInstallTime = carrier.InstallTime?.ToString("yyyyMMddHHmmssff");
                        enCarrier.carrierState = (CarrierState)uint.Parse(carrier.State);
                        enCarrier.locType = (LocationType)Convert.ToInt32(carrier.Type);
                        GlobalData.Instance.gbEnhancedCarriers.Add(enCarrier);
                    }
                    bRes = true;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("CarrierMng.cs: Initialize Failed, CarrierID: " + strCarrierID
                    + ", CarrierLocation: " + strCarrierLoc + ", Stack: " + ex.StackTrace);


            }

            return bRes;
        }

        public bool Exist(string strCarrierID)
        {
            bool bRes = false;
            lock(GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedCarrierInfo enCarrier in GlobalData.Instance.gbEnhancedCarriers)
                {
                    if (enCarrier.strCarrierID == strCarrierID)
                    {
                        bRes = true;
                        break;
                    }
                }
            }

            return bRes;
        }

        public EnhancedCarrierInfo GetEnhancedCarrierInfo(string strCarrierID)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                int iCount = GlobalData.Instance.gbEnhancedCarriers.Count;
                for (int i = 0; i < iCount; i++)
                {
                    if (GlobalData.Instance.gbEnhancedCarriers[i].strCarrierID == strCarrierID)
                    {
                        return GlobalData.Instance.gbEnhancedCarriers[i];
                    }
                }
            }

            return null;
        }

        public bool AddCarrierInfo(string strCarrierID, string strCarrierLoc)
        {
            if (ZoneMng.Instance.Exists(strCarrierLoc))
            {
                strCarrierLoc = LocationMng.Instance.GetLocationAddress(strCarrierLoc);
            }
            if (DbTrans.Instance.AddCarrier(strCarrierID, strCarrierLoc))
            {
                //记录Carrier日志，在strCarrierLoc位置添加strCarrierID盒子成功
                HistoryWriter.Instance.EqpEventLog(strCarrierID, (int)Proj.HostComm.EqpEvent.CarrierInstallCompleted,
                    Proj.HostComm.EqpEvent.CarrierInstallCompleted.ToString(), $"Add Carrier: {strCarrierID} at {strCarrierLoc} strCarrierLoc success");

                //HostIF.Instance.PostCarrierEvent(strCarrierID, EqpEvent.ZoneCapacityChange);
                return true;   
            }
            //记录Carrier日志，在strCarrierLoc位置添加strCarrierID盒子失败
            HistoryWriter.Instance.EqpEventLog(strCarrierID, (int)Proj.HostComm.EqpEvent.CarrierInstallCompleted,
                Proj.HostComm.EqpEvent.CarrierInstallCompleted.ToString(), $"Add Carrier: {strCarrierID} at {strCarrierLoc} strCarrierLoc fail");

            return false;
        }

        public bool UpdateCarrierInfo(string strCarrierID, string strCarrierLoc)
        {
            if (ZoneMng.Instance.Exists(strCarrierLoc))
            {
                strCarrierLoc = LocationMng.Instance.GetLocationAddress(strCarrierLoc);
            }
            if (DbTrans.Instance.UpdateCarrier(strCarrierID, strCarrierLoc))
            {
                //记录Carrier日志，在strCarrierLoc位置更新strCarrierID盒子成功
                HistoryWriter.Instance.EqpEventLog(strCarrierID, (int)Proj.HostComm.EqpEvent.CarrierInstallCompleted,
                    Proj.HostComm.EqpEvent.CarrierInstallCompleted.ToString(), $"Update Carrier: {strCarrierID} at {strCarrierLoc} strCarrierLoc success");
                return true;
            }
            //记录Carrier日志，在strCarrierLoc位置更新strCarrierID盒子失败
            HistoryWriter.Instance.EqpEventLog(strCarrierID, (int)Proj.HostComm.EqpEvent.CarrierInstallCompleted,
                Proj.HostComm.EqpEvent.CarrierInstallCompleted.ToString(), $"Update Carrier: {strCarrierID} at {strCarrierLoc} strCarrierLoc fail");

            return false;
        }

        public bool DeleteCarrierInfo(string strCarrierID)
        {
            if (DbTrans.Instance.DeleteCarrier(strCarrierID))
            {
                //记录Carrier日志，删除strCarrierID盒子成功
                HistoryWriter.Instance.EqpEventLog(strCarrierID, (int)Proj.HostComm.EqpEvent.CarrierRemoveCompleted,
                    Proj.HostComm.EqpEvent.CarrierRemoveCompleted.ToString(), $"Delete Carrier: {strCarrierID} success");

               // HostIF.Instance.PostCarrierEvent(strCarrierID, EqpEvent.ZoneCapacityChange);
                return true;
            }
            //记录Carrier日志，删除strCarrierID盒子失败
            HistoryWriter.Instance.EqpEventLog(strCarrierID, (int)Proj.HostComm.EqpEvent.CarrierRemoveCompleted,
                Proj.HostComm.EqpEvent.CarrierRemoveCompleted.ToString(), $"Delete Carrier: {strCarrierID} fail");
            return false;
        }

        public string GetCarrierLocation(string strCarrierID)
        {
            string carrierLoc = "";
            lock (GlobalData.Instance.objRWLock)
            {
                //GlobalData.Instance.gbEnhancedCarriers.Where(x => x.strCarrierID == strCarrierID).FirstOrDefault();
                foreach (EnhancedCarrierInfo enCarrier in GlobalData.Instance.gbEnhancedCarriers)
                {
                    if (enCarrier.strCarrierID == strCarrierID)
                    {
                        carrierLoc = enCarrier.strCarrierLoc;
                        break;
                    }
                }
            }

            return carrierLoc;
        }

        public void CranePickCarrier(string craneID, string carrierID, string sourceLocation)
        {
            if (DbTrans.Instance.CranePickCarrier(craneID, carrierID, sourceLocation))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    string strCraneZoneName = "";
                    string strSouceZoneName = "";
                    foreach (LocationInfo locationInfo in GlobalData.Instance.gbLocations)
                    {
                        if (locationInfo.strAddress == sourceLocation)
                        {
                            locationInfo.strCarrierID = "";
                            locationInfo.IsOccupied = false;
                            strSouceZoneName = locationInfo.strZoneName;
                            break;
                        }
                    }
                    string strCraneAddress = "";
                    string strCarrierLoc = "";
                    string strCarrierLocName = "";
                    LocationType strLocType = LocationType.Shelf;
                    foreach (LocationInfo locationInfo in GlobalData.Instance.gbLocations)
                    {
                        if (locationInfo.strName == craneID)
                        {
                            strCraneAddress = locationInfo.strAddress;
                            strCraneZoneName = locationInfo.strZoneName;
                            locationInfo.strCarrierID = carrierID;
                            locationInfo.IsOccupied = true;
                            strCarrierLoc = locationInfo.strAddress;
                            strCarrierLocName = locationInfo.strName;
                            strLocType = locationInfo.locType;
                            break;
                        }
                    }
                    foreach (ExtendedZoneData extendedZoneData in GlobalData.Instance.gbExtendedActiveZones)
                    {
                        if (extendedZoneData.strZoneName == strSouceZoneName)
                        {
                            if(extendedZoneData.u2ZoneCapacity < 1000)
                            {
                                extendedZoneData.u2ZoneCapacity += 1;
                            }
                            break;
                        }
                    }
                    foreach (EnhancedCarrierInfo enhancedCarrierInfo in GlobalData.Instance.gbEnhancedCarriers)
                    {
                        if (enhancedCarrierInfo.strCarrierID == carrierID)
                        {
                            enhancedCarrierInfo.strCarrierLoc = strCarrierLoc;
                            enhancedCarrierInfo.strLocationName = strCarrierLocName;
                            enhancedCarrierInfo.strCarrierZoneName = strCraneZoneName;
                            enhancedCarrierInfo.locType = strLocType;
                            break;
                        }
                    }
                    HistoryWriter.Instance.EqpEventLog(carrierID, (int)Proj.HostComm.EqpEvent.CarrierRemoved,
                        Proj.HostComm.EqpEvent.CarrierRemoved.ToString(), $"CranePickCarrier: {carrierID} , CarrierLoc:{strCarrierLoc}, CarrierLocName:{strCarrierLocName}");
                }

                LocationMng.Instance.UnReserveLocation(sourceLocation);
            }
        }

        public void CranePlaceCarrier(string craneID, string carrierID, string destLocation)
        {
            CraneMng.Instance.AddCranePlaceCount();
            if (DbTrans.Instance.CranePlaceCarrier(craneID, carrierID, destLocation))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    string strDestZoneName = "";
                    string strCarrierLoc = "";
                    string strCarrierLocName = "";
                    LocationType strLocType = LocationType.Shelf;
                    foreach (LocationInfo locationInfo in GlobalData.Instance.gbLocations)
                    {
                        if (locationInfo.strAddress == destLocation)
                        {
                            locationInfo.strCarrierID = carrierID;
                            locationInfo.IsOccupied = true;
                            strLocType = locationInfo.locType;
                            strDestZoneName = locationInfo.strZoneName;
                            strCarrierLoc = locationInfo.strAddress;
                            strCarrierLocName = locationInfo.strName;
                            break;
                        }
                    }

                    foreach (ExtendedZoneData extendedZoneData in GlobalData.Instance.gbExtendedActiveZones)
                    {
                        if (extendedZoneData.strZoneName == strDestZoneName)
                        {
                            if(extendedZoneData.u2ZoneCapacity > 0)
                            {
                                extendedZoneData.u2ZoneCapacity -= 1;
                            }
                            else
                            {
                                extendedZoneData.u2ZoneCapacity = 0;
                            }
                            break;
                        }
                    }

                    foreach (LocationInfo locationInfo in GlobalData.Instance.gbLocations)
                    {
                        if (locationInfo.strName == craneID)
                        {
                            locationInfo.strCarrierID = "";
                            locationInfo.IsOccupied = false;
                            break;
                        }
                    }
                    foreach (EnhancedCarrierInfo enhancedCarrierInfo in GlobalData.Instance.gbEnhancedCarriers)
                    {
                        if (enhancedCarrierInfo.strCarrierID == carrierID)
                        {
                            enhancedCarrierInfo.strCarrierLoc = strCarrierLoc;
                            enhancedCarrierInfo.strLocationName = strCarrierLocName;
                            enhancedCarrierInfo.strCarrierZoneName = strDestZoneName;
                            enhancedCarrierInfo.locType = strLocType;
                            break;
                        }
                    }

                    HistoryWriter.Instance.EqpEventLog(carrierID, (int)Proj.HostComm.EqpEvent.CarrierStored,
                        Proj.HostComm.EqpEvent.CarrierRemoved.ToString(), $"CranePlaceCarrier: {carrierID} , CarrierLoc:{strCarrierLoc}, CarrierLocName:{strCarrierLocName}");
                }

                LocationMng.Instance.UnReserveLocation(destLocation);
            }
        }

        public bool IsCarrierDuplicate(string carrierID, string carrierLoc, ref string dupCarrierRealLoc)
        {
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    //EnhancedCarrierInfo enhancedCarrierInfo = GlobalData.Instance.gbEnhancedCarriers.Where(x => x.strCarrierID == carrierID && x.strCarrierLoc != carrierLoc).FirstOrDefault();
                    TpCarrier enhancedCarrierInfo = TpCarrier.GetByLambda(x => x.Id == carrierID & x.Location != carrierLoc);
                    if (enhancedCarrierInfo != null)
                    {
                        dupCarrierRealLoc = enhancedCarrierInfo.Location;
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                HistoryWriter.Instance.ExceptionLog("CarrierMng.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public void UpdateCarrierState(string carrierID, CarrierState state)
        {
            if (DbCarrier.Instance.UpdateCarrierState(carrierID, state))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    EnhancedCarrierInfo enhancedCarrierInfo = GlobalData.Instance.gbEnhancedCarriers.Where(x => x.strCarrierID == carrierID).FirstOrDefault();
                    if (enhancedCarrierInfo != null)
                    {
                        enhancedCarrierInfo.carrierState = state;
                        HistoryWriter.Instance.EqpEventLog(carrierID, 0,
                            "UpdateCarrierState", $"UpdateCarrierState: {carrierID} , CarrierState:{state.ToString()}");
                    }
                }
            }
        }

        public string GetIDRFailCarrierID(string locationName)//26
        {
            return subStringForCarrierID("UNKNOWN", GlobalData.Instance.gbEqpName, DateTime.Now.ToString("yyyyMMddHHmmssfff"));
            //return "UNKNOWN-" + GlobalData.Instance.gbEqpName + "-" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
        }

        public string GenDulplicateCarrierID(string strDulplicatedID)//29
        {
            return subStringForCarrierID("UNKNOWNDUP", strDulplicatedID, DateTime.Now.ToString("yyyyMMddHHmmssfff"));
           // return "UNKNOWNDUP-" + strDulplicatedID + "-" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
        }

        public string GenDoubleStorageCarrierID(string shelfID)//29
        {
            return subStringForCarrierID("UNKNOWNDBS", (GlobalData.Instance.gbEqpName + "-" + shelfID), DateTime.Now.ToString("yyyyMMddHHmmssfff"));
            //return "UNKNOWNDBS-" + GlobalData.Instance.gbEqpName + "-" + shelfID + "-" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
        }

        public string GenEmptyRetrvScanCarrierID(string oldCarrierID)//29
        {
            return subStringForCarrierID("UNKNOWNEMP", oldCarrierID, DateTime.Now.ToString("yyyyMMddHHmmssfff"));
            //return "UNKNOWNEMP-" + oldCarrierID + "-" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
        }

        public string GenOnCraneUnkCarrierID(string strVehicleID)
        {
            return subStringForCarrierID("UNKNOWN", strVehicleID, DateTime.Now.ToString("yyyyMMddHHmmssfff"));
           // return "UNKNOWN-" + strVehicleID + "-" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
        }

        private string subStringForCarrierID(string strBefore, string strMid, string strAfter)
        {

            int iLength = 64 - strBefore.Length - strAfter.Length;
            
            if (iLength < strMid.Length)
            {
                strMid = strMid.Substring(0, iLength);
            }

            return strBefore + "-" + strMid + "-" + strAfter;
        }
    }
}
