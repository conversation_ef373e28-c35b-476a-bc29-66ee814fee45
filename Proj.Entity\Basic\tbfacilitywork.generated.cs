﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbFacilityWork and List
    [Serializable]
    [Description("设备工作记录表")]
    [LinqToDB.Mapping.Table("TB_FACILITY_WORK")]
    public partial class TbFacilityWork : GEntity<TbFacilityWork>, ITimestamp
    {
        #region Contructor(s)

        private TbFacilityWork()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CFacilityworkId = RegisterProperty<String>(p => p.CFacilityworkId);
        private static readonly PropertyInfo<String> pty_CWorkact = RegisterProperty<String>(p => p.CWorkact);
        private static readonly PropertyInfo<String> pty_CActstart = RegisterProperty<String>(p => p.CActstart);
        private static readonly PropertyInfo<String> pty_CActend = RegisterProperty<String>(p => p.CActend);
        private static readonly PropertyInfo<String> pty_CActuser = RegisterProperty<String>(p => p.CActuser);
        private static readonly PropertyInfo<String> pty_CFacilitylistId = RegisterProperty<String>(p => p.CFacilitylistId);
        private static readonly PropertyInfo<String> pty_CState = RegisterProperty<String>(p => p.CState);
        private static readonly PropertyInfo<String> pty_CUserId = RegisterProperty<String>(p => p.CUserId);
        private static readonly PropertyInfo<String> pty_CFacilityId = RegisterProperty<String>(p => p.CFacilityId);
        private static readonly PropertyInfo<String> pty_CFacilityName = RegisterProperty<String>(p => p.CFacilityName);
        private static readonly PropertyInfo<String> pty_CValidstate = RegisterProperty<String>(p => p.CValidstate);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 设备工作编号
        /// </summary>
        [Description("设备工作编号")]
        [LinqToDB.Mapping.Column("C_FACILITYWORKID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CFacilityworkId
        {
            get { return GetProperty(pty_CFacilityworkId); }
            set { SetProperty(pty_CFacilityworkId, value); }
        }
        /// <summary>
        /// 设备动作
        /// </summary>
        [Description("设备动作")]
        [LinqToDB.Mapping.Column("C_WORKACT")]
        public String CWorkact
        {
            get { return GetProperty(pty_CWorkact); }
            set { SetProperty(pty_CWorkact, value); }
        }
        /// <summary>
        /// 动作开始时间
        /// </summary>
        [Description("动作开始时间")]
        [LinqToDB.Mapping.Column("C_ACTSTART")]
        public String CActstart
        {
            get { return GetProperty(pty_CActstart); }
            set { SetProperty(pty_CActstart, value); }
        }
        /// <summary>
        /// 动作结束时间
        /// </summary>
        [Description("动作结束时间")]
        [LinqToDB.Mapping.Column("C_ACTEND")]
        public String CActend
        {
            get { return GetProperty(pty_CActend); }
            set { SetProperty(pty_CActend, value); }
        }
        /// <summary>
        /// 动作执行者
        /// </summary>
        [Description("动作执行者")]
        [LinqToDB.Mapping.Column("C_ACTUSER")]
        public String CActuser
        {
            get { return GetProperty(pty_CActuser); }
            set { SetProperty(pty_CActuser, value); }
        }
        /// <summary>
        /// 个体设备编号
        /// </summary>
        [Description("个体设备编号")]
        [LinqToDB.Mapping.Column("C_FACILITYLISTID")]
        public String CFacilitylistId
        {
            get { return GetProperty(pty_CFacilitylistId); }
            set { SetProperty(pty_CFacilitylistId, value); }
        }
        /// <summary>
        /// 个体设备状态
        /// </summary>
        [Description("个体设备状态")]
        [LinqToDB.Mapping.Column("C_STATE")]
        public String CState
        {
            get { return GetProperty(pty_CState); }
            set { SetProperty(pty_CState, value); }
        }
        /// <summary>
        /// 负责人
        /// </summary>
        [Description("负责人")]
        [LinqToDB.Mapping.Column("C_USERID")]
        public String CUserId
        {
            get { return GetProperty(pty_CUserId); }
            set { SetProperty(pty_CUserId, value); }
        }
        /// <summary>
        /// 设备编码
        /// </summary>
        [Description("设备编码")]
        [LinqToDB.Mapping.Column("C_FACILITYID")]
        public String CFacilityId
        {
            get { return GetProperty(pty_CFacilityId); }
            set { SetProperty(pty_CFacilityId, value); }
        }
        /// <summary>
        /// 设备名称
        /// </summary>
        [Description("设备名称")]
        [LinqToDB.Mapping.Column("C_FACILITYNAME")]
        public String CFacilityName
        {
            get { return GetProperty(pty_CFacilityName); }
            set { SetProperty(pty_CFacilityName, value); }
        }
        /// <summary>
        /// 当前状态
        /// </summary>
        [Description("当前状态")]
        [LinqToDB.Mapping.Column("C_VALIDSTATE")]
        public String CValidstate
        {
            get { return GetProperty(pty_CValidstate); }
            set { SetProperty(pty_CValidstate, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion


        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CFacilityworkId, "设备工作编号是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityworkId, 36, "设备工作编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkact, 40, "设备动作不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CActstart, 72, "动作开始时间不能超过72个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CActend, 72, "动作结束时间不能超过72个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CActuser, 40, "动作执行者不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilitylistId, 36, "个体设备编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CState, 40, "个体设备状态不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CUserId, 40, "负责人不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityId, 36, "设备编码不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityName, 100, "设备名称不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CValidstate, 1, "当前状态不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 40, "扩展字段A不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 40, "扩展字段B不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 40, "扩展字段C不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CFacilityworkId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbFacilityWorkList : GEntityList<TbFacilityWorkList, TbFacilityWork>
    {
        private TbFacilityWorkList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
