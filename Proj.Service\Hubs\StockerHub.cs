using Microsoft.AspNetCore.SignalR;
using Proj.Service.Interfaces;
using Proj.Service.Models;
using Proj.Log;

namespace Proj.Service.Hubs
{
    /// <summary>
    /// Stocker SignalR Hub，替代WCF的双向通信功能
    /// </summary>
    public class StockerHub : Hub
    {
        private readonly IStockerService _stockerService;
        private readonly IClientManagerService _clientManager;
        private readonly Logger _logger;

        public StockerHub(IStockerService stockerService, IClientManagerService clientManager)
        {
            _stockerService = stockerService;
            _clientManager = clientManager;
            _logger = Logger.Instance;
        }

        /// <summary>
        /// 客户端注册上线
        /// </summary>
        public async Task Register()
        {
            try
            {
                var connectionId = Context.ConnectionId;
                var remoteIpAddress = Context.GetHttpContext()?.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                
                _clientManager.RegisterClient(connectionId, remoteIpAddress);
                
                _logger.WcfLog($"Client registered: ConnectionId={connectionId}, IP={remoteIpAddress}");
                
                // 通知客户端注册成功
                await Clients.Caller.SendAsync("OnRegistered", new { Success = true, ConnectionId = connectionId });
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"Register error: {ex.Message}, Stack: {ex.StackTrace}");
                await Clients.Caller.SendAsync("OnError", new { Message = "Registration failed" });
            }
        }

        /// <summary>
        /// 客户端发送消息
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>处理结果</returns>
        public async Task<object?> ClientSendMessage(string function, Dictionary<string, object> parameters)
        {
            try
            {
                var connectionId = Context.ConnectionId;
                var remoteIpAddress = Context.GetHttpContext()?.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                
                // 更新客户端活动时间
                _clientManager.UpdateClientActivity(connectionId);
                
                // 记录日志
                var logMessage = $"Receive From {remoteIpAddress}: {function}";
                foreach (var kvp in parameters)
                {
                    logMessage += $", {kvp.Key}: {kvp.Value}";
                }
                _logger.WcfLog(logMessage);

                // 处理消息
                var result = await _stockerService.ProcessClientMessageAsync(function, parameters);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"ClientSendMessage error: {ex.Message}, Stack: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 获取系统状态
        /// </summary>
        /// <param name="stateName">状态名称</param>
        /// <returns>状态值</returns>
        public async Task<string> GetState(string stateName)
        {
            try
            {
                _clientManager.UpdateClientActivity(Context.ConnectionId);
                return await _stockerService.GetStateAsync(stateName);
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetState error: {ex.Message}, Stack: {ex.StackTrace}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取所有系统状态
        /// </summary>
        /// <returns>所有状态信息</returns>
        public async Task<SystemStateInfo> GetAllStates()
        {
            try
            {
                _clientManager.UpdateClientActivity(Context.ConnectionId);
                return await _stockerService.GetAllStatesAsync();
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetAllStates error: {ex.Message}, Stack: {ex.StackTrace}");
                return new SystemStateInfo();
            }
        }

        /// <summary>
        /// 获取标签值
        /// </summary>
        /// <param name="tagName">标签名称</param>
        /// <returns>标签值</returns>
        public async Task<object?> GetTagValue(string tagName)
        {
            try
            {
                _clientManager.UpdateClientActivity(Context.ConnectionId);
                return await _stockerService.GetTagValueAsync(tagName);
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetTagValue error: {ex.Message}, Stack: {ex.StackTrace}");
                return null;
            }
        }

        /// <summary>
        /// 连接测试
        /// </summary>
        /// <returns>连接是否正常</returns>
        public async Task<bool> ConnectTest()
        {
            try
            {
                _clientManager.UpdateClientActivity(Context.ConnectionId);
                return await _stockerService.TestConnectionAsync();
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"ConnectTest error: {ex.Message}, Stack: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 客户端连接时触发
        /// </summary>
        public override async Task OnConnectedAsync()
        {
            var connectionId = Context.ConnectionId;
            var remoteIpAddress = Context.GetHttpContext()?.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            
            _logger.WcfLog($"Client connected: ConnectionId={connectionId}, IP={remoteIpAddress}");
            
            await base.OnConnectedAsync();
        }

        /// <summary>
        /// 客户端断开连接时触发
        /// </summary>
        /// <param name="exception">断开连接的异常信息</param>
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var connectionId = Context.ConnectionId;
            
            _clientManager.RemoveClient(connectionId);
            
            if (exception != null)
            {
                _logger.ExceptionLog($"Client disconnected with error: ConnectionId={connectionId}, Error={exception.Message}");
            }
            else
            {
                _logger.WcfLog($"Client disconnected: ConnectionId={connectionId}");
            }
            
            await base.OnDisconnectedAsync(exception);
        }
    }
}
