﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Proj.Entity;
using Proj.DataTypeDef;
using Proj.Service;

namespace Proj.DB
{
    public class DbTransfer
    {
        private static DbTransfer m_Instanse;
        private static readonly object mSyncObject = new object();
        private DbTransfer() { }
        public static DbTransfer Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new DbTransfer();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public TpTransferList GetDbTransferList()
        {
            try
            {
                TpTransferList tpTransferList = TpTransferList.GetAll();
                return tpTransferList;
            }
            catch(Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTransfer.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return null;
        }

        public bool AddTransferCommand(string cmdSource, EnhancedTransferCommand command)
        {
            try
            {
                //添加指令
                TpTransfer tpTransfer = TpTransfer.New();
                tpTransfer.CmdSource = cmdSource;
                tpTransfer.Id = command.strCommandID;
                tpTransfer.CarrierId = command.strCarrierID;
                tpTransfer.SourceLocation = command.strRealSource;
                tpTransfer.DestLocation = command.strRealDest;
                tpTransfer.Priority = (int)command.u2Priority;
                tpTransfer.CraneName = command.strStockerCraneID;
                tpTransfer.CommandType = command.strCommandName;
                tpTransfer.CreateTime = DateTime.Now;
                tpTransfer.State = ((uint)command.transferState).ToString();                
                tpTransfer.Save();

                //保留目的地
                TpLocation tpLocation = TpLocation.GetById(command.strRealDest);
                tpLocation.IsReserved = 1;
                tpLocation.Save();

                //通知界面更新
                //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                //dicParams.Add("ADDRESS", command.strRealDest);
                //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTransfer.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }
        }

        public bool UpdateTransferState(string commandID, TransferState state)
        {
            try
            {
                TpTransfer tpTransfer = TpTransfer.GetById(commandID);
                if (tpTransfer != null)
                {
                    tpTransfer.State = ((int)state).ToString();
                    tpTransfer.Save();
                }
                
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTransfer.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }
        }

        public bool UpdateTransferPriority(string commandID, int iPriority)
        {
            try
            {
                TpTransfer tpTransfer = TpTransfer.GetById(commandID);
                if (tpTransfer != null)
                {
                    tpTransfer.Priority = iPriority;
                    tpTransfer.Save();
                }

                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTransfer.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }
        }

        public bool DeleteTransfer(string commandID)
        {
            try
            {
                TpTransferList.DeleteByCriteria(x => x.Id == commandID);
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbTransfer.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }
        }

    }
}
