﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntGathercollect and List
    [Serializable]
    [Description("变量采集表")]
    [LinqToDB.Mapping.Table("Tnt_GatherCollect")]
    public partial class TntGathercollect : GEntity<TntGathercollect>, ITimestamp
    {
        #region Contructor(s)

        private TntGathercollect()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CCollectId = RegisterProperty<String>(p => p.CCollectId);
        private static readonly PropertyInfo<String> pty_CParagatherId = RegisterProperty<String>(p => p.CParagatherId);
        private static readonly PropertyInfo<String> pty_CDeviceId = RegisterProperty<String>(p => p.CDeviceId);
        private static readonly PropertyInfo<String> pty_CCollectvalue = RegisterProperty<String>(p => p.CCollectvalue);
        private static readonly PropertyInfo<String> pty_CMessageType = RegisterProperty<String>(p => p.CMessageType);
        private static readonly PropertyInfo<String> pty_CMessage = RegisterProperty<String>(p => p.CMessage);
        private static readonly PropertyInfo<DateTime> pty_CCollectdate = RegisterProperty<DateTime>(p => p.CCollectdate);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion


        /// <summary>
        /// 采集主键
        /// </summary>
        [Description("采集主键")]
        [LinqToDB.Mapping.Column("c_CollectId")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CCollectId
        {
            get { return GetProperty(pty_CCollectId); }
            set { SetProperty(pty_CCollectId, value); }
        }
        /// <summary>
        /// 变量主键
        /// </summary>
        [Description("变量主键")]
        [LinqToDB.Mapping.Column("c_ParaGatherId")]
        public String CParagatherId
        {
            get { return GetProperty(pty_CParagatherId); }
            set { SetProperty(pty_CParagatherId, value); }
        }
        /// <summary>
        /// 设备主键
        /// </summary>
        [Description("设备主键")]
        [LinqToDB.Mapping.Column("c_DeviceId")]
        public String CDeviceId
        {
            get { return GetProperty(pty_CDeviceId); }
            set { SetProperty(pty_CDeviceId, value); }
        }
        /// <summary>
        /// 采集数值
        /// </summary>
        [Description("采集数值")]
        [LinqToDB.Mapping.Column("c_CollectValue")]
        public String CCollectvalue
        {
            get { return GetProperty(pty_CCollectvalue); }
            set { SetProperty(pty_CCollectvalue, value); }
        }
        /// <summary>
        /// 信息类型
        /// </summary>
        [Description("信息类型")]
        [LinqToDB.Mapping.Column("c_MessageType")]
        public String CMessageType
        {
            get { return GetProperty(pty_CMessageType); }
            set { SetProperty(pty_CMessageType, value); }
        }
        /// <summary>
        /// 失败信息
        /// </summary>
        [Description("失败信息")]
        [LinqToDB.Mapping.Column("c_Message")]
        public String CMessage
        {
            get { return GetProperty(pty_CMessage); }
            set { SetProperty(pty_CMessage, value); }
        }
        /// <summary>
        /// 采集时间
        /// </summary>
        [Description("采集时间")]
        [LinqToDB.Mapping.Column("c_CollectDate")]
        public DateTime CCollectdate
        {
            get { return GetProperty(pty_CCollectdate); }
            set { SetProperty(pty_CCollectdate, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CCollectId, "采集主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCollectId, 36, "采集主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CParagatherId, "变量主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParagatherId, 36, "变量主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeviceId, 30, "设备主键不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCollectvalue, 30, "采集数值不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMessageType, 2, "信息类型不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMessage, 200, "失败信息不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CCollectId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntGathercollectList : GEntityList<TntGathercollectList, TntGathercollect>
    {
        private TntGathercollectList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
