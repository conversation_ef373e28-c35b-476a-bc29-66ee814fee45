<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
  </configSections>

  <log4net>
    <!-- 应用程序日志 -->
    <appender name="AppLogAppender" type="log4net.Appender.RollingFileAppender">
      <file value="logs/" />
      <datePattern value="yyyy-MM-dd'/App/App_'yyyy-MM-dd-HH'.log'" />
      <staticLogFileName value="false" />
      <appendToFile value="true" />
      <rollingStyle value="Composite" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="1MB" />
      <encoding value="utf-8" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%level] - %message%newline" />
      </layout>
    </appender>

    <!-- WCF日志 -->
    <appender name="WcfLogAppender" type="log4net.Appender.RollingFileAppender">
      <file value="logs/" />
      <datePattern value="yyyy-MM-dd'/Wcf/Wcf_'yyyy-MM-dd-HH'.log'" />
      <staticLogFileName value="false" />
      <appendToFile value="true" />
      <rollingStyle value="Composite" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="1MB" />
      <encoding value="utf-8" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%level] - %message%newline" />
      </layout>
    </appender>

    <!-- 异常日志 -->
    <appender name="ExceptionLogAppender" type="log4net.Appender.RollingFileAppender">
      <file value="logs/" />
      <datePattern value="yyyy-MM-dd'/Exception/Exception_'yyyy-MM-dd-HH'.log'" />
      <staticLogFileName value="false" />
      <appendToFile value="true" />
      <rollingStyle value="Composite" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="1MB" />
      <encoding value="utf-8" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%level] - %message%newline" />
      </layout>
    </appender>

    <!-- 控制台输出 -->
    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%level] - %message%newline" />
      </layout>
    </appender>

    <!-- 应用程序日志记录器 -->
    <logger name="AppLog">
      <level value="DEBUG" />
      <appender-ref ref="AppLogAppender" />
      <appender-ref ref="ConsoleAppender" />
    </logger>

    <!-- WCF日志记录器 -->
    <logger name="WcfLog">
      <level value="DEBUG" />
      <appender-ref ref="WcfLogAppender" />
      <appender-ref ref="ConsoleAppender" />
    </logger>

    <!-- 异常日志记录器 -->
    <logger name="ExceptionLog">
      <level value="DEBUG" />
      <appender-ref ref="ExceptionLogAppender" />
      <appender-ref ref="ConsoleAppender" />
    </logger>

    <!-- 其他日志记录器 -->
    <logger name="AlarmLog">
      <level value="DEBUG" />
      <appender-ref ref="AppLogAppender" />
    </logger>

    <logger name="EventLog">
      <level value="DEBUG" />
      <appender-ref ref="AppLogAppender" />
    </logger>

    <logger name="HostLog">
      <level value="DEBUG" />
      <appender-ref ref="AppLogAppender" />
    </logger>

    <logger name="OperationLog">
      <level value="DEBUG" />
      <appender-ref ref="AppLogAppender" />
    </logger>

    <logger name="PIOLog">
      <level value="DEBUG" />
      <appender-ref ref="AppLogAppender" />
    </logger>

    <logger name="PLCLog">
      <level value="DEBUG" />
      <appender-ref ref="AppLogAppender" />
    </logger>

    <logger name="SCLog">
      <level value="DEBUG" />
      <appender-ref ref="AppLogAppender" />
    </logger>

    <logger name="TransferLog">
      <level value="DEBUG" />
      <appender-ref ref="AppLogAppender" />
    </logger>

    <!-- 根日志记录器 -->
    <root>
      <level value="DEBUG" />
      <appender-ref ref="ConsoleAppender" />
    </root>
  </log4net>
</configuration>
