﻿using log4net;
using System.IO;

namespace Proj.Log
{
    /// <summary>
    /// 应用程序日志级别
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 调试信息
        /// </summary>
        Debug,
        /// <summary>
        /// 一般信息
        /// </summary>
        Info,
        /// <summary>
        /// 警告信息
        /// </summary>
        Warning,
        /// <summary>
        /// 错误信息
        /// </summary>
        Error,
        /// <summary>
        /// 致命错误
        /// </summary>
        Fatal
    }

    public class Logger
    {
        private static Logger? m_Instanse;
        private static readonly object mSyncObject = new();
        private readonly ILog alarmLogger;
        private readonly ILog appLogger;
        private readonly ILog exceptionLogger;
        private readonly ILog eventLogger;
        private readonly ILog hostLoger;
        private readonly ILog operationLogger;
        private readonly ILog pioLogger;
        private readonly ILog plcLogger;
        private readonly ILog scLogger;
        private readonly ILog transferLogger;
        private readonly ILog wcfLoger;

        public static Logger Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new Logger();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public Logger()
        {
            // 初始化Txt日志 - 按优先级查找配置文件
            FileInfo? configFile = null;
            string? usedPath = null;

            // 优先级1: 应用程序基目录下的config文件夹
            var path1 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "Log4net.config");
            if (File.Exists(path1))
            {
                configFile = new FileInfo(path1);
                usedPath = path1;
            }

            // 优先级2: 当前工作目录下的config文件夹
            if (configFile == null)
            {
                var path2 = Path.Combine(Environment.CurrentDirectory, "config", "Log4net.config");
                if (File.Exists(path2))
                {
                    configFile = new FileInfo(path2);
                    usedPath = path2;
                }
            }

            // 优先级3: 应用程序目录的上级bin目录
            if (configFile == null)
            {
                var path3 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "bin", "config", "Log4net.config");
                if (File.Exists(path3))
                {
                    configFile = new FileInfo(path3);
                    usedPath = path3;
                }
            }

            if (configFile != null && usedPath != null)
            {
                Console.WriteLine($"[Logger] 使用配置文件: {usedPath}");
                log4net.Config.XmlConfigurator.Configure(configFile);
            }
            else
            {
                Console.WriteLine("[Logger] 警告: 未找到Log4net配置文件，使用默认配置");
                // 使用基本配置
                log4net.Config.BasicConfigurator.Configure();
            }

            alarmLogger = LogManager.GetLogger("AlarmLog");
            appLogger = LogManager.GetLogger("AppLog");
            exceptionLogger = LogManager.GetLogger("ExceptionLog");
            eventLogger = LogManager.GetLogger("EventLog");
            hostLoger = LogManager.GetLogger("HostLog");
            operationLogger = LogManager.GetLogger("OperationLog");
            pioLogger = LogManager.GetLogger("PIOLog");
            plcLogger = LogManager.GetLogger("PLCLog");
            scLogger = LogManager.GetLogger("SCLog");
            transferLogger = LogManager.GetLogger("TransferLog");
            wcfLoger = LogManager.GetLogger("WcfLog");
        }

        public void AlarmLog(string strLog)
        {
            alarmLogger.Debug(strLog);
        }

        /// <summary>
        /// 应用程序日志输出 - 基础方法
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        public void AppLog(LogLevel level, string message)
        {
            switch (level)
            {
                case LogLevel.Debug:
                    appLogger.Debug(message);
                    break;
                case LogLevel.Info:
                    appLogger.Info(message);
                    break;
                case LogLevel.Warning:
                    appLogger.Warn(message);
                    break;
                case LogLevel.Error:
                    appLogger.Error(message);
                    break;
                case LogLevel.Fatal:
                    appLogger.Fatal(message);
                    break;
                default:
                    appLogger.Info(message);
                    break;
            }
        }

        /// <summary>
        /// 应用程序日志输出 - 支持多参数拼接
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="format">格式化字符串</param>
        /// <param name="args">参数数组</param>
        public void AppLog(LogLevel level, string format, params object[] args)
        {
            try
            {
                var message = string.Format(format, args);
                AppLog(level, message);
            }
            catch (Exception ex)
            {
                // 如果格式化失败，输出原始信息和错误
                var fallbackMessage = $"日志格式化失败: {format} | 错误: {ex.Message}";
                AppLog(LogLevel.Error, fallbackMessage);
            }
        }

        /// <summary>
        /// 应用程序调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Debug(string message)
        {
            AppLog(LogLevel.Debug, message);
        }

        /// <summary>
        /// 应用程序调试日志 - 支持多参数
        /// </summary>
        /// <param name="format">格式化字符串</param>
        /// <param name="args">参数数组</param>
        public void Debug(string format, params object[] args)
        {
            AppLog(LogLevel.Debug, format, args);
        }

        /// <summary>
        /// 应用程序信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Info(string message)
        {
            AppLog(LogLevel.Info, message);
        }

        /// <summary>
        /// 应用程序信息日志 - 支持多参数
        /// </summary>
        /// <param name="format">格式化字符串</param>
        /// <param name="args">参数数组</param>
        public void Info(string format, params object[] args)
        {
            AppLog(LogLevel.Info, format, args);
        }

        /// <summary>
        /// 应用程序警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Warning(string message)
        {
            AppLog(LogLevel.Warning, message);
        }

        /// <summary>
        /// 应用程序警告日志 - 支持多参数
        /// </summary>
        /// <param name="format">格式化字符串</param>
        /// <param name="args">参数数组</param>
        public void Warning(string format, params object[] args)
        {
            AppLog(LogLevel.Warning, format, args);
        }

        /// <summary>
        /// 应用程序错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Error(string message)
        {
            AppLog(LogLevel.Error, message);
        }

        /// <summary>
        /// 应用程序错误日志 - 支持多参数
        /// </summary>
        /// <param name="format">格式化字符串</param>
        /// <param name="args">参数数组</param>
        public void Error(string format, params object[] args)
        {
            AppLog(LogLevel.Error, format, args);
        }

        /// <summary>
        /// 应用程序错误日志 - 支持异常信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        public void Error(string message, Exception exception)
        {
            var fullMessage = $"{message} | 异常: {exception.Message} | 堆栈: {exception.StackTrace}";
            AppLog(LogLevel.Error, fullMessage);
        }

        /// <summary>
        /// 应用程序致命错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Fatal(string message)
        {
            AppLog(LogLevel.Fatal, message);
        }

        /// <summary>
        /// 应用程序致命错误日志 - 支持多参数
        /// </summary>
        /// <param name="format">格式化字符串</param>
        /// <param name="args">参数数组</param>
        public void Fatal(string format, params object[] args)
        {
            AppLog(LogLevel.Fatal, format, args);
        }

        /// <summary>
        /// 应用程序致命错误日志 - 支持异常信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        public void Fatal(string message, Exception exception)
        {
            var fullMessage = $"{message} | 异常: {exception.Message} | 堆栈: {exception.StackTrace}";
            AppLog(LogLevel.Fatal, fullMessage);
        }

        public void ExceptionLog(string strLog)
        {
            exceptionLogger.Debug(strLog);
        }

        public void EventLog(string strLog)
        {
            eventLogger.Debug(strLog);
        }

        public void HostLog(string strLog)
        {
            hostLoger.Debug(strLog);
        }

        public void OperationLog(string strLog)
        {
            operationLogger.Debug(strLog);
        }

        public void PIOLog(string strLog)
        {
            pioLogger.Debug(strLog);
        }

        public void PLCLog(string strLog)
        {
            plcLogger.Debug(strLog);
        }

        public void SCLog(string strLog)
        {
            scLogger.Debug(strLog);
        }

        public void TransferLog(string strLog)
        {
            transferLogger.Debug(strLog);
        }

        public void WcfLog(string strLog)
        {
            wcfLoger.Debug(strLog);
        }
    }
}
