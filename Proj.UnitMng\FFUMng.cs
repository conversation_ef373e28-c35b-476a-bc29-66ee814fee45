﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Proj.Entity;
using Proj.DB;
using Proj.CacheData;
using Proj.DataTypeDef;
using Proj.DevComm;

namespace Proj.UnitMng
{
    public class FFUMng
    {
        private static FFUMng m_Instanse;
        private static readonly object mSyncObject = new object();
        private FFUMng() { }
        public static FFUMng Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new FFUMng();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public void SetFFUSpeed(string strFFUList, int iSpeed)
        {
            string[] ffuArray = strFFUList.Split(',');
            foreach(string strFFU in ffuArray)
            {
                StockerDev.Instance.SetFFUSpeed(strFFU, iSpeed);
            }
        }
    }
}
