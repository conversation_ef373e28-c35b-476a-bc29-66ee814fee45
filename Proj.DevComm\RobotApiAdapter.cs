using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using IoTClient.Clients.PLC;
using IoTClient.Enums;
using Proj.Log;

namespace Proj.DevComm
{
    /// <summary>
    /// Robot API 适配器 - 使用 IoTClient 替代原始 Robot 库
    /// </summary>
    public static class ProjectAPI
    {
        private static bool _isRunning = false;
        private static readonly object _lockObj = new object();

        public static bool IsRunning => _isRunning;

        /// <summary>
        /// 启动项目API
        /// </summary>
        /// <returns>0表示成功，其他值表示失败</returns>
        public static int Run()
        {
            lock (_lockObj)
            {
                try
                {
                    if (_isRunning)
                        return 0;

                    // 初始化IoTClient相关组件
                    IOAPI.Initialize();
                    TagAPI.Initialize();
                    
                    _isRunning = true;
                    Logger.Instance.EventLog("ProjectAPI.Run: 项目API启动成功");
                    return 0;
                }
                catch (Exception ex)
                {
                    Logger.Instance.ExceptionLog($"ProjectAPI.Run: 启动失败 - {ex.Message}");
                    return -1;
                }
            }
        }

        /// <summary>
        /// 退出项目API
        /// </summary>
        public static void Exit()
        {
            lock (_lockObj)
            {
                try
                {
                    if (!_isRunning)
                        return;

                    IOAPI.Shutdown();
                    TagAPI.Shutdown();
                    
                    _isRunning = false;
                    Logger.Instance.EventLog("ProjectAPI.Exit: 项目API已退出");
                }
                catch (Exception ex)
                {
                    Logger.Instance.ExceptionLog($"ProjectAPI.Exit: 退出时发生错误 - {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    /// IO API 适配器 - 使用 IoTClient 进行设备通信
    /// </summary>
    public static class IOAPI
    {
        private static readonly ConcurrentDictionary<string, object> _clients = new();
        private static readonly ConcurrentDictionary<string, DeviceConfig> _deviceConfigs = new();
        private static bool _isStarted = false;
        private static readonly object _lockObj = new object();

        public static void Initialize()
        {
            // 初始化设备配置
            LoadDeviceConfigurations();
        }

        public static void Shutdown()
        {
            lock (_lockObj)
            {
                foreach (var client in _clients.Values)
                {
                    try
                    {
                        if (client is OmronFinsClient omronClient)
                        {
                            omronClient.Close();
                        }
                        else if (client is SiemensClient siemensClient)
                        {
                            siemensClient.Close();
                        }
                        else if (client is MitsubishiClient mitsubishiClient)
                        {
                            mitsubishiClient.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.ExceptionLog($"IOAPI.Shutdown: 关闭客户端时发生错误 - {ex.Message}");
                    }
                }
                _clients.Clear();
                _isStarted = false;
            }
        }

        /// <summary>
        /// 启动IO服务
        /// </summary>
        public static void StartIO()
        {
            lock (_lockObj)
            {
                if (_isStarted)
                    return;

                _isStarted = true;
                Logger.Instance.EventLog("IOAPI.StartIO: IO服务已启动");
            }
        }

        /// <summary>
        /// 停止IO服务
        /// </summary>
        public static void StopIO()
        {
            lock (_lockObj)
            {
                if (!_isStarted)
                    return;

                foreach (var client in _clients.Values)
                {
                    try
                    {
                        if (client is OmronFinsClient omronClient)
                        {
                            omronClient.Close();
                        }
                        else if (client is SiemensClient siemensClient)
                        {
                            siemensClient.Close();
                        }
                        else if (client is MitsubishiClient mitsubishiClient)
                        {
                            mitsubishiClient.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.ExceptionLog($"IOAPI.StopIO: 停止设备时发生错误 - {ex.Message}");
                    }
                }
                _clients.Clear();
                _isStarted = false;
                Logger.Instance.EventLog("IOAPI.StopIO: IO服务已停止");
            }
        }

        /// <summary>
        /// 启动指定设备
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <returns>0表示成功，其他值表示失败</returns>
        public static int StartDevice(string deviceName)
        {
            try
            {
                if (!_deviceConfigs.TryGetValue(deviceName, out var config))
                {
                    Logger.Instance.ExceptionLog($"IOAPI.StartDevice: 未找到设备配置 - {deviceName}");
                    return -1;
                }

                object client = CreateClient(config);
                if (client == null)
                {
                    Logger.Instance.ExceptionLog($"IOAPI.StartDevice: 创建客户端失败 - {deviceName}");
                    return -2;
                }

                // 尝试连接
                if (client is OmronFinsClient omronClient)
                {
                    omronClient.Open();
                }
                else if (client is SiemensClient siemensClient)
                {
                    siemensClient.Open();
                }
                else if (client is MitsubishiClient mitsubishiClient)
                {
                    mitsubishiClient.Open();
                }
                _clients.TryAdd(deviceName, client);
                
                Logger.Instance.EventLog($"IOAPI.StartDevice: 设备启动成功 - {deviceName}");
                return 0;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"IOAPI.StartDevice: 启动设备失败 - {deviceName}, 错误: {ex.Message}");
                return -3;
            }
        }

        /// <summary>
        /// 获取设备变量值
        /// </summary>
        /// <param name="fullTagName">完整标签名 (设备名.标签名)</param>
        /// <returns>变量值</returns>
        public static object GetValue(string fullTagName)
        {
            try
            {
                var parts = fullTagName.Split('.');
                if (parts.Length < 2)
                {
                    Logger.Instance.ExceptionLog($"IOAPI.GetValue: 标签名格式错误 - {fullTagName}");
                    return null;
                }

                string deviceName = parts[0];
                string tagName = string.Join(".", parts, 1, parts.Length - 1);

                if (!_clients.TryGetValue(deviceName, out var client))
                {
                    Logger.Instance.ExceptionLog($"IOAPI.GetValue: 未找到设备客户端 - {deviceName}");
                    return null;
                }

                // 根据设备类型读取数据
                if (client is OmronFinsClient omronClient)
                {
                    var result = omronClient.ReadInt16(tagName);
                    return result.IsSucceed ? result.Value : null;
                }
                else if (client is SiemensClient siemensClient)
                {
                    var result = siemensClient.ReadInt16(tagName);
                    return result.IsSucceed ? result.Value : null;
                }
                else if (client is MitsubishiClient mitsubishiClient)
                {
                    var result = mitsubishiClient.ReadInt16(tagName);
                    return result.IsSucceed ? result.Value : null;
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"IOAPI.GetValue: 读取变量失败 - {fullTagName}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 写入设备变量值
        /// </summary>
        /// <param name="fullTagName">完整标签名 (设备名.标签名)</param>
        /// <param name="value">要写入的值</param>
        /// <returns>是否成功</returns>
        public static bool WriteTag(string fullTagName, object value)
        {
            try
            {
                var parts = fullTagName.Split('.');
                if (parts.Length < 2)
                {
                    Logger.Instance.ExceptionLog($"IOAPI.WriteTag: 标签名格式错误 - {fullTagName}");
                    return false;
                }

                string deviceName = parts[0];
                string tagName = string.Join(".", parts, 1, parts.Length - 1);

                if (!_clients.TryGetValue(deviceName, out var client))
                {
                    Logger.Instance.ExceptionLog($"IOAPI.WriteTag: 未找到设备客户端 - {deviceName}");
                    return false;
                }

                // 根据设备类型和值类型写入数据
                if (client is OmronFinsClient omronClient)
                {
                    if (value is short shortValue)
                    {
                        var result = omronClient.Write(tagName, shortValue);
                        return result.IsSucceed;
                    }
                    else if (value is int intValue)
                    {
                        var result = omronClient.Write(tagName, intValue);
                        return result.IsSucceed;
                    }
                    else if (value is bool boolValue)
                    {
                        var result = omronClient.Write(tagName, boolValue);
                        return result.IsSucceed;
                    }
                }
                else if (client is SiemensClient siemensClient)
                {
                    if (value is short shortValue)
                    {
                        var result = siemensClient.Write(tagName, shortValue);
                        return result.IsSucceed;
                    }
                    else if (value is int intValue)
                    {
                        var result = siemensClient.Write(tagName, intValue);
                        return result.IsSucceed;
                    }
                    else if (value is bool boolValue)
                    {
                        var result = siemensClient.Write(tagName, boolValue);
                        return result.IsSucceed;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"IOAPI.WriteTag: 写入变量失败 - {fullTagName}, 错误: {ex.Message}");
                return false;
            }
        }

        private static void LoadDeviceConfigurations()
        {
            // 这里可以从配置文件或数据库加载设备配置
            // 示例配置
            _deviceConfigs.TryAdd("FinsEthernet1", new DeviceConfig
            {
                Name = "FinsEthernet1",
                Type = DeviceType.Omron,
                IpAddress = "*************",
                Port = 9600
            });

            _deviceConfigs.TryAdd("MelsecQE711", new DeviceConfig
            {
                Name = "MelsecQE711",
                Type = DeviceType.Mitsubishi,
                IpAddress = "*************",
                Port = 6000
            });
        }

        private static object CreateClient(DeviceConfig config)
        {
            try
            {
                switch (config.Type)
                {
                    case DeviceType.Omron:
                        return new OmronFinsClient(config.IpAddress, config.Port);
                    case DeviceType.Mitsubishi:
                        return new MitsubishiClient(MitsubishiVersion.Qna_3E, config.IpAddress, config.Port);
                    case DeviceType.Siemens:
                        return new SiemensClient(SiemensVersion.S7_1200, config.IpAddress, config.Port);
                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"IOAPI.CreateClient: 创建客户端失败 - {config.Name}, 错误: {ex.Message}");
                return null;
            }
        }
    }

    /// <summary>
    /// 设备配置
    /// </summary>
    public class DeviceConfig
    {
        public string Name { get; set; }
        public DeviceType Type { get; set; }
        public string IpAddress { get; set; }
        public int Port { get; set; }
    }

    /// <summary>
    /// 设备类型枚举
    /// </summary>
    public enum DeviceType
    {
        Omron,
        Mitsubishi,
        Siemens,
        ModbusTcp
    }
}
