﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <!-- 禁用 CSLA 分析器以兼容生成的代码 -->
  <PropertyGroup>
    <NoWarn>$(NoWarn);CSLA0005</NoWarn>
    <WarningsAsErrors />
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Proj.Entity\Proj.Entity.csproj" />
    <ProjectReference Include="..\Proj.Log\Proj.Log.csproj" />
  </ItemGroup>

</Project>
