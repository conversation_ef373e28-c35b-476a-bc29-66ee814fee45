﻿using System;
using System.Collections.Specialized;
using System.Configuration;
using System.Xml;
using System.IO;
using System.Text;
using System.Collections.Generic;

namespace Proj.TimedTaskApp.Base
{
    /// <summary>
    /// 工具类ServiceTools
    /// 工具类实现了IConfigurationSectionHandler接口，封装了对app.config的读取、日志生成等静态方法。
    /// </summary>
    public class ServiceTools : System.Configuration.IConfigurationSectionHandler
    {
        /// <summary>
        /// 获取AppSettings节点值
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static string GetAppSetting(string key)
        {
            return ConfigurationManager.AppSettings[key].ToString();
        }

        /// <summary>
        /// 获取configSections节点
        /// </summary>
        /// <returns></returns>
        public static XmlNode GetConfigSections()
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None).FilePath);
            return doc.DocumentElement.FirstChild;
        }

        /// <summary>
        /// 获取section节点
        /// </summary>
        /// <param name="nodeName"></param>
        /// <returns></returns>
        public static NameValueCollection GetSection(string nodeName)
        {
            return (NameValueCollection)ConfigurationManager.GetSection(nodeName);
        }

        /// <summary>
        /// 停止Windows服务
        /// </summary>
        /// <param name="serviceName">服务名称</param>
        public static void WindowsServiceStop(string serviceName)
        {
            System.ServiceProcess.ServiceController control = new System.ServiceProcess.ServiceController(serviceName);
            control.Stop();
            control.Dispose();
        }

        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="path">日志文件</param>
        /// <param name="cont">日志内容</param>
        /// <param name="isAppend">是否追加方式</param>
        //public static void WriteLog(string path, string cont, bool isAppend)
        //{
        //    FileStream fs = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);

        //    StreamReader sr = new StreamReader(fs, System.Text.Encoding.Default);

        //    StringBuilder sb = new StringBuilder();
        //    while (!sr.EndOfStream)
        //    {
        //        sb.AppendLine(sr.ReadLine() + "<br>" + cont);
        //    }

        //    //using (StreamWriter sw = new StreamWriter(path, isAppend, System.Text.Encoding.UTF8))
        //    //{
        //    //    sw.WriteLine(DateTime.Now);
        //    //    sw.WriteLine(cont);
        //    //    sw.WriteLine("");
        //    //    sw.Close();
        //    //}
        //}

        private static Dictionary<long, long> lockDic = new Dictionary<long, long>();
        /// <summary>
        /// 写入文本
        /// </summary>
        /// <param name="content">文本内容</param>
        private static void Write(string _fileName, string content, string newLine)
        {
            if (string.IsNullOrEmpty(_fileName))
            {
                throw new Exception("FileName不能为空！");
            }
            using (System.IO.FileStream fs = new System.IO.FileStream(_fileName, System.IO.FileMode.OpenOrCreate, System.IO.FileAccess.ReadWrite, System.IO.FileShare.ReadWrite, 8, System.IO.FileOptions.Asynchronous))
            {
                //Byte[] dataArray = System.Text.Encoding.ASCII.GetBytes(System.DateTime.Now.ToString() + content + "/r/n");
                Byte[] dataArray = System.Text.Encoding.Default.GetBytes(content + newLine);
                bool flag = true;
                long slen = dataArray.Length;
                long len = 0;
                while (flag)
                {
                    try
                    {
                        if (len >= fs.Length)
                        {
                            fs.Lock(len, slen);
                            lockDic[len] = slen;
                            flag = false;
                        }
                        else
                        {
                            len = fs.Length;
                        }
                    }
                    catch (Exception ex)
                    {
                        while (!lockDic.ContainsKey(len))
                        {
                            len += lockDic[len];
                        }
                    }
                }
                fs.Seek(len, System.IO.SeekOrigin.Begin);
                fs.Write(dataArray, 0, dataArray.Length);
                fs.Close();
            }
        }
        /// <summary>
        /// 写入文件内容
        /// </summary>
        /// <param name="content"></param>
        public static void WriteLog(string _fileName, string content)
        {
            Write(_fileName, content, System.Environment.NewLine);
        }
        /// <summary>
        /// 写入文件
        /// </summary>
        /// <param name="content"></param>
        public static void Write(string _fileName, string content)
        {
            Write(_fileName, content, "");
        }
        /// <summary>
        /// 实现接口以读写app.config
        /// </summary>
        /// <param name="parent"></param>
        /// <param name="configContext"></param>
        /// <param name="section"></param>
        /// <returns></returns>
        public object Create(object parent, object configContext, System.Xml.XmlNode section)
        {
            System.Configuration.NameValueSectionHandler handler = new System.Configuration.NameValueSectionHandler();
            return handler.Create(parent, configContext, section);
        }

    }//end class
}

