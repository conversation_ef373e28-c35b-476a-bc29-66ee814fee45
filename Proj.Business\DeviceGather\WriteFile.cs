﻿using Proj.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Proj.Business.DeviceGather.Excute
{
    public class WriteFile
    {
        private static Dictionary<long, long> lockDic = new Dictionary<long, long>();

        public virtual OprationResult WriteLine(string[] args)
        {
            OprationResult result = new OprationResult { strCode = "0" };
            try
            {
                string file = AppDomain.CurrentDomain.BaseDirectory + "AppExeLog.txt";
                string message = args[1];
                using (var trans = new Csla.Transaction.TransactionScope())
                {
                    Write(file, message, System.Environment.NewLine);
                    trans.Complete();
                }
                Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "WriteLine Function", "3", "执行任务成功");
            }
            catch (Exception ex)
            {
                result.strCode = "1";
                result.strMessage = ex.Message + Environment.NewLine;
                //填写错误日志
                //SS.Base.Log.Default.Error("更新数据字典错误消息：" + result.strMessage + "请手动处理查询原因!");
                // by chengbao
                Log.Logger.Instance.ExceptionLog("WriteFile.WriteLine Error: " + ex.Message + ", Stack: " + ex.StackTrace);
                Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "WriteLine Function", "3", "执行任务时出错");
            }
            finally
            {
                string[] Newargs = new string[5];
                Newargs[0] = args[0];
                Newargs[1] = "WriteLine";
                if (args.Length == 2)
                {
                    Newargs[2] = "1";
                }
                else
                {
                    Newargs[2] = args[2];
                }
                Newargs[3] = "1";
                if (result.strCode == "0")
                {
                    result.strMessage = "执行完成！";
                    Newargs[3] = "0";
                }
                Newargs[4] = result.strMessage;
                WriteLog.WriteJobExeLog(Newargs);

            }
            return result;
        }

        /// <summary>
        /// 写入文本
        /// </summary>
        /// <param name="content">文本内容</param>
        private static void Write(string _fileName, string content, string newLine)
        {
            if (string.IsNullOrEmpty(_fileName))
            {
                throw new Exception("FileName不能为空！");
            }
            using (System.IO.FileStream fs = new System.IO.FileStream(_fileName, System.IO.FileMode.OpenOrCreate, System.IO.FileAccess.ReadWrite, System.IO.FileShare.ReadWrite, 8, System.IO.FileOptions.Asynchronous))
            {
                Byte[] dataArray = System.Text.Encoding.Default.GetBytes(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff") + ":  " + content + newLine);
                bool flag = true;
                long slen = dataArray.Length;
                long len = 0;
                while (flag)
                {
                    try
                    {
                        if (len >= fs.Length)
                        {
                            fs.Lock(len, slen);
                            lockDic[len] = slen;
                            flag = false;
                        }
                        else
                        {
                            len = fs.Length;
                        }
                    }
                    catch (Exception ex)
                    {
                        while (!lockDic.ContainsKey(len))
                        {
                            len += lockDic[len];
                        }
                    }
                }
                fs.Seek(len, System.IO.SeekOrigin.Begin);
                fs.Write(dataArray, 0, dataArray.Length);
                fs.Close();
            }
        }
    }
}
