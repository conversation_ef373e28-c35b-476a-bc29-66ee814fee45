﻿using Proj.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Proj.Business.DeviceGather.Excute
{
    public class WriteDB
    {
        public virtual OprationResult WriteData(string[] args)
        {
            OprationResult result = new OprationResult { strCode = "0" };
            try
            {
                string className = args[0];
                string method = args[1];
                using (var trans = new Csla.Transaction.TransactionScope())
                {
                    TpBatchmethod tp = TpBatchmethod.New();
                    tp.CPk = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                    tp.CClass = className;
                    tp.CMethod = method;
                    tp.Save();
                    trans.Complete();
                }
                Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "WriteData Function", "3", "执行任务成功");
            }
            catch (Exception ex)
            {
                result.strCode = "1";
                result.strMessage = ex.Message + Environment.NewLine;
                //填写错误日志
                //SS.Base.Log.Default.Error("更新数据字典错误消息：" + result.strMessage + "请手动处理查询原因!");
                // by chengbao
                Log.Logger.Instance.ExceptionLog("WriteDB.WriteData Error: " + ex.Message + ", Stack: " + ex.StackTrace);
                Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "WriteData Function", "3", "执行任务时出错");
            }
            finally
            {
                string[] Newargs = new string[5];
                Newargs[0] = args[0];
                Newargs[1] = "WriteData";
                if (args.Length == 2)
                {
                    Newargs[2] = "1";
                }
                else
                {
                    Newargs[2] = args[2];
                }
                Newargs[3] = "1";
                if (result.strCode == "0")
                {
                    result.strMessage = "执行完成！";
                    Newargs[3] = "0";
                }
                Newargs[4] = result.strMessage;
                WriteLog.WriteJobExeLog(Newargs);
            }
            return result;
        } 

    }
}
