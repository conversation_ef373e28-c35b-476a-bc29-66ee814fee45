﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SS.Base;
using SS.CslaBase;
using SS.BLL.Base;
using Csla.Transaction;
using Proj.Entity;
using System.Data;
using Csla.Repository;
using SS.Entity;

namespace Proj.Business
{
     [Serializable]
    public class ExcuteTaskService : SS.Base.IJob
    {

         public void Execute()
         {
             var result = new OprationResult { strCode = "0" };

             //定义事务
             using (var trans = new Csla.Transaction.TransactionScope())
             {

                 try
                 {
                     //获取数据字典中代码为1000的数据行
                     TsDict dc = TsDict.GetByLambda(x => x.CCode == "1000");
                     if (dc != null)
                     {
                         int intValue = 0;
                         if (dc.CValue != "")
                         {
                             intValue = Convert.ToInt32(dc.CValue) + 1;

                         }
                         //更新对于的值，使其自动加1
                         dc.CValue = intValue.ToString();
                         dc.Save();
                     }

                 }
                 catch (Exception ex)
                 {
                     result.strCode = "1";
                     result.strMessage = ex.Message;
                     Log.Logger.Instance.ExceptionLog("ExcuteTaskService.Execute Error: " + ex.Message + ", Stack: " + ex.StackTrace);
                 }

                 if (result.Successed)
                 {
                     //事物提交
                     trans.Complete();
                 }
                 else
                 {
                     //填写错误日志
                     //SS.Base.Log.Default.Error("更新数据字典错误消息：" + result.strMessage + "请手动处理查询原因!");
                    // by chengbao
                    Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.ExceptionLog, 0, "Proj.Business", "一般", "更新数据字典错误消息：" + result.strMessage + "请手动处理查询原因!");
                 }

             }

           
         }

    }
}
