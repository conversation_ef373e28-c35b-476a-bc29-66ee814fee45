﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpCyclelocation and List
    [Serializable]
    [Description("循环shelf位置表")]
    [LinqToDB.Mapping.Table("TP_CYCLELOCATION")]
    public partial class TpCyclelocation : GEntity<TpCyclelocation>
    {
        #region Contructor(s)

        private TpCyclelocation()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_Location = RegisterProperty<String>(p => p.Location);
        private static readonly PropertyInfo<Int32> pty_No = RegisterProperty<Int32>(p => p.No);
        #endregion

        /// <summary>
        /// location
        /// </summary>
        [Description("location")]
        [LinqToDB.Mapping.Column("location")]
        public String Location
        {
            get { return GetProperty(pty_Location); }
            set { SetProperty(pty_Location, value); }
        }
        /// <summary>
        /// No
        /// </summary>
        [Description("No")]
        [LinqToDB.Mapping.Column("No")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int32 No
        {
            get { return GetProperty(pty_No); }
            set { SetProperty(pty_No, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Location, "location是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Location, 5, "location不能超过5个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_No, "No是必填项"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.No.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class TpCyclelocationList : GEntityList<TpCyclelocationList, TpCyclelocation>
    {
        private TpCyclelocationList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
