{
  // ========================================
  // SECS/GEM 通信配置文件 - 默认配置
  // ========================================
  
  // 基本连接配置
  "isActive": true,           // 是否为主动模式 (true: 设备主动连接Host, false: 被动等待连接)
  "ipAddress": "127.0.0.1",       // IP地址 (Host的IP地址或本地回环地址)
  "port": 5555,                     // 端口号 (SECS/GEM标准端口通常为5555)
  "socketReceiveBufferSize": 8192, // Socket接收缓冲区大小 (字节)
  "deviceId": 1,                   // 设备ID (通常为1，用于标识设备)
  
  // ========================================
  // SECS/GEM 超时配置 (单位: 毫秒)
  // 根据 SEMI E5 标准设置
  // ========================================
  "t3": 45000,      // Reply Timeout - 回复超时时间 (等待回复消息的最大时间)
  "t5": 10000,      // Connect Separation Time - 连接分离时间 (连接尝试之间的间隔)
  "t6": 5000,       // Control Transaction Timeout - 控制事务超时 (控制消息处理超时)
  "t7": 10000,      // Not Selected Timeout - 未选择超时 (等待选择状态的超时)
  "t8": 5000       // Network Intercharacter Timeout - 网络字符间超时 (网络传输字符间隔超时)
  
  // ========================================
  // 配置说明:
  // 1. isActive=true: 设备作为客户端主动连接到Host
  // 2. isActive=false: 设备作为服务器等待Host连接
  // 3. 超时时间根据实际网络环境和设备响应速度调整
  // 4. 生产环境建议适当增加超时时间以提高稳定性
  // ========================================
}
