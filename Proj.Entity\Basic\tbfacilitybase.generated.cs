﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;


namespace Proj.Entity
{
    #region TbFacilityBase and List
    [Serializable]
    [Description("设备基础表")]
    [LinqToDB.Mapping.Table("TB_FACILITY_BASE")]
    public partial class TbFacilityBase : GEntity<TbFacilityBase>, ITimestamp
    {
        #region Contructor(s)

        private TbFacilityBase()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CFacilityId = RegisterProperty<String>(p => p.CFacilityId);
        private static readonly PropertyInfo<String> pty_CFacilityName = RegisterProperty<String>(p => p.CFacilityName);
        private static readonly PropertyInfo<String> pty_CFacilityDes = RegisterProperty<String>(p => p.CFacilityDes);
        private static readonly PropertyInfo<String> pty_CFacilityType = RegisterProperty<String>(p => p.CFacilityType);
        private static readonly PropertyInfo<String> pty_CKeyflag = RegisterProperty<String>(p => p.CKeyflag);
        private static readonly PropertyInfo<String> pty_CFacilityunits = RegisterProperty<String>(p => p.CFacilityunits);
        private static readonly PropertyInfo<String> pty_CValidstate = RegisterProperty<String>(p => p.CValidstate);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 设备编码
        /// </summary>
        [Description("设备编码")]
        [LinqToDB.Mapping.Column("C_FACILITYID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CFacilityId
        {
            get { return GetProperty(pty_CFacilityId); }
            set { SetProperty(pty_CFacilityId, value); }
        }
        /// <summary>
        /// 设备名称
        /// </summary>
        [Description("设备名称")]
        [LinqToDB.Mapping.Column("C_FACILITYNAME")]
        public String CFacilityName
        {
            get { return GetProperty(pty_CFacilityName); }
            set { SetProperty(pty_CFacilityName, value); }
        }
        /// <summary>
        /// 设备描述
        /// </summary>
        [Description("设备描述")]
        [LinqToDB.Mapping.Column("C_FACILITYDES")]
        public String CFacilityDes
        {
            get { return GetProperty(pty_CFacilityDes); }
            set { SetProperty(pty_CFacilityDes, value); }
        }
        /// <summary>
        /// 设备分类
        /// </summary>
        [Description("设备分类")]
        [LinqToDB.Mapping.Column("C_FACILITYTYPE")]
        public String CFacilityType
        {
            get { return GetProperty(pty_CFacilityType); }
            set { SetProperty(pty_CFacilityType, value); }
        }
        /// <summary>
        /// 是否关键设备
        /// </summary>
        [Description("是否关键设备")]
        [LinqToDB.Mapping.Column("C_KEYFLAG")]
        public String CKeyflag
        {
            get { return GetProperty(pty_CKeyflag); }
            set { SetProperty(pty_CKeyflag, value); }
        }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        [LinqToDB.Mapping.Column("C_FACILITYUNITS")]
        public String CFacilityunits
        {
            get { return GetProperty(pty_CFacilityunits); }
            set { SetProperty(pty_CFacilityunits, value); }
        }
        /// <summary>
        /// 当前状态
        /// </summary>
        [Description("当前状态")]
        [LinqToDB.Mapping.Column("C_VALIDSTATE")]
        public String CValidstate
        {
            get { return GetProperty(pty_CValidstate); }
            set { SetProperty(pty_CValidstate, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion


        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CFacilityId, "设备编码是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityId, 36, "设备编码不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityName, 100, "设备名称不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityDes, 500, "设备描述不能超过500个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityType, 1, "设备分类不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CKeyflag, 1, "是否关键设备不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityunits, 6, "单位不能超过6个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CValidstate, 1, "当前状态不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 40, "扩展字段A不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 40, "扩展字段B不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 40, "扩展字段C不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CFacilityId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbFacilityBaseList : GEntityList<TbFacilityBaseList, TbFacilityBase>
    {
        private TbFacilityBaseList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
