﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbProcessbase and List
    [Serializable]
    [Description("工序基础表")]
    [LinqToDB.Mapping.Table("TB_PROCESSBASE")]
    public partial class TbProcessbase : GEntity<TbProcessbase>, ITimestamp
    {
        #region Contructor(s)

        private TbProcessbase()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CProcessId = RegisterProperty<String>(p => p.CProcessId);
        private static readonly PropertyInfo<String> pty_CProcessName = RegisterProperty<String>(p => p.CProcessName);
        private static readonly PropertyInfo<String> pty_CProcessDes = RegisterProperty<String>(p => p.CProcessDes);
        private static readonly PropertyInfo<String> pty_CProcesshours = RegisterProperty<String>(p => p.CProcesshours);
        private static readonly PropertyInfo<String> pty_CProcessnum = RegisterProperty<String>(p => p.CProcessnum);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 工序ID
        /// </summary>
        [Description("工序ID")]
        [LinqToDB.Mapping.Column("C_PROCESSID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CProcessId
        {
            get { return GetProperty(pty_CProcessId); }
            set { SetProperty(pty_CProcessId, value); }
        }
        /// <summary>
        /// 工序名称
        /// </summary>
        [Description("工序名称")]
        [LinqToDB.Mapping.Column("C_PROCESSNAME")]
        public String CProcessName
        {
            get { return GetProperty(pty_CProcessName); }
            set { SetProperty(pty_CProcessName, value); }
        }
        /// <summary>
        /// 工序描述
        /// </summary>
        [Description("工序描述")]
        [LinqToDB.Mapping.Column("C_PROCESSDES")]
        public String CProcessDes
        {
            get { return GetProperty(pty_CProcessDes); }
            set { SetProperty(pty_CProcessDes, value); }
        }
        /// <summary>
        /// 工时
        /// </summary>
        [Description("工时")]
        [LinqToDB.Mapping.Column("C_PROCESSHOURS")]
        public String CProcesshours
        {
            get { return GetProperty(pty_CProcesshours); }
            set { SetProperty(pty_CProcesshours, value); }
        }
        /// <summary>
        /// 投入人员
        /// </summary>
        [Description("投入人员")]
        [LinqToDB.Mapping.Column("C_PROCESSNUM")]
        public String CProcessnum
        {
            get { return GetProperty(pty_CProcessnum); }
            set { SetProperty(pty_CProcessnum, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CProcessId, "工序ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProcessId, 40, "工序ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProcessName, 80, "工序名称不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProcessDes, 400, "工序描述不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProcesshours, 40, "工时不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProcessnum, 10, "投入人员不能超过10个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CProcessId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbProcessbaseList : GEntityList<TbProcessbaseList, TbProcessbase>
    {
        private TbProcessbaseList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
