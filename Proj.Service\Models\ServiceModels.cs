using System.ComponentModel.DataAnnotations;

namespace Proj.Service.Models
{
    /// <summary>
    /// 客户端发送消息的请求模型
    /// </summary>
    public class ClientMessageRequest
    {
        /// <summary>
        /// 功能名称
        /// </summary>
        [Required]
        public string Function { get; set; } = string.Empty;

        /// <summary>
        /// 参数字典
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 服务端响应模型
    /// </summary>
    public class ServiceResponse<T>
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        public static ServiceResponse<T> CreateSuccess(T data)
        {
            return new ServiceResponse<T>
            {
                Success = true,
                Data = data
            };
        }

        public static ServiceResponse<T> CreateError(string errorMessage)
        {
            return new ServiceResponse<T>
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 服务端向客户端发送消息的模型
    /// </summary>
    public class ServerMessage
    {
        /// <summary>
        /// 功能名称
        /// </summary>
        public string Function { get; set; } = string.Empty;

        /// <summary>
        /// 参数字典
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 客户端连接信息
    /// </summary>
    public class ClientConnectionInfo
    {
        /// <summary>
        /// 连接ID
        /// </summary>
        public string ConnectionId { get; set; } = string.Empty;

        /// <summary>
        /// 客户端IP地址
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime ConnectedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivity { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 系统状态信息
    /// </summary>
    public class SystemStateInfo
    {
        /// <summary>
        /// HSMS状态
        /// </summary>
        public string HSMSState { get; set; } = string.Empty;

        /// <summary>
        /// MCS通信状态
        /// </summary>
        public string MCSCommunication { get; set; } = string.Empty;

        /// <summary>
        /// 控制状态
        /// </summary>
        public string ControlState { get; set; } = string.Empty;

        /// <summary>
        /// SC状态
        /// </summary>
        public string SCState { get; set; } = string.Empty;

        /// <summary>
        /// SC模式
        /// </summary>
        public string SCMode { get; set; } = string.Empty;

        /// <summary>
        /// 机器状态
        /// </summary>
        public string MachineState { get; set; } = string.Empty;
    }

    /// <summary>
    /// 标签值请求
    /// </summary>
    public class TagValueRequest
    {
        /// <summary>
        /// 标签名称
        /// </summary>
        [Required]
        public string TagName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 状态查询请求
    /// </summary>
    public class StateRequest
    {
        /// <summary>
        /// 状态名称
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;
    }
}
