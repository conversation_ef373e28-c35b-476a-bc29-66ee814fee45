﻿using System;
using System.Collections.Generic;

using Proj.HostComm;
using Proj.UnitMng;
using Proj.CacheData;
using Proj.DataTypeDef;
using SecsLite.Gem;

namespace Proj.Controller
{
    public enum ImmediateCommandState : uint
    {
        Queue,
        Active,
        Completed
    }

    public class ImmediateCommand
    {
        public string CommandName { get; set; }
        public Dictionary<string, object> DicParam = new Dictionary<string, object>();
        public ImmediateCommandState State = ImmediateCommandState.Queue;
        public virtual void Execute() { }
    }

    public class InstallCommand : ImmediateCommand
    {
        public override void Execute()
        {
            string carrierID = DicParam["CARRIERID"].ToString();
            string carrierLoc = DicParam["CARRIERLOC"].ToString();
            //Install指令：在数据库中添加或更新盒子数据
            //检查盒子是否存在
            if (CarrierMng.Instance.Exist(carrierID))
            {
                //盒子信息已经存在，更新盒子信息
                if (CarrierMng.Instance.UpdateCarrierInfo(carrierID, carrierLoc))
                {
                    HostIF.Instance.PostCarrierEvent(carrierID, EqpEvent.CarrierInstallCompleted);
                }
            }
            else
            {
                //添加盒子信息
                if (CarrierMng.Instance.AddCarrierInfo(carrierID, carrierLoc))
                {
                    HostIF.Instance.PostCarrierEvent(carrierID, EqpEvent.CarrierInstallCompleted);
                    //HostIF.Instance.PostCarrierEvent(carrierID, EqpEvent.ZoneCapacityChange);
                }
            }
            this.State = ImmediateCommandState.Completed;
        }
    }

    public class RemoveCommand : ImmediateCommand
    {
        public override void Execute()
        {
            string carrierID = DicParam["CARRIERID"].ToString();
            //Remove指令：在数据库中删除盒子数据
            //检查盒子是否存在
            //if (!CarrierMng.Instance.Exist(carrierID))
            //{
            //    HostIF.Instance.PostCarrierEvent(carrierID, EqpEvent.ZoneCapacityChange);
            //}
            HostIF.Instance.PostCarrierEvent(carrierID, EqpEvent.CarrierRemoveCompleted);
            CarrierMng.Instance.DeleteCarrierInfo(carrierID);

            this.State = ImmediateCommandState.Completed;
        }
    }

    public class ResumeCommand : ImmediateCommand
    {
        public override void Execute()
        {
            //Resume指令：使SC进入Auto状态
            CraneMng.Instance.Resume();
            SCStateMng.Instance.SetState(SCState.Auto);
            HostIF.Instance.PostScEvent(EqpEvent.SCAutoComplete);
            this.State = ImmediateCommandState.Completed;
        }
    }
    //在指令执行流程里实现，不在此处实现
    public class AbortCommand : ImmediateCommand
    {
        public override void Execute()
        {

            this.State = ImmediateCommandState.Completed;
        }
    }

    public class CancelCommand : ImmediateCommand
    {
        public override void Execute()
        {
            string commandID = DicParam["COMMANDID"].ToString();
#if false
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    if (enhancedTransferCommand.strCommandID == commandID)
                    {
                        LocationMng.Instance.UnReserveLocation(enhancedTransferCommand.strRealSource);
                        LocationMng.Instance.UnReserveLocation(enhancedTransferCommand.strRealDest);

                        //判断源位置和目标位置是否禁用，禁用则执行Abort流程
                        if (LocationMng.Instance.IsAddressProhibited(enhancedTransferCommand.strRealSource)
                            || LocationMng.Instance.IsAddressProhibited(enhancedTransferCommand.strRealDest))
                        {
                            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelInitiated);
                            TransferMng.Instance.UpdataTransferState(commandID, 1, TransferState.None, ResultCode.OtherError);
                            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelCompleted);
                            this.State = ImmediateCommandState.Completed;
                            return;
                        }

                        //判断IO Port状态，不满足则执行Abort流程
                        if (LocationMng.Instance.GetLocationType(enhancedTransferCommand.strRealSource) == LocationType.IoPort
                            && PortMng.Instance.GetIOPortWorkingState(enhancedTransferCommand.strSourceZone) != IOPortWorkingStatus.WaitCranePick)
                        {
                            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelInitiated);
                            TransferMng.Instance.UpdataTransferState(commandID, 1, TransferState.None, ResultCode.OtherError);
                            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelCompleted);
                            this.State = ImmediateCommandState.Completed;
                            return;
                        }
                        if (LocationMng.Instance.GetLocationType(enhancedTransferCommand.strRealDest) == LocationType.IoPort
                            && PortMng.Instance.GetIOPortWorkingState(enhancedTransferCommand.strDestZone) != IOPortWorkingStatus.WaitCranePlace)
                        {
                            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelInitiated);
                            TransferMng.Instance.UpdataTransferState(commandID, 1, TransferState.None, ResultCode.OtherError);
                            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelCompleted);
                            this.State = ImmediateCommandState.Completed;
                            return;
                        }

                        //判断EQ Port状态，不满足则执行Abort流程
                        if (LocationMng.Instance.GetLocationType(enhancedTransferCommand.strRealSource) == LocationType.EqPort
                            && PortMng.Instance.GetEQPortInfo(enhancedTransferCommand.strSourceZone).eqReqStatus != EqReqStatus.UReq)
                        {
                            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelInitiated);
                            TransferMng.Instance.UpdataTransferState(commandID, 1, TransferState.None, ResultCode.OtherError);
                            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelCompleted);
                            this.State = ImmediateCommandState.Completed;
                            return;
                        }
                        if (LocationMng.Instance.GetLocationType(enhancedTransferCommand.strRealDest) == LocationType.EqPort
                            && PortMng.Instance.GetEQPortInfo(enhancedTransferCommand.strDestZone).eqReqStatus != EqReqStatus.LReq)
                        {
                            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelInitiated);
                            TransferMng.Instance.UpdataTransferState(commandID, 1, TransferState.None, ResultCode.OtherError);
                            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelCompleted);
                            this.State = ImmediateCommandState.Completed;
                            return;
                        }

                        break;
                    }
                }
            }
#endif
            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelInitiated);
            TransferMng.Instance.UpdataTransferState(commandID, 1, TransferState.None, ResultCode.OtherError);
            HostIF.Instance.PostTransferEvent(commandID, EqpEvent.TransferCancelCompleted);
            this.State = ImmediateCommandState.Completed;
        }
    }

    public class PauseCommand : ImmediateCommand
    {
        public override void Execute()
        {
            //Resume指令：使SC进入Pausing状态
            SCStateMng.Instance.SetState(SCState.Pausing);
            HostIF.Instance.PostScEvent(EqpEvent.SCPausedInitialized);
            CraneMng.Instance.Pause();
            this.State = ImmediateCommandState.Completed;
        }
    }

    public class ClearPLCTaskCommand : ImmediateCommand
    {
        public override void Execute()
        {
            //Resume指令：使SC进入Auto状态
            CraneMng.Instance.CleanPLCTask();
            this.State = ImmediateCommandState.Completed;
        }
    }

    public class LocationReservedCommand : ImmediateCommand
    {
        public override void Execute()
        {
            string strLocation = DicParam["LOCATION"].ToString();
            LocationMng.Instance.ReserveLocation(strLocation);
            this.State = ImmediateCommandState.Completed;
        }
    }

    public class ClearReservedCommand : ImmediateCommand
    {
        public override void Execute()
        {
            string strLocation = DicParam["LOCATION"].ToString();
            LocationMng.Instance.UnReserveLocation(strLocation);
            this.State = ImmediateCommandState.Completed;
        }
    }

    //public class ScanCommand : ImmediateCommand
    //{
    //    public override void Execute()
    //    {
    //        this.State = ImmediateCommandState.Completed;
    //    }
    //}

    //文档中没有该指令的场景描述，不实现
    public class LocateCommand : ImmediateCommand
    {
        public override void Execute()
        {
            //Locate指令：Host查询SC数据库中的Carrier信息

            this.State = ImmediateCommandState.Completed;
        }
    }

    //Scan、Transfer、PortTypeChange不在立即执行指令中实现


    public class CraneSetSpeedCommand : ImmediateCommand
    {
        public override void Execute()
        {
            string craneID = DicParam["CRANE"].ToString();
            int emptySpeed = Convert.ToInt32(DicParam["EMPTYSPEED"]);
            int loadSpeed = Convert.ToInt32(DicParam["LOADSPEED"]);

            CraneMng.Instance.SendCraneSpeed(craneID, emptySpeed, loadSpeed);
            this.State = ImmediateCommandState.Completed;
        }
    }

    public class PortSetSpeedCommand : ImmediateCommand
    {
        public override void Execute()
        {
            string portID = DicParam["PORT"].ToString();
          //  int speed = Convert.ToInt32(DicParam["SPEED"]);
            int emptySpeed = Convert.ToInt32(DicParam["EMPTYSPEED"]);
            int loadSpeed = Convert.ToInt32(DicParam["LOADSPEED"]);

            if(!portID.Contains("MR"))
            {
                PortMng.Instance.SendPortSpeed(portID, emptySpeed, loadSpeed);
            }
            
            this.State = ImmediateCommandState.Completed;
        }
    }

    public class FFUSetSpeedCommand : ImmediateCommand
    {
        public override void Execute()
        {
            string strFFUList = DicParam["ADDRESS"].ToString();
            int iSpeed = Convert.ToInt32(DicParam["SPEED"]);
            FFUMng.Instance.SetFFUSpeed(strFFUList, iSpeed);
            this.State = ImmediateCommandState.Completed;
        }
    }
}
