﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;


 
namespace Proj.Entity
{
    #region TbMatrlGroup1 and List
    [Serializable]
    [Description("物料组表1")]
    [LinqToDB.Mapping.Table("TB_MATRL_GROUP1")]
    public partial class TbMatrlGroup1 : GEntity<TbMatrlGroup1>
    {
        #region Contructor(s)

        private TbMatrlGroup1()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CMaterialgroupId = RegisterProperty<String>(p => p.CMaterialgroupId);
        private static readonly PropertyInfo<String> pty_CMaterialgroupDes = RegisterProperty<String>(p => p.CMaterialgroupDes);
        private static readonly PropertyInfo<String> pty_CUplevelCode = RegisterProperty<String>(p => p.CUplevelCode);
        #endregion

        /// <summary>
        /// 物料组编码
        /// </summary>
        [Description("物料组编码")]
        [LinqToDB.Mapping.Column("C_MATERIALGROUPID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CMaterialgroupId
        {
            get { return GetProperty(pty_CMaterialgroupId); }
            set { SetProperty(pty_CMaterialgroupId, value); }
        }
        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组描述")]
        [LinqToDB.Mapping.Column("C_MATERIALGROUPDES")]
        public String CMaterialgroupDes
        {
            get { return GetProperty(pty_CMaterialgroupDes); }
            set { SetProperty(pty_CMaterialgroupDes, value); }
        }
        /// <summary>
        /// 上层分类代码
        /// </summary>
        [Description("上层分类代码")]
        [LinqToDB.Mapping.Column("C_UPLEVELCODE")]
        public String CUplevelCode
        {
            get { return GetProperty(pty_CUplevelCode); }
            set { SetProperty(pty_CUplevelCode, value); }
        }

      
     
        
        #endregion 
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CMaterialgroupId, "物料组编码是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialgroupId, 40, "物料组编码不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialgroupDes, 160, "物料组描述不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CUplevelCode, 40, "上层分类代码不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CMaterialgroupId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbMatrlGroup1List : GEntityList<TbMatrlGroup1List, TbMatrlGroup1>
    {
        private TbMatrlGroup1List() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
