﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <!-- 禁用 CSLA 分析器以兼容生成的代码 -->
  <PropertyGroup>
    <NoWarn>$(NoWarn);CSLA0003;CSLA0008</NoWarn>
    <WarningsAsErrors />
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <!-- CSLA.NET Framework - 使用较低版本以提高兼容性 -->
    <PackageReference Include="CSLA" Version="6.2.2" />

    <!-- LinqToDB for database mapping - 使用较低版本 -->
    <PackageReference Include="linq2db" Version="4.4.0" />

    <!-- MySQL Data Provider -->
    <PackageReference Include="MySql.Data" Version="8.4.0" />

    <!-- JSON.NET -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

    <!-- SqlSugar ORM - 使用较低版本以提高兼容性 -->
    <PackageReference Include="SqlSugar" Version="5.1.3.103" />
  </ItemGroup>

  <ItemGroup>
    <!-- Reference to custom SS.BasFunction if available -->
    <Reference Include="SS.BasFunction" Condition="Exists('$(SolutionDir)bin\SS.BasFunction.dll')">
      <HintPath>$(SolutionDir)bin\SS.BasFunction.dll</HintPath>
    </Reference>
    <Reference Include="SS.BasFunction" Condition="Exists('$(MSBuildProjectDirectory)\..\bin\net8.0\SS.BasFunction.dll')">
      <HintPath>$(MSBuildProjectDirectory)\..\bin\net8.0\SS.BasFunction.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
