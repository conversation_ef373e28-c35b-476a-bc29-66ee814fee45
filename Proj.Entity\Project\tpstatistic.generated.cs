﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpStatistic and List
    [Serializable]
    [Description("TP_STATISTIC")]
    [LinqToDB.Mapping.Table("TP_STATISTIC")]
    public partial class TpStatistic : GEntity<TpStatistic>
    {
        #region Contructor(s)

        private TpStatistic()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_Group = RegisterProperty<String>(p => p.Group);
        private static readonly PropertyInfo<String> pty_Name = RegisterProperty<String>(p => p.Name);
        private static readonly PropertyInfo<String> pty_Value = RegisterProperty<String>(p => p.Value);
        #endregion

        /// <summary>
        /// Group
        /// </summary>
        [Description("Group")]
        [LinqToDB.Mapping.Column("Group")]
        public String Group
        {
            get { return GetProperty(pty_Group); }
            set { SetProperty(pty_Group, value); }
        }
        /// <summary>
        /// Name
        /// </summary>
        [Description("Name")]
        [LinqToDB.Mapping.Column("Name")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String Name
        {
            get { return GetProperty(pty_Name); }
            set { SetProperty(pty_Name, value); }
        }
        /// <summary>
        /// Value
        /// </summary>
        [Description("Value")]
        [LinqToDB.Mapping.Column("Value")]
        public String Value
        {
            get { return GetProperty(pty_Value); }
            set { SetProperty(pty_Value, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Group, 64, "Group不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Name, "Name是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Name, 64, "Name不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Value, 64, "Value不能超过64个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Name; }
        #endregion
    }       
        
    [Serializable]
    public partial class TpStatisticList : GEntityList<TpStatisticList, TpStatistic>
    {
        private TpStatisticList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
