namespace Proj.API.Services
{
    /// <summary>
    /// SECS/GEM 配置服务实现
    /// </summary>
    public class SecsGemConfigService : ISecsGemConfigService
    {
        private readonly string[] _configPaths;

        /// <summary>
        /// 构造函数
        /// </summary>
        public SecsGemConfigService()
        {
            // 按优先级定义配置文件查找路径
            _configPaths = new[]
            {
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "secs-gem-config.json"),
                Path.Combine(Environment.CurrentDirectory, "config", "secs-gem-config.json"),
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "secs-gem-config.json"),
                Path.Combine(Environment.CurrentDirectory, "secs-gem-config.json"),
            };
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        /// <returns>配置对象</returns>
        public SecsGemConfig LoadConfig()
        {
            // 按优先级查找配置文件
            foreach (var configPath in _configPaths)
            {
                if (File.Exists(configPath))
                {
                    try
                    {
                        Console.WriteLine($"[SecsGemConfigService] 找到配置文件: {configPath}");
                        var config = SecsGemConfig.LoadFromFile(configPath);
                        Console.WriteLine($"[SecsGemConfigService] 配置文件加载成功");
                        return config;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[SecsGemConfigService] 配置文件加载失败: {ex.Message}");
                        Console.WriteLine($"[SecsGemConfigService] 尝试下一个配置文件路径...");
                        continue;
                    }
                }
            }

            Console.WriteLine("[SecsGemConfigService] 未找到有效配置文件，创建默认配置文件");

            try
            {
                // 创建默认配置文件
                var defaultConfig = CreateDefaultConfig();
                var defaultConfigPath = GetDefaultConfigPath();

                // 确保目录存在
                var directory = Path.GetDirectoryName(defaultConfigPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    Console.WriteLine($"[SecsGemConfigService] 创建配置目录: {directory}");
                }

                defaultConfig.SaveToFile(defaultConfigPath);
                Console.WriteLine($"[SecsGemConfigService] 默认配置文件已创建: {defaultConfigPath}");

                return defaultConfig;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SecsGemConfigService] 创建默认配置文件失败: {ex.Message}");
                Console.WriteLine($"[SecsGemConfigService] 返回内存中的默认配置");
                return CreateDefaultConfig();
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="config">配置对象</param>
        public void SaveConfig(SecsGemConfig config)
        {
            var configPath = GetConfigFilePath();
            config.SaveToFile(configPath);
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        public string GetConfigFilePath()
        {
            // 优先返回已存在的配置文件路径
            foreach (var configPath in _configPaths)
            {
                if (File.Exists(configPath))
                {
                    return configPath;
                }
            }

            // 如果没有找到，返回默认路径
            return GetDefaultConfigPath();
        }

        /// <summary>
        /// 获取默认配置文件路径
        /// </summary>
        /// <returns>默认配置文件路径</returns>
        private string GetDefaultConfigPath()
        {
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "secs-gem-config.json");
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        /// <returns>默认配置对象</returns>
        private SecsGemConfig CreateDefaultConfig()
        {
            return new SecsGemConfig
            {
                IsActive = true,                    // 主动模式 - 设备主动连接到Host
                IpAddress = "127.0.0.1",           // 本地回环地址，用于测试
                Port = 5555,                       // SECS/GEM 标准端口
                SocketReceiveBufferSize = 8192,    // 8KB 接收缓冲区
                DeviceId = 1,                      // 设备ID，通常为1
                
                // SECS/GEM 标准超时时间设置 (单位: 毫秒)
                T3 = 45000,    // Reply Timeout - 回复超时时间 (45秒)
                T5 = 10000,    // Connect Separation Time - 连接分离时间 (10秒)
                T6 = 5000,     // Control Transaction Timeout - 控制事务超时 (5秒)
                T7 = 10000,    // Not Selected Timeout - 未选择超时 (10秒)
                T8 = 5000      // Network Intercharacter Timeout - 网络字符间超时 (5秒)
            };
        }
    }
}
