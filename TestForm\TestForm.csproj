﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Proj.CacheData\Proj.CacheData.csproj" />
    <ProjectReference Include="..\Proj.DataTypeDef\Proj.DataTypeDef.csproj" />
    <ProjectReference Include="..\Proj.DB\Proj.DB.csproj" />
    <ProjectReference Include="..\Proj.DevComm\Proj.DevComm.csproj" />
    <ProjectReference Include="..\Proj.Entity\Proj.Entity.csproj" />
    <ProjectReference Include="..\Proj.Log\Proj.Log.csproj" />
    <ProjectReference Include="..\Proj.UnitMng\Proj.UnitMng.csproj" />
  </ItemGroup>

</Project>