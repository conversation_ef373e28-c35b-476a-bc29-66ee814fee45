﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntEventconfig and List
    [Serializable]
    [Description("事件配置表")]
    [LinqToDB.Mapping.Table("Tnt_EventConfig")]
    public partial class TntEventconfig : GEntity<TntEventconfig>, ITimestamp
    {
        #region Contructor(s)

        private TntEventconfig()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CEventType = RegisterProperty<String>(p => p.CEventType);
        private static readonly PropertyInfo<String> pty_CEventName = RegisterProperty<String>(p => p.CEventName);
        private static readonly PropertyInfo<String> pty_CEventparam = RegisterProperty<String>(p => p.CEventparam);
        private static readonly PropertyInfo<String> pty_CEventlevel = RegisterProperty<String>(p => p.CEventlevel);
        private static readonly PropertyInfo<String> pty_CDescription = RegisterProperty<String>(p => p.CDescription);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 事件类型
        /// </summary>
        [Description("事件类型")]
        [LinqToDB.Mapping.Column("c_EventType")]
        public String CEventType
        {
            get { return GetProperty(pty_CEventType); }
            set { SetProperty(pty_CEventType, value); }
        }
        /// <summary>
        /// 事件名称
        /// </summary>
        [Description("事件名称")]
        [LinqToDB.Mapping.Column("c_EventName")]
        public String CEventName
        {
            get { return GetProperty(pty_CEventName); }
            set { SetProperty(pty_CEventName, value); }
        }
        /// <summary>
        /// 事件参数
        /// </summary>
        [Description("事件参数")]
        [LinqToDB.Mapping.Column("c_EventParam")]
        public String CEventparam
        {
            get { return GetProperty(pty_CEventparam); }
            set { SetProperty(pty_CEventparam, value); }
        }
        /// <summary>
        /// 事件级别
        /// </summary>
        [Description("事件级别")]
        [LinqToDB.Mapping.Column("c_EventLevel")]
        public String CEventlevel
        {
            get { return GetProperty(pty_CEventlevel); }
            set { SetProperty(pty_CEventlevel, value); }
        }
        /// <summary>
        /// 事件描述
        /// </summary>
        [Description("事件描述")]
        [LinqToDB.Mapping.Column("c_Description")]
        public String CDescription
        {
            get { return GetProperty(pty_CDescription); }
            set { SetProperty(pty_CDescription, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 36, "主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CEventType, 30, "事件类型不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CEventName, 20, "事件名称不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CEventparam, 400, "事件参数不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CEventlevel, 2, "事件级别不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDescription, 200, "事件描述不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntEventconfigList : GEntityList<TntEventconfigList, TntEventconfig>
    {
        private TntEventconfigList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
