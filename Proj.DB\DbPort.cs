﻿using System;
using System.Linq;

using Proj.Entity;
using Proj.DataTypeDef;

namespace Proj.DB
{
    public class DbPort
    {
        private static DbPort m_Instanse;
        private static readonly object mSyncObject = new object();
        private DbPort() { }
        public static DbPort Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new DbPort();
                        }
                    }
                }
                return m_Instanse;
            }
        }
        
        /// <summary>
        /// 获取数据库中的Port信息
        /// </summary>
        /// <param name="portNo">Port编号</param>
        /// <returns></returns>
        public TpPort GetPortDbInfoByNo(int portNo)
        {
            TpPort tpPort = null;
            try 
            {
                tpPort = TpPort.GetById(portNo);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return tpPort;
        }
        /// <summary>
        /// 获取数据库中的Port信息
        /// </summary>
        /// <param name="portName">Port名称</param>
        /// <returns></returns>
        public TpPort GetPortDbInfoByName(string portName)
        {
            TpPort tpPort = null;
            try
            {
                tpPort = TpPort.GetById(TpPortList.GetByLambda(x => x.Name == portName).First().No);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return tpPort;
        }

        public TpPortViewList GetDbPortViewList()
        {
            try
            {
                TpPortViewList tpPortList = TpPortViewList.GetAll();
                return tpPortList;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return null;
        }
        public TpPortList GetDbPortList()
        {
            try
            {
                TpPortList tpPortList = TpPortList.GetAll();
                return tpPortList;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return null;
        }

        /// <summary>
        /// 在数据库中添加一条Port信息
        /// </summary>
        /// <param name="portNo">Port编号</param>
        /// <param name="portName">Port名称</param>
        /// <param name="location">Port所在位置</param>
        /// <returns></returns>
        public bool AddPortInfo(int portNo, string portName, string location)
        {
            try
            {
                if (TpPort.Exists(portNo))
                {
                    //记录Port日志：向数据库中添加Port信息失败{portNo}已存在
                    return false;
                }
                else
                {
                    //检查位置是否存在
                    //...

                    TpPort tpPort = TpPort.New();
                    tpPort.No = portNo;
                    tpPort.Name = portName;
                    tpPort.Location = location;
                    tpPort.Save();
                    //记录Port日志：向数据库中添加Port信息
                    return true;
                }

            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        /// <summary>
        /// 在数据库中删除一条Port信息
        /// </summary>
        /// <param name="portNo">Port编号</param>
        /// <returns></returns>
        public bool DeletePortDbInfo(int portNo)
        {
            try
            {
                TpPortList.DeleteByCriteria(x => x.No == portNo);
                //记录Port日志：从数据库中删除Port信息{portNo}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 修改数据库中Port的名字
        /// </summary>
        /// <param name="portNo">Port编号</param>
        /// <param name="portNewName">Port名称<</param>
        /// <returns></returns>
        public bool ChangePortName(int portNo, string portNewName)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.Name = portNewName;
                tpPort.Save();
                //记录Port日志：在数据库中修改Port名称{portNo, portNewName}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中设置Port的位置信息
        /// </summary>
        /// <param name="portNo">Port编号</param>
        /// <param name="location">位置</param>
        /// <returns></returns>
        public bool SetPortLocation(int portNo, string location)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.Location = location;
                tpPort.Save();
                //记录Port日志：在数据库中修改Port的位置{portNo, location}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中获取Port的位置信息
        /// </summary>
        /// <param name="portNo">Port编号</param>
        /// <returns></returns>
        public string GetPortLocationByNo(int portNo)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                return tpPort.Location;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return "";
        }
        /// <summary>
        /// 在数据库中设置Port的工作类型（入口\出口\双向）
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="unitType"></param>
        /// <returns></returns>
        public bool SetPortUnitType(int portNo, PortUnitType unitType)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.UnitType = ((int)unitType).ToString();
                tpPort.Save();
                //记录Port日志：在数据库中修改Port的单元类型{portNo, unitType}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中获取Port的工作类型
        /// </summary>
        /// <param name="portNo"></param>
        /// <returns></returns>
        public PortUnitType GetPortUnitType(int portNo)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                return (PortUnitType)int.Parse(tpPort.UnitType);
            }
            catch(Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return PortUnitType.None;
        }
        /// <summary>
        /// 在数据库中设置Port是否为人工Port
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="flag"></param>
        /// <returns></returns>
        public bool SetPortIsMPort(int portNo, bool flag)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.IsMport = flag ? 1 : 0;
                tpPort.Save();
                //记录Port日志：在数据库中修改Port的单元类型{portNo, unitType}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 查询Port是否为人工Port
        /// </summary>
        /// <param name="portNo"></param>
        /// <returns></returns>
        public bool IsMPort(int portNo)
        {
            bool bRes = false;
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                bRes = tpPort.IsMport == 1 ? true : false;
                return bRes;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }
        /// <summary>
        /// 在数据库中更新Port的工作状态
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        public bool UpdatePortTransferState(int portNo, PortTransferState state)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.TransferState = ((int)state).ToString();
                tpPort.Save();
                //记录Port日志：在数据库中修改Port的服务状态{portNo, state}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        public bool UpdatePortTransferState(string portName, PortTransferState state)
        {
            try
            {
                TpPort tpPort = TpPort.GetByLambda(x=>x.Name == portName);
                if(tpPort != null)
                {
                    tpPort.TransferState = ((int)state).ToString();
                    tpPort.Save();
                    //记录Port日志：在数据库中修改Port的服务状态{portNo, state}
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中设置Port是否有读码器
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="flag"></param>
        /// <returns></returns>
        public bool SetPortIsHaveIDR(int portNo, bool flag)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.IsIdr = flag ? 1 : 0;
                tpPort.Save();
                //记录Port日志：在数据库中修改Port的读码器设置{portNo, flag}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 查询数据库中Port是否有IDR的设置
        /// </summary>
        /// <param name="portNo"></param>
        /// <returns></returns>
        public bool IsPortHaveIDR(int portNo)
        {
            bool bRes = false;
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                bRes = tpPort.IsIdr == 1 ? true : false;
                return bRes;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }
        /// <summary>
        /// 在数据库中设置Port的优先级
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="priority"></param>
        /// <returns></returns>
        public bool SetPortPriority(int portNo, int priority)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.Priority = priority;
                tpPort.Save();
                //记录Port日志：在数据库中修改Port的优先级{portNo, priority}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中获取Port的优先级设置
        /// </summary>
        /// <param name="portNo"></param>
        /// <returns></returns>
        public int GetPortPriority(int portNo)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                return (int)tpPort.Priority;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return 0;
        }
        /// <summary>
        /// 在数据库中设置Port对应的设备编号
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="eqNumber"></param>
        /// <returns></returns>
        public bool SetPortEqNumber(int portNo, int eqNumber)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.EqNumber = eqNumber;
                tpPort.Save();
                //记录Port日志：在数据库中修改Port的设备编号{portNo, eqNumber}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 查询数据库中Port对应的设备编号设置
        /// </summary>
        /// <param name="portNo"></param>
        /// <returns></returns>
        public int GetPortEqNumber(int portNo)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                return (int)tpPort.EqNumber;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return 0;
        }
        /// <summary>
        /// 更新数据库中Port的速度设定值
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="speed"></param>
        /// <returns></returns>
        public bool UpdatePortSpeedSetting(int portNo, PortSpeed speed)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.SpeedXAxis = speed.xAxisSpeed;
                tpPort.SpeedYAxis = speed.yAxisSpeed;
                tpPort.SpeedZAxis = speed.zAxisSpeed;
                tpPort.SpeedTAxis = speed.tAxisSpeed;
                tpPort.Save();
                //记录Port日志：在数据库中更新了Port的速度设置{portNo, speed}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 获取数据库中port的速度设定值
        /// </summary>
        /// <param name="portNo"></param>
        /// <returns></returns>
        public PortSpeed GetPortSpeedSetting(int portNo)
        {
            PortSpeed speed = new PortSpeed();
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                speed.xAxisSpeed = (int)tpPort.SpeedXAxis;
                speed.yAxisSpeed = (int)tpPort.SpeedYAxis;
                speed.zAxisSpeed = (int)tpPort.SpeedZAxis;
                speed.tAxisSpeed = (int)tpPort.SpeedTAxis;
                return speed;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return speed;
        }
        /// <summary>
        /// 更新数据库中Port的里程信息
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="mileage"></param>
        /// <returns></returns>
        public bool UpdatePortMileage(int portNo, AxisMileage mileage)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.MileageTravel = mileage.travelMileage;
                tpPort.MileageElevation = mileage.elevationMileage;
                tpPort.MileageFork = mileage.forkMileage;
                tpPort.MileageTurn = mileage.turnMileage;
                tpPort.Save();
                //记录Port日志：在数据库中更新了Port的里程数据{portNo, mileage}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中设置Port的缓存位置数量
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="bufCount"></param>
        /// <returns></returns>
        public bool SetPortBufferCount(int portNo, int bufCount)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.BufferCount = bufCount;
                tpPort.Save();
                //记录Port日志：在数据库中更新了Port的Buffer数量信息{portNo, bufCount}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中获取Port的缓存位置数量的设置值
        /// </summary>
        /// <param name="portNo"></param>
        /// <returns></returns>
        public int GetPortBufferCount(int portNo)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                return (int)tpPort.BufferCount;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return 0;
        }
        /// <summary>
        /// 在数据库中设置Port中台车的数量
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="carCount"></param>
        /// <returns></returns>
        public bool SetPortCarCount(int portNo, int carCount)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.CarCount = carCount;
                tpPort.Save();
                //记录Port日志：在数据库中更新了Port的车辆数量信息{portNo, carCount}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中获取Port台车数量的设置值
        /// </summary>
        /// <param name="portNo"></param>
        /// <returns></returns>
        public int GetPortCarCount(int portNo)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                return (int)tpPort.CarCount;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return 0;
        }
        /// <summary>
        /// 在数据库中更新Port的备注信息
        /// </summary>
        /// <param name="portNo"></param>
        /// <param name="commnet"></param>
        /// <returns></returns>
        public bool UpdatePortComment(int portNo, string commnet)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                tpPort.Comment = commnet;
                tpPort.Save();
                //记录Port日志：在数据库中更新了Port的车辆数量信息{portNo, carCount}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中获取Port的备注信息
        /// </summary>
        /// <param name="portNo"></param>
        /// <returns></returns>
        public string GetPortComment(int portNo)
        {
            try
            {
                TpPort tpPort = TpPort.GetById(portNo);
                return tpPort.Comment;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbPort.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return "";
        }
    }
}
