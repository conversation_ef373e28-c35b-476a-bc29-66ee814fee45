﻿using System;
using System.Windows.Forms;
using Proj.DevComm;
using Proj.DataTypeDef;
using Proj.Entity;
using Proj.DB;
using Proj.Controller;
using Proj.HostComm;

namespace TestForm
{
    public partial class MainFrm : Form
    {
        public MainFrm()
        {
            InitializeComponent();
            timerPlcTaskData = new Timer();
            timerPlcTaskData.Tick += timerPlcTaskData_Tick;
            timerPlcTaskData.Interval = 500;
            timerPlcTaskData.Enabled = false;
        }

        void timerPlcTaskData_Tick(object sender, EventArgs e)
        {
            //欧姆龙测试
            //object objStructBool = PLCTest.Instance.GetIOValue("TaskStruct.memBool");
            //txtStructBoolR.Text = objStructBool.ToString();

            //object objStructInt = PLCTest.Instance.GetIOValue("TaskStruct.memInt");
            //txtStructIntR.Text = objStructInt.ToString();

            //object objStructFloat = PLCTest.Instance.GetIOValue("TaskStruct.memFloat");
            //txtStructFloatR.Text = objStructFloat.ToString();

            //object objStructString = PLCTest.Instance.GetIOValue("TaskStruct.memString");
            //txtStructStringR.Text = objStructString.ToString();

            //object objArrayInt0 = PLCTest.Instance.GetIOValue("TaskArray[0]");
            //txtArrayInt0R.Text = objArrayInt0.ToString();

            //object objArrayInt1 = PLCTest.Instance.GetIOValue("TaskArray[1]");
            //txtArrayInt1R.Text = objArrayInt1.ToString();

            //object objArrayInt2 = PLCTest.Instance.GetIOValue("TaskArray[2]");
            //txtArrayInt2R.Text = objArrayInt2.ToString();

            //object objTaskInt = PLCTest.Instance.GetIOValue("TaskInt");
            //txtTaskIntR.Text = objTaskInt.ToString();

            //object objTaskInt2 = PLCTest.Instance.GetIOValue("TaskInt2");
            //txtTaskInt2R.Text = objTaskInt2.ToString();

            //object objTaskDint = PLCTest.Instance.GetIOValue("TaskDint");
            //txtTaskDintR.Text = objTaskDint.ToString();

            //object objTaskFloat = PLCTest.Instance.GetIOValue("TaskFloat");
            //txtTaskFloatR.Text = objTaskFloat.ToString();

            //object objTaskDouble = PLCTest.Instance.GetIOValue("TaskDouble");
            //txtTaskDoubleR.Text = objTaskDouble.ToString();

            //object objTaskString = PLCTest.Instance.GetIOValue("TaskString");
            //txtTaskStringR.Text = objTaskString.ToString();

            //object objTaskBool = PLCTest.Instance.GetIOValue("TaskBool");
            //txtTaskBoolR.Text = objTaskBool.ToString();

            //IC Stocker
            craneCurrTaskData = CraneDev.Instance.GetCraneCurrTaskData("Crane1");
            txtPlcCmdType.Text = craneCurrTaskData.taskType.ToString();
            txtPlcCarrierID.Text = craneCurrTaskData.strCarrierID;
            txtPlcSourceAddr.Text = craneCurrTaskData.strSourceAddr;
            txtPlcDestAddr.Text = craneCurrTaskData.strDestAddr;
        }

        private Timer timerPlcTaskData = null;
        private CraneTaskData craneCurrTaskData;

        private void MainFrm_Load(object sender, EventArgs e)
        {
            ////IC Stocker
            //if (PlcComm.Instance.Start())
            //{
            //    timerPlcTaskData.Enabled = true;
            //}
            //else
            //{
            //    MessageBox.Show("Start PLC Communication Failed");
            //}

            //欧姆龙测试
            //if (PLCTest.Instance.Start())
            //{
            //    timerPlcTaskData.Enabled = true;
            //}
            //else
            //{
            //    MessageBox.Show("Start PLC Communication Failed");
            //}
        }

        private void MainFrm_FormClosing(object sender, FormClosingEventArgs e)
        {
            timerPlcTaskData.Enabled = false;
            //PlcComm.Instance().Stop();

            //PLCTest.Instance.Stop();
        }

        private void btnTransfer_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtTaskCarrierID.Text))
            {
                MessageBox.Show("Please Input CarrierID");
                return;
            } 
            if (string.IsNullOrEmpty(txtTaskSourceAddr.Text))
            {
                MessageBox.Show("Source");
                return;
            }
            if (string.IsNullOrEmpty(txtTaskDestAddr.Text))
            {
                MessageBox.Show("Dest");
                return;
            }
            CraneTaskData taskInfo = new CraneTaskData();
            taskInfo.taskType = CraneTaskType.Transfer;
            taskInfo.strCarrierID = txtTaskCarrierID.Text;
            taskInfo.strSourceAddr = txtTaskSourceAddr.Text;
            taskInfo.strDestAddr = txtTaskDestAddr.Text;
            if (!CraneDev.Instance.SendCraneCommand("Crane1", taskInfo))
            {
                MessageBox.Show("Send Command to Crane Failed ");
            }
        }

        private void btnWriteData_Click(object sender, EventArgs e)
        {
            bool bRes = false;
            bool strcutBool = Boolean.Parse(txtStructBoolW.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskStruct.memBool", strcutBool);
            //Console.WriteLine(string.Format("TaskStruct.memBool:{0}", bRes));

            int structInt = int.Parse(txtStructIntW.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskStruct.memInt", structInt);
            //Console.WriteLine(string.Format("TaskStruct.memInt:{0}", bRes));

            float stuctFloat = float.Parse(txtStructFloatW.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskStruct.memFloat", stuctFloat);
            //Console.WriteLine(string.Format("TaskStruct.memFloat:{0}", bRes));

            bRes = PLCTest.Instance.WriteIOValue("TaskStruct.memString", txtStructStringW.Text);
            //Console.WriteLine(string.Format("TaskStruct.memString:{0}", bRes));

            int arrayInt0 = int.Parse(txtArrayInt0W.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskArray[0]", arrayInt0);
            //Console.WriteLine(string.Format("TaskArray[0]:{0}", bRes));

            int arrayInt1 = int.Parse(txtArrayInt1W.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskArray[1]", arrayInt1);
            //Console.WriteLine(string.Format("TaskArray[1]:{0}", bRes));

            int arrayInt2 = int.Parse(txtArrayInt2W.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskArray[2]", arrayInt2);
            //Console.WriteLine(string.Format("TaskArray[2]:{0}", bRes));

            int taskInt = int.Parse(txtTaskIntW.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskInt", taskInt);
            //Console.WriteLine(string.Format("TaskInt:{0}", bRes));

            int taskInt2 = int.Parse(txtTaskInt2W.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskInt2", taskInt2);
            //Console.WriteLine(string.Format("TaskInt2:{0}", bRes));

            long taskDInt = long.Parse(txtTaskDintW.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskDint", taskDInt);
            //Console.WriteLine(string.Format("TaskDint:{0}", bRes));

            float taskFloat = float.Parse(txtTaskFloatW.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskFloat", taskFloat);
            //Console.WriteLine(string.Format("TaskFloat:{0}", bRes));

            double taskDouble = double.Parse(txtTaskDoubleW.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskDouble", taskDouble);
            //Console.WriteLine(string.Format("TaskDouble:{0}", bRes));

            bRes = PLCTest.Instance.WriteIOValue("TaskString", txtTaskStringW.Text);
            //Console.WriteLine(string.Format("TaskString:{0}", bRes));

            bool taskBool = Boolean.Parse(txtTaskBoolW.Text);
            bRes = PLCTest.Instance.WriteIOValue("TaskBool", taskBool);
            //Console.WriteLine(string.Format("TaskBool:{0}", bRes));
        }

        private void btnDbTest_Click(object sender, EventArgs e)
        {
            int craneNo = 1;
            CraneSpeed speed = new CraneSpeed();
            speed.emptySpeed = 20;
            speed.loadSpeed = 50;
            speed.xAxisSpeed = 100;
            speed.yAxisSpeed = 100;
            speed.zAxisSpeed = 100;
            speed.tAxisSpeed = 100;

            AxisMileage mileage = new AxisMileage();
            mileage.travelMileage = 1.2;
            mileage.elevationMileage = 2.3;
            mileage.turnMileage = 3.4;
            mileage.forkMileage = 4.5;

            bool bRes = false;
            bRes = DbCrane.Instance.AddCraneInfo(craneNo, "MSTHK100C1");
            //bRes = DbCrane.Instance.ChangeCraneName(craneNo, "Crane02");
            //bRes = DbCrane.Instance.SetCraneIsHaveIDR(craneNo, false);
            //bRes = DbCrane.Instance.UpdateCraneTransferState(craneNo, TransferState.None);
            //bRes = DbCrane.Instance.UpdateCraneSpeedSetting(craneNo, speed);
            //bRes = DbCrane.Instance.UpdateCraneMileage(craneNo, mileage);
            //bRes = DbCrane.Instance.UpdateCraneComment(craneNo, "Test123");
            //TpCrane crane = DbCrane.Instance.GetCraneDbInfoByNo(craneNo);
            //bRes = DbCrane.Instance.DeleteCraneDbInfo(craneNo);

            //TpCarrier tpCarrier = TpCarrier.New();
            //tpCarrier.Id = "Carrier01";
            //tpCarrier.Location = "10201";
            //tpCarrier.State = ((int)(CarrierState.Installed)).ToString();
            //tpCarrier.IdReadStatus = ((int)IDReadStatus.Success).ToString();
            //tpCarrier.InstallTime = DateTime.Now;
            //tpCarrier.Comment = "";
            //tpCarrier.Save();

        }

        private void btnHsmsStart_Click(object sender, EventArgs e)
        {
            StockerController.Instance.Initialize();
        }

        private void start_Click(object sender, EventArgs e)
        {
            if (!CraneDev.Instance.CraneTaskStart("Crane1"))
            {
                MessageBox.Show("启动失败");
            }
        }
    }
}
