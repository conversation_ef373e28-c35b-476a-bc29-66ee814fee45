﻿using Proj.Entity;
using SS.Base;
using SS.BLL.Base;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Proj.Business
{
    public abstract class Logger
    {
        public static void WriteDB(DateTime logTime, SS.Base.LogMananger.LogType type, int logId, string model, string level,string message)
        {
            try
            {
                TntLogrecord log = TntLogrecord.New();
                log.CPk = SequenceService.GenerateLocalId();
                log.CLogtime = logTime.ToString("yyyy-MM-dd HH:mm:ss");
                log.CLogId = logId;
                log.CUserId = SS.Base.UserInfo.UserID;
                log.CLogmodel = model;
                log.CLoglevel = level == "一般"?"3": level;
                log.CLogType = ((int)type).ToString();
                log.CLogtext = message;
                log.Save();
            }
            catch(Exception e)
            {
                SS.Base.LogMananger.Logger.Create("log").WriteFile(logTime, SS.Base.LogMananger.LogType.ExceptionLog, logId, model, level, UserInfo.UserID, message + ":==>" + e.Message);
            }
        }
    }
}
