using Microsoft.Extensions.Options;
using Secs4Net;

namespace Proj.API.Services
{
    /// <summary>
    /// SECS 连接工厂接口
    /// </summary>
    public interface ISecsConnectionFactory
    {
        /// <summary>
        /// 创建 HSMS 连接
        /// </summary>
        /// <param name="options">配置选项</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>HSMS 连接实例</returns>
        HsmsConnection CreateHsmsConnection(IOptions<SecsGemOptions> options, ISecsGemLogger logger);

        /// <summary>
        /// 创建 SecsGem 实例
        /// </summary>
        /// <param name="options">配置选项</param>
        /// <param name="connection">连接实例</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>SecsGem 实例</returns>
        SecsGem CreateSecsGem(IOptions<SecsGemOptions> options, HsmsConnection connection, ISecsGemLogger logger);
    }
}
