﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="exceptionHandling" type="ControlEase.Nexus.ExceptionHandling.ExceptionHandlingConfigurationSection,CE.Nexus.ComponentModel" />
    <section name="logging" type="ControlEase.Nexus.Logging.LoggingConfigurationSection,CE.Nexus.ComponentModel" />
    <section name="extensions" type="ControlEase.Nexus.AddIn.AddInConfigurationSection,CE.Nexus.Composition" />
    <section name="Options" type="ControlEase.Nexus.ComponentModel.OptionConfigurationSection,CE.Nexus.ComponentModel" />
    <section name="DataAccess" type="ControlEase.Nexus.DataAccess.DataAccessConfigurationSection,CE.Nexus.ComponentModel" />
  </configSections>

  <appSettings>
    <add key="CurrentCulture" value="en-US" />
    <add key="appName" value="Project Runtime" />
    <add key="appLogo" value="pack://application:,,,/InRun;component/Resources/InRun.ico" />
    <add key="appCopyright" value="Copyright © ControlEase 2012, All Rights Reserved." />
    <add key="appVersion" value="INSPEC NX" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />

    <add key="isdemo" value="false" />
    <add key="default.db" value="SSMES_MYSQL" />
    <add key="default.db.encrypt" value="false" />
    <add key="mylog.loglevel" value="Debug" />
    <add key="AutoCloneOnUpdate" value="false" />
    <add key="CslaDataPortalProxy--" value="Csla.Extend.Client.WcfProxy, SS.MidFunction" />
    <add key="CslaDataPortalUrl" value="http://localhost:12847/CslaWsHttpPortal" />
  </appSettings>

  <!--<system.data>
    <DbProviderFactories>
      <add name="MySQL Data Provider" invariant="MySql.Data.MySqlClient" description=".Net Framework Data Provider for MySQL" type="MySql.Data.MySqlClient.MySqlClientFactory, MySql.Data, Version=********, Culture=neutral, PublicKeyToken=c5687fc88969c44d"/>
    </DbProviderFactories>
  </system.data>-->

  <connectionStrings>
    <add name="SSMES" connectionString="Data Source=PC-SY/SSMES;User Id=*****;Password=*****;enlist=dynamic" providerName="Oracle.ManagedDataAccess.Client" />
    <add name="SQMES" connectionString="Data Source=PC-SY;Initial Catalog=SSMES;User ID=sa;Password=***" providerName="System.Data.SqlClient" />
    <add name="SSMES_ZS" providerName="System.Data.SqlClient" connectionString="Data Source=HPSVR;Initial Catalog=FA;Persist Security Info=True;User ID=yjyadmin;Password=`*****100;Pooling=False" />
    <add name="SSMES_MYSQL" providerName="MySql.Data.MySqlClient" connectionString="Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True" />
  </connectionStrings>

  <exceptionHandling>
    <exceptionPolicies>
      <add name="ViewExceptionPolicy">
        <exceptionTypes>
          <add name="Exception" type="System.Exception, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" postHandlingAction="None">
            <exceptionHandlers>
              <add name="View handler" type="ControlEase.Nexus.Windows.ViewExceptionHandler,CE.Nexus.Windows" />
            </exceptionHandlers>
          </add>
        </exceptionTypes>
      </add>
    </exceptionPolicies>
  </exceptionHandling>

  <logging throwExceptions="true" enabled="true" autoReload="true" internalLogToConsole="true" internalLogToConsoleError="true" internalLogFile="internalLogFile" internalLogLevel="All">
    <targets>
      <target name="debug" type="Debug" />
      <target name="LogService" type="MethodCall" Class="InRun.LogService, InRun" Method="LogMessage">
        <parameter name="level" Layout="${level}" />
        <parameter name="date" Layout="${date:format=yyyy-MM-dd HH\:mm\:ss}" />
        <parameter name="logger" Layout="${logger}" />
        <parameter name="message" Layout="${message}" />
      </target>
    </targets>
    <rules>
      <logger name="*" writeTo="debug" />
      <logger name="InRun" writeTo="LogService" />
      <logger name="Project" writeTo="LogService" />
      <logger name="IO" writeTo="LogService" />
      <logger name="Tag" writeTo="LogService" />
      <logger name="Alarm&amp;Event" writeTo="LogService" />
      <logger name="ExternalAccess" writeTo="LogService" />
      <logger name="Resources" writeTo="LogService" />
      <logger name="Advanced" writeTo="LogService" />
      <logger name="API" writeTo="LogService" />
    </rules>
  </logging>

  <extensions>
    <addIns>
      <addIn name="OptionRunner" assembly="CE.NX.OPTD.dll" type="ControlEase.Inspec.Options.OptionRunner, CE.NX.OPTD" />
      <addIn name="ResourcesRunner" assembly="CE.NX.RSP.dll" type="ControlEase.Inspec.ResourcesPresentation.ResourcesRunner, CE.NX.RSP" />
      <addIn name="MessageRunner" assembly="CE.NX.AEP.dll" type="ControlEase.Inspec.MessagePresentation.MessageRunner, CE.NX.AEP" />
      <addIn name="TagRunner" assembly="CE.NX.TGP.dll" type="ControlEase.Inspec.TagPresentation.TagRunner, CE.NX.TGP" />
      <addIn name="IORunner" assembly="CE.NX.ICP.dll" type="ControlEase.Inspec.IOPresentation.IORunner, CE.NX.ICP" />
      <addIn name="EARunner" assembly="CE.NX.EAA.dll" type="ControlEase.Inspec.ExternalAccessAddIn.EARunner, CE.NX.EAA" />
      <addIn name="NetRunner" assembly="CE.NX.NTA.dll" type="ControlEase.Inspec.NetAddIn.NetRunner, CE.NX.NTA" />
      <addIn name="IONetRunner" assembly="CE.NX.ICN.dll" type="ControlEase.Inspec.IONet.IONetRunner, CE.NX.ICN" />
      <addIn name="TagNetRunner" assembly="CE.NX.TGN.dll" type="ControlEase.Inspec.TagNet.TagNetRunner, CE.NX.TGN" />
      <addIn name="MessageNetRunner" assembly="CE.NX.AEN.dll" type="ControlEase.Inspec.MessageNet.MessageNetRunner, CE.NX.AEN" />
      <addIn name="ScriptRunner" assembly="CE.NX.SPP.dll" type="ControlEase.Inspec.ScriptPresentation.ScriptRunner, CE.NX.SPP" />
    </addIns>
  </extensions>

  <Options>
    <Preserver elementType="ControlEase.Nexus.ComponentModel.FileBasedPreserverConfigurationElement" type="ControlEase.Nexus.ComponentModel.XmlOptionPreserver" />
  </Options>

  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>

  <runtime>
    <enforceFIPSPolicy enabled="false" />
    <AppContextSwitchOverrides value="Switch.UseLegacyAccessibilityFeatures=false;Switch.UseLegacyAccessibilityFeatures.2=false" />
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="MySql.Data" publicKeyToken="c5687fc88969c44d" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
    </assemblyBinding>

  </runtime>

  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0" />
  </startup>

  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_INXWebService" />
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://127.0.0.1:6000/T1Stocker" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_INXWebService" contract="ServiceReference1.INXWebService" name="BasicHttpBinding_INXWebService" />
    </client>
  </system.serviceModel>
</configuration>