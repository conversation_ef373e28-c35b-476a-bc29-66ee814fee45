﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;


 
namespace Proj.Entity
{
    #region TbStockIn and List
    [Serializable]
    [Description("库存入库明细表")]
    [LinqToDB.Mapping.Table("TB_STOCK_IN")]
    public partial class TbStockIn : GEntity<TbStockIn>, ITimestamp
    {
        #region Contructor(s)

        private TbStockIn()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CInstockId = RegisterProperty<String>(p => p.CInstockId);
        private static readonly PropertyInfo<String> pty_CFinancedate = RegisterProperty<String>(p => p.CFinancedate);
        private static readonly PropertyInfo<String> pty_CStockId = RegisterProperty<String>(p => p.CStockId);
        private static readonly PropertyInfo<String> pty_CDocType = RegisterProperty<String>(p => p.CDocType);
        private static readonly PropertyInfo<String> pty_CInstockdate = RegisterProperty<String>(p => p.CInstockdate);
        private static readonly PropertyInfo<String> pty_CJzdate = RegisterProperty<String>(p => p.CJzdate);
        private static readonly PropertyInfo<String> pty_CSupplierId = RegisterProperty<String>(p => p.CSupplierId);
        private static readonly PropertyInfo<String> pty_CSupplierDesc = RegisterProperty<String>(p => p.CSupplierDesc);
        private static readonly PropertyInfo<String> pty_CMaterialgroup = RegisterProperty<String>(p => p.CMaterialgroup);
        private static readonly PropertyInfo<String> pty_CMaterialgroupDes = RegisterProperty<String>(p => p.CMaterialgroupDes);
        private static readonly PropertyInfo<String> pty_CMaterialId = RegisterProperty<String>(p => p.CMaterialId);
        private static readonly PropertyInfo<String> pty_CMaterialDesc = RegisterProperty<String>(p => p.CMaterialDesc);
        private static readonly PropertyInfo<String> pty_CBatchId = RegisterProperty<String>(p => p.CBatchId);
        private static readonly PropertyInfo<Double> pty_NQty = RegisterProperty<Double>(p => p.NQty);
        private static readonly PropertyInfo<String> pty_CWeightunit = RegisterProperty<String>(p => p.CWeightunit);
        private static readonly PropertyInfo<String> pty_CStorageId = RegisterProperty<String>(p => p.CStorageId);
        private static readonly PropertyInfo<String> pty_CStorageDesc = RegisterProperty<String>(p => p.CStorageDesc);
        private static readonly PropertyInfo<String> pty_CLocationId = RegisterProperty<String>(p => p.CLocationId);
        private static readonly PropertyInfo<String> pty_CLocationDesc = RegisterProperty<String>(p => p.CLocationDesc);
        private static readonly PropertyInfo<String> pty_CFactoryId = RegisterProperty<String>(p => p.CFactoryId);
        private static readonly PropertyInfo<String> pty_CFactoryDesc = RegisterProperty<String>(p => p.CFactoryDesc);
        private static readonly PropertyInfo<String> pty_CStockmode = RegisterProperty<String>(p => p.CStockmode);
        private static readonly PropertyInfo<String> pty_CMainId = RegisterProperty<String>(p => p.CMainId);
        private static readonly PropertyInfo<String> pty_CEmpId = RegisterProperty<String>(p => p.CEmpId);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        private static readonly PropertyInfo<String> pty_CSw04 = RegisterProperty<String>(p => p.CSw04);
        private static readonly PropertyInfo<String> pty_CSw05 = RegisterProperty<String>(p => p.CSw05);
        #endregion

        /// <summary>
        /// 入库单号
        /// </summary>
        [Description("入库单号")]
        [LinqToDB.Mapping.Column("C_INSTOCKID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CInstockId
        {
            get { return GetProperty(pty_CInstockId); }
            set { SetProperty(pty_CInstockId, value); }
        }
        /// <summary>
        /// 账务年月
        /// </summary>
        [Description("账务年月")]
        [LinqToDB.Mapping.Column("C_FINANCEDATE")]
        public String CFinancedate
        {
            get { return GetProperty(pty_CFinancedate); }
            set { SetProperty(pty_CFinancedate, value); }
        }
        /// <summary>
        /// 库存编码
        /// </summary>
        [Description("库存编码")]
        [LinqToDB.Mapping.Column("C_STOCKID")]
        public String CStockId
        {
            get { return GetProperty(pty_CStockId); }
            set { SetProperty(pty_CStockId, value); }
        }
        /// <summary>
        /// 入库类型
        /// </summary>
        [Description("入库类型")]
        [LinqToDB.Mapping.Column("C_DOCTYPE")]
        public String CDocType
        {
            get { return GetProperty(pty_CDocType); }
            set { SetProperty(pty_CDocType, value); }
        }
        /// <summary>
        /// 入库日期
        /// </summary>
        [Description("入库日期")]
        [LinqToDB.Mapping.Column("C_INSTOCKDATE")]
        public String CInstockdate
        {
            get { return GetProperty(pty_CInstockdate); }
            set { SetProperty(pty_CInstockdate, value); }
        }
        /// <summary>
        /// 记账日期
        /// </summary>
        [Description("记账日期")]
        [LinqToDB.Mapping.Column("C_JZDATE")]
        public String CJzdate
        {
            get { return GetProperty(pty_CJzdate); }
            set { SetProperty(pty_CJzdate, value); }
        }
        /// <summary>
        /// 供应商编码
        /// </summary>
        [Description("供应商编码")]
        [LinqToDB.Mapping.Column("C_SUPPLIERID")]
        public String CSupplierId
        {
            get { return GetProperty(pty_CSupplierId); }
            set { SetProperty(pty_CSupplierId, value); }
        }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        [LinqToDB.Mapping.Column("C_SUPPLIERDESC")]
        public String CSupplierDesc
        {
            get { return GetProperty(pty_CSupplierDesc); }
            set { SetProperty(pty_CSupplierDesc, value); }
        }
        /// <summary>
        /// 物料组
        /// </summary>
        [Description("物料组")]
        [LinqToDB.Mapping.Column("C_MATERIALGROUP")]
        public String CMaterialgroup
        {
            get { return GetProperty(pty_CMaterialgroup); }
            set { SetProperty(pty_CMaterialgroup, value); }
        }
        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组描述")]
        [LinqToDB.Mapping.Column("C_MATERIALGROUPDES")]
        public String CMaterialgroupDes
        {
            get { return GetProperty(pty_CMaterialgroupDes); }
            set { SetProperty(pty_CMaterialgroupDes, value); }
        }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        [LinqToDB.Mapping.Column("C_MATERIALID")]
        public String CMaterialId
        {
            get { return GetProperty(pty_CMaterialId); }
            set { SetProperty(pty_CMaterialId, value); }
        }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        [LinqToDB.Mapping.Column("C_MATERIALDESC")]
        public String CMaterialDesc
        {
            get { return GetProperty(pty_CMaterialDesc); }
            set { SetProperty(pty_CMaterialDesc, value); }
        }
        /// <summary>
        /// 批次号
        /// </summary>
        [Description("批次号")]
        [LinqToDB.Mapping.Column("C_BATCHID")]
        public String CBatchId
        {
            get { return GetProperty(pty_CBatchId); }
            set { SetProperty(pty_CBatchId, value); }
        }
        /// <summary>
        /// 入库量
        /// </summary>
        [Description("入库量")]
        [LinqToDB.Mapping.Column("N_QTY")]
        public Double NQty
        {
            get { return GetProperty(pty_NQty); }
            set { SetProperty(pty_NQty, value); }
        }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        [LinqToDB.Mapping.Column("C_WEIGHTUNIT")]
        public String CWeightunit
        {
            get { return GetProperty(pty_CWeightunit); }
            set { SetProperty(pty_CWeightunit, value); }
        }
        /// <summary>
        /// 库房ID
        /// </summary>
        [Description("库房ID")]
        [LinqToDB.Mapping.Column("C_STORAGEID")]
        public String CStorageId
        {
            get { return GetProperty(pty_CStorageId); }
            set { SetProperty(pty_CStorageId, value); }
        }
        /// <summary>
        /// 库房描述
        /// </summary>
        [Description("库房描述")]
        [LinqToDB.Mapping.Column("C_STORAGEDESC")]
        public String CStorageDesc
        {
            get { return GetProperty(pty_CStorageDesc); }
            set { SetProperty(pty_CStorageDesc, value); }
        }
        /// <summary>
        /// 库位编码
        /// </summary>
        [Description("库位编码")]
        [LinqToDB.Mapping.Column("C_LOCATIONID")]
        public String CLocationId
        {
            get { return GetProperty(pty_CLocationId); }
            set { SetProperty(pty_CLocationId, value); }
        }
        /// <summary>
        /// 库位描述
        /// </summary>
        [Description("库位描述")]
        [LinqToDB.Mapping.Column("C_LOCATIONDESC")]
        public String CLocationDesc
        {
            get { return GetProperty(pty_CLocationDesc); }
            set { SetProperty(pty_CLocationDesc, value); }
        }
        /// <summary>
        /// 工厂编码
        /// </summary>
        [Description("工厂编码")]
        [LinqToDB.Mapping.Column("C_FACTORYID")]
        public String CFactoryId
        {
            get { return GetProperty(pty_CFactoryId); }
            set { SetProperty(pty_CFactoryId, value); }
        }
        /// <summary>
        /// 工厂描述
        /// </summary>
        [Description("工厂描述")]
        [LinqToDB.Mapping.Column("C_FACTORYDESC")]
        public String CFactoryDesc
        {
            get { return GetProperty(pty_CFactoryDesc); }
            set { SetProperty(pty_CFactoryDesc, value); }
        }
        /// <summary>
        /// 库管方式
        /// </summary>
        [Description("库管方式")]
        [LinqToDB.Mapping.Column("C_STOCKMODE")]
        public String CStockmode
        {
            get { return GetProperty(pty_CStockmode); }
            set { SetProperty(pty_CStockmode, value); }
        }
        /// <summary>
        /// 业务表主ID
        /// </summary>
        [Description("业务表主ID")]
        [LinqToDB.Mapping.Column("C_MAINID")]
        public String CMainId
        {
            get { return GetProperty(pty_CMainId); }
            set { SetProperty(pty_CMainId, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 操作人
        /// </summary>
        [Description("操作人")]
        [LinqToDB.Mapping.Column("C_EMPID")]
        public String CEmpId
        {
            get { return GetProperty(pty_CEmpId); }
            set { SetProperty(pty_CEmpId, value); }
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("C_SW01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("C_SW02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("C_SW03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
        /// <summary>
        /// 扩展4
        /// </summary>
        [Description("扩展4")]
        [LinqToDB.Mapping.Column("C_SW04")]
        public String CSw04
        {
            get { return GetProperty(pty_CSw04); }
            set { SetProperty(pty_CSw04, value); }
        }
        /// <summary>
        /// 扩展5
        /// </summary>
        [Description("扩展5")]
        [LinqToDB.Mapping.Column("C_SW05")]
        public String CSw05
        {
            get { return GetProperty(pty_CSw05); }
            set { SetProperty(pty_CSw05, value); }
        }

      
     
        
        #endregion 
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CInstockId, "入库单号是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CInstockId, 80, "入库单号不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFinancedate, 24, "账务年月不能超过24个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CStockId, 80, "库存编码不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDocType, 80, "入库类型不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CInstockdate, 76, "入库日期不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CJzdate, 76, "记账日期不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSupplierId, 80, "供应商编码不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSupplierDesc, 160, "供应商名称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialgroup, 18, "物料组不能超过18个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialgroupDes, 160, "物料组描述不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialId, 80, "物料编码不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialDesc, 200, "物料描述不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CBatchId, 80, "批次号不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWeightunit, 24, "单位不能超过24个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CStorageId, 80, "库房ID不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CStorageDesc, 160, "库房描述不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLocationId, 80, "库位编码不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLocationDesc, 160, "库位描述不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryId, 40, "工厂编码不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryDesc, 200, "工厂描述不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CStockmode, 40, "库管方式不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMainId, 80, "业务表主ID不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CEmpId, 40, "操作人不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw04, 40, "扩展4不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw05, 40, "扩展5不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CInstockId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbStockInList : GEntityList<TbStockInList, TbStockIn>
    {
        private TbStockInList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
