{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Proj.Service/1.0.0": {"dependencies": {"Microsoft.AspNetCore.OpenApi": "8.0.18", "Microsoft.AspNetCore.SignalR.Client": "8.0.18", "Proj.CacheData": "1.0.0", "Proj.Common": "1.0.0", "Proj.DataTypeDef": "1.0.0", "Proj.Log": "1.0.0", "Swashbuckle.AspNetCore": "6.8.1"}, "runtime": {"Proj.Service.dll": {}}}, "CommunityToolkit.HighPerformance/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.HighPerformance.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "log4net/2.0.17": {"dependencies": {"System.Configuration.ConfigurationManager": "4.5.0"}, "runtime": {"lib/netstandard2.0/log4net.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.18": {"dependencies": {"Microsoft.Extensions.Features": "8.0.18", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.AspNetCore.Http.Connections.Client/8.0.18": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Common": "8.0.18", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Http.Connections.Client.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.AspNetCore.Http.Connections.Common/8.0.18": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.18"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.AspNetCore.OpenApi/8.0.18": {"dependencies": {"Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.AspNetCore.SignalR.Client/8.0.18": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Client": "8.0.18", "Microsoft.AspNetCore.SignalR.Client.Core": "8.0.18"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Client.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.AspNetCore.SignalR.Client.Core/8.0.18": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.18", "Microsoft.AspNetCore.SignalR.Protocols.Json": "8.0.18", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Threading.Channels": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Client.Core.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.AspNetCore.SignalR.Common/8.0.18": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.18", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/8.0.18": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.18"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.Features/8.0.18": {"runtime": {"lib/net8.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3"}}, "Microsoft.Extensions.Logging/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1325.6609"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.NETCore.Platforms/2.0.0": {}, "Microsoft.OpenApi/1.6.14": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.14.0", "fileVersion": "1.6.14.0"}}}, "Microsoft.Win32.SystemEvents/9.0.7": {"runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "PooledAwait/1.0.49": {"runtime": {"lib/netcoreapp3.0/PooledAwait.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.48.55943"}}}, "Secs4Net/2.4.4": {"dependencies": {"CommunityToolkit.HighPerformance": "8.4.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "PooledAwait": "1.0.49", "System.IO.Pipelines": "8.0.0", "System.Threading.Channels": "8.0.0"}, "runtime": {"lib/net8.0/Secs4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/6.8.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.8.1", "Swashbuckle.AspNetCore.SwaggerGen": "6.8.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.8.1"}}, "Swashbuckle.AspNetCore.Swagger/6.8.1": {"dependencies": {"Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.8.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.8.1"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.8.1": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}}, "System.Configuration.ConfigurationManager/4.5.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.5.0", "System.Security.Permissions": "4.5.0"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Drawing.Common/9.0.7": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.7"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "lib/net8.0/System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}}}, "System.IO.Pipelines/8.0.0": {}, "System.Security.AccessControl/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Security.Principal.Windows": "4.5.0"}}, "System.Security.Cryptography.ProtectedData/4.5.0": {}, "System.Security.Permissions/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.5.0"}}, "System.Security.Principal.Windows/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0"}}, "System.Threading.Channels/8.0.0": {}, "Proj.API/1.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Proj.Log": "1.0.0", "Secs4Net": "2.4.4"}, "runtime": {"Proj.API.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Proj.CacheData/1.0.0": {"dependencies": {"Proj.API": "1.2.0", "Proj.DataTypeDef": "1.0.0"}, "runtime": {"Proj.CacheData.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Proj.Common/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "9.0.7"}, "runtime": {"Proj.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Proj.DataTypeDef/1.0.0": {"runtime": {"Proj.DataTypeDef.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Proj.Log/1.0.0": {"dependencies": {"log4net": "2.0.17"}, "runtime": {"Proj.Log.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Proj.Service/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommunityToolkit.HighPerformance/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-flxspiBs0G/0GMp7IK2J2ijV9bTG6hEwFc/z6ekHqB6nwRJ4Ry2yLdx+TkbCUYFCl4XhABkAwomeKbT6zM2Zlg==", "path": "communitytoolkit.highperformance/8.4.0", "hashPath": "communitytoolkit.highperformance.8.4.0.nupkg.sha512"}, "log4net/2.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-qnnDf/ubJzwm2i1xH7nRMjEDoD+ctse7nZDqb+p7L1PvZc6ykpMoEesWr1/9hFqlsbII2v9e8yyQHJhoDQh7ZA==", "path": "log4net/2.0.17", "hashPath": "log4net.2.0.17.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-iL8EEvmjrFI1kWgIUqVx4Jm9VNhmWMGrQFFft8wyn1xnFRFefLPVleQ7ZfNt8NTx9SY5gmIFN11uijEt9h+0Ww==", "path": "microsoft.aspnetcore.connections.abstractions/8.0.18", "hashPath": "microsoft.aspnetcore.connections.abstractions.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Client/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-JVK810ExsgdbTORlHmRmuz9NW4ubGiaO+RhHlrO5lzPcb2JMyOP2d9QQWEek6A+SBZMSlLZWctHAbuM95duDyg==", "path": "microsoft.aspnetcore.http.connections.client/8.0.18", "hashPath": "microsoft.aspnetcore.http.connections.client.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-t+LR3XjIt4S+qmF5xhQdlasZttIr563eRbZdkgMC5gO5OxCfKAfQKXyZPTVkhm8Koe5Wdqwh5nLF70POXRFfZA==", "path": "microsoft.aspnetcore.http.connections.common/8.0.18", "hashPath": "microsoft.aspnetcore.http.connections.common.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-iHqDbyK66dTL2M32nQ/kqYXGbQFj5bUr0eXJskJMsuPCF4CfJbJCuSnwWKq0m2TrMGuNV01RJLU61S2uvS04+g==", "path": "microsoft.aspnetcore.openapi/8.0.18", "hashPath": "microsoft.aspnetcore.openapi.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-3V2n27mGWy8R5k8BaSBzO3Q/KZQ5PuaHGFrKh7cUmGaZivASdVTNspc1PaDxwQw8kqIMF8+dTSkGuwqt60zX/Q==", "path": "microsoft.aspnetcore.signalr.client/8.0.18", "hashPath": "microsoft.aspnetcore.signalr.client.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client.Core/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-M3/zfB8/KZ5OOYQG8MYxCMp5G4y8UEUdSFBQ+LJ64aYHHT5w/puMSyYhwFH9oJ+ogyVvVHX5rs0/LgE+kMOhaw==", "path": "microsoft.aspnetcore.signalr.client.core/8.0.18", "hashPath": "microsoft.aspnetcore.signalr.client.core.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-RzALh+AB58TXcxS1zYtzA5y21wHQRY9ba7kEn/NJZrbfjJe5Mwfc5vVdUFNNPkfFtprvpUHq8fVTE7EPniBgjg==", "path": "microsoft.aspnetcore.signalr.common/8.0.18", "hashPath": "microsoft.aspnetcore.signalr.common.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-51Faqcla4VPGCBwrWtw7smnTaFYgLZd8agfOtWtPaCGY2KsQCkH8l2YhPlJ2mgVXVuMC+OGNG/ktnuoFjdMbQA==", "path": "microsoft.aspnetcore.signalr.protocols.json/8.0.18", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.8.0.18.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Features/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-TflSnyz8Cc16IWNif+/sbZ5GsCcoD2od8oe6tajW75DbUtYGOsDv3tNRBV+8nRsOz/ETBuMPFAgXbllNdHFQxQ==", "path": "microsoft.extensions.features/8.0.18", "hashPath": "microsoft.extensions.features.8.0.18.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "path": "microsoft.extensions.logging/8.0.1", "hashPath": "microsoft.extensions.logging.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-dL0QGToTxggRLMYY4ZYX5AMwBb+byQBd/5dMiZE07Nv73o6I5Are3C7eQTh7K2+A4ct0PVISSr7TZANbiNb2yQ==", "path": "microsoft.extensions.logging.abstractions/8.0.3", "hashPath": "microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VdLJOCXhZaEMY7Hm2GKiULmn7IEPFE4XC5LPSfBVCUIA8YLZVh846gtfBJalsPQF2PlzdD7ecX7DZEulJ402ZQ==", "path": "microsoft.netcore.platforms/2.0.0", "hashPath": "microsoft.netcore.platforms.2.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.14": {"type": "package", "serviceable": true, "sha512": "sha512-tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw==", "path": "microsoft.openapi/1.6.14", "hashPath": "microsoft.openapi.1.6.14.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-lFGY2aGgmMREPJEfOmZcA6v0CLjWVpcfNHRgqYMoSQhy80+GxhYqdW5xe+DCLrVqE1M7/0RpOkIo49/KH/cd/A==", "path": "microsoft.win32.systemevents/9.0.7", "hashPath": "microsoft.win32.systemevents.9.0.7.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "PooledAwait/1.0.49": {"type": "package", "serviceable": true, "sha512": "sha512-stq3Af/4ptnLFbtrniDcZCeSl0JtZoiTnPHm1pkJ6sQM07Jj1zG9NN5rOP5dVh2VNSblloKTxBVAnBJ8MktlFw==", "path": "pooledawait/1.0.49", "hashPath": "pooledawait.1.0.49.nupkg.sha512"}, "Secs4Net/2.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-8/5WWoPWyDHHs3RYLrcbPjBk45bp/o8WqD+I6I5mslsjQfAAiJ3SDpA2AUI5769gkc/KeDA7AuYYP92nQT58Hw==", "path": "secs4net/2.4.4", "hashPath": "secs4net.2.4.4.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-JN6ccH37QKtNOwBrvSxc+jBYIB+cw6RlZie2IKoJhjjf6HzBH+2kPJCpxmJ5EHIqmxvq6aQG+0A8XklGx9rAxA==", "path": "swashbuckle.aspnetcore/6.8.1", "hashPath": "swashbuckle.aspnetcore.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-eOkdM4bsWBU5Ty3kWbyq5O9L+05kZT0vOdGh4a92vIb/LLQGQTPLRHXuJdnUBNIPNC8XfKWfSbtRfqzI6nnbqw==", "path": "swashbuckle.aspnetcore.swagger/6.8.1", "hashPath": "swashbuckle.aspnetcore.swagger.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-TjBPxsN0HeJzxEXZYeDXBNNMSyhg+TYXtkbwX+Cn8GH/y5ZeoB/chw0p71kRo5tR2sNshbKwL24T6f9pTF9PHg==", "path": "swashbuckle.aspnetcore.swaggergen/6.8.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-lpEszYJ7vZaTTE5Dp8MrsbSHrgDfjhDMjzW1qOA1Xs1Dnj3ZRBJAcPZUTsa5Bva+nLaw91JJ8OI8FkSg8hhIyA==", "path": "swashbuckle.aspnetcore.swaggerui/6.8.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.8.1.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UIFvaFfuKhLr9u5tWMxmVoDPkFeD+Qv8gUuap4aZgVGYSYMdERck4OhLN/2gulAc0nYTEigWXSJNNWshrmxnng==", "path": "system.configuration.configurationmanager/4.5.0", "hashPath": "system.configuration.configurationmanager.4.5.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Drawing.Common/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-1k/Pk7hcM3vP2tfIRRS2ECCCN7ya+hvocsM1JMc4ZDCU6qw7yOuUmqmCDfgXZ4Q4FS6jass2EAai5ByKodDi0g==", "path": "system.drawing.common/9.0.7", "hashPath": "system.drawing.common.9.0.7.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-vW8Eoq0TMyz5vAG/6ce483x/CP83fgm4SJe5P8Tb1tZaobcvPrbMEL7rhH1DRdrYbbb6F0vq3OlzmK0Pkwks5A==", "path": "system.security.accesscontrol/4.5.0", "hashPath": "system.security.accesscontrol.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "path": "system.security.cryptography.protecteddata/4.5.0", "hashPath": "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512"}, "System.Security.Permissions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "path": "system.security.permissions/4.5.0", "hashPath": "system.security.permissions.4.5.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-U77HfRXlZlOeIXd//Yoj6Jnk8AXlbeisf1oq1os+hxOGVnuG+lGSfGqTwTZBoORFF6j/0q7HXIl8cqwQ9aUGqQ==", "path": "system.security.principal.windows/4.5.0", "hashPath": "system.security.principal.windows.4.5.0.nupkg.sha512"}, "System.Threading.Channels/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CMaFr7v+57RW7uZfZkPExsPB6ljwzhjACWW1gfU35Y56rk72B/Wu+sTqxVmGSk4SFUlPc3cjeKND0zktziyjBA==", "path": "system.threading.channels/8.0.0", "hashPath": "system.threading.channels.8.0.0.nupkg.sha512"}, "Proj.API/1.2.0": {"type": "project", "serviceable": false, "sha512": ""}, "Proj.CacheData/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Proj.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Proj.DataTypeDef/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Proj.Log/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}