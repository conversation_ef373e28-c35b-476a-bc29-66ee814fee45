﻿using Proj.Business.DeviceGather;
using Proj.Entity;
using SS.Base.Configuration;
using SS.BLL.Base;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using static Proj.UI.FileManager.FrmTfInfoEdit;

namespace Proj.Business.TimedTask
{
    public class LogBacker
    {
        private static String FtpAddress = GEnvironment.FtpAddress;      //从系统参数表读取Ftp地址

        public void ZipBackDeteleFile()
        {
            try
            {
                string path = AppDomain.CurrentDomain.BaseDirectory + "..\\..\\..\\STK_Log\\Server_Log";
                string strBackFolderPath = AppDomain.CurrentDomain.BaseDirectory + "..\\..\\..\\STK_Log\\LogBackup\\";
                if (Directory.Exists(strBackFolderPath) == false)
                {
                    Directory.CreateDirectory(strBackFolderPath);
                }

                /// 遍历目录
                string[] childFolderNames = Directory.GetDirectories(path + "/");
                foreach (string strLogFolderPath in childFolderNames)
                {
                    string strLogFolderName = strLogFolderPath.Substring(strLogFolderPath.LastIndexOf("/") + 1);
                    string strZipFilePath = strBackFolderPath + strLogFolderName + ".zip";
                    DateTime dt;
                    if (DateTime.TryParse(strLogFolderName, out dt))
                    {
                        TimeSpan ts = DateTime.Now.Subtract(dt);
                        if (ts.Days > 3) // 判断目录文件名与今天的差值，如果大于3天，压缩。否则不动。
                        {
                            ZipHelper.CreateZip(strLogFolderPath, strZipFilePath); // 压缩，需要判断输入输出是否存在。
                        }
                    }

                    // 删除已经压缩的文件夹，需要判断压缩文件是否生成，压缩文件大小是否为零
                    if (File.Exists(strZipFilePath))
                    {
                        FileInfo fi = new FileInfo(strZipFilePath);
                        if (fi.Length > 10)
                        {
                            Directory.Delete(strLogFolderPath, true);
                        }
                    }
                }
           

                //删除超过时间的压缩文件
                //遍历目录
                TpSettings DbSaveSetting = TpSettings.GetByLambda(x => x.Name.Equals("ZipSaveDays"));
                if (DbSaveSetting == null)
                {
                    Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "CleanDB Function", "3", "Execute Failed");
                    return;
                }

                string strLogBackupFolderPath = AppDomain.CurrentDomain.BaseDirectory + "..\\..\\..\\STK_Log\\LogBackup\\";
                if (Directory.Exists(strLogBackupFolderPath) == true)
                {
                    string[] zipFolderNames = Directory.GetFiles(strLogBackupFolderPath);
                    foreach (string tmp in zipFolderNames)
                    {
                        FileInfo fi = new FileInfo(tmp);
                        TimeSpan ts = DateTime.Now.Subtract(fi.CreationTime);
                        if (ts.Days > Convert.ToInt32(DbSaveSetting.Value))
                        {
                            File.Delete(tmp);
                        }
                    }
                }
            
                //清理数据库
                string[] args = { "", "" };
                CleanDB(args);
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("WriteLog.BackFile Error: " + ex.Message + ", Stack: " + ex.StackTrace);
                Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "BackFile Function", "3", "Execute Failed");
            }
        }
        public OprationResult BackFile(string[] args)
        {
            OprationResult result = new OprationResult { strCode = "0" };
            try
            {
                DateTime lastDate = DateTime.Now.AddDays(-1);
                string strLogFolderName = lastDate.ToString("yyyy-MM-dd");
                string strLogFolderPath = AppDomain.CurrentDomain.BaseDirectory + "..\\Log\\" + strLogFolderName;
                if (Directory.Exists(strLogFolderPath) == false)
                {
                    //如果有更早时间的日志尚未压缩和上传，则需手动压缩和上传
                    Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "BackFile Function", "3", "There is no log file need to backup.");
                    return result;
                }
                string strBackFolderPath = AppDomain.CurrentDomain.BaseDirectory + "..\\LogBackup\\";
                if (Directory.Exists(strBackFolderPath) == false)
                {
                    Directory.CreateDirectory(strBackFolderPath);
                }

                //压缩前一天的日志文件
                string strZipFilePath = strBackFolderPath + strLogFolderName + ".zip";
                ZipHelper.CreateZip(strLogFolderPath, strZipFilePath);
                if(File.Exists(strZipFilePath))
                {
                    //上传到FTP指定文件夹目录
                    TpSettings FtpUpload = TpSettings.GetByLambda(x => x.Name.Equals("FtpUpload"));
                    if(FtpUpload.Value == "ON")
                    {
                        DateTime start = DateTime.Now;
                        var cfg = GetFtpConfig(FtpAddress);
                        var ftp = new SS.Base.Network.FtpClient(cfg.host, cfg.port, "/", cfg.username, cfg.password);
                        ftp.ChangeDir("/logs/");
                        DateTime end1 = DateTime.Now;
                        TimeSpan time1 = end1 - start;
                        ftp.Upload(strZipFilePath);
                        Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "BackFile Function", "3", "Ftp Upload Success");
                        DateTime end2 = DateTime.Now;
                        TimeSpan time2 = end2 - end1;
                    }


                    Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "BackFile Function", "3", "Execute Success");
                }
                else
                {
                    Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "BackFile Function", "3", "Execute Failed");
                }
            }
            catch (Exception ex)
            {
                result.strCode = "1";
                result.strMessage = ex.Message + Environment.NewLine;
                //填写错误日志
                //SS.Base.Log.Default.Error("更新数据字典错误消息：" + result.strMessage + "请手动处理查询原因!");
                // by chengbao
                Log.Logger.Instance.ExceptionLog("WriteLog.BackFile Error: " + ex.Message + ", Stack: " + ex.StackTrace);
                Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "BackFile Function", "3", "Execute Failed");
            }
            finally
            {
                string[] Newargs = new string[5];
                Newargs[0] = args[0];
                Newargs[1] = "BackFile";
                if (args.Length == 2)
                {
                    Newargs[2] = "1";
                }
                else
                {
                    Newargs[2] = args[2];
                }
                Newargs[3] = "1";
                if (result.strCode == "0")
                {
                    result.strMessage = "Execute Success";
                    Newargs[3] = "0";
                }
                Newargs[4] = result.strMessage;
                WriteLog.WriteJobExeLog(Newargs);

            }
            return result;
        }

//        public OprationResult BackDB(string[] args)
//        {
//            OprationResult result = new OprationResult { strCode = "0" };
//            try
//            {
//                //多个日志数据表需要分别进行备份


//                string sql = @"INSERT into tnt_logrecord 
//(c_pk, c_LogTime, c_LogType, c_LogModel, c_LogId, c_LogLevel, c_LogText, c_UserId, c_TimeStamp, c_Sw01, c_Sw02, c_Sw03)
//values ('{0}','{1}','{2}','{3}',{4},'{5}','{6}','{7}','{8}','{9}','{10}','{11}')";

//                TntLogbackconfig config = TntLogbackconfig.GetByLambda(x => x.CSaveType == "0" && x.CValId == "0");
//                if (config != null)
//                {
//                    SS.Base.DBHelper helper = SS.Base.DBHelper.Instance(config.CConnectstr, config.CProvider);
//                    DateTime date = DateTime.Now.AddDays(-config.CSavedays).Date;
//                    TntLogrecordList list = TntLogrecordList.GetByLambda(x => x.CTimestamp < date);

//                    using (var trans = new Csla.Transaction.TransactionScope())
//                    {
//                        foreach(var item in list)
//                        {
//                            string strSql = string.Format(sql,item.CPk,item.CLogtime,item.CLogType,item.CLogmodel,item.CLogId,item.CLoglevel,item.CLogtext,item.CUserId,item.CTimestamp,item.CSw01,item.CSw02,item.CSw03);
//                            helper.ExecuteNonQuery(strSql);
//                        }
//                        list.Clear();
//                        list.Save();
//                        trans.Complete();
//                        Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "BackDB Function", "3", "执行任务成功");
//                    }
//                }
//            }
//            catch (Exception ex)
//            {
//                result.strCode = "1";
//                result.strMessage = ex.Message + Environment.NewLine;
//                //填写错误日志
//                //SS.Base.Log.Default.Error("更新数据字典错误消息：" + result.strMessage + "请手动处理查询原因!");
//                // by chengbao
//                Log.Logger.Instance.ExceptionLog("LogBacker.Execute Error: " + ex.Message + ", Stack: " + ex.StackTrace);
//                Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "BackDB Function", "3", "执行任务时出错");
//            }
//            finally
//            {
//                string[] Newargs = new string[5];
//                Newargs[0] = args[0];
//                Newargs[1] = "BackFile";
//                if (args.Length == 2)
//                {
//                    Newargs[2] = "1";
//                }
//                else
//                {
//                    Newargs[2] = args[2];
//                }
//                Newargs[3] = "1";
//                if (result.strCode == "0")
//                {
//                    result.strMessage = "执行完成！";
//                    Newargs[3] = "0";
//                }
//                Newargs[4] = result.strMessage;
//                WriteLog.WriteJobExeLog(Newargs);

//            }
//            return result;
//        }

        private static FtpConfig GetFtpConfig(string ftpAddress)
        {
            var cfg = new FtpConfig();
            string content = @"ftp://(.*):(.*)@(.*):(.*)";  //正则表达式
            Regex r = new Regex(content);
            Match m = r.Match(ftpAddress);
            cfg.username = m.Groups[1].Value;
            cfg.password = m.Groups[2].Value;
            cfg.host = m.Groups[3].Value;
            cfg.port = m.Groups[4].Value;
            return cfg;
        }

        /// <summary>
        /// 定时清理数据库中的数据
        /// </summary>
        public OprationResult CleanDB(string[] args)
        {
            OprationResult result = new OprationResult { strCode = "0" };
            try
            {
                TpSettings DbSaveSetting = TpSettings.GetByLambda(x => x.Name.Equals("DbSaveDays"));
                if(DbSaveSetting == null)
                {
                    Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "CleanDB Function", "3", "Execute Failed");
                    return result;
                }
                int nDbSaveDays = Convert.ToInt32(DbSaveSetting.Value);
                DateTime cleanDate = DateTime.Now.AddDays(-nDbSaveDays).Date;
                using (var trans = new Csla.Transaction.TransactionScope())
                {
                    ThAlarmList.DeleteByCriteria(x => x.StartTime < cleanDate);
                    ThEqpList.DeleteByCriteria(x => x.Time < cleanDate);
                    ThHostList.DeleteByCriteria(x => x.Time < cleanDate);
                    ThOperationList.DeleteByCriteria(x => x.Time < cleanDate);
                    ThPioList.DeleteByCriteria(x => x.Time < cleanDate);
                    ThPlcList.DeleteByCriteria(x => x.Time < cleanDate);
                    ThScList.DeleteByCriteria(x => x.Time < cleanDate);
                    ThTransferList.DeleteByCriteria(x => x.CmdTime < cleanDate);
                    trans.Complete();
                    Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "CleanDB Function", "3", "Execute Success");
                }
            }
            catch (Exception ex)
            {
                result.strCode = "1";
                result.strMessage = ex.Message + Environment.NewLine;
                Log.Logger.Instance.ExceptionLog("CleanDB Error: " + ex.Message + ", Stack: " + ex.StackTrace);
                Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "CleanDB Function", "3", "Execute Failed");
            }

            return result;
        }

        /// <summary>
        /// 定时清理日志文件
        /// </summary>
        public OprationResult CleanFiles(string[] args)
        {
            OprationResult result = new OprationResult { strCode = "0" };
            try
            {
                TpSettings FileSaveSetting = TpSettings.GetByLambda(x => x.Name.Equals("FileSaveDays"));
                TpSettings ZipSaveSetting = TpSettings.GetByLambda(x => x.Name.Equals("ZipSaveDays"));
                if (FileSaveSetting == null || ZipSaveSetting == null)
                {
                    Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "CleanFiles Function", "3", "Execute Failed");
                    return result;
                }
                int nFileSaveDays = Convert.ToInt32(FileSaveSetting.Value);
                int nZipSaveDays = Convert.ToInt32(ZipSaveSetting.Value);
                DateTime fileCleanDate = DateTime.Now.AddDays(-nFileSaveDays);
                DateTime zipCleanDate = DateTime.Now.AddDays(-nZipSaveDays);

                //Clean Log Folder
                string strLogFolderPath = AppDomain.CurrentDomain.BaseDirectory + "..\\Log\\";
                string[] folderNames = Directory.GetDirectories(strLogFolderPath);
                foreach(string strFolderName in folderNames)
                {
                    string strDate = strFolderName.Replace(strLogFolderPath, "");
                    int nYear = Convert.ToInt32(strDate.Substring(0, 4));
                    int nMon = Convert.ToInt32(strDate.Substring(5, 2));
                    int nDay = Convert.ToInt32(strDate.Substring(8, 2));
                    DateTime folderDate = new DateTime(nYear, nMon, nDay);
                    if (folderDate < fileCleanDate)
                    {
                        Directory.Delete(strFolderName, true);
                    }
                }

                //Clean Log Zip
                string strLogBackupFolderPath = AppDomain.CurrentDomain.BaseDirectory + "..\\LogBackup\\";
                string[] zipNames = Directory.GetFiles(strLogBackupFolderPath);
                foreach (string strZipName in zipNames)
                {
                    string strDate = strZipName.Replace(strLogBackupFolderPath, "").Replace(".zip", "");
                    int nYear = Convert.ToInt32(strDate.Substring(0, 4));
                    int nMon = Convert.ToInt32(strDate.Substring(5, 2));
                    int nDay = Convert.ToInt32(strDate.Substring(8, 2));
                    DateTime zipDate = new DateTime(nYear, nMon, nDay);
                    if (zipDate < zipCleanDate)
                    {
                        File.Delete(strZipName);
                    }
                }

            }
            catch (Exception ex)
            {
                result.strCode = "1";
                result.strMessage = ex.Message + Environment.NewLine;
                Log.Logger.Instance.ExceptionLog("CleanDB Error: " + ex.Message + ", Stack: " + ex.StackTrace);
                Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.TaskExcuteLog, 0, "CleanDB Function", "3", "Execute Failed");
            }
            finally
            {
                string[] Newargs = new string[5];
                Newargs[0] = args[0];
                Newargs[1] = "BackFile";
                if (args.Length == 2)
                {
                    Newargs[2] = "1";
                }
                else
                {
                    Newargs[2] = args[2];
                }
                Newargs[3] = "1";
                if (result.strCode == "0")
                {
                    result.strMessage = "Execute Success";
                    Newargs[3] = "0";
                }
                Newargs[4] = result.strMessage;
                WriteLog.WriteJobExeLog(Newargs);
            }

            return result;
        }
    }
}
