﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <OutputType>Library</OutputType>

    <!-- 程序集版本信息 -->
    <AssemblyVersion>1.2.0</AssemblyVersion>
    <FileVersion>1.2.0</FileVersion>
    <Version>1.2.0</Version>
    <AssemblyTitle>Proj.API - SECS/GEM Equipment Interface</AssemblyTitle>
    <AssemblyDescription>SECS/GEM communication interface library for industrial equipment</AssemblyDescription>
    <AssemblyCompany>Industrial Control Systems</AssemblyCompany>
    <AssemblyProduct>ICStocker Framework</AssemblyProduct>
    <Copyright>Copyright © 2024 Industrial Control Systems</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="GemEquipmentExample.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.0" />
    <PackageReference Include="Secs4Net" Version="2.4.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Proj.Log\Proj.Log.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="config\event_def.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="config\secs-gem-config.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="config\test-data-config.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
