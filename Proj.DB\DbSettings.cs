﻿using System;
using System.Collections.Generic;
using Proj.Log;
using Proj.Entity;

namespace Proj.DB
{
    public class DbSettings
    {
        private static DbSettings m_Instanse;
        private static readonly object mSyncObject = new object();
        private DbSettings() { }
        public static DbSettings Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new DbSettings();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        //public bool AddGroupSettings(string groupName, Dictionary<string,string> dicSettings)
        //{
        //    try
        //    {
        //        foreach (KeyValuePair<string, string> setting in dicSettings)
        //        {
        //            TpSettings tpSetting = TpSettings.New();
        //            tpSetting.Name = setting.Key;
        //            tpSetting.Value = setting.Value;
        //            tpSetting.Group = groupName;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志
        //        Logger.Instance.ExceptionLog("DbSettings.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //        return false;
        //    }

        //    return true;
        //}

        //public bool DeleteGroupSettings(string groupName)
        //{
        //    try
        //    {
        //        TpSettingsList.DeleteByCriteria(x => x.Group == groupName); 
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志
        //        Logger.Instance.ExceptionLog("DbSettings.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //        return false;
        //    }
        //    return true;
        //}

        public Dictionary<string, string> GetGroupSettings(string groupName)
        {
            Dictionary<string, string> dicSettings = new Dictionary<string, string>();
            try
            {
                TpSettingsList tpSettingsList = TpSettingsList.GetByLambda(x => x.Group == groupName);
                foreach (TpSettings tpSetting in tpSettingsList)
                {
                    dicSettings.Add(tpSetting.Name, tpSetting.Value);
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Logger.Instance.ExceptionLog("DbSettings.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return dicSettings;
        }
        
    }
}
