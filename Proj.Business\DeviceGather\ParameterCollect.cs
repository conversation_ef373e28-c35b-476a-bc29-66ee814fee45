﻿using Proj.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Proj.Business.DeviceGather
{
    public class ParameterCollect : SS.CslaBase.ServiceV2<ParameterCollect>
    {
        /// <summary>
        /// 废弃不用了
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        //public virtual OprationResult CollectData(object list)
        //{
        //    OprationResult result = new OprationResult { strCode = "0" };
        //    try
        //    {
        //        using (var trans = new Csla.Transaction.TransactionScope())
        //        {
        //            dynamic dyn = list;
        //            TntGathercollectList gatherlist = TntGathercollectList.NewList();
        //            foreach (var obj in dyn)
        //            {
        //                string name = obj.GetType().GetProperty("Name").GetValue(obj, null);
        //                string value = obj.GetType().GetProperty("Value").GetValue(obj, null).ToString();

        //                TntParagather par = TntParagather.GetByLambda(x => x.CParameterCode == name);
        //                TntGathercollect tnt = gatherlist.AddNew();
        //                Random random = new Random();
        //                tnt.CCollectId = DateTime.Now.ToString("yyyyMMddHHmmssfff") + random.Next(0,1000).ToString();
        //                tnt.CParagatherId = par.CParagatherId;
        //                tnt.CDeviceId = par.CDeviceId;
        //                tnt.CCollectvalue = value;
        //                tnt.CCollectdate = DateTime.Now;
        //                tnt.CMessageType = "0";
        //            }

        //            //事物提交
        //            gatherlist.Save();
        //            trans.Complete();
        //        }

        //    }
        //    catch (Exception ex)
        //    {
        //        result.strCode = "1";
        //        result.strMessage = ex.Message + Environment.NewLine;
        //    }
        //    finally
        //    {
        //        if (result.strCode == "0")
        //        {
        //            result.strMessage = "数据存储完成！";
        //        }
        //    }
        //    return result;
        //}

        public virtual OprationResult CollectData(object list)
        {
            OprationResult result = new OprationResult { strCode = "0" };
            string sql = @"INSERT into tnt_gathercollect 
(c_CollectId, c_ParaGatherId, c_DeviceId, c_CollectValue, c_MessageType, c_Message, c_CollectDate, c_TimeStamp, c_Sw01, c_Sw02, c_Sw03)
values ('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}')";
            try
            {
                using (var trans = new Csla.Transaction.TransactionScope())
                {
                    dynamic dyn = list;
                    foreach (var obj in dyn)
                    {
                        string name = obj.GetType().GetProperty("Name").GetValue(obj, null);
                        string value = obj.GetType().GetProperty("Value").GetValue(obj, null).ToString();
                        TntParagather par = TntParagather.GetByLambda(x => x.CParameterCode == name);
                        Random random = new Random();
                        string CCollectId = DateTime.Now.ToString("yyyyMMddHHmmssfff") + random.Next(0, 1000).ToString();
                        string strSql = string.Format(sql, CCollectId, par.CParagatherId, par.CDeviceId, value, "0","", DateTime.Now, DateTime.Now, "","","");
                        SS.Base.DBHelper helper = SS.Base.DBHelper.Instance();
                        helper.ExecuteNonQuery(strSql);
                    }
                    //事物提交
                    trans.Complete();
                }

            }
            catch (Exception ex)
            {
                result.strCode = "1";
                result.strMessage = ex.Message + Environment.NewLine;
            }
            finally
            {
                if (result.strCode == "0")
                {
                    result.strMessage = "数据存储完成！";
                }
            }
            return result;
        }
    }
}
