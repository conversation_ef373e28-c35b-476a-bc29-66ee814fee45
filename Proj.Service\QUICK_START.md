# Stocker Service (.NET 8.0) 快速开始指南

## 🚀 快速启动

### 1. 环境要求
- .NET 8.0 SDK
- Windows 10/11 或 Windows Server 2019+
- 端口 9900 可用

### 2. 启动服务

#### 方法一：使用启动脚本（推荐）
```powershell
# 测试模式启动
.\start-service.ps1 -Test

# 生产模式启动
.\start-service.ps1
```

#### 方法二：手动启动
```bash
# 开发模式
dotnet run --project Proj.Service

# 生产模式
dotnet publish -c Release -o ./publish
cd publish
dotnet Proj.Service.dll
```

### 3. 验证服务

#### 使用测试脚本
```powershell
.\test-service.ps1
```

#### 手动验证
- 浏览器访问: http://localhost:9900
- 健康检查: http://localhost:9900/health
- API文档: http://localhost:9900/swagger

## 📋 功能对照表

| 原WCF功能 | 新服务实现 | 访问方式 |
|-----------|------------|----------|
| 客户端注册 | SignalR Hub | `connection.InvokeAsync("Register")` |
| 发送消息 | Web API + SignalR | `POST /api/stocker/message` |
| 获取状态 | Web API | `GET /api/stocker/states` |
| 获取标签值 | Web API | `POST /api/stocker/tag` |
| 服务端推送 | SignalR | `ServerSendMessage` 事件 |

## 🔧 集成现有代码

### 1. 替换WCF服务引用

```csharp
// 原代码
WCFService.Instance.eventWCFClientSendMessage += HandleMessage;
WCFService.Instance.eventWCFClientGetTagValue += HandleTagValue;

// 新代码
WCFCompatibilityService.Instance.ClientSendMessage += HandleMessage;
WCFCompatibilityService.Instance.ClientGetTagValue += HandleTagValue;
```

### 2. 发送消息给客户端

```csharp
// 原代码
WCFService.Instance.ServerSendMessage(function, parameters);

// 新代码
await WCFCompatibilityService.Instance.ServerSendMessageAsync(function, parameters);
```

### 3. 初始化兼容性服务

```csharp
// 在应用程序启动时
var clientManager = serviceProvider.GetService<IClientManagerService>();
WCFCompatibilityService.Instance.Initialize(clientManager);
```

## 🌐 客户端连接示例

### HTTP API客户端
```csharp
var client = new HttpClient();
client.BaseAddress = new Uri("http://localhost:9900");

// 连接测试
var response = await client.GetAsync("/api/stocker/test");

// 发送消息
var request = new {
    Function = "ConnectTest",
    Parameters = new Dictionary<string, object>()
};
var json = JsonSerializer.Serialize(request);
var content = new StringContent(json, Encoding.UTF8, "application/json");
response = await client.PostAsync("/api/stocker/message", content);
```

### SignalR客户端
```csharp
var connection = new HubConnectionBuilder()
    .WithUrl("http://localhost:9900/stockerhub")
    .Build();

// 注册消息处理器
connection.On<string, Dictionary<string, object>>("ServerSendMessage", 
    (function, parameters) => {
        Console.WriteLine($"收到消息: {function}");
    });

// 连接并注册
await connection.StartAsync();
await connection.InvokeAsync("Register");

// 发送消息
var result = await connection.InvokeAsync<object>("ClientSendMessage", 
    "ConnectTest", new Dictionary<string, object>());
```

## ⚙️ 配置说明

### appsettings.json
```json
{
  "StockerService": {
    "ServerPort": 9900,
    "DefaultIP": "",
    "EnableHttps": true
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:9900"
      }
    }
  }
}
```

### wcf-config.xml
```xml
<?xml version="1.0" encoding="utf-8"?>
<WCFConfig>
  <Host ip="127.0.0.1" />
  <Host ip="*************" />
</WCFConfig>
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   netstat -an | findstr :9900
   ```

2. **服务启动失败**
   - 检查.NET 8.0是否安装: `dotnet --version`
   - 检查日志文件: `D:/STK_Log/Server_Log/`

3. **API调用失败**
   - 确认服务正在运行
   - 检查请求格式是否正确
   - 查看控制台输出

### 日志位置
- 应用程序日志: `D:/STK_Log/Server_Log/[日期]/App/`
- WCF日志: `D:/STK_Log/Server_Log/[日期]/Wcf/`
- 异常日志: `D:/STK_Log/Server_Log/[日期]/Exception/`

## 📚 更多信息

- 详细文档: [README.md](README.md)
- 移植总结: [MIGRATION_SUMMARY.md](MIGRATION_SUMMARY.md)
- 集成示例: [Examples/IntegrationExample.cs](Examples/IntegrationExample.cs)

## 🆘 获取帮助

如果遇到问题，请：
1. 查看日志文件
2. 运行测试脚本验证功能
3. 检查配置文件
4. 参考示例代码

---

**注意**: 这是从.NET Framework 4.0 WCF服务移植到.NET 8.0的现代化版本，保持了原有功能的同时提供了更好的性能和可维护性。
