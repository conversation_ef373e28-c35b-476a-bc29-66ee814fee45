using System.Net;
using System.Xml;
using Proj.Log;
using Proj.Service.Services;

namespace Proj.Service.Core
{
    /// <summary>
    /// 服务主机，用于启动和停止.NET 8.0服务，替代原WCF宿主
    /// </summary>
    public class ServiceHost
    {
        private readonly string _defaultIP = "";  // 不设置默认IP，则自动获取IP
        private readonly int _serverPort = 9900;
        private readonly List<string> _ipList = new();
        private readonly Logger _logger;
        private WebApplication? _app;
        private CancellationTokenSource? _cancellationTokenSource;

        public ServiceHost()
        {
            _logger = Logger.Instance;
            LoadServiceConfig();
        }

        /// <summary>
        /// 加载服务配置
        /// </summary>
        private void LoadServiceConfig()
        {
            try
            {
                var configPath = Path.Combine(Directory.GetCurrentDirectory(), "config", "wcf-config.xml");
                if (File.Exists(configPath))
                {
                    var xmlConfig = new XmlDocument();
                    xmlConfig.Load(configPath);
                    
                    var hostNodes = xmlConfig.SelectNodes("WCFConfig/Host");
                    if (hostNodes != null)
                    {
                        foreach (XmlNode node in hostNodes)
                        {
                            var ipAttribute = node.Attributes?["ip"];
                            if (ipAttribute != null)
                            {
                                var ip = ipAttribute.Value;
                                if (!_ipList.Contains(ip))
                                {
                                    _ipList.Add(ip);
                                }
                            }
                        }
                    }
                }
                else
                {
                    _logger.Warning($"Configuration file not found: {configPath}, using default settings");
                    _ipList.Add("127.0.0.1");
                }
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"LoadServiceConfig error: {ex.Message}, Stack: {ex.StackTrace}");
                _ipList.Add("127.0.0.1"); // 默认配置
            }
        }

        /// <summary>
        /// 启动服务
        /// </summary>
        public async Task StartServerAsync()
        {
            try
            {
                // 获取本机IP
                var ip = GetServerIP();
                var urls = new List<string>();

                // 添加HTTP和HTTPS URL
                urls.Add($"http://{ip}:{_serverPort}");
                urls.Add($"https://{ip}:{_serverPort + 1}");

                _logger.Info($"Starting Stocker Service on {string.Join(", ", urls)}");

                var builder = WebApplication.CreateBuilder();
                
                // 配置Kestrel服务器
                builder.WebHost.UseUrls(urls.ToArray());
                builder.WebHost.ConfigureKestrel(options =>
                {
                    options.Listen(IPAddress.Parse(ip), _serverPort);
                    options.Listen(IPAddress.Parse(ip), _serverPort + 1, listenOptions =>
                    {
                        listenOptions.UseHttps(); // HTTPS支持
                    });
                });

                // 配置服务
                ConfigureServices(builder.Services);

                _app = builder.Build();

                // 配置中间件
                ConfigureMiddleware(_app);

                _cancellationTokenSource = new CancellationTokenSource();

                // 启动服务
                await _app.RunAsync(_cancellationTokenSource.Token);

                _logger.Info($"Stocker Service started successfully on {string.Join(", ", urls)}");
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"StartServerAsync error: {ex.Message}, Stack: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        public async Task StopServerAsync()
        {
            try
            {
                if (_cancellationTokenSource != null && !_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    _cancellationTokenSource.Cancel();
                }

                if (_app != null)
                {
                    await _app.StopAsync();
                    await _app.DisposeAsync();
                }

                _logger.Info("Stocker Service stopped successfully");
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"StopServerAsync error: {ex.Message}, Stack: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 配置服务
        /// </summary>
        /// <param name="services">服务集合</param>
        private void ConfigureServices(IServiceCollection services)
        {
            // 这里可以添加额外的服务配置
            // 主要配置已在Program.cs中完成
        }

        /// <summary>
        /// 配置中间件
        /// </summary>
        /// <param name="app">应用程序</param>
        private void ConfigureMiddleware(WebApplication app)
        {
            // 这里可以添加额外的中间件配置
            // 主要配置已在Program.cs中完成
        }

        /// <summary>
        /// 获取服务器IP地址
        /// </summary>
        /// <returns>IP地址</returns>
        private string GetServerIP()
        {
            try
            {
                // 如果有默认IP，返回默认值
                if (!string.IsNullOrEmpty(_defaultIP))
                {
                    return _defaultIP;
                }

                // 如果配置中包含127.0.0.1，优先使用
                if (_ipList.Contains("127.0.0.1"))
                {
                    return "127.0.0.1";
                }

                // 获取本机IP地址
                var hostName = Dns.GetHostName();
                var hostEntry = Dns.GetHostEntry(hostName);
                var addresses = hostEntry.AddressList;

                foreach (var address in addresses)
                {
                    var ipString = address.ToString();
                    if (address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork
                        && _ipList.Contains(ipString))
                    {
                        return ipString;
                    }
                }

                // 如果没有找到匹配的IP，返回第一个IPv4地址或默认地址
                var firstIPv4 = addresses.FirstOrDefault(a => a.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork);
                return firstIPv4?.ToString() ?? "127.0.0.1";
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetServerIP error: {ex.Message}");
                return "127.0.0.1";
            }
        }

        /// <summary>
        /// 获取配置的IP列表
        /// </summary>
        /// <returns>IP列表</returns>
        public IReadOnlyList<string> GetConfiguredIPs()
        {
            return _ipList.AsReadOnly();
        }

        /// <summary>
        /// 获取服务端口
        /// </summary>
        /// <returns>端口号</returns>
        public int GetServerPort()
        {
            return _serverPort;
        }
    }
}
