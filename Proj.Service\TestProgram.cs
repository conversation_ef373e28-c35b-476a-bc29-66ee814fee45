using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Proj.Service
{
    /// <summary>
    /// 简化的测试程序，用于验证基本功能
    /// </summary>
    public class TestProgram
    {
        public static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("Starting Stocker Service Test...");

                var builder = WebApplication.CreateBuilder(args);

                // Add basic services
                builder.Services.AddControllers();
                builder.Services.AddSignalR();

                // Configure URLs
                builder.WebHost.UseUrls("http://localhost:9901");

                var app = builder.Build();

                // Configure the HTTP request pipeline
                app.UseRouting();
                app.MapControllers();

                // Add a simple test endpoint
                app.MapGet("/test", () => new { Status = "OK", Message = "Service is running", Time = DateTime.Now });

                Console.WriteLine("Service starting on http://localhost:9901");
                Console.WriteLine("Test endpoint: http://localhost:9901/test");

                app.Run();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting service: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
