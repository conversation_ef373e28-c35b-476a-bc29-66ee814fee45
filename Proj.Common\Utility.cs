﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Proj.Common
{
    public class Utility
    {
        public Utility() { }
        private static long manTransCmdIndex = 0;

        public static void Screenshot()
        {
            int iWidth = Screen.PrimaryScreen.Bounds.Width;
            int iHeight = Screen.PrimaryScreen.Bounds.Height;
            Image img = new Bitmap(iWidth, iHeight);
            Graphics gc = Graphics.FromImage(img);
            gc.CopyFromScreen(new Point(0, 0), new Point(0, 0), new Size(iWidth, iHeight));
            img.Save(@"D:\" + DateTime.Now.ToString("yyyyMMdd-HHmmssff") + ".jpg");
        }
        public static string GenManualCommandID()
        {
            if (manTransCmdIndex == 99999)
            {
                manTransCmdIndex = 0;
            }
            StringBuilder sb = new StringBuilder();
            sb.Append("THMTS100"); //STKC ID
            sb.Append(DateTime.Now.ToString("yyyyMMddHHmmss")); //时间
            sb.Append((++manTransCmdIndex).ToString("D5")); //5个数字
            sb.Append("MC"); //2个字符："HS"：Host(MES)，"MC"：界面， 其他：MCS

            return sb.ToString();
        }
        public static string GenControlCommandID(string cmdSource)
        {
            if (manTransCmdIndex == 99999)
            {
                manTransCmdIndex = 0;
            }
            StringBuilder sb = new StringBuilder();
            sb.Append("THMTS100"); //STKC ID
            sb.Append(DateTime.Now.ToString("yyyyMMddHHmmss")); //时间
            sb.Append((++manTransCmdIndex).ToString("D5")); //5个数字
            sb.Append(cmdSource);

            return sb.ToString();
        }
    
        public static void CatchScreen()
        {
            int iWidth = Screen.PrimaryScreen.Bounds.Width;
            int iHeight = Screen.PrimaryScreen.Bounds.Height;

            Image img = new Bitmap(iWidth, iHeight);

            Graphics gc = Graphics.FromImage(img);
            gc.CopyFromScreen(new Point(0, 0), new Point(0, 0), new Size(iWidth, iHeight));

            img.Save(@"./log/" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".jpg");
        }
    }


    
}
