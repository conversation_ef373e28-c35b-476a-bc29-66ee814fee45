﻿using System;
using System.Collections.Generic;

using Proj.Entity;
using Proj.DB;
using Proj.DataTypeDef;
using Proj.CacheData;
using Proj.DevComm;
using System.Linq;

namespace Proj.UnitMng
{
    public delegate void deleNoticeCraneAction(string strCraneName, string strAction, string strCarrierID, string strSource, string strDest);
    public delegate void deleAutoAddTransferCommand(Dictionary<string, object> dicParameter);
    public class CraneMng
    {
        private static CraneMng m_Instanse;
        private static readonly object mSyncObject = new object();
        private CraneMng() { }
        public static CraneMng Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new CraneMng();
                        }
                    }
                }
                return m_Instanse;
            }
        }
        private List<CraneObj> craneObjList = new List<CraneObj>();
        public event deleNoticeCraneAction NoticeCraneAction = null;
        public event deleAutoAddTransferCommand AutoAddTransferCommand = null;

        public bool Initialize()
        {
            bool bRes = false;
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    TpCraneList tpCraneList = DbCrane.Instance.GetDbCraneList();
                    foreach (TpCrane tpCrane in tpCraneList)
                    {
                        GlobalData.Instance.gbStockerCraneID = tpCrane.Name;

                        //SendCraneSpeed(GlobalData.Instance.gbStockerCraneID, tpCrane.SpeedEmpty, tpCrane.SpeedLoad);

                        CraneObj craneObj = new CraneObj(tpCrane.No, tpCrane.Name, tpCrane.IsIdr == 1, CraneOperationState.Normal);
                        craneObjList.Add(craneObj);
                        craneObj.Start();
                        SendCraneSpeed(GlobalData.Instance.gbStockerCraneID, tpCrane.SpeedEmpty, tpCrane.SpeedLoad);//先对craneObjList赋值，然后SendCraneSpeed，否则设置失败
                        bRes = true;
                    }
                    return bRes;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("CraneMng.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return bRes;
        }

        public bool UnInit()
        {
            foreach(CraneObj crane in craneObjList)
            {
                crane.Stop();
            }
            return true;
        }

        public string GetCraneName()
        {
            foreach (CraneObj craneObj in craneObjList)
            {
                return craneObj.strCraneName;
            }
            return "";
        }

        public string GetIdleCrane()
        {
            foreach (CraneObj craneObj in craneObjList)
            {
                if (craneObj.OperState == CraneOperationState.Normal 
                    //&& craneObj.ModeState == CraneModeState.Idle  //去除多余的判断，防止卡住
                    && !craneObj.IsHaveTask())
                {
                    return craneObj.strCraneName;
                }
            }

            return "";
        }
        public bool IsHaveCase()
        {
            return craneObjList[0].IsHaveCase();
        }

        public bool CraneHasAlarm()
        {
            if (craneObjList.Count > 0)
            {
                return craneObjList[0].CraneHasAlarm();
            }
            else
            {
                return false;
            }
        }

        public void Pause()
        {
            if (craneObjList.Count > 0)
            {
                craneObjList[0].Pause();
            }
        }

        public void Resume()
        {
            if (craneObjList.Count > 0)
            {
                craneObjList[0].Resume();
            }
        }

        public void setUiIsRetryFlag(uint ui)//0=初始化； 1=Isretry； 2=not retry
        {
            if (craneObjList.Count > 0)
            {
                craneObjList[0].setUiIsRetryFlag(ui);
            }
        }
        public void Abort()
        {
            if (craneObjList.Count > 0)
            {
                craneObjList[0].Abort();
            }
        }

        public void CleanPLCTask()
        {
            if (craneObjList.Count > 0)
            {
                craneObjList[0].CleanPLCTask();
            }
        }

        public void AddCommand(string strCraneID, EnhancedTransferCommand enhancedTransferCommand)
        {
            int iCraneCount = craneObjList.Count;
            for (int i = 0; i < iCraneCount; i++)
            {
                if (craneObjList[i].strCraneName == strCraneID)
                {
                    UpdateCraneCommandID(strCraneID, enhancedTransferCommand.strCommandID);
                    craneObjList[i].AddTask(enhancedTransferCommand);
                    break;
                }
            }
        }

        public void SendCraneActionToUI(string strCraneName, string strAction, string strCarrierID, string strSource, string strDest)
        {
            if (NoticeCraneAction != null)
            {
                NoticeCraneAction(strCraneName, strAction, strCarrierID, strSource, strDest);
            }
        }

        public void AddTransferCommand(Dictionary<string, object> dicParameter)
        {
            if (AutoAddTransferCommand != null)
            {
                AutoAddTransferCommand(dicParameter);
            }
        }

        public bool SendCraneSpeed(string strCraneID, int emptySpeed, int storageSpeed, int xSpeed = 100, int ySpeed = 100, int zSpeed = 100, int tSpeed = 100)
        {
            int iCraneCount = craneObjList.Count;
            for (int i = 0; i < iCraneCount; i++)
            {
                if (craneObjList[i].strCraneName == strCraneID)
                {
                    bool ret = craneObjList[i].SetCraneSpeed(emptySpeed, storageSpeed, xSpeed, ySpeed, zSpeed, tSpeed);
                    if (ret)
                    {
                        try
                        {
                        TpCrane tpCrane = TpCrane.GetById(TpCraneList.GetByLambda(x => x.Name == strCraneID).First().No);
                        tpCrane.SpeedEmpty = emptySpeed;
                        tpCrane.SpeedLoad = storageSpeed;
                        tpCrane.Save();
						return ret;
                        }
                        catch (Exception ex)
                        {
                            Log.Logger.Instance.ExceptionLog("CraneMng.cs:SendCraneSpeed " + ex.Message + ", Stack: " + ex.StackTrace);
                        }
                    }
                }
            }
            return false;
        }

        public void CraneOperationStateChg(string strCraneID, CraneOperationState state)
        {
            if (DbCrane.Instance.UpdateCraneOperationState(strCraneID, state))
            {
                int iCraneCount = craneObjList.Count;
                for (int i = 0; i < iCraneCount; i++)
                {
                    if (craneObjList[i].strCraneName == strCraneID)
                    {
                        craneObjList[i].OperState = state;
                    }
                }
            }
        }
        
        public void UpdateCraneCommandID(string strCraneID, string strCommandID)
        {
            DbCrane.Instance.UpdateCraneCommandID(strCraneID, strCommandID);            
        }

        public bool ResetCraneAlarm(string strCraneID)
        {
            bool bResult = false;
            int iCraneCount = craneObjList.Count;
            for (int i = 0; i < iCraneCount; i++)
            {
                if (craneObjList[i].strCraneName == strCraneID)
                {
                    bResult = craneObjList[i].ResetAlarm();
                    break;
                }
            }
            if(bResult)
            {
                return Alarm.AlarmController.Instance.ClearAlarmByUnit(strCraneID);
            }
            return false;
        }

        public bool ResetCraneAlarmWithEmptyOrDouble(string strCraneID)
        {
            bool bResult = false;
            int iCraneCount = craneObjList.Count;
            for (int i = 0; i < iCraneCount; i++)
            {
                if (craneObjList[i].strCraneName == strCraneID)
                {
                    bResult = craneObjList[i].ResetAlarm();
                    break;
                }
            }
          
            return bResult;
        }

        public bool ResetSTKAlarm()
        {
            StockerDev.Instance.STKAlarmClear();
            System.Threading.Thread.Sleep(200);
            StockerDev.Instance.ResetSTKAlarmClear();
            return Alarm.AlarmController.Instance.ClearAlarmByUnit(GlobalData.Instance.gbEqpName);
        }

        public bool AddCranePlaceCount()
        {
            try
            {
                TpStatistic tpST = TpStatistic.GetByLambda(x => x.Name == "CranePlaceCount");
                if (tpST != null)
                {
                    int nCount = Convert.ToInt32(tpST.Value);
                    tpST.Value = (nCount + 1).ToString();
                    tpST.Save();
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("CraneMng.cs:AddCranePlaceCount " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }
        }

        public bool ResetCranePlaceCount()
        {
            try
            {
                TpStatistic tpST = TpStatistic.GetByLambda(x => x.Name == "CranePlaceCount");
                if (tpST != null)
                {
                    tpST.Value = "0";
                    tpST.Save();
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("CraneMng.cs:ResetCranePlaceCount " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }
        }

        public int GetCranePlaceCount()
        {
            try
            {
                TpStatistic tpST = TpStatistic.GetByLambda(x => x.Name == "CranePlaceCount");
                if (tpST != null)
                {
                    return Convert.ToInt32(tpST.Value);
                }
                else
                {
                    return 0;
                }
            }
            catch (Exception ex)
            {
                Proj.Log.Logger.Instance.ExceptionLog("CraneMng.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return 0;
        }
    }
}
