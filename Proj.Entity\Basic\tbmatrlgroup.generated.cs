﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbMatrlGroup and List
    [Serializable]
    [Description("物料组表")]
    [LinqToDB.Mapping.Table("TB_MATRL_GROUP")]
    public partial class TbMatrlGroup : GEntity<TbMatrlGroup>, ITimestamp
    {
        #region Contructor(s)

        private TbMatrlGroup()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CMaterialgroupId = RegisterProperty<String>(p => p.CMaterialgroupId);
        private static readonly PropertyInfo<String> pty_CMaterialgroupName = RegisterProperty<String>(p => p.CMaterialgroupName);
        private static readonly PropertyInfo<String> pty_CMaterialgroupDes = RegisterProperty<String>(p => p.CMaterialgroupDes);
        private static readonly PropertyInfo<String> pty_CUplevelCode = RegisterProperty<String>(p => p.CUplevelCode);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        private static readonly PropertyInfo<String> pty_CToplevelCode = RegisterProperty<String>(p => p.CToplevelCode);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("C_PK")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 物料组代码
        /// </summary>
        [Description("物料组代码")]
        [LinqToDB.Mapping.Column("C_MATERIALGROUPID")]
        public String CMaterialgroupId
        {
            get { return GetProperty(pty_CMaterialgroupId); }
            set { SetProperty(pty_CMaterialgroupId, value); }
        }
        /// <summary>
        /// 物料组名称
        /// </summary>
        [Description("物料组名称")]
        [LinqToDB.Mapping.Column("C_MATERIALGROUPNAME")]
        public String CMaterialgroupName
        {
            get { return GetProperty(pty_CMaterialgroupName); }
            set { SetProperty(pty_CMaterialgroupName, value); }
        }
        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组描述")]
        [LinqToDB.Mapping.Column("C_MATERIALGROUPDES")]
        public String CMaterialgroupDes
        {
            get { return GetProperty(pty_CMaterialgroupDes); }
            set { SetProperty(pty_CMaterialgroupDes, value); }
        }
        /// <summary>
        /// 上层物料组代码
        /// </summary>
        [Description("上层物料组代码")]
        [LinqToDB.Mapping.Column("C_UPLEVELCODE")]
        public String CUplevelCode
        {
            get { return GetProperty(pty_CUplevelCode); }
            set { SetProperty(pty_CUplevelCode, value); }
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 顶层物料组代码
        /// </summary>
        [Description("顶层物料组代码")]
        [LinqToDB.Mapping.Column("C_TOPLEVELCODE")]
        public String CToplevelCode
        {
            get { return GetProperty(pty_CToplevelCode); }
            set { SetProperty(pty_CToplevelCode, value); }
        }
        #endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 40, "主键不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CMaterialgroupId, "物料组代码是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialgroupId, 40, "物料组代码不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialgroupName, 160, "物料组名称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialgroupDes, 500, "物料组描述不能超过500个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CUplevelCode, 40, "上层物料组代码不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CToplevelCode, 40, "顶层物料组代码不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbMatrlGroupList : GEntityList<TbMatrlGroupList, TbMatrlGroup>
    {
        private TbMatrlGroupList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
