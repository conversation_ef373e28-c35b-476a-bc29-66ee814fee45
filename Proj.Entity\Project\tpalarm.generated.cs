﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpAlarm and List
    [Serializable]
    [Description("TP_ALARM")]
    [LinqToDB.Mapping.Table("TP_ALARM")]
    public partial class TpAlarm : GEntity<TpAlarm>
    {
        #region Contructor(s)

        private TpAlarm()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<Int32> pty_Code = RegisterProperty<Int32>(p => p.Code);
        private static readonly PropertyInfo<String> pty_Comment = RegisterProperty<String>(p => p.Comment);
        private static readonly PropertyInfo<Int32> pty_Count = RegisterProperty<Int32>(p => p.Count);
        private static readonly PropertyInfo<DateTime?> pty_LastTime = RegisterProperty<DateTime?>(p => p.LastTime);
        private static readonly PropertyInfo<DateTime?> pty_StartTime = RegisterProperty<DateTime?>(p => p.StartTime);
        private static readonly PropertyInfo<String> pty_Unit = RegisterProperty<String>(p => p.Unit);
        private static readonly PropertyInfo<String> pty_Type = RegisterProperty<String>(p => p.Type);
        #endregion

        /// <summary>
        /// AlarmCode
        /// </summary>
        [Description("AlarmCode")]
        [LinqToDB.Mapping.Column("Code")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int32 Code
        {
            get { return GetProperty(pty_Code); }
            set { SetProperty(pty_Code, value); }
        }
        /// <summary>
        /// Comment
        /// </summary>
        [Description("Comment")]
        [LinqToDB.Mapping.Column("Comment")]
        public String Comment
        {
            get { return GetProperty(pty_Comment); }
            set { SetProperty(pty_Comment, value); }
        }
        /// <summary>
        /// Count
        /// </summary>
        [Description("Count")]
        [LinqToDB.Mapping.Column("Count")]
        public Int32 Count
        {
            get { return GetProperty(pty_Count); }
            set { SetProperty(pty_Count, value); }
        }
        /// <summary>
        /// LastTime
        /// </summary>
        [Description("LastTime")]
        [LinqToDB.Mapping.Column("Last_Time")]
        public DateTime? LastTime
        {
            get { return GetProperty(pty_LastTime); }
            set { SetProperty(pty_LastTime, value); }
        }
        /// <summary>
        /// StartTime
        /// </summary>
        [Description("StartTime")]
        [LinqToDB.Mapping.Column("Start_Time")]
        public DateTime? StartTime
        {
            get { return GetProperty(pty_StartTime); }
            set { SetProperty(pty_StartTime, value); }
        }
        /// <summary>
        /// AlarmUnit
        /// </summary>
        [Description("AlarmUnit")]
        [LinqToDB.Mapping.Column("Unit")]
        public String Unit
        {
            get { return GetProperty(pty_Unit); }
            set { SetProperty(pty_Unit, value); }
        }
        /// <summary>
        /// AlarmType
        /// </summary>
        [Description("AlarmType")]
        [LinqToDB.Mapping.Column("Type")]
        public String Type
        {
            get { return GetProperty(pty_Type); }
            set { SetProperty(pty_Type, value); }
        }
        #endregion


        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Code, "AlarmCode是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Comment, 255, "Comment不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Count, "Count是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Unit, "AlarmUnit是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Unit, 32, "AlarmUnit不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Type, 32, "AlarmType不能超过32个字符"));

            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Code.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class TpAlarmList : GEntityList<TpAlarmList, TpAlarm>
    {
        private TpAlarmList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
