﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <!-- 禁用 CSLA 分析器以兼容生成的代码 -->
  <PropertyGroup>
    <NoWarn>$(NoWarn);CSLA0005</NoWarn>
    <WarningsAsErrors />
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Proj.CacheData\Proj.CacheData.csproj" />
    <ProjectReference Include="..\Proj.DataTypeDef\Proj.DataTypeDef.csproj" />
    <ProjectReference Include="..\Proj.Entity\Proj.Entity.csproj" />
    <ProjectReference Include="..\Proj.Log\Proj.Log.csproj" />
    <ProjectReference Include="..\Proj.Service\Proj.Service.csproj" />
  </ItemGroup>

</Project>
