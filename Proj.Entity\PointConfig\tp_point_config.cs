﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Proj.Entity.PointConfig
{
    /// <summary>
    /// PLC点位加载
    /// </summary>
    public class tp_point_config
    {
        public tp_point_config() { }
        [SugarColumn(IsPrimaryKey = true)]
        /// <summary>
        /// 站点编号
        /// </summary>
        public string STATION_NO { get; set; }
        /// <summary>
        /// 点位名称
        /// </summary>
        public string POINT_NAME { get; set; }
        /// <summary>
        /// 点位地址
        /// </summary>
        public string POINT_ADDRESS { get; set; }
        /// <summary>
        /// 点位字段类型
        /// </summary>
        public string POINT_TYPE { get; set; }
        /// <summary>
        /// 点位字段长度
        /// </summary>
        public string POINT_LENGTH { get; set; }
        /// <summary>
        /// 点位描述
        /// </summary>
        public string POINT_DES { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EQUIPMENT_NAME { get; set; }
        /// <summary>
        /// 设备PLCIP地址
        /// </summary>
        public string EQUIPMENT_IP { get; set; }
        /// <summary>
        /// 设备端口
        /// </summary>
        public string EQUIPMENT_Port { get; set; }
        /// <summary>
        /// 设备型号
        /// </summary>
        public string EQUIPMENT_MODEL { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnable { get; set; }
    }
}
