using Microsoft.Extensions.Options;
using Secs4Net;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Proj.API
{
    /// <summary>
    /// GemEquipment使用示例
    /// </summary>
    public class GemEquipmentExample
    {
        private GemEquipmentImpl? _gemEquipment;

        public async Task RunExampleAsync()
        {
            try
            {
                // 1. 创建和配置GEM设备
                await InitializeGemEquipmentAsync();

                // 2. 加载配置数据
                await LoadConfigurationDataAsync();

                // 3. 启动GEM服务
                await StartGemServiceAsync();

                // 4. 处理消息
                await HandleMessagesAsync();

                // 5. 发送事件和报警示例
                await SendEventsAndAlarmsAsync();

                Console.WriteLine("GEM Equipment example completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GEM Equipment example: {ex.Message}");
            }
            finally
            {
                // 清理资源
                await CleanupAsync();
            }
        }

        private async Task InitializeGemEquipmentAsync()
        {
            Console.WriteLine("Initializing GEM Equipment...");

            _gemEquipment = new GemEquipmentImpl
            {
                MDLN = "STKC_Equipment",
                SoftRev = "V2.0.0"
            };

            // 配置连接选项
            _gemEquipment.Option =Options.Create( new SecsGemOptions
            {
                IsActive = true,
                IpAddress = "127.0.0.1",
                Port = 5000,
                DeviceId = 1,
                SocketReceiveBufferSize = 65536
            });

            // 订阅事件
            _gemEquipment.ConnectionChanged += OnConnectionChanged;
            _gemEquipment.EventPosted += OnEventPosted;
            _gemEquipment.AlarmPosted += OnAlarmPosted;
            _gemEquipment.AlarmCleared += OnAlarmCleared;

            await Task.CompletedTask;
        }

        private async Task LoadConfigurationDataAsync()
        {
            Console.WriteLine("Loading configuration data...");

            if (_gemEquipment == null) return;

            // 加载DV (Data Variables)
            var dvItems = new List<DVItem>
            {
                new() { Id = 1, Name = "Clock", Value = DateTime.Now.ToString("yyyyMMddHHmmss") },
                new() { Id = 2, Name = "EqpName", Value = "STKC" },
                new() { Id = 3, Name = "EqpState", Value = "Online" }
            };
            await _gemEquipment.LoadDVAsync(dvItems);

            // 加载SV (Status Variables)
            var svItems = new List<SVItem>
            {
                new() { Id = 1, Name = "ControlState", Value = 5 }, // OnlineRemote
                new() { Id = 2, Name = "PreviousControlState", Value = 4 }, // OnlineLocal
                new() { Id = 3, Name = "EventsEnabled", Value = true }
            };
            await _gemEquipment.LoadSVAsync(svItems);

            // 加载EC (Equipment Constants)
            var ecItems = new List<ECItem>
            {
                new() { Id = 1, Name = "EstablishTimeout", Value = 10, MinValue = 1, MaxValue = 120 },
                new() { Id = 2, Name = "MaxCarriers", Value = 100, MinValue = 1, MaxValue = 1000 },
                new() { Id = 3, Name = "ProcessTimeout", Value = 300, MinValue = 60, MaxValue = 3600 }
            };
            await _gemEquipment.LoadECAsync(ecItems);

            // 加载事件
            var eventItems = new List<EventItem>
            {
                new() { Id = 1, Name = "EquipmentOffline", IsEnabled = true },
                new() { Id = 2, Name = "ControlStateLocal", IsEnabled = true },
                new() { Id = 3, Name = "ControlStateRemote", IsEnabled = true },
                new() { Id = 100, Name = "CarrierArrived", IsEnabled = true },
                new() { Id = 101, Name = "CarrierDeparted", IsEnabled = true }
            };
            await _gemEquipment.LoadEventAsync(eventItems);

            // 加载报警
            var alarmItems = new List<AlarmItem>
            {
                new() { Id = 1, Name = "CommunicationFailure", Description = "Communication with host failed", IsEnabled = true },
                new() { Id = 2, Name = "CraneError", Description = "Crane operation error", IsEnabled = true },
                new() { Id = 3, Name = "CarrierJam", Description = "Carrier jammed in system", IsEnabled = true }
            };
            await _gemEquipment.LoadAlarmAsync(alarmItems);

            Console.WriteLine("Configuration data loaded successfully.");
        }

        private async Task StartGemServiceAsync()
        {
            Console.WriteLine("Starting GEM service...");

            if (_gemEquipment == null) return;

            // 连接到主机
            _gemEquipment.ConnectAsync();

            // 启动GEM服务
            await _gemEquipment.StartGEMAsync();

            // 等待连接建立
            await Task.Delay(2000);

            Console.WriteLine("GEM service started.");
        }

        private async Task HandleMessagesAsync()
        {
            Console.WriteLine("Starting message handling...");

            if (_gemEquipment == null) return;

            // 创建取消令牌，用于停止消息处理
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

            try
            {
                // 处理主消息
                await foreach (var messageWrapper in _gemEquipment.GetPrimaryMessageAsync(cts.Token))
                {
                    using (messageWrapper)
                    {
                        var message = messageWrapper.PrimaryMessage;
                        Console.WriteLine($"Received message: S{message.S}F{message.F}");

                        // 根据消息类型进行处理
                        var reply = await ProcessPrimaryMessageAsync(message);
                        if (reply != null)
                        {
                            await messageWrapper.TryReplyAsync(reply);
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Message handling stopped.");
            }
        }

        private async Task<SecsMessage?> ProcessPrimaryMessageAsync(SecsMessage message)
        {
            await Task.CompletedTask;

            return (message.S, message.F) switch
            {
                // S1F1 - Are You There Request
                (1, 1) => new SecsMessage(1, 2) { SecsItem = Item.L() },

                // S1F13 - Establish Communication Request
                (1, 13) => new SecsMessage(1, 14) 
                { 
                    SecsItem = Item.L(
                        Item.U1(0), // COMMACK - Accept
                        Item.L(
                            Item.A(_gemEquipment?.MDLN ?? ""),
                            Item.A(_gemEquipment?.SoftRev ?? "")
                        )
                    )
                },

                // S2F17 - Date and Time Request
                (2, 17) => new SecsMessage(2, 18) 
                { 
                    SecsItem = Item.A(DateTime.Now.ToString("yyyyMMddHHmmss"))
                },

                // S2F31 - Date and Time Set Request
                (2, 31) => new SecsMessage(2, 32) { SecsItem = Item.U1(0) }, // TIACK - Accept

                // 其他消息返回null，表示不需要回复
                _ => null
            };
        }

        private async Task SendEventsAndAlarmsAsync()
        {
            Console.WriteLine("Sending events and alarms...");

            if (_gemEquipment == null) return;

            // 发送设备上线事件
            await _gemEquipment.PostEventAsync(1); // EquipmentOffline

            await Task.Delay(1000);

            // 发送载具到达事件
            await _gemEquipment.PostEventAsync(100, Item.L(
                Item.A("CARRIER001"),
                Item.A("PORT01")
            ));

            await Task.Delay(1000);

            // 发送报警
            await _gemEquipment.PostAlarmAsync(2, "CRANE01");

            await Task.Delay(2000);

            // 清除报警
            await _gemEquipment.ClearAlarmAsync(2, "CRANE01");

            Console.WriteLine("Events and alarms sent.");
        }

        private async Task CleanupAsync()
        {
            Console.WriteLine("Cleaning up...");

            if (_gemEquipment != null)
            {
                await _gemEquipment.StopGEMAsync();
                _gemEquipment.Disconnect();
                _gemEquipment.Dispose();
            }

            Console.WriteLine("Cleanup completed.");
        }

        #region Event Handlers
        private void OnConnectionChanged(object? sender, ConnectionState state)
        {
            Console.WriteLine($"Connection state changed: {state}");
        }

        private void OnEventPosted(object? sender, uint eventId)
        {
            Console.WriteLine($"Event posted: {eventId}");
        }

        private void OnAlarmPosted(object? sender, uint alarmId)
        {
            Console.WriteLine($"Alarm posted: {alarmId}");
        }

        private void OnAlarmCleared(object? sender, uint alarmId)
        {
            Console.WriteLine($"Alarm cleared: {alarmId}");
        }
        #endregion

        /// <summary>
        /// 程序入口点示例
        /// </summary>
        public static async Task Main(string[] args)
        {
            var example = new GemEquipmentExample();
            await example.RunExampleAsync();
        }
    }
}
