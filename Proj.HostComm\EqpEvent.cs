﻿namespace Proj.HostComm
{
	/// <summary>
    /// ceid
	/// 0:		 Invalid event ID
	/// 1-99:	 Reserved
	/// 100-999: SecsLite predefined event ID 
	/// 1000-:	 User defined event ID
	/// </summary>
	public enum EqpEvent : uint
	{
		InvalidEventID = 0,

        //***Predefined Event***
        EquipmentOffLine = 1,           //ID需要与event_def.cs一致
        ControlStatusLocal = 2,       //ID需要与event_def.cs一致
        ControlStatusRemote = 3,      //ID需要与event_def.cs一致
        PPChanged = 4,              //?????????????????????????????    //ID需要与event_def.cs一致
        AlarmCleared = 51,     //         //ID需要与event_def.cs一致
        AlarmSet = 52,          //        //ID需要与event_def.cs一致

        //SC Event
        SCAutoComplete = 53,//
        SCAutoInitialized = 54,//
        SCPauseCompleted = 55,//
        SCPaused = 56,//
        SCPausedInitialized = 57,//

        //UnitAlarm Event
        UnitAlarmCleared = 503,
        UnitAlarmSet = 504,

        //Transfer Event
        TransferAbortCompleted = 101,
        TransferAbortFailed = 102,
        TransferAbortInitiated = 103,
        TransferCancelCompleted = 104,
        TransferCancelFailed = 105,
        TransferCancelInitiated = 106,
        TransferCompleted = 107,
        TransferInitiated = 108,
        TransferPaused = 109,
        TransferResumed = 110,
            PriorityUpdateCompleted = 662,  //DFK
            PriorityUpdateFailed = 663,     //DFK
            ScanInitiated = 681,            //DFK
            ScanCompleted = 682,            //DFK

        //Carrier Event
        CarrierInstallCompleted = 151,
        CarrierRemoveCompleted = 152,
        CarrierRemoved = 153,
        CarrierResumed = 154,
        CarrierStored = 155,
        CarrierStoredAlt = 156,
        CarrierTransferring = 157,
        CarrierWaitIn = 158,
        CarrierWaitOut = 159,
        ZoneCapacityChange = 160,

        //Crane Event
        CraneActive = 201,
        CraneIdle = 202,
            CraneOutOfService = 530,     //DFK
            CraneInService = 531,        //DFK  
            ForkingStarted = 811,        //DFK 
            ForkingCompleted = 812,      //DFK 
            ForkRised = 813,             //DFK 
            ForkDowned = 814,            //DFK 
            CraneArrived = 815,          //DFK 

        //Other Event
        CarrierIDRead = 251,
        CarrierLocateCompleted = 252,   //Not use
        IDReaderError = 253,
        OperatorInitiatedAction = 254,
        ShelfStateChanged = 625,

        //Port Event
        PortOutofService = 260,
        PortInService = 261,
            PortTypeInput = 603,        //DFK
            PortTypeOutput = 604,       //DFK 
            PortTypeChanging = 605,     //DFK 
            EqNoRequest = 606,
            EqLoadRequest = 607,
            EqUnloadRequest = 608,
            EqPresence = 609,          //not use
            EqNoPresence = 610,        //not use
            OperatorChangeZoneData = 611, //not use
            SCState = 701,                //not use
            PortAutoMode = 614,           //DFK
            PortManualMode = 615,         //DFK
            PortModeChanging = 616,       //DFK
            ZoneShelfStateChanged = 627,  //DFK
            CassetteStockerStateChange = 701, //DFK
        
            PortTypeChange = 143,

        //opration
            MessageRecognition = 10,
            EstablishIntervalTimeChange = 11,
    }
}
