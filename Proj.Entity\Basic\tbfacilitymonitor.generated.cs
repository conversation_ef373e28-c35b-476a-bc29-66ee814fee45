﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;


 
namespace Proj.Entity
{
    #region TbFacilitymonitor and List
    [Serializable]
    [Description("设备监控表")]
    [LinqToDB.Mapping.Table("TB_FACILITYMONITOR")]
    public partial class TbFacilitymonitor : GEntity<TbFacilitymonitor>, ITimestamp
    {
        #region Contructor(s)

        private TbFacilitymonitor()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CFacilityId = RegisterProperty<String>(p => p.CFacilityId);
        private static readonly PropertyInfo<String> pty_CFacilityName = RegisterProperty<String>(p => p.CFacilityName);
        private static readonly PropertyInfo<String> pty_CFacilityDes = RegisterProperty<String>(p => p.CFacilityDes);
        private static readonly PropertyInfo<String> pty_NMainspeed = RegisterProperty<String>(p => p.NMainspeed);
        private static readonly PropertyInfo<String> pty_NFeedspeed = RegisterProperty<String>(p => p.NFeedspeed);
        private static readonly PropertyInfo<String> pty_NMainpower = RegisterProperty<String>(p => p.NMainpower);
        private static readonly PropertyInfo<String> pty_NFeedpower = RegisterProperty<String>(p => p.NFeedpower);
        private static readonly PropertyInfo<String> pty_CUser = RegisterProperty<String>(p => p.CUser);
        private static readonly PropertyInfo<String> pty_CLasttime = RegisterProperty<String>(p => p.CLasttime);
        private static readonly PropertyInfo<String> pty_CErrormessage = RegisterProperty<String>(p => p.CErrormessage);
        private static readonly PropertyInfo<String> pty_CState = RegisterProperty<String>(p => p.CState);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        private static readonly PropertyInfo<byte[]> pty_CPicture = RegisterProperty<byte[]>(p => p.CPicture);
        private static readonly PropertyInfo<byte[]> pty_CManphoto = RegisterProperty<byte[]>(p => p.CManphoto);
        #endregion

        /// <summary>
        /// 设备编码
        /// </summary>
        [Description("设备编码")]
        [LinqToDB.Mapping.Column("C_FACILITYID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CFacilityId
        {
            get { return GetProperty(pty_CFacilityId); }
            set { SetProperty(pty_CFacilityId, value); }
        }
        /// <summary>
        /// 设备名称
        /// </summary>
        [Description("设备名称")]
        [LinqToDB.Mapping.Column("C_FACILITYNAME")]
        public String CFacilityName
        {
            get { return GetProperty(pty_CFacilityName); }
            set { SetProperty(pty_CFacilityName, value); }
        }
        /// <summary>
        /// 设备描述
        /// </summary>
        [Description("设备描述")]
        [LinqToDB.Mapping.Column("C_FACILITYDES")]
        public String CFacilityDes
        {
            get { return GetProperty(pty_CFacilityDes); }
            set { SetProperty(pty_CFacilityDes, value); }
        }
        /// <summary>
        /// 主轴转速
        /// </summary>
        [Description("主轴转速")]
        [LinqToDB.Mapping.Column("N_MAINSPEED")]
        public String NMainspeed
        {
            get { return GetProperty(pty_NMainspeed); }
            set { SetProperty(pty_NMainspeed, value); }
        }
        /// <summary>
        /// 进给速度
        /// </summary>
        [Description("进给速度")]
        [LinqToDB.Mapping.Column("N_FEEDSPEED")]
        public String NFeedspeed
        {
            get { return GetProperty(pty_NFeedspeed); }
            set { SetProperty(pty_NFeedspeed, value); }
        }
        /// <summary>
        /// 主轴倍率
        /// </summary>
        [Description("主轴倍率")]
        [LinqToDB.Mapping.Column("N_MAINPOWER")]
        public String NMainpower
        {
            get { return GetProperty(pty_NMainpower); }
            set { SetProperty(pty_NMainpower, value); }
        }
        /// <summary>
        /// 进给倍率
        /// </summary>
        [Description("进给倍率")]
        [LinqToDB.Mapping.Column("N_FEEDPOWER")]
        public String NFeedpower
        {
            get { return GetProperty(pty_NFeedpower); }
            set { SetProperty(pty_NFeedpower, value); }
        }
        /// <summary>
        /// 操作人员
        /// </summary>
        [Description("操作人员")]
        [LinqToDB.Mapping.Column("C_USER")]
        public String CUser
        {
            get { return GetProperty(pty_CUser); }
            set { SetProperty(pty_CUser, value); }
        }
        /// <summary>
        /// 最新登录时间
        /// </summary>
        [Description("最新登录时间")]
        [LinqToDB.Mapping.Column("C_LASTTIME")]
        public String CLasttime
        {
            get { return GetProperty(pty_CLasttime); }
            set { SetProperty(pty_CLasttime, value); }
        }
        /// <summary>
        /// 报警消息
        /// </summary>
        [Description("报警消息")]
        [LinqToDB.Mapping.Column("C_ERRORMESSAGE")]
        public String CErrormessage
        {
            get { return GetProperty(pty_CErrormessage); }
            set { SetProperty(pty_CErrormessage, value); }
        }
        /// <summary>
        /// 监控状态
        /// </summary>
        [Description("监控状态")]
        [LinqToDB.Mapping.Column("C_STATE")]
        public String CState
        {
            get { return GetProperty(pty_CState); }
            set { SetProperty(pty_CState, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        /// <summary>
        /// 设备图片
        /// </summary>
        [Description("设备图片")]
        [LinqToDB.Mapping.Column("C_PICTURE")]
        public byte[] CPicture
        {
            get { return GetProperty(pty_CPicture); }
            set { SetProperty(pty_CPicture, value); }
        }
        /// <summary>
        /// 人员图片
        /// </summary>
        [Description("人员图片")]
        [LinqToDB.Mapping.Column("C_MANPHOTO")]
        public byte[] CManphoto
        {
            get { return GetProperty(pty_CManphoto); }
            set { SetProperty(pty_CManphoto, value); }
        }

      
     
        
        #endregion 
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CFacilityId, "设备编码是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityId, 36, "设备编码不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityName, 100, "设备名称不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityDes, 500, "设备描述不能超过500个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_NMainspeed, 40, "主轴转速不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_NFeedspeed, 40, "进给速度不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_NMainpower, 40, "主轴倍率不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_NFeedpower, 40, "进给倍率不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CUser, 20, "操作人员不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLasttime, 20, "最新登录时间不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CErrormessage, 100, "报警消息不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CState, 1, "监控状态不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 40, "扩展字段B不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 40, "扩展字段C不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPicture, 4000, "设备图片不能超过4000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CManphoto, 4000, "人员图片不能超过4000个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CFacilityId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbFacilitymonitorList : GEntityList<TbFacilitymonitorList, TbFacilitymonitor>
    {
        private TbFacilitymonitorList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
