﻿using Proj.WCF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.ServiceModel;
using System.ServiceModel.Description;
using System.Text;
using System.Xml;

namespace Proj.Service.Core
{
    /// <summary>
    /// WCF宿主服务器，用于启动和停止WCF服务
    /// </summary>
    public class WCFHost
    {
        private string DefaultIP = "";  //不设置默认IP，则自动获取IP
        private int ServerPort = 9900;
        private List<string> m_IPList = new List<string>();

        public WCFHost()
        {
            LoadWCFConfig();
        }

        private void LoadWCFConfig()
        {
            XmlDocument xmlConfig = new XmlDocument();
            string strBinPath = System.IO.Directory.GetCurrentDirectory();
            xmlConfig.Load(strBinPath + @"\config\wcf-config.xml");
            foreach (XmlNode node in xmlConfig.SelectNodes("WCFConfig/Host"))
            {
                string strIP = node.Attributes["ip"].Value;
                if(m_IPList.Contains(strIP) == false)
                {
                    m_IPList.Add(node.Attributes["ip"].Value);
                }
            }
        }

        /// <summary>
        /// 启动wcf服务
        /// </summary>
        public void StartServer()
        {
            try
            {
                //获取本机IP
                string ip = "";
                if (m_IPList.Contains("127.0.0.1"))
                {
                    ip = "127.0.0.1";
                }
                else
                {
                    ip = GetIP();
                }
                string tcpaddress = string.Format("net.tcp://{0}:{1}/", ip, ServerPort);
                //定义服务主机
                ServiceHost host = new ServiceHost(typeof(WCFService), new Uri(tcpaddress));
                //设置netTCP协议
                NetTcpBinding tcpBinding = new NetTcpBinding();
                tcpBinding.MaxBufferPoolSize = 2147483647;
                tcpBinding.MaxReceivedMessageSize = 2147483647;
                tcpBinding.MaxBufferSize = 2147483647;
                //提供安全传输
                tcpBinding.Security.Mode = SecurityMode.None;
                //需要提供证书
                tcpBinding.Security.Transport.ClientCredentialType = TcpClientCredentialType.Certificate;
                //添加多个服务终结点
                //使用指定的协定、绑定和终结点地址将服务终结点添加到承载服务中
                //netTcp协议终结点
                host.AddServiceEndpoint(typeof(IWCFService), tcpBinding, tcpaddress);

                #region 添加行为
                //元数据发布行为
                ServiceMetadataBehavior behavior = new ServiceMetadataBehavior();
                //支持get请求
                behavior.HttpGetEnabled = false;

                //设置到Host中
                host.Description.Behaviors.Add(behavior);
                #endregion

                //设置数据交换类型
                host.AddServiceEndpoint(typeof(IMetadataExchange), MetadataExchangeBindings.CreateMexTcpBinding(), "mex");
                //启动服务
                host.Open();
                string s = string.Format("服务启动成功,正在运行...\r\n{0}", tcpaddress);
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("WCFHost.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                Environment.Exit(0);
            }
        }

        public void StopServer()
        {
          
        }

        /// <summary>
        /// 获取本机IP地址
        /// </summary>
        /// <returns></returns>
        public string GetIP()
        {
            // 如果有默认IP，返回默认值
            if (DefaultIP != "")
            {
                return DefaultIP;
            }

            string name = Dns.GetHostName();
            IPHostEntry me = Dns.GetHostEntry(name);
            IPAddress[] ips = me.AddressList;
            foreach (IPAddress ip in ips)
            {
                string strIP = ip.ToString();
                if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork
                    && m_IPList.Contains(strIP))
                {
                    return strIP;
                }
            }
            return (ips != null && ips.Length > 0 ? ips[0] : new IPAddress(0x0)).ToString();
        }
    }
}
