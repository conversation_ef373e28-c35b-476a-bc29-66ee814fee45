﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntDeviceinfo and List
    [Serializable]
    [Description("设备基本表")]
    [LinqToDB.Mapping.Table("Tnt_DeviceInfo")]
    public partial class TntDeviceinfo : GEntity<TntDeviceinfo>, ITimestamp
    {
        #region Contructor(s)

        private TntDeviceinfo()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CDeviceId = RegisterProperty<String>(p => p.CDeviceId);
        private static readonly PropertyInfo<String> pty_CDeviceCode = RegisterProperty<String>(p => p.CDeviceCode);
        private static readonly PropertyInfo<String> pty_CDeviceName = RegisterProperty<String>(p => p.CDeviceName);
        private static readonly PropertyInfo<String> pty_CDeviceip = RegisterProperty<String>(p => p.CDeviceip);
        private static readonly PropertyInfo<String> pty_CItemdescription = RegisterProperty<String>(p => p.CItemdescription);
        private static readonly PropertyInfo<DateTime?> pty_DCreatedate = RegisterProperty<DateTime?>(p => p.DCreatedate);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion


        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_DeviceID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CDeviceId
        {
            get { return GetProperty(pty_CDeviceId); }
            set { SetProperty(pty_CDeviceId, value); }
        }
        /// <summary>
        /// 设备ID
        /// </summary>
        [Description("设备ID")]
        [LinqToDB.Mapping.Column("c_DeviceCode")]
        public String CDeviceCode
        {
            get { return GetProperty(pty_CDeviceCode); }
            set { SetProperty(pty_CDeviceCode, value); }
        }
        /// <summary>
        /// 设备名称
        /// </summary>
        [Description("设备名称")]
        [LinqToDB.Mapping.Column("c_DeviceName")]
        public String CDeviceName
        {
            get { return GetProperty(pty_CDeviceName); }
            set { SetProperty(pty_CDeviceName, value); }
        }
        /// <summary>
        /// 设备IP地址
        /// </summary>
        [Description("设备IP地址")]
        [LinqToDB.Mapping.Column("c_DeviceIp")]
        public String CDeviceip
        {
            get { return GetProperty(pty_CDeviceip); }
            set { SetProperty(pty_CDeviceip, value); }
        }
        /// <summary>
        /// 描述
        /// </summary>
        [Description("描述")]
        [LinqToDB.Mapping.Column("c_ItemDescription")]
        public String CItemdescription
        {
            get { return GetProperty(pty_CItemdescription); }
            set { SetProperty(pty_CItemdescription, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        [LinqToDB.Mapping.Column("d_CreateDate")]
        public DateTime? DCreatedate
        {
            get { return GetProperty(pty_DCreatedate); }
            set { SetProperty(pty_DCreatedate, value); }
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CDeviceId, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeviceId, 36, "主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeviceCode, 30, "设备ID不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeviceName, 40, "设备名称不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDeviceip, 36, "设备IP地址不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CItemdescription, 400, "描述不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CDeviceId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntDeviceinfoList : GEntityList<TntDeviceinfoList, TntDeviceinfo>
    {
        private TntDeviceinfoList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
