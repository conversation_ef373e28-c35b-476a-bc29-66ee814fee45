﻿using Proj.Entity;
using Proj.History;
using System;
using System.Collections.Generic;
using Proj.Log;
using Proj.HostComm;
using System.Threading;
using System.Xml;
using System.Reflection;
using Proj.DevComm;
using Proj.API;

namespace Proj.Alarm
{
    public class AlarmController
    {
        private static AlarmController m_Instanse;
        private static readonly object mSyncObject = new object();
        private XmlDocument m_xmlCPS = null;

        public static AlarmController Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new AlarmController();
                        }
                    }
                }
                return m_Instanse;
            }
        }
        private List<AlarmItem> alarmItems = new List<AlarmItem>();

        public List<AlarmItem> GetAlarmItems()
        {
            alarmItems.Clear();
            try
            {
                //应该在Alarm定义表中加载Alarm定义
                TpAlarmConfigList tpAlarmList = TpAlarmConfigList.GetAll();
                foreach (TpAlarmConfig tpAlarm in tpAlarmList)
                {
                    AlarmItem alarmItem = new AlarmItem( (uint)tpAlarm.Code, tpAlarm.Text,(int)tpAlarm.Enabled == 1 );
                    alarmItems.Add(alarmItem);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog("AlarmController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return alarmItems;
        }

        public List<AlarmItem> GetCurrentAlarms()
        {
            List<AlarmItem> alarmItems = new List<AlarmItem>();
            try
            {
                TpAlarmList tpAlarmList = TpAlarmList.GetAll();
                foreach (TpAlarm tpAlarm in tpAlarmList)
                {
                    AlarmItem alarmItem = new AlarmItem((uint)tpAlarm.Code, tpAlarm.Comment, true);
                    alarmItems.Add(alarmItem);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog("AlarmController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return alarmItems;
        }

        /// <summary>
        /// 根据报警ID获取报警内容
        /// </summary>
        /// <param name="alarmID"></param>
        /// <returns></returns>
        public string GetAlarmText(int alarmID)
        {
            string alarmText = "";
            foreach (AlarmItem alarmItem in alarmItems)
            {
                if (alarmItem.Id == alarmID)
                {
                    alarmText = alarmItem.Description;
                    break;
                }
            }
            return alarmText;
        }

        /// <summary>
        /// 发生报警时调用此函数
        /// </summary>
        /// <param name="nAlarmCode">报警编码</param>
        /// <param name="strAlarmUnit">报警单元</param>
        /// <param name="strComment">报警备注</param>
        public bool SetAlarm(int nAlarmCode, string strAlarmUnit, string strComment)
        {
            try
            {
                DateTime alarmTime = DateTime.Now;
                if (TpAlarm.Exists(nAlarmCode))  //如果此报警在Alarm表中已存在，更新Count
                {
                    //TpAlarm tAlarm = TpAlarm.GetById(nAlarmCode);
                    //tAlarm.LastTime = alarmTime;
                    //tAlarm.Count++;
                    //tAlarm.Save();
                    return false;
                }
                else  //如果此报警在Alarm表中不存在，新建Alarm，并通知GUI
                {
                    string strAlarmType = "2";
                    //if (nAlarmCode == 33 || nAlarmCode == 1226 || nAlarmCode == 1227)  //CPS Alarm
                    //{
                    //    strAlarmType = "1";
                    //    strComment = GetCPSAlarmComment(nAlarmCode);
                    //}
                    //else
                    {
                        TpAlarmConfig tAlarmCfg = TpAlarmConfig.GetById(nAlarmCode);
                        if (tAlarmCfg != null)
                        {
                            if (strComment == "")
                            {
                                strComment = tAlarmCfg.Text;
                            }

                        if(tAlarmCfg.Type != "")
                        {
                            strAlarmType = tAlarmCfg.Type;
                        }
                    }
                }

                if(strAlarmUnit.Length == 0)
                {
                    strAlarmUnit = "STK";
                }
                //存储到数据库
                TpAlarm tAlarm = TpAlarm.New();
                tAlarm.Code = nAlarmCode;
                tAlarm.Unit = strAlarmUnit;
                tAlarm.Type = strAlarmType;
                tAlarm.StartTime = alarmTime;
                tAlarm.LastTime = alarmTime;
                tAlarm.Count = 1;
                tAlarm.Comment = strComment;
                var savedAlarm = tAlarm.Save();
                HistoryWriter.Instance.AddAlarmHistory(alarmTime, strAlarmUnit, nAlarmCode, strComment);

                //上报MCS UnitAlarm
                HostIF.Instance.PostUnitAlarmEvent(strAlarmUnit, nAlarmCode, strComment, EqpEvent.UnitAlarmSet);

                return true;
                }
            }
            catch(Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("AlarmController.cs: SetAlarm:" + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }
        }

        /// <summary>
        /// 根据报警码清除报警
        /// </summary>
        /// <param name="strStartTime">第一次发生报警的时间</param>
        /// <param name="nAlarmCode">报警编码</param>
        /// <param name="strAlarmUnit">报警单元</param>
        public bool ClearAlarmByCode(int nAlarmCode, string strAlarmUnit)
        {
            DateTime clearTime = DateTime.Now;
            if (TpAlarm.Exists(nAlarmCode))  //如果此报警在TpAlarm表中已存在，则更新History并从TpAlarm表删除
            {
                TpAlarm tAlarm = TpAlarm.GetById(nAlarmCode);
                HistoryWriter.Instance.UpdateAlarmClearTime((DateTime)tAlarm.StartTime, strAlarmUnit, nAlarmCode);
                HostIF.Instance.PostUnitAlarmEvent(strAlarmUnit, tAlarm.Code, tAlarm.Comment, EqpEvent.UnitAlarmCleared);
                TpAlarmList.DeleteByCriteria(x => x.Code.Equals(nAlarmCode));

                ////通过WCF通知GUI清除报警
                //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                //dicParams.Add("AlarmCode", nAlarmCode);
                //Proj.WCF.WCFService.Instance.ServerSendMessage("ClearAlarm", dicParams);
            }
            return true;
        }

        public bool HasAlarm()
        {
            try
            {
                if (TpAlarmList.GetByLambda(x => x.Code < 2000 && x.Type == "1").Count > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch
            {
                return false;
            }
           
        }

        public bool ToMCSClearAlarm(string strCommandID, string strAlarmUnit)
        {
            DateTime clearTime = DateTime.Now;
            try
            {
                TpAlarmList alarmList = TpAlarmList.GetByLambda(x => x.Unit.Equals(strAlarmUnit));
                if (alarmList.Count > 0)
                {
                    HostIF.Instance.PostAlarmEvent(strCommandID, "", strAlarmUnit, "ABORT", 0, EqpEvent.AlarmCleared);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog("AlarmController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return true;
        }
        /// <summary>
        /// 根据报警模块单元清除报警
        /// </summary>
        /// <param name="strAlarmUnit">报警模块单元</param>
        public bool ClearAlarmByUnit(string strAlarmUnit)
        {
            DateTime clearTime = DateTime.Now;
            try
            {
                TpAlarmList alarmList = TpAlarmList.GetByLambda(x => x.Unit.Equals(strAlarmUnit));
                if (alarmList.Count > 0)
                {
                    TpPort portInfo = TpPort.GetByLambda(x => x.Name == strAlarmUnit);
                    foreach (TpAlarm tAlarm in alarmList)
                    {
                        HistoryWriter.Instance.UpdateAlarmClearTime((DateTime)tAlarm.StartTime, strAlarmUnit, tAlarm.Code);
                        if (portInfo == null || portInfo.Name == "")  //报警单元不是PORT
                        {
                            HostIF.Instance.ClearAlarm(Convert.ToUInt32(tAlarm.Code), strAlarmUnit);
                        }
                        HostIF.Instance.PostUnitAlarmEvent(strAlarmUnit, tAlarm.Code, tAlarm.Comment, EqpEvent.UnitAlarmCleared);
                    }
                    TpAlarmList.DeleteByCriteria(x => x.Unit.Equals(strAlarmUnit));
                    Thread clearThread = new Thread(SendClearAlarmToGUI);
                    clearThread.Start();
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog("AlarmController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            
            return true;
        }

        private void SendClearAlarmToGUI()
        {
            //通过WCF兼容性服务通知GUI清除报警
            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            dicParams.Add("AlarmUnit", "");
            Proj.WCF.WCFService.Instance.ServerSendMessage("ClearAlarm", dicParams);

        }

        private string GetCPSAlarmComment(int nAlarmCode)
        {
            string strComment = "CPS Alarm";
            string strXpath = "";
            int nCPSErrorCode = 0;
            if(nAlarmCode == 33)
            {
                nCPSErrorCode = StockerDev.Instance.GetCPSConverterErrorCode();
                strXpath = "/CPSConfig/ErrorCode/Converter";
                strComment = "CPS Converter Alarm, Error Code: " + nCPSErrorCode;
            }
            else if (nAlarmCode == 1226)
            {
                nCPSErrorCode = StockerDev.Instance.GetCPSRegulatorErrorCode();
                strXpath = "/CPSConfig/ErrorCode/RegulatorECO";
                strComment = "CPS Regulator Alarm, Error Code: " + nCPSErrorCode;
            }
            else if (nAlarmCode == 1227)
            {
                nCPSErrorCode = StockerDev.Instance.GetCPSECOErrorCode();
                strXpath = "/CPSConfig/ErrorCode/RegulatorECO";
                strComment = "CPS ECO Alarm, Error Code: " + nCPSErrorCode;
            }

            if (nCPSErrorCode <= 0)
            {
                return strComment;
            }

            if (m_xmlCPS == null)
            {
                string strDllPath = Assembly.GetEntryAssembly().Location;
                m_xmlCPS = new XmlDocument();
                m_xmlCPS.Load(strDllPath + "\\..\\config\\cps-config.xml");
            }
            XmlNode converterErrorNode = m_xmlCPS.SelectSingleNode(strXpath);
            foreach (XmlNode node in converterErrorNode.ChildNodes)
            {
                if (node.Attributes["id"].Value == nCPSErrorCode.ToString())
                {
                    strComment = strComment + ", Error Name: " + node.Attributes["name"].Value;
                    return strComment;
                }
            }

            return strComment;
        }

        public bool bIsAlarmWithSTKorCrane()
        {
            bool bRet = false;

            if(CraneDev.Instance.IsCraneHasAlarm("Crane1") || StockerDev.Instance.IsSTKAlarm())
            {
                bRet = true;
            }

            return bRet;
        }
    }
}
