﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntKeyvalueconfig and List
    [Serializable]
    [Description("TNT_KEYVALUECONFIG")]
    [LinqToDB.Mapping.Table("TNT_KEYVALUECONFIG")]
    public partial class TntKeyvalueconfig : GEntity<TntKeyvalueconfig>
    {
        #region Contructor(s)

        private TntKeyvalueconfig()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_PrimaryKey = RegisterProperty<String>(p => p.PrimaryKey);
        private static readonly PropertyInfo<String> pty_Key = RegisterProperty<String>(p => p.Key);
        private static readonly PropertyInfo<String> pty_Vlaue = RegisterProperty<String>(p => p.Vlaue);
        private static readonly PropertyInfo<String> pty_Color = RegisterProperty<String>(p => p.Color);
        #endregion

        /// <summary>
        /// primary_key
        /// </summary>
        [Description("primary_key")]
        [LinqToDB.Mapping.Column("primary_key")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String PrimaryKey
        {
            get { return GetProperty(pty_PrimaryKey); }
            set { SetProperty(pty_PrimaryKey, value); }
        }
        /// <summary>
        /// key
        /// </summary>
        [Description("key")]
        [LinqToDB.Mapping.Column("key")]
        public String Key
        {
            get { return GetProperty(pty_Key); }
            set { SetProperty(pty_Key, value); }
        }
        /// <summary>
        /// vlaue
        /// </summary>
        [Description("vlaue")]
        [LinqToDB.Mapping.Column("vlaue")]
        public String Vlaue
        {
            get { return GetProperty(pty_Vlaue); }
            set { SetProperty(pty_Vlaue, value); }
        }
        /// <summary>
        /// color
        /// </summary>
        [Description("color")]
        [LinqToDB.Mapping.Column("color")]
        public String Color
        {
            get { return GetProperty(pty_Color); }
            set { SetProperty(pty_Color, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_PrimaryKey, "primary_key是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_PrimaryKey, 32, "primary_key不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Key, 64, "key不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Vlaue, 64, "vlaue不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Color, 64, "color不能超过64个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.PrimaryKey; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntKeyvalueconfigList : GEntityList<TntKeyvalueconfigList, TntKeyvalueconfig>
    {
        private TntKeyvalueconfigList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
