{"format": 1, "restore": {"\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Service\\Proj.Service.csproj": {}}, "projects": {"\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj": {"version": "1.2.0", "restore": {"projectUniqueName": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj", "projectName": "Proj.API", "projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj": {"projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.0, )"}, "Secs4Net": {"target": "Package", "version": "[2.4.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.CacheData\\Proj.CacheData.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.CacheData\\Proj.CacheData.csproj", "projectName": "Proj.<PERSON><PERSON><PERSON><PERSON>", "projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.CacheData\\Proj.CacheData.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.CacheData\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj": {"projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj"}, "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DataTypeDef\\Proj.DataTypeDef.csproj": {"projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DataTypeDef\\Proj.DataTypeDef.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Common\\Proj.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Common\\Proj.Common.csproj", "projectName": "Proj.<PERSON>", "projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Common\\Proj.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Common\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DataTypeDef\\Proj.DataTypeDef.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DataTypeDef\\Proj.DataTypeDef.csproj", "projectName": "Proj.DataTypeDef", "projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DataTypeDef\\Proj.DataTypeDef.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DataTypeDef\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj", "projectName": "Proj.<PERSON><PERSON>", "projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"log4net": {"target": "Package", "version": "[2.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Service\\Proj.Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Service\\Proj.Service.csproj", "projectName": "Proj.<PERSON>", "projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Service\\Proj.Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Service\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.CacheData\\Proj.CacheData.csproj": {"projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.CacheData\\Proj.CacheData.csproj"}, "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Common\\Proj.Common.csproj": {"projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Common\\Proj.Common.csproj"}, "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DataTypeDef\\Proj.DataTypeDef.csproj": {"projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DataTypeDef\\Proj.DataTypeDef.csproj"}, "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj": {"projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.18, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[8.0.18, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.8.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}