﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;


namespace Proj.Entity
{
    #region TbFacilityList and List
    [Serializable]
    [Description("设备明细表")]
    [LinqToDB.Mapping.Table("TB_FACILITY_LIST")]
    public partial class TbFacilityList : GEntity<TbFacilityList>, ITimestamp
    {
        #region Contructor(s)

        private TbFacilityList()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CFacilitylistId = RegisterProperty<String>(p => p.CFacilitylistId);
        private static readonly PropertyInfo<String> pty_CIntime = RegisterProperty<String>(p => p.CIntime);
        private static readonly PropertyInfo<String> pty_CState = RegisterProperty<String>(p => p.CState);
        private static readonly PropertyInfo<String> pty_CUserId = RegisterProperty<String>(p => p.CUserId);
        private static readonly PropertyInfo<String> pty_CFacilityId = RegisterProperty<String>(p => p.CFacilityId);
        private static readonly PropertyInfo<String> pty_CFacilityName = RegisterProperty<String>(p => p.CFacilityName);
        private static readonly PropertyInfo<String> pty_CFacilityDes = RegisterProperty<String>(p => p.CFacilityDes);
        private static readonly PropertyInfo<String> pty_CFacilityType = RegisterProperty<String>(p => p.CFacilityType);
        private static readonly PropertyInfo<String> pty_CKeyflag = RegisterProperty<String>(p => p.CKeyflag);
        private static readonly PropertyInfo<String> pty_CFacilityunits = RegisterProperty<String>(p => p.CFacilityunits);
        private static readonly PropertyInfo<String> pty_CValidstate = RegisterProperty<String>(p => p.CValidstate);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 个体设备编号
        /// </summary>
        [Description("个体设备编号")]
        [LinqToDB.Mapping.Column("C_FACILITYLISTID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CFacilitylistId
        {
            get { return GetProperty(pty_CFacilitylistId); }
            set { SetProperty(pty_CFacilitylistId, value); }
        }
        /// <summary>
        /// 个体购入时间
        /// </summary>
        [Description("个体购入时间")]
        [LinqToDB.Mapping.Column("C_INTIME")]
        public String CIntime
        {
            get { return GetProperty(pty_CIntime); }
            set { SetProperty(pty_CIntime, value); }
        }
        /// <summary>
        /// 个体设备状态
        /// </summary>
        [Description("个体设备状态")]
        [LinqToDB.Mapping.Column("C_STATE")]
        public String CState
        {
            get { return GetProperty(pty_CState); }
            set { SetProperty(pty_CState, value); }
        }
        /// <summary>
        /// 负责人
        /// </summary>
        [Description("负责人")]
        [LinqToDB.Mapping.Column("C_USERID")]
        public String CUserId
        {
            get { return GetProperty(pty_CUserId); }
            set { SetProperty(pty_CUserId, value); }
        }
        /// <summary>
        /// 设备编码
        /// </summary>
        [Description("设备编码")]
        [LinqToDB.Mapping.Column("C_FACILITYID")]
        public String CFacilityId
        {
            get { return GetProperty(pty_CFacilityId); }
            set { SetProperty(pty_CFacilityId, value); }
        }
        /// <summary>
        /// 设备名称
        /// </summary>
        [Description("设备名称")]
        [LinqToDB.Mapping.Column("C_FACILITYNAME")]
        public String CFacilityName
        {
            get { return GetProperty(pty_CFacilityName); }
            set { SetProperty(pty_CFacilityName, value); }
        }
        /// <summary>
        /// 设备描述
        /// </summary>
        [Description("设备描述")]
        [LinqToDB.Mapping.Column("C_FACILITYDES")]
        public String CFacilityDes
        {
            get { return GetProperty(pty_CFacilityDes); }
            set { SetProperty(pty_CFacilityDes, value); }
        }
        /// <summary>
        /// 设备分类
        /// </summary>
        [Description("设备分类")]
        [LinqToDB.Mapping.Column("C_FACILITYTYPE")]
        public String CFacilityType
        {
            get { return GetProperty(pty_CFacilityType); }
            set { SetProperty(pty_CFacilityType, value); }
        }
        /// <summary>
        /// 是否关键设备
        /// </summary>
        [Description("是否关键设备")]
        [LinqToDB.Mapping.Column("C_KEYFLAG")]
        public String CKeyflag
        {
            get { return GetProperty(pty_CKeyflag); }
            set { SetProperty(pty_CKeyflag, value); }
        }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        [LinqToDB.Mapping.Column("C_FACILITYUNITS")]
        public String CFacilityunits
        {
            get { return GetProperty(pty_CFacilityunits); }
            set { SetProperty(pty_CFacilityunits, value); }
        }
        /// <summary>
        /// 当前状态
        /// </summary>
        [Description("当前状态")]
        [LinqToDB.Mapping.Column("C_VALIDSTATE")]
        public String CValidstate
        {
            get { return GetProperty(pty_CValidstate); }
            set { SetProperty(pty_CValidstate, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion

        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CFacilitylistId, "个体设备编号是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilitylistId, 36, "个体设备编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CIntime, 76, "个体购入时间不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CState, 40, "个体设备状态不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CUserId, 40, "负责人不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityId, 36, "设备编码不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityName, 100, "设备名称不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityDes, 500, "设备描述不能超过500个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityType, 1, "设备分类不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CKeyflag, 1, "是否关键设备不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityunits, 6, "单位不能超过6个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CValidstate, 1, "当前状态不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 40, "扩展字段A不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 40, "扩展字段B不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 40, "扩展字段C不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CFacilitylistId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbFacilityListList : GEntityList<TbFacilityListList, TbFacilityList>
    {
        private TbFacilityListList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
