﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Proj.API;
using Proj.DataTypeDef;
using ControlState = Proj.DataTypeDef.ControlState;
//using SecsLite.Gem;

namespace Proj.CacheData
{
    public class GlobalData
    {
        private static GlobalData m_Instanse;
        private static readonly object mSyncObject = new object();
        private GlobalData() { }
        public static GlobalData Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new GlobalData();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public object objRWLock = new object();
        public object objAnalyzeTransferCommandLock = new object();
        public object objAutoInTransferCommandLock = new object();

        //1. Status Variables(SV)
        //S1F3, S6F11 : Equipment Status
        public string gbClock = "";                                                         //vid = 5
        public List<uint> gbAlarmEnabled = new List<uint>();                                //vid = 3
        public List<uint> gbAlarmSet = new List<uint>();                                    //vid = 4
        public List<uint> gbEventEnabled = new List<uint>();                                //vid = 7
        public string gbSpecVersion = "E88-0307";                                           //vid = 114
        public ControlState gbControlState = ControlState.EqOffline;                        //vid = 6
        public CassetteStockerState gbCassetteStockerState = CassetteStockerState.Normal;   //vid = 401
        public SCState gbSCState = SCState.SCInit;                                          //vid = 79
        public List<PortInfo> gbCurrentPortStates = new List<PortInfo>();                   // vid = 118    一直存放所有Port数据信息的变量
        public List<EqPortInfo> gbCurrentEqPortStatus = new List<EqPortInfo>();             // vid = 350    一直存放所有EQPort数据信息的变量
        public List<PortTypeInfo> gbPortTypes = new List<PortTypeInfo>();                   // vid = 121
        public PortTypeInfo gbPortTypeInfo_i = new PortTypeInfo();                          // vid = 122
        public PortUnitType gbPortUnitType = PortUnitType.Input;                            // vid = 123
        //S1F3 : Zone Information 
        public List<ZoneData> gbActiveZones = new List<ZoneData>();                         // vid = 53
        public List<EnhancedZoneData> gbEnhancedActiveZones = new List<EnhancedZoneData>(); // vid = 85
        public List<ExtendedZoneData> gbExtendedActiveZones = new List<ExtendedZoneData>(); // vid = 95      一直存放所有Zone数据信息的变量
        public ExtendedZoneData gbExtendedZoneData = new ExtendedZoneData();                // vid = 97
        //S1F3 : Crane Status 
        public List<CraneOperationInfo> gbCraneOperationInformation = new List<CraneOperationInfo>();   // vid = 411
        public List<CraneModeStateInfo> gbCraneModeStateInformation = new List<CraneModeStateInfo>();   // vid = 412

        //S1F3 : Carrier Information 
        public List<CarrierInfo> gbActiveCarriers = new List<CarrierInfo>();                            // vid = 51
        public List<EnhancedCarrierInfo> gbEnhancedCarriers = new List<EnhancedCarrierInfo>();          // vid = 81  一直存放所有Carrier数据信息的变量
        //S1F3 : Transfer Command 
        public List<TransferCommand> gbActiveTransfers = new List<TransferCommand>();                   // vid = 52
        public List<EnhancedTransferCommand> gbEnhancedTransfers = new List<EnhancedTransferCommand>(); // vid = 83  一直存放所有的Tranfer（包括Scan、PortTypeChg）数据信息的变量

        //2.  Data Values (DV)
        //S6F11 : Transfer Data 
        public TransferState gbTransferState = TransferState.None;                    // vid = 202
        public CarrierState gbCarrierState = CarrierState.None;                       // vid = 203
        public PortTransferState gbPortTransferState = PortTransferState.None;        // vid = 251
        public ResultCode gbResultCode = ResultCode.Success;                          // vid = 69
        public IDReadStatus gbIDReadStatus = IDReadStatus.Success;                    // vid = 65
        public CraneState gbCraneState = CraneState.None;                             // vid = 252
        public EqReqStatus gbEqReqStatus = EqReqStatus.ReqOff;                        // vid = 352
        public EqPresenceStatus gbEqPresenceStatus = EqPresenceStatus.NoPresence;     // vid = 353
        public TransferCommand gbTransferCommand = new TransferCommand();             // vid = 74
        public CommandInfo gbCommandInfo = new CommandInfo();                         // vid = 59
        public TransferInfo gbTransferInfo = new TransferInfo();                      // vid = 75
        public CarrierInfo gbCarrierInfo = new CarrierInfo();                         // vid = 55
        public ZoneData gbZoneData = new ZoneData();                                  // vid = 77
        public EnhancedZoneData gbEnhancedZoneData = new EnhancedZoneData();          // vid = 356
        public StockerUnitInfo gbStockerUnitInfo = new StockerUnitInfo();             // vid = 72
        public string gbCommandID = "";                                               // vid = 58
        public string gbCommandType = "TRANSFER";                                     // vid = 80
        public uint gbPriority = 10;                                                  // vid = 67
        public string gbCarrierID = "";                                               // vid = 54
        public string gbCarrierLoc = "";                                              // vid = 56
        public string gbDest = "";                                                    // vid = 60
        public string gbSource = "";                                                  // vid = 79
        public string gbCarrierZoneName = "";                                         // vid = 90
        public string gbZoneName = "";                                                // vid = 78
        public uint gbZoneCapacity = 0;                                               // vid = 76
        public ZoneType gbZoneType = ZoneType.None;                                   // vid = 357
        public string gbStockerCraneID = "";                                          // vid = 88
        public string gbStockerUnitID = "";                                           // vid = 71
        public StockerUnitState gbStockerUnitState = StockerUnitState.Normal;         // vid = 73
        public string gbRecoveryOptions = "ABORT";                                    // vid = 68
        public string gbPortID = "";                                                  // vid = 115
        public HandoffType gbHandoffType = HandoffType.Automated;                        // vid = 64
        public string gbPortType = "LP";                                              // vid = 66
        public string gbErrorID = "";                                                 // vid = 63
        public EmptyCarrier gbEmptyCarrier = EmptyCarrier.NotEmpty;                      // vid = 61
        public string gbUnitID = "";                                                  // vid = 11
        public uint gbAlarmID = 0;                                                    // vid = 1
        public string gbAlarmText = "";                                               // vid = 12
        public List<DisabledLoc> gbDisabledLocations = new List<DisabledLoc>();       // vid = 82
        public DisabledLoc gbDisabledLoc = new DisabledLoc();                         // vid = 84
        public GlassExist gbGlassExist = GlassExist.Empty;                            // vid = 100
        public HandoffType gbHandoffType64 = HandoffType.Automated;
        //3. Equipment Constants (EC)
        //S2F13, S2F15, S6F11 : Equipment Parameter 
        public string gbEqpName = "";                                                 // vid = 62
        public uint gbEstablishCommunicationTimeOut = 30;                             // vid = 2
        public IDReadFailOptions gbIDReadDuplicateOption = IDReadFailOptions.Reject;  // vid = 111
        public IDReadFailOptions gbIDReadFailureOption = IDReadFailOptions.Reject;    // vid = 112
        public IDReadFailOptions gbIDReadMismatchOption = IDReadFailOptions.Reject;   // vid = 113
        //S2F13, S2F15, S6F11 : HSMS Parameter
        public uint gbT3TimeOut = 40;                                                 // vid = 501
        public uint gbT5TimeOut = 10;                                                 // vid = 502
        public uint gbT6TimeOut = 5;                                                  // vid = 503
        public uint gbT7TimeOut = 10;                                                 // vid = 504
        public uint gbT8TimeOut = 5;                                                  // vid = 505
        public uint gbRetryCount = 0;                                                 // vid = 506
        public uint gbZoneTotalSize = 0;                                               // vid = 377

        //程序内部使用
        public HSMSState gbHSMSState = HSMSState.NotConnected;                              //程序内部使用的HSMS连接状态
        public MCSCommunication gbMCSCommunication = MCSCommunication.Disabled;             //程序内部使用的与MCS的通信状态
        public SCMode gbSCMode = SCMode.Normal;                                             //程序内部使用的SC工作模式
        public CraneOperationState gbMachineState = CraneOperationState.None;               //系统是否存在报警或手动模式
        public List<CurrentCraneState> gbCurrentCraneState = new List<CurrentCraneState>(); //程序内部使用的Crane信息
        public List<LocationInfo> gbLocations = new List<LocationInfo>();                   //程序内部使用的位置信息
        public CycleTestInfo gbCycleTestInfo = null;                                        //程序内部使用的循环测试信息
        public List<AlarmItem> gbAlarmItems = new List<AlarmItem>();                        //程序内部使用的报警定义信息
        public List<AlarmItem> gbCurrAlarms = new List<AlarmItem>();                        //程序内部使用的当前报警信息

        public bool gbSimulation = false;  //仿真标志
        public bool gbInPortReadFailed = false;  //入口Port读码失败标示
        public string gbInPorttrName = "";  //入口Port name
        public string gbOutPorttrName = "BSSTKS03M_OUT01";  //出口Port name
        public string gbVersion = "1.0.10";  //STKC服务端版本号
    }
}
