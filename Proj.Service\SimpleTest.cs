using System;

namespace Proj.Service
{
    public class SimpleTest
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Simple test starting...");
            Console.WriteLine("This is a basic console output test.");
            Console.WriteLine("If you can see this, console output is working.");
            
            try
            {
                Console.WriteLine("Testing basic functionality...");
                var now = DateTime.Now;
                Console.WriteLine($"Current time: {now}");
                
                Console.WriteLine("Test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
            
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
