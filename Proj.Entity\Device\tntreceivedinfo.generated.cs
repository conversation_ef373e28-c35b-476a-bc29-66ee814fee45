﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntReceivedinfo and List
    [Serializable]
    [Description("接收方信息表")]
    [LinqToDB.Mapping.Table("Tnt_ReceivedInfo")]
    public partial class TntReceivedinfo : GEntity<TntReceivedinfo>, ITimestamp
    {
        #region Contructor(s)

        private TntReceivedinfo()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CCountName = RegisterProperty<String>(p => p.CCountName);
        private static readonly PropertyInfo<String> pty_CCount = RegisterProperty<String>(p => p.CCount);
        private static readonly PropertyInfo<String> pty_CStarttime = RegisterProperty<String>(p => p.CStarttime);
        private static readonly PropertyInfo<String> pty_CEndtime = RegisterProperty<String>(p => p.CEndtime);
        private static readonly PropertyInfo<String> pty_CDescription = RegisterProperty<String>(p => p.CDescription);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 账户名称
        /// </summary>
        [Description("账户名称")]
        [LinqToDB.Mapping.Column("c_CountName")]
        public String CCountName
        {
            get { return GetProperty(pty_CCountName); }
            set { SetProperty(pty_CCountName, value); }
        }
        /// <summary>
        /// 账户
        /// </summary>
        [Description("账户")]
        [LinqToDB.Mapping.Column("c_Count")]
        public String CCount
        {
            get { return GetProperty(pty_CCount); }
            set { SetProperty(pty_CCount, value); }
        }
        /// <summary>
        /// 开始时间
        /// </summary>
        [Description("开始时间")]
        [LinqToDB.Mapping.Column("c_StartTime")]
        public String CStarttime
        {
            get { return GetProperty(pty_CStarttime); }
            set { SetProperty(pty_CStarttime, value); }
        }
        /// <summary>
        /// 结束时间
        /// </summary>
        [Description("结束时间")]
        [LinqToDB.Mapping.Column("c_EndTime")]
        public String CEndtime
        {
            get { return GetProperty(pty_CEndtime); }
            set { SetProperty(pty_CEndtime, value); }
        }
        /// <summary>
        /// 描述
        /// </summary>
        [Description("描述")]
        [LinqToDB.Mapping.Column("c_Description")]
        public String CDescription
        {
            get { return GetProperty(pty_CDescription); }
            set { SetProperty(pty_CDescription, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 36, "主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCountName, 30, "账户名称不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCount, 20, "账户不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CStarttime, 50, "开始时间不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CEndtime, 50, "结束时间不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDescription, 200, "描述不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntReceivedinfoList : GEntityList<TntReceivedinfoList, TntReceivedinfo>
    {
        private TntReceivedinfoList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
