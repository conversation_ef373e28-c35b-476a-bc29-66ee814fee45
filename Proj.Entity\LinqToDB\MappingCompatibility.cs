using System;

namespace LinqToDB.Mapping
{
    /// <summary>
    /// 版本列特性 - 兼容性实现
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
    public class VersionColumnAttribute : Attribute
    {
        public string ColumnName { get; set; }

        public VersionColumnAttribute()
        {
        }

        public VersionColumnAttribute(string columnName)
        {
            ColumnName = columnName;
        }
    }

    // 移除 VersionColumn 类以避免歧义
    // 只保留 VersionColumnAttribute
}
