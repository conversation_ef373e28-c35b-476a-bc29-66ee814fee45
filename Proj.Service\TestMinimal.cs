using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Proj.Service
{
    public class TestMinimal
    {
        public static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("Starting minimal test...");
                
                var builder = WebApplication.CreateBuilder(args);
                
                // Add basic services
                builder.Services.AddControllers();
                
                // Configure to listen on specific port
                builder.WebHost.UseUrls("http://localhost:9900");
                
                var app = builder.Build();
                
                // Add a simple test endpoint
                app.MapGet("/test", () => new { Status = "OK", Message = "Minimal test working" });
                
                Console.WriteLine("Service configured, starting...");
                Console.WriteLine("Test endpoint: http://localhost:9900/test");
                
                app.Run();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack: {ex.StackTrace}");
                throw;
            }
        }
    }
}
