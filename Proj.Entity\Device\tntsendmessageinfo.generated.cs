﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntSendmessageinfo and List
    [Serializable]
    [Description("邮件发送方信息表")]
    [LinqToDB.Mapping.Table("Tnt_SendMessageInfo")]
    public partial class TntSendmessageinfo : GEntity<TntSendmessageinfo>, ITimestamp
    {
        #region Contructor(s)

        private TntSendmessageinfo()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CCountName = RegisterProperty<String>(p => p.CCountName);
        private static readonly PropertyInfo<String> pty_CEmail = RegisterProperty<String>(p => p.CEmail);
        private static readonly PropertyInfo<String> pty_CPassword = RegisterProperty<String>(p => p.CPassword);
        private static readonly PropertyInfo<String> pty_CCountType = RegisterProperty<String>(p => p.CCountType);
        private static readonly PropertyInfo<String> pty_CDescription = RegisterProperty<String>(p => p.CDescription);
        private static readonly PropertyInfo<String> pty_CSendserver = RegisterProperty<String>(p => p.CSendserver);
        private static readonly PropertyInfo<String> pty_CReceivedserver = RegisterProperty<String>(p => p.CReceivedserver);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 账户名称
        /// </summary>
        [Description("账户名称")]
        [LinqToDB.Mapping.Column("c_CountName")]
        public String CCountName
        {
            get { return GetProperty(pty_CCountName); }
            set { SetProperty(pty_CCountName, value); }
        }
        /// <summary>
        /// 账户
        /// </summary>
        [Description("账户")]
        [LinqToDB.Mapping.Column("c_Email")]
        public String CEmail
        {
            get { return GetProperty(pty_CEmail); }
            set { SetProperty(pty_CEmail, value); }
        }
        /// <summary>
        /// 密码
        /// </summary>
        [Description("密码")]
        [LinqToDB.Mapping.Column("c_Password")]
        public String CPassword
        {
            get { return GetProperty(pty_CPassword); }
            set { SetProperty(pty_CPassword, value); }
        }
        /// <summary>
        /// 账户类型
        /// </summary>
        [Description("账户类型")]
        [LinqToDB.Mapping.Column("c_CountType")]
        public String CCountType
        {
            get { return GetProperty(pty_CCountType); }
            set { SetProperty(pty_CCountType, value); }
        }
        /// <summary>
        /// 描述
        /// </summary>
        [Description("描述")]
        [LinqToDB.Mapping.Column("c_Description")]
        public String CDescription
        {
            get { return GetProperty(pty_CDescription); }
            set { SetProperty(pty_CDescription, value); }
        }
        /// <summary>
        /// 发送服务器
        /// </summary>
        [Description("发送服务器")]
        [LinqToDB.Mapping.Column("c_SendServer")]
        public String CSendserver
        {
            get { return GetProperty(pty_CSendserver); }
            set { SetProperty(pty_CSendserver, value); }
        }
        /// <summary>
        /// 接收服务器
        /// </summary>
        [Description("接收服务器")]
        [LinqToDB.Mapping.Column("c_ReceivedServer")]
        public String CReceivedserver
        {
            get { return GetProperty(pty_CReceivedserver); }
            set { SetProperty(pty_CReceivedserver, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 36, "主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCountName, 30, "账户名称不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CEmail, 20, "账户不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPassword, 400, "密码不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCountType, 20, "账户类型不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDescription, 200, "描述不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSendserver, 50, "发送服务器不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CReceivedserver, 50, "接收服务器不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntSendmessageinfoList : GEntityList<TntSendmessageinfoList, TntSendmessageinfo>
    {
        private TntSendmessageinfoList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
