﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Proj.Entity;
using Proj.DB;
using Proj.DataTypeDef;
using Proj.CacheData;
using Proj.History;
using System.Diagnostics;

namespace Proj.UnitMng
{
    public class LocationMng
    {
        private static LocationMng m_Instanse;
        private static readonly object mSyncObject = new object();
        private LocationMng() { }
        public static LocationMng Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new LocationMng();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        private int Compare(LocationInfo x, LocationInfo y)
        {
            return x.iOrder.CompareTo(y.iOrder);
        }
        public bool Initialize()
        {
            bool bRes = false;
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    TpLocationList tpLocationList = DbLocation.Instance.GetDbLocationList();
                    GlobalData.Instance.gbLocations.Clear();
                    foreach (TpLocation tpLocation in tpLocationList)
                    {
                        LocationInfo locInfo = new LocationInfo();
                        locInfo.strAddress = tpLocation.Address;
                        locInfo.strName = tpLocation.Name;
                        locInfo.locType = (LocationType)uint.Parse(tpLocation.Type);
                        locInfo.strCarrierID = tpLocation.CarrierId;
                        locInfo.IsOccupied = tpLocation.IsOccupied == 1 ? true : false;
                        locInfo.IsProhibited = tpLocation.IsProhibited == 1 ? true : false;
                        locInfo.IsReserved = tpLocation.IsReserved == 1 ? true : false;
                        locInfo.strZoneName = tpLocation.ZoneName;
                        locInfo.iOrder = tpLocation.Order;
                        GlobalData.Instance.gbLocations.Add(locInfo);
                    }
                    //对储位排序
                    GlobalData.Instance.gbLocations.Sort(Compare);

                    GlobalData.Instance.gbDisabledLocations.Clear();
                    foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                    {
                        if (loc.IsProhibited && loc.locType == LocationType.Shelf)
                        {
                            DisabledLoc disLoc = new DisabledLoc();
                            disLoc.strCarrierId = loc.strCarrierID;
                            disLoc.strCarrierLoc = loc.strAddress;
                            GlobalData.Instance.gbDisabledLocations.Add(disLoc);
                        }
                    }
                }
                bRes = true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("LocationMng.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public bool Exists(string strAddress)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.strAddress == strAddress)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public LocationType GetLocationType(string strAddress)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.strAddress == strAddress)
                    {
                        return loc.locType;
                    }
                }
            }
            return LocationType.Unknown;
        }

        public string GetLocationZone(string strAddress)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.strAddress == strAddress)
                    {
                        return loc.strZoneName;
                    }
                }
            }
            return "";
        }

        public string GetEmptyLocation(string strZoneName)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.strZoneName == strZoneName && loc.IsProhibited == false)
                    {
                        if(loc.locType == LocationType.Shelf || loc.locType == LocationType.EqPort)
                        {
                            if(loc.IsOccupied == false && loc.IsReserved == false)
                            {
                                return loc.strAddress;
                            }
                        }
                        else
                        {
                            return loc.strAddress;
                        }
                    }
                }
            }
            return "";
        }

        public string GetEmptyShelf()
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.locType == LocationType.Shelf && loc.IsOccupied == false
                        && loc.IsProhibited == false && loc.IsReserved == false)
                    {
                        return loc.strAddress;
                    }
                }
            }
            return "";
        }

        public bool bIsFullShelf()
        {
            TpLocationList tpLocationList = TpLocationList.GetByLambda(x => x.ZoneName == "Shelf");
            foreach (TpLocation tpLocation in tpLocationList)
            {   //检查储位是否已占用，已禁用，已预约
                if(tpLocation.IsOccupied != 1 && tpLocation.IsProhibited != 1 && tpLocation.IsReserved != 1)
                {
                    return false;
                }
            }

            return true;
        }
        public string GetDoubleCheckShelf()
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.locType == LocationType.Shelf && loc.IsOccupied == true
                        && loc.IsProhibited == false && loc.IsReserved == false)
                    {
                        return loc.strAddress;
                    }
                }
            }
            return "";
        }

        public string GetLocationAddress(string strLocationName)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.strName == strLocationName)
                    {
                        return loc.strAddress;
                    }
                }
            }
            return "";
        }

        public string GetLocationCarrierByLocName(string strLocationName)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.strName == strLocationName)
                    {
                        return loc.strCarrierID;
                    }
                }
            }
            return "";
        }

        public string GetLocationCarrierByAddress(string strLocationName)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.strAddress == strLocationName)
                    {
                        return loc.strCarrierID;
                    }
                }
            }
            return "";
        }

        public string GetLocationNameByAddress(string strLocationAddress)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.strAddress == strLocationAddress)
                    {
                        return loc.strName;
                    }
                }
            }
            return "";
        }

        public void ReserveLocation(string strAddress)
        {
            if (DbLocation.Instance.ReserveLocation(strAddress))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                    {
                        if (loc.strAddress == strAddress)
                        {
                            loc.IsReserved = true;
                            History.HistoryWriter.Instance.EqpEventLog(strAddress, 0, "ReserveLocation", $"ReserveLocation: Location {strAddress}");
                            break;
                        }
                    }
                }
            }
        }

        public void UnReserveLocation(string strAddress)
        {
            if (DbLocation.Instance.UnReserveLocation(strAddress))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                    {
                        if (loc.strAddress == strAddress)
                        {
                            loc.IsReserved = false;
                            History.HistoryWriter.Instance.EqpEventLog(strAddress, 0, "UnReserveLocation", $"UnReserveLocation: Location {strAddress}");
                            break;
                        }
                    }
                }
            }
        }

        public void EnableLocation(string strAddress)
        {
            if (DbLocation.Instance.EnableLocation(strAddress))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                    {
                        if (loc.strAddress == strAddress)
                        {
                            loc.IsProhibited = false;
                            History.HistoryWriter.Instance.EqpEventLog(strAddress, 0, "EnableLocation", $"EnableLocation: Location {strAddress}");
                            if(loc.locType == LocationType.IoPort || loc.locType == LocationType.EqPort)
                            {
                                PortMng.Instance.EnablePort(loc.strZoneName);
                            }
                            break;
                        }
                    }
                }
            }
        }

        public void DisableLocation(string strAddress)
        {
            if (DbLocation.Instance.DisableLocation(strAddress))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                    {
                        if (loc.strAddress == strAddress)
                        {
                            loc.IsProhibited = true;
                            History.HistoryWriter.Instance.EqpEventLog(strAddress, 0, "DisableLocation", $"DisableLocation: Location {strAddress}");
                            if (loc.locType == LocationType.IoPort || loc.locType == LocationType.EqPort)
                            {
                                PortMng.Instance.DisablePort(loc.strZoneName);
                            }
                            break;
                        }
                    }
                }
            }
        }

        public void ClearReserved(string strAddress)
        {
            if (DbLocation.Instance.UnReserveLocation(strAddress))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                    {
                        if (loc.strAddress == strAddress)
                        {
                            loc.IsReserved = false;
                            History.HistoryWriter.Instance.EqpEventLog(strAddress, 0, "ClearReserved", $"ClearReserved: Location {strAddress}");
                            break;
                        }
                    }
                }
            }
        }

        public bool IsLocationProhibited(string strLocationName)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.strName == strLocationName)
                    {
                        return loc.IsProhibited;
                    }
                }
            }
            return false;
        }

        public bool IsAddressProhibited(string strLocationAddress)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                {
                    if (loc.strAddress == strLocationAddress)
                    {
                        return loc.IsProhibited;
                    }
                }
            }
            return false;
        }
    }
}
