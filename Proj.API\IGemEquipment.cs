﻿using Microsoft.Extensions.Options;
using Secs4Net;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Proj.API
{
    public interface IGemEquipment:IDisposable
    {
        string MDLN { get; set; }
        string SoftRev { get; }
        int CommunicationState { get; set; }
        int ControlState { get; set; }

        // 消息发送接口
        Task<SecsMessage> SendAsync(SecsMessage message, CancellationToken cancellationToken = default);
        void SendMessage(int stream, int function, Item secsItem, bool replyExpected = true);
        void ReplyMessage(SecsMessage primaryMessage, SecsMessage replyMessage);

        // 连接状态管理
        Task<bool> OnCommEstablishedAsync();
        Task OnCommDisabledAsync();
        Task<bool> OnHostRequestOnlineAsync();
        Task OnSwitchOfflineAsync();
        Task OnSwitchLocalAsync();
        Task OnSwitchRemoteAsync();

        // 变量访问接口
        Task<Item> OnGetDVAsync(uint vid);
        Task<Item> OnGetSVAsync(uint svid);
        Task<Item> OnGetECAsync(uint ecid);
        Task<bool> OnSetECAsync(uint ecid, Item item);
        Task<bool> UpdateSVAsync(uint svid, object value);
        bool OnConnect();
        void OnDisconnect();

        // 主机命令处理
        Task<Item> OnHostCommandAsync(string cmd, Item param);
        Task<Item> OnHostEnhancedCommandAsync(string cmd, string objspec, Item param);

        // 消息接收处理
        Task<SecsMessage> OnReceiveMessageAsync(SecsMessage message);
        IAsyncEnumerable<PrimaryMessageWrapper> GetPrimaryMessageAsync(CancellationToken cancellationToken = default);
        
        // 数据加载接口
        Task<bool> LoadDVAsync(IEnumerable<DVItem> dvList);
        Task<bool> LoadSVAsync(IEnumerable<SVItem> svList);
        Task<bool> LoadECAsync(IEnumerable<ECItem> ecList);
        Task<bool> LoadEventAsync(string strEventCsvPath);
        Task<bool> LoadEventAsync(IEnumerable<EventItem> eventList);
        Task<bool> LoadAlarmAsync(IEnumerable<AlarmItem> alarmList);
        
        // 事件和报告管理
        void LinkEventReport(uint eventId, uint reportId);
        Task PostEventAsync(uint ceid, Item? eventData = null);
        void ReportAppendVID(uint reportId, uint variableId);

        // 报警管理
        Task ClearAlarmAsync(uint alarmCode, string? alarmUnit = null);
        Task PostAlarmAsync(uint alarmCode, string? unitName = null, Item? alarmData = null);

        // 事件订阅
        event EventHandler<uint>? EventPosted;
        event EventHandler<uint>? AlarmPosted;
        event EventHandler<uint>? AlarmCleared;
        
        // GEM服务管理
        Task StartGEMAsync();
        Task StopGEMAsync();

        // 状态切换
        Task<bool> SwitchOfflineAsync();
        Task<bool> SwitchOnlineAsync();
        Task<bool> SwitchLocalAsync();
        Task<bool> SwitchRemoteAsync();

        // 状态查询
        ConnectionState GetConnectionState();
        ControlState GetControlState();
        bool IsOnline { get; }
        bool IsLocal { get; }

        // 新增加接口
        IOptions<SecsGemOptions> Option { get; set; }
        SecsGem SecsGem { get; set; }
        HsmsConnection Connection { get; set; }

        event EventHandler<ConnectionState>? ConnectionChanged;

        Task ConnectAsync();
        void Disconnect();
    }

    // 数据类型定义
    public class DVItem
    {
        public uint Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public object Value { get; set; } = new object();
    }

    public class SVItem
    {
        public uint Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public object Value { get; set; } = new object();
    }

    public class ECItem
    {
        public uint Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public object Value { get; set; } = new object();
        public object MinValue { get; set; } = new object();
        public object MaxValue { get; set; } = new object();
    }

    public class EventItem
    {
        public uint Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public bool IsEnabled { get; set; }
    }

    public class AlarmItem
    {
        public uint Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsEnabled { get; set; }
    }

    public enum ControlState
    {
        Unknown = 0,
        OfflineEquipment = 1,
        AttemptOnline = 2,
        HostOffline = 3,
        OnlineLocal = 4,
        OnlineRemote = 5
    }

    public class PrimaryMessageWrapper : IDisposable
    {
        private readonly SecsGem? _secsGem;
        private bool _disposed = false;

        public SecsMessage PrimaryMessage { get; set; } = null!;

        public PrimaryMessageWrapper(SecsGem? secsGem = null)
        {
            _secsGem = secsGem;
        }

        public async Task<bool> TryReplyAsync(SecsMessage replyMessage)
        {
            await Task.CompletedTask;
            try
            {
                if (_secsGem != null && PrimaryMessage != null)
                {
                    // 简化实现，暂时不支持回复
                    Console.WriteLine($"Reply to S{PrimaryMessage.S}F{PrimaryMessage.F} with S{replyMessage.S}F{replyMessage.F}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error replying to message: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                PrimaryMessage?.Dispose();
                _disposed = true;
            }
        }
    }
}
