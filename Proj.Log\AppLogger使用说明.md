# AppLogger 使用说明

## 概述

AppLogger 是 Proj.Log 库中新增的应用程序日志功能，支持多种日志级别和参数拼接，适用于应用程序的各种日志输出需求。

## 功能特性

- **多级别日志**: 支持 Debug、Info、Warning、Error、Fatal 五个级别
- **参数拼接**: 支持 string.Format 风格的多参数拼接
- **异常处理**: 内置格式化异常处理，确保日志系统稳定性
- **双重输出**: 同时输出到文件和控制台（Info级别以上）
- **自动滚动**: 支持按时间和大小自动滚动日志文件

## 日志级别说明

| 级别 | 用途 | 示例场景 |
|------|------|----------|
| Debug | 调试信息 | 变量值、方法调用、详细执行流程 |
| Info | 一般信息 | 系统启动、用户操作、业务流程 |
| Warning | 警告信息 | 配置缺失、性能问题、非致命错误 |
| Error | 错误信息 | 操作失败、异常捕获、业务错误 |
| Fatal | 致命错误 | 系统崩溃、关键服务不可用 |

## 基本使用方法

### 1. 获取Logger实例

```csharp
using Proj.Log;

var logger = Logger.Instance;
```

### 2. 基础日志输出

```csharp
// 简单消息
logger.Debug("调试信息");
logger.Info("系统启动完成");
logger.Warning("配置文件不存在，使用默认配置");
logger.Error("数据库连接失败");
logger.Fatal("系统关键错误");
```

### 3. 多参数拼接

```csharp
// 使用 string.Format 风格的参数拼接
string userName = "张三";
int userId = 12345;
DateTime loginTime = DateTime.Now;

logger.Info("用户登录: 用户名={0}, ID={1}, 时间={2:yyyy-MM-dd HH:mm:ss}", 
           userName, userId, loginTime);

logger.Debug("处理数据: 总数={0}, 成功={1}, 失败={2}", 1000, 995, 5);

logger.Warning("系统负载: CPU={0}%, 内存={1}%, 磁盘={2}%", 
              85.5, 67.2, 45.8);
```

### 4. 异常日志

```csharp
try
{
    // 业务代码
}
catch (Exception ex)
{
    // 记录异常信息（包含堆栈跟踪）
    logger.Error("操作失败", ex);
    
    // 或者自定义异常消息
    logger.Error("处理文件 {0} 时发生错误: {1}", fileName, ex.Message);
}
```

### 5. 使用LogLevel枚举

```csharp
// 直接指定日志级别
logger.AppLog(LogLevel.Info, "系统状态正常");
logger.AppLog(LogLevel.Warning, "磁盘空间不足: 剩余 {0} GB", 2.5);
```

## 高级用法

### 性能监控日志

```csharp
var stopwatch = System.Diagnostics.Stopwatch.StartNew();

// 执行业务操作
DoSomeWork();

stopwatch.Stop();
logger.Info("操作完成: 耗时 {0} 毫秒", stopwatch.ElapsedMilliseconds);
```

### 设备状态日志

```csharp
var deviceInfo = new
{
    DeviceId = "DEV001",
    Status = "Online",
    Temperature = 25.5,
    LastUpdate = DateTime.Now
};

logger.Info("设备状态: ID={0}, 状态={1}, 温度={2}°C, 更新时间={3:HH:mm:ss}",
           deviceInfo.DeviceId, deviceInfo.Status, 
           deviceInfo.Temperature, deviceInfo.LastUpdate);
```

### 数据处理日志

```csharp
logger.Info("开始数据处理任务");
logger.Debug("读取数据源: {0} 条记录", totalCount);

if (invalidCount > 0)
{
    logger.Warning("发现 {0} 条无效数据，已跳过", invalidCount);
}

logger.Info("数据处理完成: 成功 {0} 条，失败 {1} 条", 
           successCount, failureCount);
```

## 配置说明

AppLogger 的配置在 `Log4net.config` 文件中：

- **文件输出**: 日志文件保存在 `D:/STK_Log/Server_Log/[日期]/App/` 目录
- **控制台输出**: Info 级别以上的日志会同时输出到控制台
- **文件滚动**: 按小时滚动，单文件最大 1MB
- **编码格式**: UTF-8
- **日志格式**: `时间 [级别] - 消息`

## 注意事项

1. **格式化安全**: 内置格式化异常处理，即使参数不匹配也不会导致程序崩溃
2. **性能考虑**: Debug 级别日志在生产环境中可以通过配置文件关闭
3. **线程安全**: Logger 实例是线程安全的，可以在多线程环境中使用
4. **文件权限**: 确保应用程序对日志目录有写入权限

## 示例代码

完整的使用示例请参考 `APITest/AppLoggerTest.cs` 文件。
