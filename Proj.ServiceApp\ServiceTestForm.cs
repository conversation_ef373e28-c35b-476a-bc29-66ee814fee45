﻿using Proj.CacheData;
using Proj.WCF;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Proj.Service.Core
{
    public partial class ServiceTestForm : Form
    {
        private WCFHost m_WCFServer = null;

        public ServiceTestForm()
        {
            InitializeComponent();
            m_WCFServer = new WCFHost();
        }

        private void CoreTestForm_Load(object sender, EventArgs e)
        {
            Proj.Log.Logger.Instance.OperationLog("Form Load... ...");
            this.Text = "STKC Service -- Version: " + GlobalData.Instance.gbVersion;
            //Maticsoft.DBUtility.PubConstant.ConnectionString = "Server=localhost;Database=stkc;Uid=root;Pwd=********;Port=3306;Charset=utf8;";
            m_WCFServer.StartServer();
            if(!Proj.Controller.StockerController.Instance.Initialize())//***连接PLC
            {
                this.panelErrorDialog.Visible = true;
            }
            else
            {
                this.panelErrorDialog.Visible = false;
                //this.panelErrorDialog.Visible = true;
            }
        }

        private bool Instance_eventWCFClientSendMessage(string strFunction, Dictionary<string, string> dicParameter)
        {
            throw new NotImplementedException();
        }

        public bool SendMessage(string strMessageName, Dictionary<string, string> dicParams)
        {
            if (strMessageName == "Register")
            {
                //m_WCFServer.Register();
            }
            return true;
        }

        private void btnSendToClient_Click(object sender, EventArgs e)
        {
            Dictionary<string, object> dicParams = new Dictionary<string, object>();
            WCFService.Instance.ServerSendMessage("Hello", dicParams);
        }

        private void btnWriteLog_Click(object sender, EventArgs e)
        {
            //LogWriter.Instance.ExceptionLog("Exception Test");
            //LogWriter.Instance.EventLog("Port1", 21001, "PortStateChanged", "Port state changed from IN to OUT");
            //LogWriter.Instance.HostLog(1, "S1F1", 1, "Select", "Select Host");
            //LogWriter.Instance.OperationLog("GUI1", "Admin", "Module Management", "Add New Module", "ModuleName: ctrlAccount, Parameters: 11,22");
            //LogWriter.Instance.PIOLog(1, 1, "AGV", "IO_E84_Output_1", 1);
            //LogWriter.Instance.PLCLog("Crane1", 3, "Crane_IO_01", 3.1415926);
            //LogWriter.Instance.SCLog("ControlState", "Convert", "Local", "Remote");
        }

        private void btnReadRTDB_Click(object sender, EventArgs e)
        {
            //Proj.DB.Model.alarm alarmModel = DBAlarm.Instance.GetModel(1001);
            //int nCount = alarmModel.alarm_count;
        }

        private void btnWriteRTDB_Click(object sender, EventArgs e)
        {
            //int nAlarmID = 1001;
            //if(DBAlarm.Instance.Exists(nAlarmID))
            //{
            //    Proj.DB.Model.alarm alarmModel = DBAlarm.Instance.GetModel(nAlarmID);
            //    alarmModel.alarm_last_time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            //    alarmModel.alarm_count++;
            //    DBAlarm.Instance.Update(alarmModel);
            //}
            //else
            //{
            //    Proj.DB.Model.alarm alarmModel = new DB.Model.alarm();
            //    alarmModel.alarm_id = nAlarmID;
            //    alarmModel.alarm_stocker_unit = "Crane1";
            //    alarmModel.alarm_start_time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            //    alarmModel.alarm_last_time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            //    alarmModel.alarm_count = 1;
            //    alarmModel.alarm_remark = "Crane Error";
            //    DBAlarm.Instance.Add(alarmModel);
            //}
        }

        private void btnSetAlarm_Click(object sender, EventArgs e)
        {
            Proj.Alarm.AlarmController.Instance.SetAlarm(1001, "Crane1", "Crane Error");
        }

        private void btnClearAlarm_Click(object sender, EventArgs e)
        {
            //Proj.Alarm.AlarmController.Instance.ClearAlarm(1001, "Crane1");
        }

        private void btnCreateTrasfer_Click(object sender, EventArgs e)
        {
            //string strCreateTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            //LogWriter.Instance.CreateTransferCmd(strCreateTime, "AR_EQ10001", 1, 10, "10101", "20202", 21);
        }

        private void btnStartTransfer_Click(object sender, EventArgs e)
        {
            //LogWriter.Instance.CraneTransferStart("20191026152553812", "AR_EQ10001", 1, "Crane1");

        }

        private void btnStopTransfer_Click(object sender, EventArgs e)
        {
            //LogWriter.Instance.CraneTransferStop("20191026152553812", "AR_EQ10001", 1, "Completed", "Not Delay");
        }

        private void ServiceTestForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            Proj.Log.Logger.Instance.OperationLog("Form Closing... ...");
            Proj.Controller.StockerController.Instance.UnInit();
            m_WCFServer.StopServer();
        }

        private void ServiceTestForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            System.Environment.Exit(0);
        }
    }
}
