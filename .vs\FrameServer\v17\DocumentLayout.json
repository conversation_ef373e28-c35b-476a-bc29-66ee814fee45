{"Version": 1, "WorkspaceRootPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{26C85E3D-9F87-40B5-936C-7D0D9256F491}|Proj.Entity\\Proj.Entity.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.entity\\proj.entity.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{2BA9E46C-12F0-4F1B-8635-00658148B733}|Proj.DB\\Proj.DB.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.db\\dbtrans.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2BA9E46C-12F0-4F1B-8635-00658148B733}|Proj.DB\\Proj.DB.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.db\\dbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2BA9E46C-12F0-4F1B-8635-00658148B733}|Proj.DB\\Proj.DB.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.db\\proj.db.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{7B558CAF-EFE1-4A05-8AD1-7242C65BD245}|Proj.History\\Proj.History.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.history\\historywriter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BBED1B4D-4443-4094-B43E-2EB885442078}|Proj.DevComm\\Proj.DevComm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.devcomm\\portdev.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BBED1B4D-4443-4094-B43E-2EB885442078}|Proj.DevComm\\Proj.DevComm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.devcomm\\plccomm.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BBED1B4D-4443-4094-B43E-2EB885442078}|Proj.DevComm\\Proj.DevComm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.devcomm\\cranedev.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1E67258-9C2E-4C24-AB7B-F6E5D8C34575}|Proj.DevObj\\Proj.DevObj.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.devobj\\proj.devobj.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{2C3F75EC-4915-44E4-88F6-A55D30FFE7AA}|Proj.Common\\Proj.Common.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.common\\proj.common.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{7B558CAF-EFE1-4A05-8AD1-7242C65BD245}|Proj.History\\Proj.History.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.history\\logwriter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DF96041A-06FD-604E-F186-60A5D6836155}|Proj.Service\\Proj.Service.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.service\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DF96041A-06FD-604E-F186-60A5D6836155}|Proj.Service\\Proj.Service.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.service\\proj.service.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{D28455F7-6E81-4080-8CA4-3703D5C9A6F6}|Proj.ServiceApp\\Proj.ServiceApp.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.serviceapp\\servicetestform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{970BAED3-670F-40CB-AF79-092AAB6B9933}|Proj.WCFService\\Proj.WCFService.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.wcfservice\\wcfservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Proj.<PERSON><PERSON><PERSON>", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Entity\\Proj.Entity.csproj", "RelativeDocumentMoniker": "Proj.<PERSON><PERSON><PERSON>\\Proj.Entity.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Entity\\Proj.Entity.csproj", "RelativeToolTip": "Proj.<PERSON><PERSON><PERSON>\\Proj.Entity.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:24:46.678Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DbTrans.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbTrans.cs", "RelativeDocumentMoniker": "Proj.DB\\DbTrans.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbTrans.cs [只读]", "RelativeToolTip": "Proj.DB\\DbTrans.cs [只读]", "ViewState": "AgIAACkAAAAAAAAAAAAQwD0AAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:24:07.35Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 2, "Title": "DbContext.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbContext.cs", "RelativeDocumentMoniker": "Proj.DB\\DbContext.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbContext.cs [只读]", "RelativeToolTip": "Proj.DB\\DbContext.cs [只读]", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:23:44.958Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Proj.<PERSON>", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\Proj.DB.csproj", "RelativeDocumentMoniker": "Proj.DB\\Proj.DB.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\Proj.DB.csproj", "RelativeToolTip": "Proj.DB\\Proj.DB.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:21:02.323Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "PlcComm.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevComm\\PlcComm.cs", "RelativeDocumentMoniker": "Proj.<PERSON>\\PlcComm.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevComm\\PlcComm.cs [只读]", "RelativeToolTip": "Proj.<PERSON>\\PlcComm.cs [只读]", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:18:22.291Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 7, "Title": "CraneDev.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevComm\\CraneDev.cs", "RelativeDocumentMoniker": "Proj.<PERSON>\\CraneDev.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevComm\\CraneDev.cs [只读]", "RelativeToolTip": "Proj.<PERSON>\\CraneDev.cs [只读]", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:18:01.167Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 5, "Title": "PortDev.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevComm\\PortDev.cs", "RelativeDocumentMoniker": "Proj.<PERSON>\\PortDev.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevComm\\PortDev.cs [只读]", "RelativeToolTip": "Proj.<PERSON>\\PortDev.cs [只读]", "ViewState": "AgIAAEYAAAAAAAAAAAAEwFUAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:17:47.511Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 9, "Title": "Proj.<PERSON>", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Common\\Proj.Common.csproj", "RelativeDocumentMoniker": "Proj.<PERSON>\\Proj.Common.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Common\\Proj.Common.csproj", "RelativeToolTip": "Proj.<PERSON>\\Proj.Common.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:15:33.526Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "Proj.<PERSON>", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevObj\\Proj.DevObj.csproj", "RelativeDocumentMoniker": "Proj.<PERSON><PERSON>bj\\Proj.DevObj.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevObj\\Proj.DevObj.csproj", "RelativeToolTip": "Proj.<PERSON><PERSON>bj\\Proj.DevObj.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:14:36.573Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "HistoryWriter.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.History\\HistoryWriter.cs", "RelativeDocumentMoniker": "Proj.History\\HistoryWriter.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.History\\HistoryWriter.cs [只读]", "RelativeToolTip": "Proj.History\\HistoryWriter.cs [只读]", "ViewState": "AgIAADQAAAAAAAAAAAD4v0IAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:13:24.416Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 10, "Title": "LogWriter.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.History\\LogWriter.cs", "RelativeDocumentMoniker": "Proj.History\\LogWriter.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.History\\LogWriter.cs", "RelativeToolTip": "Proj.History\\LogWriter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:13:02.791Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "Program.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Service\\Program.cs", "RelativeDocumentMoniker": "Proj.Service\\Program.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Service\\Program.cs", "RelativeToolTip": "Proj.Service\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACIAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T06:47:45.49Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "Proj.Service.csproj", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Service\\Proj.Service.csproj", "RelativeDocumentMoniker": "Proj.Service\\Proj.Service.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Service\\Proj.Service.csproj", "RelativeToolTip": "Proj.Service\\Proj.Service.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T06:39:03.829Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ServiceTestForm.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.ServiceApp\\ServiceTestForm.cs", "RelativeDocumentMoniker": "Proj.<PERSON>pp\\ServiceTestForm.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.ServiceApp\\ServiceTestForm.cs [只读]", "RelativeToolTip": "Proj.ServiceApp\\ServiceTestForm.cs [只读]", "ViewState": "AgIAAAAAAAAAAAAAAAAAAIMAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T06:18:13.818Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 14, "Title": "WCFService.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.WCFService\\WCFService.cs", "RelativeDocumentMoniker": "Proj.WCFSer<PERSON>\\WCFService.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.WCFService\\WCFService.cs [只读]", "RelativeToolTip": "Proj.WCFService\\WCFService.cs [只读]", "ViewState": "AgIAAAMAAAAAAAAAAAAjwCAAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T06:02:36.788Z", "EditorCaption": " [只读]"}]}]}]}