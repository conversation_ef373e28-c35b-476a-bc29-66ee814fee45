{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\STKC.Net8\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DF96041A-06FD-604E-F186-60A5D6836155}|Proj.Service\\Proj.Service.csproj|c:\\users\\<USER>\\desktop\\stkc.net8\\proj.service\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DF96041A-06FD-604E-F186-60A5D6836155}|Proj.Service\\Proj.Service.csproj|solutionrelative:proj.service\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DF96041A-06FD-604E-F186-60A5D6836155}|Proj.Service\\Proj.Service.csproj|c:\\users\\<USER>\\desktop\\stkc.net8\\proj.service\\proj.service.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{DF96041A-06FD-604E-F186-60A5D6836155}|Proj.Service\\Proj.Service.csproj|solutionrelative:proj.service\\proj.service.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{D28455F7-6E81-4080-8CA4-3703D5C9A6F6}|Proj.ServiceApp\\Proj.ServiceApp.csproj|c:\\users\\<USER>\\desktop\\stkc.net8\\proj.serviceapp\\servicetestform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D28455F7-6E81-4080-8CA4-3703D5C9A6F6}|Proj.ServiceApp\\Proj.ServiceApp.csproj|solutionrelative:proj.serviceapp\\servicetestform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{970BAED3-670F-40CB-AF79-092AAB6B9933}|Proj.WCFService\\Proj.WCFService.csproj|c:\\users\\<USER>\\desktop\\stkc.net8\\proj.wcfservice\\wcfservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{970BAED3-670F-40CB-AF79-092AAB6B9933}|Proj.WCFService\\Proj.WCFService.csproj|solutionrelative:proj.wcfservice\\wcfservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STKC.Net8\\Proj.Service\\Program.cs", "RelativeDocumentMoniker": "Proj.Service\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STKC.Net8\\Proj.Service\\Program.cs*", "RelativeToolTip": "Proj.Service\\Program.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T06:47:45.49Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Proj.<PERSON>", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STKC.Net8\\Proj.Service\\Proj.Service.csproj", "RelativeDocumentMoniker": "Proj.Service\\Proj.Service.csproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STKC.Net8\\Proj.Service\\Proj.Service.csproj", "RelativeToolTip": "Proj.Service\\Proj.Service.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T06:39:03.829Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ServiceTestForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STKC.Net8\\Proj.ServiceApp\\ServiceTestForm.cs", "RelativeDocumentMoniker": "Proj.<PERSON>pp\\ServiceTestForm.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STKC.Net8\\Proj.ServiceApp\\ServiceTestForm.cs [只读]", "RelativeToolTip": "Proj.ServiceApp\\ServiceTestForm.cs [只读]", "ViewState": "AgIAAAAAAAAAAAAAAAAAAIMAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T06:18:13.818Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 3, "Title": "WCFService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STKC.Net8\\Proj.WCFService\\WCFService.cs", "RelativeDocumentMoniker": "Proj.WCFSer<PERSON>\\WCFService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STKC.Net8\\Proj.WCFService\\WCFService.cs [只读]", "RelativeToolTip": "Proj.WCFService\\WCFService.cs [只读]", "ViewState": "AgIAAAMAAAAAAAAAAAAjwCAAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T06:02:36.788Z", "EditorCaption": " [只读]"}]}]}]}