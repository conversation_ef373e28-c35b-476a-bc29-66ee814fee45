﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpCrane and List
    [Serializable]
    [Description("Crane数据表")]
    [LinqToDB.Mapping.Table("TP_CRANE")]
    public partial class TpCrane : GEntity<TpCrane>
    {
        #region Contructor(s)

        private TpCrane()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<Int32> pty_No = RegisterProperty<Int32>(p => p.No);
        private static readonly PropertyInfo<String> pty_Name = RegisterProperty<String>(p => p.Name);
        private static readonly PropertyInfo<Int32> pty_IsIdr = RegisterProperty<Int32>(p => p.IsIdr);
        private static readonly PropertyInfo<String> pty_OperState = RegisterProperty<String>(p => p.OperState);
        private static readonly PropertyInfo<String> pty_CommandId = RegisterProperty<String>(p => p.CommandId);
        private static readonly PropertyInfo<Int32> pty_SpeedLoad = RegisterProperty<Int32>(p => p.SpeedLoad);
        private static readonly PropertyInfo<Int32> pty_SpeedEmpty = RegisterProperty<Int32>(p => p.SpeedEmpty);
        private static readonly PropertyInfo<Int32> pty_SpeedXAxis = RegisterProperty<Int32>(p => p.SpeedXAxis);
        private static readonly PropertyInfo<Int32> pty_SpeedYAxis = RegisterProperty<Int32>(p => p.SpeedYAxis);
        private static readonly PropertyInfo<Int32> pty_SpeedZAxis = RegisterProperty<Int32>(p => p.SpeedZAxis);
        private static readonly PropertyInfo<Int32> pty_SpeedTAxis = RegisterProperty<Int32>(p => p.SpeedTAxis);
        private static readonly PropertyInfo<Double> pty_MileageTravel = RegisterProperty<Double>(p => p.MileageTravel);
        private static readonly PropertyInfo<Double> pty_MileageElevation = RegisterProperty<Double>(p => p.MileageElevation);
        private static readonly PropertyInfo<Double> pty_MileageTurn = RegisterProperty<Double>(p => p.MileageTurn);
        private static readonly PropertyInfo<Double> pty_MileageFork = RegisterProperty<Double>(p => p.MileageFork);
        private static readonly PropertyInfo<String> pty_Comment = RegisterProperty<String>(p => p.Comment);
        #endregion

        /// <summary>
        /// No
        /// </summary>
        [Description("No")]
        [LinqToDB.Mapping.Column("No")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int32 No
        {
            get { return GetProperty(pty_No); }
            set { SetProperty(pty_No, value); }
        }
        /// <summary>
        /// Name
        /// </summary>
        [Description("Name")]
        [LinqToDB.Mapping.Column("Name")]
        public String Name
        {
            get { return GetProperty(pty_Name); }
            set { SetProperty(pty_Name, value); }
        }
        /// <summary>
        /// IsIDR
        /// </summary>
        [Description("IsIDR")]
        [LinqToDB.Mapping.Column("Is_IDR")]
        public Int32 IsIdr
        {
            get { return GetProperty(pty_IsIdr); }
            set { SetProperty(pty_IsIdr, value); }
        }
        /// <summary>
        /// OperationState
        /// </summary>
        [Description("OperationState")]
        [LinqToDB.Mapping.Column("Oper_State")]
        public String OperState
        {
            get { return GetProperty(pty_OperState); }
            set { SetProperty(pty_OperState, value); }
        }
        /// <summary>
        /// CommandID
        /// </summary>
        [Description("CommandID")]
        [LinqToDB.Mapping.Column("Command_ID")]
        public String CommandId
        {
            get { return GetProperty(pty_CommandId); }
            set { SetProperty(pty_CommandId, value); }
        }
        /// <summary>
        /// SpeedLoad
        /// </summary>
        [Description("SpeedLoad")]
        [LinqToDB.Mapping.Column("Speed_Load")]
        public Int32 SpeedLoad
        {
            get { return GetProperty(pty_SpeedLoad); }
            set { SetProperty(pty_SpeedLoad, value); }
        }
        /// <summary>
        /// SpeedEmpty
        /// </summary>
        [Description("SpeedEmpty")]
        [LinqToDB.Mapping.Column("Speed_Empty")]
        public Int32 SpeedEmpty
        {
            get { return GetProperty(pty_SpeedEmpty); }
            set { SetProperty(pty_SpeedEmpty, value); }
        }
        /// <summary>
        /// SpeedXAxis
        /// </summary>
        [Description("SpeedXAxis")]
        [LinqToDB.Mapping.Column("Speed_X_Axis")]
        public Int32 SpeedXAxis
        {
            get { return GetProperty(pty_SpeedXAxis); }
            set { SetProperty(pty_SpeedXAxis, value); }
        }
        /// <summary>
        /// SpeedYAxis
        /// </summary>
        [Description("SpeedYAxis")]
        [LinqToDB.Mapping.Column("Speed_Y_Axis")]
        public Int32 SpeedYAxis
        {
            get { return GetProperty(pty_SpeedYAxis); }
            set { SetProperty(pty_SpeedYAxis, value); }
        }
        /// <summary>
        /// SpeedZAxis
        /// </summary>
        [Description("SpeedZAxis")]
        [LinqToDB.Mapping.Column("Speed_Z_Axis")]
        public Int32 SpeedZAxis
        {
            get { return GetProperty(pty_SpeedZAxis); }
            set { SetProperty(pty_SpeedZAxis, value); }
        }
        /// <summary>
        /// SpeedTAxis
        /// </summary>
        [Description("SpeedTAxis")]
        [LinqToDB.Mapping.Column("Speed_T_Axis")]
        public Int32 SpeedTAxis
        {
            get { return GetProperty(pty_SpeedTAxis); }
            set { SetProperty(pty_SpeedTAxis, value); }
        }
        /// <summary>
        /// MileageTravel
        /// </summary>
        [Description("MileageTravel")]
        [LinqToDB.Mapping.Column("Mileage_Travel")]
        public Double MileageTravel
        {
            get { return GetProperty(pty_MileageTravel); }
            set { SetProperty(pty_MileageTravel, value); }
        }
        /// <summary>
        /// MileageElevation
        /// </summary>
        [Description("MileageElevation")]
        [LinqToDB.Mapping.Column("Mileage_Elevation")]
        public Double MileageElevation
        {
            get { return GetProperty(pty_MileageElevation); }
            set { SetProperty(pty_MileageElevation, value); }
        }
        /// <summary>
        /// MileageTurn
        /// </summary>
        [Description("MileageTurn")]
        [LinqToDB.Mapping.Column("Mileage_Turn")]
        public Double MileageTurn
        {
            get { return GetProperty(pty_MileageTurn); }
            set { SetProperty(pty_MileageTurn, value); }
        }
        /// <summary>
        /// MileageFork
        /// </summary>
        [Description("MileageFork")]
        [LinqToDB.Mapping.Column("Mileage_Fork")]
        public Double MileageFork
        {
            get { return GetProperty(pty_MileageFork); }
            set { SetProperty(pty_MileageFork, value); }
        }
        /// <summary>
        /// Comment
        /// </summary>
        [Description("Comment")]
        [LinqToDB.Mapping.Column("Comment")]
        public String Comment
        {
            get { return GetProperty(pty_Comment); }
            set { SetProperty(pty_Comment, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_No, "No是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Name, "Name是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Name, 64, "Name不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_IsIdr, "IsIDR是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_OperState, "OperationState是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_OperState, 1, "OperationState不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CommandId, 64, "CommandID不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_SpeedLoad, "SpeedLoad是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_SpeedEmpty, "SpeedEmpty是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_SpeedXAxis, "SpeedXAxis是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_SpeedYAxis, "SpeedYAxis是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_SpeedZAxis, "SpeedZAxis是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_SpeedTAxis, "SpeedTAxis是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_MileageTravel, "MileageTravel是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_MileageElevation, "MileageElevation是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_MileageTurn, "MileageTurn是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_MileageFork, "MileageFork是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Comment, 255, "Comment不能超过255个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.No.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class TpCraneList : GEntityList<TpCraneList, TpCrane>
    {
        private TpCraneList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
