using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;
using Proj.Service.Interfaces;
using Proj.Service.Models;
using Proj.Service.Hubs;
using Proj.Log;

namespace Proj.Service.Services
{
    /// <summary>
    /// 客户端管理服务实现类
    /// </summary>
    public class ClientManagerService : IClientManagerService
    {
        private readonly ConcurrentDictionary<string, ClientConnectionInfo> _connectedClients;
        private readonly IHubContext<StockerHub> _hubContext;
        private readonly Logger _logger;

        public ClientManagerService(IHubContext<StockerHub> hubContext)
        {
            _connectedClients = new ConcurrentDictionary<string, ClientConnectionInfo>();
            _hubContext = hubContext;
            _logger = Logger.Instance;
        }

        /// <summary>
        /// 注册客户端连接
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        /// <param name="ipAddress">客户端IP地址</param>
        public void RegisterClient(string connectionId, string ipAddress)
        {
            var clientInfo = new ClientConnectionInfo
            {
                ConnectionId = connectionId,
                IpAddress = ipAddress,
                ConnectedAt = DateTime.Now,
                LastActivity = DateTime.Now
            };

            _connectedClients.TryAdd(connectionId, clientInfo);
            _logger.WcfLog($"Client registered: {connectionId} from {ipAddress}");
        }

        /// <summary>
        /// 移除客户端连接
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        public void RemoveClient(string connectionId)
        {
            if (_connectedClients.TryRemove(connectionId, out var clientInfo))
            {
                _logger.WcfLog($"Client removed: {connectionId} from {clientInfo.IpAddress}");
            }
        }

        /// <summary>
        /// 获取所有连接的客户端
        /// </summary>
        /// <returns>客户端连接信息列表</returns>
        public IEnumerable<ClientConnectionInfo> GetConnectedClients()
        {
            return _connectedClients.Values.ToList();
        }

        /// <summary>
        /// 向所有客户端发送消息
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> SendMessageToAllClientsAsync(string function, Dictionary<string, object> parameters)
        {
            try
            {
                var logMessage = $"Send: {function}";
                foreach (var kvp in parameters)
                {
                    logMessage += $", {kvp.Key}: {kvp.Value}";
                }
                _logger.WcfLog(logMessage);

                await _hubContext.Clients.All.SendAsync("ServerSendMessage", function, parameters);
                return true;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Server Send Message Error: {ex.Message}, Stack: {ex.StackTrace}";
                _logger.ExceptionLog(errorMessage);
                return false;
            }
        }

        /// <summary>
        /// 向指定客户端发送消息
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> SendMessageToClientAsync(string connectionId, string function, Dictionary<string, object> parameters)
        {
            try
            {
                var logMessage = $"Send to {connectionId}: {function}";
                foreach (var kvp in parameters)
                {
                    logMessage += $", {kvp.Key}: {kvp.Value}";
                }
                _logger.WcfLog(logMessage);

                await _hubContext.Clients.Client(connectionId).SendAsync("ServerSendMessage", function, parameters);
                return true;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Server Send Message to {connectionId} Error: {ex.Message}, Stack: {ex.StackTrace}";
                _logger.ExceptionLog(errorMessage);
                return false;
            }
        }

        /// <summary>
        /// 更新客户端活动时间
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        public void UpdateClientActivity(string connectionId)
        {
            if (_connectedClients.TryGetValue(connectionId, out var clientInfo))
            {
                clientInfo.LastActivity = DateTime.Now;
            }
        }
    }
}
