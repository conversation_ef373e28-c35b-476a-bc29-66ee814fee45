﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpPortView and List
    [Serializable]
    [Description("Port数据表")]
    [LinqToDB.Mapping.Table("TP_PORT_VIEW")]
    public partial class TpPortView : GEntity<TpPortView>
    {
        #region Contructor(s)

        private TpPortView()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<Int32> pty_No = RegisterProperty<Int32>(p => p.No);
        private static readonly PropertyInfo<String> pty_Name = RegisterProperty<String>(p => p.Name);
        private static readonly PropertyInfo<String> pty_Location = RegisterProperty<String>(p => p.Location);
        private static readonly PropertyInfo<String> pty_UnitType = RegisterProperty<String>(p => p.UnitType);
        private static readonly PropertyInfo<Int32> pty_IsMport = RegisterProperty<Int32>(p => p.IsMport);
        private static readonly PropertyInfo<String> pty_TransferState = RegisterProperty<String>(p => p.TransferState);
        private static readonly PropertyInfo<Int32> pty_IsIdr = RegisterProperty<Int32>(p => p.IsIdr);
        private static readonly PropertyInfo<Int32> pty_Priority = RegisterProperty<Int32>(p => p.Priority);
        private static readonly PropertyInfo<Int32> pty_EqNumber = RegisterProperty<Int32>(p => p.EqNumber);
        private static readonly PropertyInfo<Int32> pty_SpeedXAxis = RegisterProperty<Int32>(p => p.SpeedXAxis);
        private static readonly PropertyInfo<Int32> pty_SpeedYAxis = RegisterProperty<Int32>(p => p.SpeedYAxis);
        private static readonly PropertyInfo<Int32> pty_SpeedZAxis = RegisterProperty<Int32>(p => p.SpeedZAxis);
        private static readonly PropertyInfo<Int32> pty_SpeedTAxis = RegisterProperty<Int32>(p => p.SpeedTAxis);
        private static readonly PropertyInfo<Double> pty_MileageTravel = RegisterProperty<Double>(p => p.MileageTravel);
        private static readonly PropertyInfo<Double> pty_MileageElevation = RegisterProperty<Double>(p => p.MileageElevation);
        private static readonly PropertyInfo<Double> pty_MileageTurn = RegisterProperty<Double>(p => p.MileageTurn);
        private static readonly PropertyInfo<Double> pty_MileageFork = RegisterProperty<Double>(p => p.MileageFork);
        private static readonly PropertyInfo<Int32> pty_BufferCount = RegisterProperty<Int32>(p => p.BufferCount);
        private static readonly PropertyInfo<Int32> pty_CarCount = RegisterProperty<Int32>(p => p.CarCount);
        private static readonly PropertyInfo<Int32> pty_DispColumn = RegisterProperty<Int32>(p => p.DispColumn);
        private static readonly PropertyInfo<Int32> pty_DispRow = RegisterProperty<Int32>(p => p.DispRow);
        private static readonly PropertyInfo<String> pty_CarrierId = RegisterProperty<String>(p => p.CarrierId);
        private static readonly PropertyInfo<Int32> pty_IsOccupied = RegisterProperty<Int32>(p => p.IsOccupied);
        private static readonly PropertyInfo<Int32> pty_IsProhibited = RegisterProperty<Int32>(p => p.IsProhibited);
        private static readonly PropertyInfo<Int32> pty_IsReserved = RegisterProperty<Int32>(p => p.IsReserved);
        private static readonly PropertyInfo<String> pty_ZoneName = RegisterProperty<String>(p => p.ZoneName);
        private static readonly PropertyInfo<String> pty_Comment = RegisterProperty<String>(p => p.Comment);
        private static readonly PropertyInfo<String> pty_TagName = RegisterProperty<String>(p => p.TagName);
        #endregion

        /// <summary>
        /// No
        /// </summary>
        [Description("No")]
        [LinqToDB.Mapping.Column("No")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int32 No
        {
            get { return GetProperty(pty_No); }
            set { SetProperty(pty_No, value); }
        }
        /// <summary>
        /// Name
        /// </summary>
        [Description("Name")]
        [LinqToDB.Mapping.Column("Name")]
        public String Name
        {
            get { return GetProperty(pty_Name); }
            set { SetProperty(pty_Name, value); }
        }
        /// <summary>
        /// Location
        /// </summary>
        [Description("Location")]
        [LinqToDB.Mapping.Column("Location")]
        public String Location
        {
            get { return GetProperty(pty_Location); }
            set { SetProperty(pty_Location, value); }
        }
        /// <summary>
        /// UnitType
        /// </summary>
        [Description("UnitType")]
        [LinqToDB.Mapping.Column("Unit_Type")]
        public String UnitType
        {
            get { return GetProperty(pty_UnitType); }
            set { SetProperty(pty_UnitType, value); }
        }
        /// <summary>
        /// IsMPort
        /// </summary>
        [Description("IsMPort")]
        [LinqToDB.Mapping.Column("Is_MPort")]
        public Int32 IsMport
        {
            get { return GetProperty(pty_IsMport); }
            set { SetProperty(pty_IsMport, value); }
        }
        /// <summary>
        /// TransferState
        /// </summary>
        [Description("TransferState")]
        [LinqToDB.Mapping.Column("Transfer_State")]
        public String TransferState
        {
            get { return GetProperty(pty_TransferState); }
            set { SetProperty(pty_TransferState, value); }
        }
        /// <summary>
        /// IsIDR
        /// </summary>
        [Description("IsIDR")]
        [LinqToDB.Mapping.Column("Is_IDR")]
        public Int32 IsIdr
        {
            get { return GetProperty(pty_IsIdr); }
            set { SetProperty(pty_IsIdr, value); }
        }
        /// <summary>
        /// Priority
        /// </summary>
        [Description("Priority")]
        [LinqToDB.Mapping.Column("Priority")]
        public Int32 Priority
        {
            get { return GetProperty(pty_Priority); }
            set { SetProperty(pty_Priority, value); }
        }
        /// <summary>
        /// EqNumber
        /// </summary>
        [Description("EqNumber")]
        [LinqToDB.Mapping.Column("Eq_Number")]
        public Int32 EqNumber
        {
            get { return GetProperty(pty_EqNumber); }
            set { SetProperty(pty_EqNumber, value); }
        }
        /// <summary>
        /// SpeedXAxis
        /// </summary>
        [Description("SpeedXAxis")]
        [LinqToDB.Mapping.Column("Speed_X_Axis")]
        public Int32 SpeedXAxis
        {
            get { return GetProperty(pty_SpeedXAxis); }
            set { SetProperty(pty_SpeedXAxis, value); }
        }
        /// <summary>
        /// SpeedYAxis
        /// </summary>
        [Description("SpeedYAxis")]
        [LinqToDB.Mapping.Column("Speed_Y_Axis")]
        public Int32 SpeedYAxis
        {
            get { return GetProperty(pty_SpeedYAxis); }
            set { SetProperty(pty_SpeedYAxis, value); }
        }
        /// <summary>
        /// SpeedZAxis
        /// </summary>
        [Description("SpeedZAxis")]
        [LinqToDB.Mapping.Column("Speed_Z_Axis")]
        public Int32 SpeedZAxis
        {
            get { return GetProperty(pty_SpeedZAxis); }
            set { SetProperty(pty_SpeedZAxis, value); }
        }
        /// <summary>
        /// SpeedTAxis
        /// </summary>
        [Description("SpeedTAxis")]
        [LinqToDB.Mapping.Column("Speed_T_Axis")]
        public Int32 SpeedTAxis
        {
            get { return GetProperty(pty_SpeedTAxis); }
            set { SetProperty(pty_SpeedTAxis, value); }
        }
        /// <summary>
        /// MileageTravel
        /// </summary>
        [Description("MileageTravel")]
        [LinqToDB.Mapping.Column("Mileage_Travel")]
        public Double MileageTravel
        {
            get { return GetProperty(pty_MileageTravel); }
            set { SetProperty(pty_MileageTravel, value); }
        }
        /// <summary>
        /// MileageElevation
        /// </summary>
        [Description("MileageElevation")]
        [LinqToDB.Mapping.Column("Mileage_Elevation")]
        public Double MileageElevation
        {
            get { return GetProperty(pty_MileageElevation); }
            set { SetProperty(pty_MileageElevation, value); }
        }
        /// <summary>
        /// MileageTurn
        /// </summary>
        [Description("MileageTurn")]
        [LinqToDB.Mapping.Column("Mileage_Turn")]
        public Double MileageTurn
        {
            get { return GetProperty(pty_MileageTurn); }
            set { SetProperty(pty_MileageTurn, value); }
        }
        /// <summary>
        /// MileageFork
        /// </summary>
        [Description("MileageFork")]
        [LinqToDB.Mapping.Column("Mileage_Fork")]
        public Double MileageFork
        {
            get { return GetProperty(pty_MileageFork); }
            set { SetProperty(pty_MileageFork, value); }
        }
        /// <summary>
        /// BufferCount
        /// </summary>
        [Description("BufferCount")]
        [LinqToDB.Mapping.Column("Buffer_Count")]
        public Int32 BufferCount
        {
            get { return GetProperty(pty_BufferCount); }
            set { SetProperty(pty_BufferCount, value); }
        }
        /// <summary>
        /// CarCount
        /// </summary>
        [Description("CarCount")]
        [LinqToDB.Mapping.Column("Car_Count")]
        public Int32 CarCount
        {
            get { return GetProperty(pty_CarCount); }
            set { SetProperty(pty_CarCount, value); }
        }
        /// <summary>
        /// DispColumn
        /// </summary>
        [Description("DispColumn")]
        [LinqToDB.Mapping.Column("Disp_Column")]
        public Int32 DispColumn
        {
            get { return GetProperty(pty_DispColumn); }
            set { SetProperty(pty_DispColumn, value); }
        }
        /// <summary>
        /// DispRow
        /// </summary>
        [Description("DispRow")]
        [LinqToDB.Mapping.Column("Disp_Row")]
        public Int32 DispRow
        {
            get { return GetProperty(pty_DispRow); }
            set { SetProperty(pty_DispRow, value); }
        }
        /// <summary>
        /// CarrierID
        /// </summary>
        [Description("CarrierID")]
        [LinqToDB.Mapping.Column("Carrier_ID")]
        public String CarrierId
        {
            get { return GetProperty(pty_CarrierId); }
            set { SetProperty(pty_CarrierId, value); }
        }
        /// <summary>
        /// IsOccupied
        /// </summary>
        [Description("IsOccupied")]
        [LinqToDB.Mapping.Column("Is_Occupied")]
        public Int32 IsOccupied
        {
            get { return GetProperty(pty_IsOccupied); }
            set { SetProperty(pty_IsOccupied, value); }
        }
        /// <summary>
        /// IsProhibited
        /// </summary>
        [Description("IsProhibited")]
        [LinqToDB.Mapping.Column("Is_Prohibited")]
        public Int32 IsProhibited
        {
            get { return GetProperty(pty_IsProhibited); }
            set { SetProperty(pty_IsProhibited, value); }
        }
        /// <summary>
        /// IsReserved
        /// </summary>
        [Description("IsReserved")]
        [LinqToDB.Mapping.Column("Is_Reserved")]
        public Int32 IsReserved
        {
            get { return GetProperty(pty_IsReserved); }
            set { SetProperty(pty_IsReserved, value); }
        }
        /// <summary>
        /// ZoneName
        /// </summary>
        [Description("ZoneName")]
        [LinqToDB.Mapping.Column("Zone_Name")]
        public String ZoneName
        {
            get { return GetProperty(pty_ZoneName); }
            set { SetProperty(pty_ZoneName, value); }
        }
        /// <summary>
        /// Comment
        /// </summary>
        [Description("Comment")]
        [LinqToDB.Mapping.Column("Comment")]
        public String Comment
        {
            get { return GetProperty(pty_Comment); }
            set { SetProperty(pty_Comment, value); }
        }
        /// <summary>
        /// TagName
        /// </summary>
        [Description("TagName")]
        [LinqToDB.Mapping.Column("Tag_Name")]
        public String TagName
        {
            get { return GetProperty(pty_TagName); }
            set { SetProperty(pty_TagName, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_No, "No是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Name, 64, "Name不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Location, 5, "Location不能超过5个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_UnitType, 1, "UnitType不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_IsMport, "IsMPort是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_TransferState, 1, "TransferState不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_IsIdr, "IsIDR是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Priority, "Priority是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_EqNumber, "EqNumber是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_SpeedXAxis, "SpeedXAxis是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_SpeedYAxis, "SpeedYAxis是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_SpeedZAxis, "SpeedZAxis是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_SpeedTAxis, "SpeedTAxis是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_MileageTravel, "MileageTravel是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_MileageElevation, "MileageElevation是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_MileageTurn, "MileageTurn是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_MileageFork, "MileageFork是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_BufferCount, "BufferCount是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CarCount, "CarCount是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_DispColumn, "DispColumn是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_DispRow, "DispRow是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CarrierId, 255, "CarrierID不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_IsOccupied, "IsOccupied是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_IsProhibited, "IsProhibited是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_IsReserved, "IsReserved是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_ZoneName, 255, "ZoneName不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Comment, 255, "Comment不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_TagName, "TagName是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_TagName, 64, "TagName不能超过64个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.No.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class TpPortViewList : GEntityList<TpPortViewList, TpPortView>
    {
        private TpPortViewList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
