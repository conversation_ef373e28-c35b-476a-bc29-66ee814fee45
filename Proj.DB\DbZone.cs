﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Proj.Entity;
namespace Proj.DB
{
    public class DbZone
    {
        private static DbZone m_Instanse;
        private static readonly object mSyncObject = new object();
        private DbZone() { }
        public static DbZone Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new DbZone();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public bool Exists(string zoneName)
        {
            try
            {
                return TpZone.Exists(zoneName);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbZone.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public TpZoneList GetDbZoneList()
        {
            try
            {
                TpZoneList tpZoneList = TpZoneList.GetAll();
                return tpZoneList;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbZone.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return null;
        }
    }
}
