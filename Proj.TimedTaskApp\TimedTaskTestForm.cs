﻿using System.Windows.Forms;
using Proj.Entity;
using System;
using System.Collections;
using System.IO;
using System.Reflection;
using System.ServiceProcess;
using System.Threading;
using System.Xml;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using SS.Base;
using SS.Entity;
using SS.CslaBase;
using SS.BLL.Base;
using Proj.TimedTaskApp.Base;

namespace Proj.TimedTaskApp
{
    public partial class TimedTaskTestForm : Form
    {
        //用哈希表存放任务项
        private Hashtable hashJobs;
        private bool isRunning = true;
        private string Log = System.Windows.Forms.Application.StartupPath.ToString() + @"\" + "AppLog.txt";

        public TimedTaskTestForm()
        {
            InitializeComponent();
        }

        private void TimedTaskTestForm_Load(object sender, EventArgs e)
        {
            //启动服务
            this.runJobs();
        }

        #region 自定义方法

        private void restartJobs()
        {
            try
            {
                TntJobsgroupList list = TntJobsgroupList.GetByLambda(x => x.CEnabled.Equals("1"));
                TimedTaskApp.Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + "\\" + "Error.txt", list.Count.ToString());

                foreach (TntJobsgroup item in list)
                {
                    if (!this.hashJobs.ContainsKey(item.CPk))
                    {
                        //创建工作对象
                        Job job = new TimedTaskApp.Base.Job(item);
                        //将工作对象加载进HashTable
                        this.hashJobs.Add(item.CPk, job);
                        System.Threading.ThreadPool.QueueUserWorkItem(threadCallBack, item.CPk);
                    }
                    else
                    {
                        (this.hashJobs[item.CPk] as Job).ConfigObject = item;
                    }
                }
                // 获取失效列表
                TntJobsgroupList disablelist = TntJobsgroupList.GetByLambda(x => x.CEnabled.Equals("0"));
                foreach (TntJobsgroup item in disablelist)
                {
                    if (this.hashJobs.ContainsKey(item.CPk))
                    {
                        // 移除失效列表
                        this.hashJobs.Remove(item.CPk);
                    }
                }
            }
            catch (Exception error)
            {
                TimedTaskApp.Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + "\\" + "Error.txt", error.ToString());
            }
        }

        private void runJobs()
        {
            try
            {
                //加载工作项
                if (this.hashJobs == null || this.hashJobs.Count == 0)
                {
                    hashJobs = new Hashtable();
                    TntJobsgroupList list = TntJobsgroupList.GetByLambda(x => x.CEnabled.Equals("1"));
                    TimedTaskApp.Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + "\\" + "Error.txt", "--" + list.Count.ToString());

                    foreach (TntJobsgroup item in list)
                    {
                        //创建工作对象
                        Job job = new TimedTaskApp.Base.Job(item);

                        //将工作对象加载进HashTable
                        this.hashJobs.Add(item.CPk, job);
                    }
                }

                //执行工作项
                if (this.hashJobs.Keys.Count > 0)
                {
                    foreach (TimedTaskApp.Base.Job job in hashJobs.Values)
                    {
                        //插入一个新的请求到线程池
                        if (System.Threading.ThreadPool.QueueUserWorkItem(threadCallBack, job.ConfigObject.CPk))
                        {
                            //方法成功排入队列
                        }
                        else
                        {
                            //失败
                        }
                    }
                }
            }
            catch (Exception error)
            {
                TimedTaskApp.Base.ServiceTools.WriteLog(System.Windows.Forms.Application.StartupPath.ToString() + "\\" + "Error.txt", error.ToString());
            }
        }

        private void stopJobs()
        {
            //停止
            if (this.hashJobs != null)
            {
                this.hashJobs.Clear();
            }
        }

        /// <summary>
        /// 线程池回调方法
        /// </summary>
        /// <param name="state"></param>
        private void threadCallBack(Object state)
        {
            while (true)
            {
                if (isRunning)
                {
                    if (this.hashJobs.ContainsKey(state))
                    {
                        ((TimedTaskApp.Base.Job)this.hashJobs[state]).StartJob();
                    }
                }
                //休眠1秒
                Thread.Sleep(500);
            }
        }

        #endregion
    }
}
