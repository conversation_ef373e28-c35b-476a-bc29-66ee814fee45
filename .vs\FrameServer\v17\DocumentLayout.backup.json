{"Version": 1, "WorkspaceRootPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B7EA6F6D-7211-4B63-91B1-97C2E99FF3C6}|Proj.Business\\Proj.Business.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.business\\timedtask\\logbacker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{161B2CBF-228B-4593-AFFC-180E52A0DC6F}|Proj.HostComm\\Proj.HostComm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.hostcomm\\hostif.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FFDD195B-6BD1-7BDA-AF20-080E2BEEC143}|Proj.API\\Proj.API.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.api\\gemequipmentimpl.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FFDD195B-6BD1-7BDA-AF20-080E2BEEC143}|Proj.API\\Proj.API.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.api\\igemequipment.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BBED1B4D-4443-4094-B43E-2EB885442078}|Proj.DevComm\\Proj.DevComm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.devcomm\\plccomm.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BBED1B4D-4443-4094-B43E-2EB885442078}|Proj.DevComm\\Proj.DevComm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.devcomm\\plctools.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2BA9E46C-12F0-4F1B-8635-00658148B733}|Proj.DB\\Proj.DB.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.db\\dbcarrier.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2BA9E46C-12F0-4F1B-8635-00658148B733}|Proj.DB\\Proj.DB.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.db\\dbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "LogBacker.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Business\\TimedTask\\LogBacker.cs", "RelativeDocumentMoniker": "Proj.Business\\TimedTask\\LogBacker.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Business\\TimedTask\\LogBacker.cs [只读]", "RelativeToolTip": "Proj.Business\\TimedTask\\LogBacker.cs [只读]", "ViewState": "AgIAAN4AAAAAAAAAAAApwPcAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T03:59:12.611Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 2, "Title": "GemEquipmentImpl.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\GemEquipmentImpl.cs", "RelativeDocumentMoniker": "Proj.API\\GemEquipmentImpl.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\GemEquipmentImpl.cs", "RelativeToolTip": "Proj.API\\GemEquipmentImpl.cs", "ViewState": "AgIAANQFAAAAAAAAAAAswAEGAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T02:10:16.862Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IGemEquipment.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\IGemEquipment.cs", "RelativeDocumentMoniker": "Proj.API\\IGemEquipment.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\IGemEquipment.cs", "RelativeToolTip": "Proj.API\\IGemEquipment.cs", "ViewState": "AgIAAGEAAAAAAAAAAAAqwG8AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T01:36:37.324Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "HostIF.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.HostComm\\HostIF.cs", "RelativeDocumentMoniker": "Proj.HostComm\\HostIF.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.HostComm\\HostIF.cs", "RelativeToolTip": "Proj.HostComm\\HostIF.cs", "ViewState": "AgIAAB4BAAAAAAAAAAAQwDMBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T01:24:20.418Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "PLCTools.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevComm\\PLCTools.cs", "RelativeDocumentMoniker": "Proj.<PERSON>\\PLCTools.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevComm\\PLCTools.cs [只读]", "RelativeToolTip": "Proj.<PERSON>\\PLCTools.cs [只读]", "ViewState": "AgIAAOAAAAAAAAAAAAAQwHIAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:52:25.67Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 4, "Title": "PlcComm.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevComm\\PlcComm.cs", "RelativeDocumentMoniker": "Proj.<PERSON>\\PlcComm.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevComm\\PlcComm.cs", "RelativeToolTip": "Proj.<PERSON>\\PlcComm.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAAAC0AAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T10:49:11.31Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "DbCarrier.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbCarrier.cs", "RelativeDocumentMoniker": "Proj.DB\\DbCarrier.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbCarrier.cs [只读]", "RelativeToolTip": "Proj.DB\\DbCarrier.cs [只读]", "ViewState": "AgIAAFYAAAAAAAAAAAAtwGAAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:41:14.499Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 7, "Title": "DbContext.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbContext.cs", "RelativeDocumentMoniker": "Proj.DB\\DbContext.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbContext.cs", "RelativeToolTip": "Proj.DB\\DbContext.cs", "ViewState": "AgIAABYAAAAAAAAAAAAAwAMAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:40:35.17Z"}]}]}]}