{"Version": 1, "WorkspaceRootPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{26C85E3D-9F87-40B5-936C-7D0D9256F491}|Proj.Entity\\Proj.Entity.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.entity\\linqtodb\\mappingcompatibility.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54D8C498-C39D-41A3-923D-DBC30CD7FCD1}|TestForm\\TestForm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\testform\\looprunfrm.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6F928C4A-9B51-47EB-8A33-48985A87FE26}|Proj.UnitMng\\Proj.UnitMng.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.unitmng\\proj.unitmng.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{744C22A5-3134-4928-85F5-E599CC2E5B2B}|Proj.TimedTaskApp\\Proj.TimedTaskApp.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.timed<PERSON><PERSON><PERSON>\\base\\servicetools.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F7AFD4B5-4814-48AA-8FC9-15526A2BC340}|Proj.Setting\\Proj.Setting.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.setting\\proj.setting.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{161B2CBF-228B-4593-AFFC-180E52A0DC6F}|Proj.HostComm\\Proj.HostComm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.hostcomm\\hostif.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B558CAF-EFE1-4A05-8AD1-7242C65BD245}|Proj.History\\Proj.History.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.history\\historywriter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2BA9E46C-12F0-4F1B-8635-00658148B733}|Proj.DB\\Proj.DB.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.db\\dbtransfer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2BA9E46C-12F0-4F1B-8635-00658148B733}|Proj.DB\\Proj.DB.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.db\\dbtrans.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2BA9E46C-12F0-4F1B-8635-00658148B733}|Proj.DB\\Proj.DB.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.db\\dblocation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2BA9E46C-12F0-4F1B-8635-00658148B733}|Proj.DB\\Proj.DB.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.db\\dbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2BA9E46C-12F0-4F1B-8635-00658148B733}|Proj.DB\\Proj.DB.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.db\\proj.db.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{75FC8249-8F23-42AE-9B9E-50EEA5D5429A}|Proj.Controller\\Proj.Controller.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.controller\\proj.controller.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{B7EA6F6D-7211-4B63-91B1-97C2E99FF3C6}|Proj.Business\\Proj.Business.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.business\\proj.business.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{ECF4DD08-C496-40A8-811A-99AA0D344918}|Proj.Alarm\\Proj.Alarm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.alarm\\alarmcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B558CAF-EFE1-4A05-8AD1-7242C65BD245}|Proj.History\\Proj.History.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.history\\proj.history.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{ECF4DD08-C496-40A8-811A-99AA0D344918}|Proj.Alarm\\Proj.Alarm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.alarm\\proj.alarm.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{FFDD195B-6BD1-7BDA-AF20-080E2BEEC143}|Proj.API\\Proj.API.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.api\\proj.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{FFDD195B-6BD1-7BDA-AF20-080E2BEEC143}|Proj.API\\Proj.API.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.api\\proj.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{26C85E3D-9F87-40B5-936C-7D0D9256F491}|Proj.Entity\\Proj.Entity.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.entity\\proj.entity.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{ECF4DD08-C496-40A8-811A-99AA0D344918}|Proj.Alarm\\Proj.Alarm.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.alarm\\proj.alarm.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MappingCompatibility.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Entity\\LinqToDB\\MappingCompatibility.cs", "RelativeDocumentMoniker": "Proj.Entity\\LinqToDB\\MappingCompatibility.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Entity\\LinqToDB\\MappingCompatibility.cs", "RelativeToolTip": "Proj.Entity\\LinqToDB\\MappingCompatibility.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:39:38.123Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "LooprunFrm.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\TestForm\\LooprunFrm.cs", "RelativeDocumentMoniker": "TestForm\\LooprunFrm.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\TestForm\\LooprunFrm.cs [只读]", "RelativeToolTip": "TestForm\\LooprunFrm.cs [只读]", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:37:11.186Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Proj.<PERSON><PERSON>ng", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.UnitMng\\Proj.UnitMng.csproj", "RelativeDocumentMoniker": "Proj.UnitMng\\Proj.UnitMng.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.UnitMng\\Proj.UnitMng.csproj", "RelativeToolTip": "Proj.UnitMng\\Proj.UnitMng.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T09:36:15.092Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ServiceTools.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.TimedTaskApp\\Base\\ServiceTools.cs", "RelativeDocumentMoniker": "Proj.TimedTaskApp\\Base\\ServiceTools.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.TimedTaskApp\\Base\\ServiceTools.cs [只读]", "RelativeToolTip": "Proj.TimedTaskApp\\Base\\ServiceTools.cs [只读]", "ViewState": "AgIAAA0AAAAAAAAAAAAkwA4AAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:19:08.514Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 4, "Title": "Proj.<PERSON>", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Setting\\Proj.Setting.csproj", "RelativeDocumentMoniker": "Proj.Setting\\Proj.Setting.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Setting\\Proj.Setting.csproj", "RelativeToolTip": "Proj.Setting\\Proj.Setting.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T09:18:30.005Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "HostIF.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.HostComm\\HostIF.cs", "RelativeDocumentMoniker": "Proj.HostComm\\HostIF.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.HostComm\\HostIF.cs [只读]", "RelativeToolTip": "Proj.HostComm\\HostIF.cs [只读]", "ViewState": "AgIAACsCAAAAAAAAAAAkwDUCAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:15:22.223Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 6, "Title": "HistoryWriter.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.History\\HistoryWriter.cs", "RelativeDocumentMoniker": "Proj.History\\HistoryWriter.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.History\\HistoryWriter.cs [只读]", "RelativeToolTip": "Proj.History\\HistoryWriter.cs [只读]", "ViewState": "AgIAADwAAAAAAAAAAAAkwEIAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:13:14.535Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 7, "Title": "DbTransfer.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbTransfer.cs", "RelativeDocumentMoniker": "Proj.DB\\DbTransfer.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbTransfer.cs", "RelativeToolTip": "Proj.DB\\DbTransfer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:08:29.281Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "DbTrans.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbTrans.cs", "RelativeDocumentMoniker": "Proj.DB\\DbTrans.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbTrans.cs", "RelativeToolTip": "Proj.DB\\DbTrans.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:07:20.188Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "DbLocation.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbLocation.cs", "RelativeDocumentMoniker": "Proj.DB\\DbLocation.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbLocation.cs", "RelativeToolTip": "Proj.DB\\DbLocation.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:06:50.047Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "DbContext.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbContext.cs", "RelativeDocumentMoniker": "Proj.DB\\DbContext.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\DbContext.cs [只读]", "RelativeToolTip": "Proj.DB\\DbContext.cs [只读]", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T09:06:41.843Z", "EditorCaption": " [只读]"}, {"$type": "Document", "DocumentIndex": 11, "Title": "Proj.<PERSON>", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\Proj.DB.csproj", "RelativeDocumentMoniker": "Proj.DB\\Proj.DB.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DB\\Proj.DB.csproj", "RelativeToolTip": "Proj.DB\\Proj.DB.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T09:06:10.328Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "Proj.Controller", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Controller\\Proj.Controller.csproj", "RelativeDocumentMoniker": "Proj.Controller\\Proj.Controller.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Controller\\Proj.Controller.csproj", "RelativeToolTip": "Proj.Controller\\Proj.Controller.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T09:03:14.985Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "Proj.Business", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Business\\Proj.Business.csproj", "RelativeDocumentMoniker": "Proj.Business\\Proj.Business.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Business\\Proj.Business.csproj", "RelativeToolTip": "Proj.Business\\Proj.Business.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:59:24.271Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "Proj.History", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.History\\Proj.History.csproj", "RelativeDocumentMoniker": "Proj.History\\Proj.History.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.History\\Proj.History.csproj", "RelativeToolTip": "Proj.History\\Proj.History.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:54:59.568Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "Proj.<PERSON>", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Alarm\\Proj.Alarm.csproj", "RelativeDocumentMoniker": "Proj.<PERSON>\\Proj.Alarm.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Alarm\\Proj.Alarm.csproj", "RelativeToolTip": "Proj.<PERSON>\\Proj.Alarm.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:50:27.177Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "Proj.API", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj", "RelativeDocumentMoniker": "Proj.API\\Proj.API.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj", "RelativeToolTip": "Proj.API\\Proj.API.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:50:14.896Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "Proj.API", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj", "RelativeDocumentMoniker": "Proj.API\\Proj.API.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj", "RelativeToolTip": "Proj.API\\Proj.API.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:50:16.443Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "AlarmController.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Alarm\\AlarmController.cs", "RelativeDocumentMoniker": "Proj.Al<PERSON>\\AlarmController.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Alarm\\AlarmController.cs", "RelativeToolTip": "Proj.Al<PERSON>\\AlarmController.cs", "ViewState": "AgIAAAEBAAAAAAAAAAAcwBQBAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:49:45.365Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "Proj.<PERSON>", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Alarm\\Proj.Alarm.csproj", "RelativeDocumentMoniker": "Proj.<PERSON>\\Proj.Alarm.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Alarm\\Proj.Alarm.csproj", "RelativeToolTip": "Proj.<PERSON>\\Proj.Alarm.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:46:55.067Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "Proj.<PERSON><PERSON><PERSON>", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Entity\\Proj.Entity.csproj", "RelativeDocumentMoniker": "Proj.<PERSON><PERSON><PERSON>\\Proj.Entity.csproj", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Entity\\Proj.Entity.csproj", "RelativeToolTip": "Proj.<PERSON><PERSON><PERSON>\\Proj.Entity.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T08:46:49.99Z", "EditorCaption": ""}]}]}]}