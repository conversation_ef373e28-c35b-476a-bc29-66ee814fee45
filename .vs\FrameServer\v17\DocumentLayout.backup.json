{"Version": 1, "WorkspaceRootPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6F928C4A-9B51-47EB-8A33-48985A87FE26}|Proj.UnitMng\\Proj.UnitMng.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.unitmng\\craneobj.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1E67258-9C2E-4C24-AB7B-F6E5D8C34575}|Proj.DevObj\\Proj.DevObj.csproj|\\\\vmware-host\\shared folders\\gitsource\\stkc.net8\\proj.devobj\\craneobj.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "CraneObj.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.UnitMng\\CraneObj.cs", "RelativeDocumentMoniker": "Proj.Unit<PERSON>ng\\CraneObj.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.UnitMng\\CraneObj.cs*", "RelativeToolTip": "Proj.<PERSON>\\CraneObj.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:39:24.989Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CraneObj.cs", "DocumentMoniker": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevObj\\CraneObj.cs", "RelativeDocumentMoniker": "Proj.<PERSON>bj\\CraneObj.cs", "ToolTip": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.DevObj\\CraneObj.cs", "RelativeToolTip": "Proj.<PERSON>bj\\CraneObj.cs", "ViewState": "AgIAANYBAAAAAAAAAAAqwNgBAABoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:23:40.005Z", "EditorCaption": ""}]}]}]}