﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpAlarmConfig and List
    [Serializable]
    [Description("TP_ALARM_CONFIG")]
    [LinqToDB.Mapping.Table("TP_ALARM_CONFIG")]
    public partial class TpAlarmConfig : GEntity<TpAlarmConfig>
    {
        #region Contructor(s)

        private TpAlarmConfig()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<Int32> pty_Code = RegisterProperty<Int32>(p => p.Code);
        private static readonly PropertyInfo<Int32?> pty_Enabled = RegisterProperty<Int32?>(p => p.Enabled);
        private static readonly PropertyInfo<String> pty_Level = RegisterProperty<String>(p => p.Level);
        private static readonly PropertyInfo<String> pty_Solution = RegisterProperty<String>(p => p.Solution);
        private static readonly PropertyInfo<String> pty_Text = RegisterProperty<String>(p => p.Text);
        private static readonly PropertyInfo<String> pty_Type = RegisterProperty<String>(p => p.Type);
        private static readonly PropertyInfo<String> pty_Unit = RegisterProperty<String>(p => p.Unit);
        #endregion

        /// <summary>
        /// AlarmCode
        /// </summary>
        [Description("AlarmCode")]
        [LinqToDB.Mapping.Column("Code")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int32 Code
        {
            get { return GetProperty(pty_Code); }
            set { SetProperty(pty_Code, value); }
        }
        /// <summary>
        /// Enabled
        /// </summary>
        [Description("Enabled")]
        [LinqToDB.Mapping.Column("Enabled")]
        public Int32? Enabled
        {
            get { return GetProperty(pty_Enabled); }
            set { SetProperty(pty_Enabled, value); }
        }
        /// <summary>
        /// AlarmLevel
        /// </summary>
        [Description("AlarmLevel")]
        [LinqToDB.Mapping.Column("Level")]
        public String Level
        {
            get { return GetProperty(pty_Level); }
            set { SetProperty(pty_Level, value); }
        }
        /// <summary>
        /// Solution
        /// </summary>
        [Description("Solution")]
        [LinqToDB.Mapping.Column("Solution")]
        public String Solution
        {
            get { return GetProperty(pty_Solution); }
            set { SetProperty(pty_Solution, value); }
        }
        /// <summary>
        /// AlarmText
        /// </summary>
        [Description("AlarmText")]
        [LinqToDB.Mapping.Column("Text")]
        public String Text
        {
            get { return GetProperty(pty_Text); }
            set { SetProperty(pty_Text, value); }
        }
        /// <summary>
        /// AlarmType
        /// </summary>
        [Description("AlarmType")]
        [LinqToDB.Mapping.Column("Type")]
        public String Type
        {
            get { return GetProperty(pty_Type); }
            set { SetProperty(pty_Type, value); }
        }
        /// <summary>
        /// AlarmUnit
        /// </summary>
        [Description("AlarmUnit")]
        [LinqToDB.Mapping.Column("Unit")]
        public String Unit
        {
            get { return GetProperty(pty_Unit); }
            set { SetProperty(pty_Unit, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Code, "AlarmCode是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Level, 32, "AlarmLevel不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Solution, 255, "Solution不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Text, 255, "AlarmText不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Type, 32, "AlarmType不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Unit, 32, "AlarmUnit不能超过32个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Code.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class TpAlarmConfigList : GEntityList<TpAlarmConfigList, TpAlarmConfig>
    {
        private TpAlarmConfigList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
