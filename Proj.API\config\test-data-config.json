{"StatusVariables": [{"Id": 1, "Name": "CLOCK", "Description": "Equipment Clock", "DataType": "A", "DefaultValue": "2024-01-01T00:00:00"}, {"Id": 2, "Name": "CONTROL_STATE", "Description": "Equipment Control State", "DataType": "U1", "DefaultValue": 1}, {"Id": 3, "Name": "PREVIOUS_CONTROL_STATE", "Description": "Previous Equipment Control State", "DataType": "U1", "DefaultValue": 0}, {"Id": 4, "Name": "EVENTS_ENABLED", "Description": "Events Enabled", "DataType": "BOOLEAN", "DefaultValue": true}, {"Id": 5, "Name": "ALARMS_ENABLED", "Description": "Alarms Enabled", "DataType": "BOOLEAN", "DefaultValue": true}], "DataVariables": [{"Id": 1001, "Name": "RECIPE_NAME", "Description": "Current Recipe Name", "DataType": "A", "DefaultValue": "DEFAULT_RECIPE"}, {"Id": 1002, "Name": "LOT_ID", "Description": "Current Lot ID", "DataType": "A", "DefaultValue": "LOT001"}, {"Id": 1003, "Name": "WAFER_COUNT", "Description": "Wafer Count in Lot", "DataType": "U4", "DefaultValue": 25}, {"Id": 1004, "Name": "PROCESS_TIME", "Description": "Process Time in Seconds", "DataType": "U4", "DefaultValue": 3600}, {"Id": 1005, "Name": "TEMPERATURE", "Description": "Chamber Temperature", "DataType": "F4", "DefaultValue": 25.5}], "Reports": [{"Id": 1, "Name": "EQUIPMENT_STATUS_REPORT", "Description": "Basic equipment status information", "VariableIds": [1, 2, 4]}, {"Id": 2, "Name": "SYSTEM_STATUS_REPORT", "Description": "System level status information", "VariableIds": [3, 5]}, {"Id": 1001, "Name": "PROCESS_RECIPE_REPORT", "Description": "Current recipe and batch information", "VariableIds": [1001, 1002]}, {"Id": 1002, "Name": "PROCESS_WAFER_REPORT", "Description": "Wafer count and process information", "VariableIds": [1003, 1004]}, {"Id": 1003, "Name": "PROCESS_TEMPERATURE_REPORT", "Description": "Temperature and environmental data", "VariableIds": [1005]}, {"Id": 1004, "Name": "PROCESS_TIMING_REPORT", "Description": "Process timing information", "VariableIds": [1004]}, {"Id": 1005, "Name": "PROCESS_ENVIRONMENT_REPORT", "Description": "Environmental conditions", "VariableIds": [1005]}], "Events": [{"Id": 100, "Name": "EQUIPMENT_OFFLINE", "Description": "Equipment went offline", "ReportIds": [1, 2]}, {"Id": 101, "Name": "EQUIPMENT_ONLINE", "Description": "Equipment came online", "ReportIds": [1, 2]}, {"Id": 200, "Name": "PROCESS_START", "Description": "Process started", "ReportIds": [1001, 1002, 1003]}, {"Id": 201, "Name": "PROCESS_END", "Description": "Process completed", "ReportIds": [1001, 1004, 1005]}, {"Id": 300, "Name": "ALARM_SET", "Description": "Alarm was set", "ReportIds": [2]}], "Alarms": [{"Id": 1001, "Name": "TEMPERATURE_HIGH", "Description": "Chamber temperature too high", "Text": "Temperature exceeded maximum limit", "IsEnabled": true}, {"Id": 1002, "Name": "PRESSURE_LOW", "Description": "Chamber pressure too low", "Text": "Pressure below minimum threshold", "IsEnabled": true}, {"Id": 1003, "Name": "VACUUM_LOSS", "Description": "Vacuum system failure", "Text": "Vacuum system malfunction detected", "IsEnabled": true}, {"Id": 1004, "Name": "DOOR_OPEN", "Description": "Chamber door is open", "Text": "Chamber door opened during process", "IsEnabled": true}, {"Id": 1005, "Name": "POWER_FAILURE", "Description": "Power supply failure", "Text": "Main power supply interrupted", "IsEnabled": true}], "EquipmentConstants": [{"Id": 1, "Name": "MAX_TEMPERATURE", "Description": "Maximum allowed temperature", "DataType": "F4", "DefaultValue": 100.0, "MinValue": 0.0, "MaxValue": 200.0}, {"Id": 2, "Name": "MIN_PRESSURE", "Description": "Minimum required pressure", "DataType": "F4", "DefaultValue": 1.0, "MinValue": 0.1, "MaxValue": 10.0}, {"Id": 3, "Name": "PROCESS_TIMEOUT", "Description": "Process timeout in seconds", "DataType": "U4", "DefaultValue": 7200, "MinValue": 60, "MaxValue": 86400}, {"Id": 4, "Name": "MAX_WAFER_COUNT", "Description": "Maximum wafers per lot", "DataType": "U4", "DefaultValue": 25, "MinValue": 1, "MaxValue": 50}]}