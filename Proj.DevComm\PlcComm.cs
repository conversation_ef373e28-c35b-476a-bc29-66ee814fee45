using System;
using System.Collections.Generic;
using System.Linq;

using Proj.History;
using Proj.Log;

namespace Proj.DevComm
{
    public class PlcComm
    {
        #region 单实例
        private static PlcComm m_Instanse = null;
        private static object mSyncObject = new object();
        private object m_lockObj = new object();
        private PlcComm(){ }
        public static PlcComm Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new PlcComm();
                        }
                    }
                }
                return m_Instanse;
            }
        }
        #endregion

        private List<string> listDeviceNames = new List<string>(); //用于控制多个硬件设备
        private string masterDevName = "FinsEthernet1";  //用于控制一个主设备，如Stocker的Master PLC
        private int historyDays = 0;

        /// <summary>
        /// 加载设备的配置信息
        /// </summary>
        /// <returns></returns>
        private int loadDeviceConfigInfo()
        {
            listDeviceNames.Clear();
            //以下内容可以从配置文件或数据库中获取
            listDeviceNames.Add("FinsEthernet1");
            masterDevName = "FinsEthernet1";
            historyDays = 90;
            return listDeviceNames.Count;
        }

        public string GetMasterDevName()
        {
            return this.masterDevName;
        }
        
        /// <summary>
        /// 启动PLC通信--与一个Master设备通信的情况
        /// </summary>
        /// <returns>是否成功</returns>
        public bool Start()
        {
            loadDeviceConfigInfo();

            DateTime startTime = DateTime.Now;
            int iRes = ProjectAPI.Run();
            DateTime endTime = DateTime.Now;
            TimeSpan time = endTime - startTime;
            Log.Logger.Instance.EventLog("PLC Start，API.Run Time：" + time.TotalSeconds.ToString("0.00") + "s");

            if (0 != iRes)
            {
                //记录程序异常日志:ProjectAPI启动失败  ProjectAPI Run Failed
                Log.Logger.Instance.ExceptionLog($"ProjectAPI Run Failed{iRes}");
                return false;
            }
            if (string.IsNullOrEmpty(masterDevName))
            {
                //记录程序异常日志:未获取到设备名称 Not Get Device Names
                Log.Logger.Instance.ExceptionLog($"Not Get Device Names");
                ProjectAPI.Exit();
                return false;
            }
            //try
            //{
            //    IOAPI.StartIO();
            //}
            //catch (Exception ex)
            //{
            //    //记录程序异常日志：Start IO Failed {ex.Message}
            //    Log.Logger.Instance.ExceptionLog("PlcComm.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            //    ProjectAPI.Exit();
            //    return false;
            //}
            //记录Event日志:PLC通信启动成功  PLC Communication Started
            HistoryWriter.Instance.EqpEventLog(masterDevName, 0, "PLCCommunicationStarted", "PLC Communication Started");
            return true;
        }

        /// <summary>
        /// 启动PLC通信--与多个PLC通信的情况
        /// </summary>
        /// <returns></returns>
        //public bool Starts()
        //{
        //    int iDevCount = loadDeviceConfigInfo();
        //    if (iDevCount == 0)
        //    {
        //        //记录PLC日志:未获取到设备名称 Not Get Device Names
        //        return false;
        //    }
        //    int iRes = ProjectAPI.Run();
        //    if (0 != iRes)
        //    {
        //        //记录程序异常日志:ProjectAPI启动失败  ProjectAPI Run Failed
        //        Log.Logger.Instance.ExceptionLog($"ProjectAPI Run Failed{iRes}");
        //        return false;
        //    }
        //    try
        //    {
        //        IOAPI.StartIO();
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志：Start IO Failed {ex.Message}
        //        Log.Logger.Instance.ExceptionLog("PlcComm.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
        //        ProjectAPI.Exit();
        //        return false;
        //    }
        //    for (int i = 0; i < iDevCount; i++)
        //    {
        //        iRes = IOAPI.StartDevice(listDeviceNames[i]);
        //        if (0 != iRes)
        //        {
        //            //记录PLC日志:设备启动失败{设备名,结果码}  Device Start Failed{DevceName,ResultCode}
        //            IOAPI.StopIO();
        //            ProjectAPI.Exit();
        //            return false;
        //        }
        //    }
        //    //记录PLC日志:PLC通信启动成功  PLC Communication Started

        //    return true;
        //}

        /// <summary>
        /// 停止PLC通信
        /// </summary>
        public void Stop()
        {
            //IOAPI.StopIO();
            if(ProjectAPI.IsRunning)
            {
                ProjectAPI.Exit();
            }
            
            //记录Event日志:PLC通信停止  PLC Communication Stoped
            HistoryWriter.Instance.EqpEventLog(masterDevName, 0, "PLCCommunicationStoped", "PLC Communication Stoped");
        }

        /// <summary>
        /// 启动历史数据记录
        /// </summary>
        public void StartHistoryDataRecord()
        {
            if (historyDays > 0)
            {
                TagAPI.SetHistoryLength(historyDays);
                bool bRes = TagAPI.StartRecord();
                //记录PLC日志:历史数据记录是否成功启动{bRes}  Is History Data Record Start Successful{bRes}
                HistoryWriter.Instance.EqpEventLog(masterDevName, 0, "HistoryRecordStart", $"Is History Data Record Start Successful{bRes}");
            }
        }

        /// <summary>
        /// 停止历史数据记录
        /// </summary>
        public void StopHistoryDataRecord()
        {
            TagAPI.StopRecord();
            //记录PLC日志:历史数据记录停止  History Data Record Stoped
            HistoryWriter.Instance.EqpEventLog(masterDevName, 0, "HistoryRecordStoped", " History Data Record Stoped");
        }

        /// <summary>
        /// 从设备变量（IO）中读取变量值
        /// </summary>
        /// <param name="devName">设备名</param>
        /// <param name="tagName">标签名</param>
        /// <returns></returns>
        public object GetIOValue(string devName, string tagName)
        {
            DateTime startTime = DateTime.Now;
            object objValue = IOAPI.GetValue(devName + "." + tagName);
            TimeSpan time = DateTime.Now - startTime;
            int iMilliseconds = Convert.ToInt32(time.TotalMilliseconds);
            //记录PLC日志:获取PLC变量值{DeviceName=,value=}   Get PLC Variable Value {DeviceName=,value=}
            if (objValue == null)
            {
                HistoryWriter.Instance.PLCLog(masterDevName, "ReadFailed", tagName + " time: " + iMilliseconds.ToString(), "null");
            }
            else
            {
                //HistoryWriter.Instance.PLCLog(masterDevName, "ReadSuccess", tagName + " time: " + iMilliseconds.ToString(), objValue.ToString());
            }
            return objValue;
        }
        /// <summary>
        /// 从设备变量（IO）中读取变量值
        /// </summary>
        /// <param name="tagName">标签名</param>
        /// <returns></returns>
        public object GetIOValue(string tagName)
        {
            DateTime startTime = DateTime.Now;
            object objValue = IOAPI.GetValue(this.masterDevName + "." + tagName);//设备名称+点位名称
            TimeSpan time = DateTime.Now - startTime;
            int iMilliseconds = Convert.ToInt32(time.TotalMilliseconds);
            //记录PLC日志:获取PLC变量值{DeviceName=,value=}   Get PLC Variable Value {DeviceName=,value=}
            if (objValue == null)
            {
                HistoryWriter.Instance.PLCLog(masterDevName, "ReadFailed", tagName + " time: " + iMilliseconds.ToString(), "null");
            }
            //else
            //{
            //    HistoryWriter.Instance.PLCLog(masterDevName, "ReadSuccess", tagName + " time: " + iMilliseconds.ToString(), objValue.ToString());
            //}
            return objValue;
        }

        /// <summary>
        /// 从变量数据库中（内存中）读取变量值
        /// </summary>
        /// <param name="paramName">变量名</param>
        /// <returns></returns>
        public object GetTagValue(string paramName)
        {
            DateTime startTime = DateTime.Now;
            object objValue = TagAPI.GetValue("Tag." + paramName);
            TimeSpan time = DateTime.Now - startTime;
            int iMilliseconds = Convert.ToInt32(time.TotalMilliseconds);
            //记录PLC日志:获取Tag变量值{DeviceName=,value=}   Get Tag Variable Value {DeviceName=,value=}
            if (objValue == null)
            {
                HistoryWriter.Instance.PLCLog(masterDevName, "ReadFailed", paramName + " time: " + iMilliseconds.ToString(), "null");
            }
            else
            {
                //HistoryWriter.Instance.PLCLog(masterDevName, "ReadSuccess", paramName + " time: " + iMilliseconds.ToString(), objValue.ToString());
            }
            return objValue;
        }

        /// <summary>
        /// 写设备变量（IO）中的变量(测试OK)
        /// </summary>
        /// <param name="tagName">标签名</param>
        /// <param name="value">写入值</param>
        /// <returns></returns>
        public bool WriteIOValue(string tagName, object value)
        {
            DateTime startTime = DateTime.Now;
            bool bRes = IOAPI.WriteTag(this.masterDevName + "." + tagName, value);//设备名称+.+点位名称+写入数据
            TimeSpan time = DateTime.Now - startTime;
            int iMilliseconds = Convert.ToInt32(time.TotalMilliseconds);
            //记录PLC日志:向PLC写值{ParamName=,value=,Result=} Write Value to PLC {DeviceName=,value=,Result=}if (bRes)
            if (bRes)
            {
                //HistoryWriter.Instance.PLCLog(masterDevName, "WriteSuccess", tagName + " time: " + iMilliseconds.ToString(), value.ToString());
            }
            else
            {
                HistoryWriter.Instance.PLCLog(masterDevName, "WriteFailed", tagName + " time: " + iMilliseconds.ToString(), value.ToString());
            }
            return bRes;
        }
        /// <summary>
        /// 写设备变量（IO）中的变量
        /// </summary>
        /// <param name="devName">设备名</param>
        /// <param name="tagName">标签名</param>
        /// <param name="value">写入值</param>
        /// <returns></returns>
        public bool WriteIOValue(string devName, string tagName, object value)
        {
            lock(m_lockObj)
            {
                bool bRes = false;
                bRes = WriteIOValue(devName + "." + tagName, value);  //OK
                                                                      //记录PLC日志:向PLC写值{DeviceName=,TagName=,value=,Result=} Write Value to PLC {DeviceName=,value=,Result=}
                if (bRes)
                {
                    //HistoryWriter.Instance.PLCLog(masterDevName, "WriteSuccess", tagName, value.ToString());
                }
                else
                {
                    HistoryWriter.Instance.PLCLog(masterDevName, "WriteFailed", tagName, value.ToString());
                }
                return bRes;
            }
        }
        
        /// <summary>
        /// 批量写设备变量（IO）中的变量
        /// </summary>
        /// <param name="keyValues"></param>
        /// <returns></returns>
        public bool WriteIOValue(List<KeyValuePair<string, object>> keyValues)
        {
            lock(m_lockObj)
            {
                bool bRes = false;
                if (keyValues == null || keyValues.Count() == 0)
                {
                    bRes = false;
                    //记录PLC日志:向PLC批量写值失败：无参数和值信息   Lots Write to PLC Failed: No Param & Value Info
                    Logger.Instance.ExceptionLog("Lots Write to PLC Failed: No Param & Value Info");
                }
                else
                {
                    foreach (KeyValuePair<string, object> keyValue in keyValues)
                    {
                        if (!WriteIOValue(keyValue.Key, keyValue.Value))
                        {
                            HistoryWriter.Instance.PLCLog(masterDevName, "WriteFailed", keyValue.Key, keyValue.Value.ToString());
                            return false;
                        }
                        else
                        {
                            //HistoryWriter.Instance.PLCLog(masterDevName, "WriteSuccess", keyValue.Key, keyValue.Value.ToString());
                        }
                    }
                    bRes = true;
                }
                return bRes;
            }  
        }
        /// <summary>
        /// 写变量数据库中（内存中）的变量
        /// </summary>
        /// <param name="param">变量全名：Tag.tagName</param>
        /// <param name="value">变量值</param>
        /// <returns></returns>
        public bool WriteTagValue(string param, object value)
        {
            lock(m_lockObj)
            {
                DateTime startTime = DateTime.Now;
                bool bRes = TagAPI.SetValue("Tag." + param, value);
                TimeSpan time = DateTime.Now - startTime;
                int iMilliseconds = Convert.ToInt32(time.TotalMilliseconds);
                //记录PLC日志:向Tag写值{ParamName=,value=,Result=} Write Value to PLC {DeviceName=,value=,Result=}
                if (bRes)
                {
                    //HistoryWriter.Instance.PLCLog(masterDevName, "WriteSuccess", param + " time: " + iMilliseconds.ToString(), value.ToString());
                }
                else
                {
                    HistoryWriter.Instance.PLCLog(masterDevName, "WriteFailed", param + " time: " + iMilliseconds.ToString(), value.ToString());
                }
                return bRes;
            }
        }
        public bool WriteTagValue(List<KeyValuePair<string, object>> keyValues)
        {
            lock(m_lockObj)
            {
                bool bRes = false;
                if (keyValues == null || keyValues.Count() == 0)
                {
                    bRes = false;
                    //记录PLC日志:向PLC批量写值失败：无参数和值信息   Lots Write to PLC Failed: No Param & Value Info
                    Logger.Instance.ExceptionLog("Lots Write to PLC Failed: No Param & Value Info");
                }
                else
                {
                    foreach (KeyValuePair<string, object> keyValue in keyValues)
                    {
                        DateTime startTime = DateTime.Now;
                        if (!TagAPI.SetValue("Tag." + keyValue.Key, keyValue.Value))//只需要把这个写入的方法替换掉就可以了
                        {
                            TimeSpan time = DateTime.Now - startTime;
                            int iMilliseconds = Convert.ToInt32(time.TotalMilliseconds);
                            HistoryWriter.Instance.PLCLog(masterDevName, "WriteFailed", keyValue.Key + " time: " + iMilliseconds.ToString(), keyValue.Value.ToString());
                            return false;
                        }
                        else
                        {
                            TimeSpan time = DateTime.Now - startTime;
                            int iMilliseconds = Convert.ToInt32(time.TotalMilliseconds);
                            //HistoryWriter.Instance.PLCLog(masterDevName, "WriteSuccess", keyValue.Key + " time: " + iMilliseconds.ToString(), keyValue.Value.ToString());
                        }
                    }
                    bRes = true;
                }

                return bRes;
            }
        }
        public bool FINS_SetValue(string address,string value)
        {
            return true;
        }
    }
}
