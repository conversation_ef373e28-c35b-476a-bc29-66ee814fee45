﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Contexts;
using System.Text;
using SqlSugar;
using MySql;

namespace Proj.DB
{
    /// <summary>
    /// 引入sqlsugar与数据库交互
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class DbSet<T> : SimpleClient<T> where T : class, new()
    {

        public DbSet(SqlSugarClient context) : base(context)
        {

        }
        //SimpleClient中的方法满足不了你，你可以扩展自已的方法
        /// <summary>
        /// 拓展方法GetByIds
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<T> GetByIds(dynamic[] ids)
        {
            return Context.Queryable<T>().In(ids).ToList();
        }
    }

    public class DbContext<T> where T : class, new()
    {
        public static string DBConect = "Data Source=localhost;Port=3306;database=stkc;User Id=root;pwd=********;charset='utf8';pooling=true;sslmode=none;";
        //public static string DBConect = "Data Source=***********;Port=3306;database=pm1263_wcs;User Id=root;pwd=************;charset='utf8';pooling=true;sslmode=none;";
        public DbContext()
        {
            Db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = DBConect,
                DbType = SqlSugar.DbType.MySql,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute
            });
            //调式代码 用来打印SQL 
            Db.Aop.OnLogExecuting = (sql, pars) =>
            {
                Console.WriteLine(sql + "\r\n" + Db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
                Console.WriteLine();
            };
        }

        public SqlSugarClient Db;//用来处理事务多表查询和复杂的操作
        public DbSet<T> CurrentDb { get { return new DbSet<T>(Db); } }//用来处理T表的常用操作
    }
    public class DbContext
    {
        public static string DBConect = "Data Source=localhost;Port=3306;database=pm22122_wcs;User Id=root;pwd=********;charset='utf8';pooling=true;sslmode=none;";
        //public static string DBConect = "Data Source=***********;Port=3306;database=pm1263_wcs;User Id=root;pwd=************;charset='utf8';pooling=true;sslmode=none;";
        public DbContext()
        {
            Db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = DBConect,
                DbType = SqlSugar.DbType.MySql,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute
            });
            //调式代码 用来打印SQL 
            Db.Aop.OnLogExecuting = (sql, pars) =>
            {
                Console.WriteLine(sql + "\r\n" + Db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
                Console.WriteLine();
            };
        }
        public SqlSugarClient Db;//用来处理事务多表查询和复杂的操作
    }
}
