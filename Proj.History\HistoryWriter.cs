﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Proj.Log;
using Proj.Entity;

namespace Proj.History
{
    public enum EnumLogTableName
    {
        th_alarm,
        th_eqp,
        th_host,
        th_operation,
        th_pio,
        th_plc,
        th_sc,
        th_transfer
    }

    public class HistoryWriter
    {
        private static HistoryWriter m_Instanse;
        private static readonly object mSyncObject = new object();

        public static HistoryWriter Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new HistoryWriter();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public HistoryWriter()
        {

        }

        /// <summary>
        /// 向alarm_history表写入新的Alarm
        /// </summary>
        /// <param name="startTime">报警第一次发生的时间</param>
        /// <param name="strAlarmUnit">模块</param>
        /// <param name="nAlarmCode">报警编码</param>
        /// <param name="strComment">报警内容备注</param>
        public bool AddAlarmHistory(DateTime startTime, string strAlarmUnit, int nAlarmCode, string strComment)
        {
            //Write Txt
            Logger.Instance.AlarmLog("[Set Alarm] AlarmStartTime: " + startTime.ToString("yyyy-MM-dd HH:mm:ss")
                + $", AlarmUnit: {strAlarmUnit}, AlarmCode: {nAlarmCode}, Comment: {strComment}");

            //Write DB th_alarm
            ThAlarm tAlarm = ThAlarm.New();
            tAlarm.Code = nAlarmCode;
            tAlarm.Unit = strAlarmUnit;
            tAlarm.StartTime = startTime;
            tAlarm.Comment = strComment;
            try
            {
                tAlarm.Save();
                return true;
            }
            catch (Exception ex)
            {
                string strEx = "Write DB th_alarm Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }

        /// <summary>
        /// 更新alarm_history表中的报警清除时间
        /// </summary>
        /// <param name="startTime">报警第一次发生的时间</param>
        /// <param name="nAlarmCode">报警编码</param>
        public bool UpdateAlarmClearTime(DateTime startTime, string strAlarmUnit, int nAlarmCode)
        {
            DateTime clearTime = DateTime.Now;
            try
            {
                //Write Txt
                Logger.Instance.AlarmLog("[Clear Alarm] AlarmStartTime: " + startTime.ToString("yyyy-MM-dd HH:mm:ss")
                + $", AlarmUnit: {strAlarmUnit}, AlarmCode: {nAlarmCode}");

                //Write DB th_alarm
                ///ThAlarm tAlarm = ThAlarm.GetByLambda(x => x.StartTime.Equals(startTime) & x.Unit.Equals(strAlarmUnit) & x.Code.Equals(nAlarmCode));
                ThAlarm tAlarm = ThAlarm.GetByLambda(x => x.StartTime.Equals(startTime) & x.Code.Equals(nAlarmCode));
                tAlarm.ClearTime = clearTime;
            
                tAlarm.Save();
                return true;
            }
            catch(Exception ex)
            {
                string strEx = "Write DB th_alarm Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }

        private long DateTimeToLong(DateTime dtTime)
        {
            string strTime = dtTime.ToString("yyyyMMddHHmmssfff");
            return Convert.ToInt64(strTime);
        }

        /// <summary>
        /// 异常日志，只写入TXT
        /// </summary>
        /// <param name="strException">异常内容</param>
        public bool ExceptionLog(string strException)
        {
            Logger.Instance.ExceptionLog(strException);
            return true;
        }

        /// <summary>
        /// 设备事件日志
        /// </summary>
        /// <param name="strUnit">设备单元名称</param>
        /// <param name="nEventID">事件ID</param>
        /// <param name="strEventName">事件名称</param>
        /// <param name="strEventText">事件具体内容</param>
        /// <returns></returns>
        public bool EqpEventLog(string strUnit, int nEventID, string strEventName, string strEventText, string strCommandID = "unknow")
        {
            DateTime logTime = DateTime.Now;
            
            //Write Txt
            Logger.Instance.EventLog($"[CommandID]:{strCommandID}, [EQP Event] Unit: {strUnit}, Event ID: {nEventID}, Event Name: {strEventName}, Event Text: {strEventText}");
            return true;

            ////Write DB th_eqp
            //ThEqp tEqp = ThEqp.New();
            //tEqp.Time = logTime;
            //tEqp.Unit = strUnit;
            //tEqp.EventId = nEventID;
            //tEqp.EventName = strEventName;
            //tEqp.Text = strEventText;
            //try
            //{
            //    tEqp.Save();
            //    return true;
            //}
            //catch (Exception ex)
            //{
            //    string strEx = "Write DB th_eqp Error: " + ex.Message + ", Stack:" + ex.StackTrace;
            //    Logger.Instance.ExceptionLog(strEx);
            //    return false;
            //}
        }

        /// <summary>
        /// 与Host的通信日志
        /// </summary>
        /// <param name="strDirection">数据传输方向：Send/Receive</param>
        /// <param name="strStreamFunction">如S1F1</param>
        /// <param name="strMsgType">消息类型: Request/Reply</param>
        /// <param name="strMsgName">消息名称</param>
        /// <param name="strMsgData">消息具体内容</param>
        public bool HostLog(string strDirection, string strStreamFunction, string strMsgType, string strMsgName, string strMsgData)
        {
            DateTime logTime = DateTime.Now;

            //Write Txt
            Logger.Instance.HostLog($"[Host Comm] Direction: {strDirection}, Stream Function: {strStreamFunction}"
                + $", Msg Type: {strMsgType}, Msg Name: {strMsgName}, Msg Data: {strMsgData}");

            //Write DB th_host
            ThHost tHost = ThHost.New();
            tHost.Time = logTime;
            tHost.Direction = strDirection;
            tHost.StreamFunction = strStreamFunction;
            tHost.MsgType = strMsgType;
            tHost.MsgName = strMsgName;
            tHost.Text = strMsgData;
            try
            {
                tHost.Save();
                return true;
            }
            catch (Exception ex)
            {
                string strEx = "Write DB th_host Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }

        /// <summary>
        /// 用户界面操作日志
        /// </summary>
        /// <param name="strClient">GUI客户端ID</param>
        /// <param name="strUser">用户名</param>
        /// <param name="strUIName">操作的界面名称</param>
        /// <param name="strFunctionName">界面中的功能(界面中的区域、按钮、菜单项等)</param>
        /// <param name="strFunctionData">功能中的具体数据</param>
        public bool OperationLog(string strClient, string strUser, string strUIName, string strFunctionName, string strFunctionData)
        {
            DateTime logTime = DateTime.Now;

            //Write Txt
            Logger.Instance.OperationLog($"[Operation] Client: {strClient}, User: {strUser}"
                + $", UI Name: {strUIName}, Function Name: {strFunctionName}, Function Data: {strFunctionData}");

            //Write DB th_operation
            ThOperation tOperation = ThOperation.New();
            tOperation.Time = logTime;
            tOperation.Client = strClient;
            tOperation.User = strUser;
            tOperation.UiName = strUIName;
            tOperation.FunctionName = strFunctionName;
            tOperation.FunctionData = strFunctionData;
            try
            {
                tOperation.Save();
                return true;
            }
            catch (Exception ex)
            {
                string strEx = "Write DB th_operation Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }

        /// <summary>
        /// PIO日志(E84)
        /// </summary>
        /// <param name="strPortName">Port名称</param>
        /// <param name="strDirection">数据传输方向：Send/Recive</param>
        /// <param name="strConnectModule">与Port通讯的模块名称</param>
        /// <param name="strIOName">IO名称</param>
        /// <param name="nIOValue">IO值</param>
        public bool PIOLog(string strPortName, string strDirection, string strConnectModule, string strIOName, int nIOValue)
        {
            DateTime logTime = DateTime.Now;

            //Write Txt
            Logger.Instance.PIOLog($"[PIO] Port Name: {strPortName}, Direction: {strDirection}"
                + $", Connect Module: {strConnectModule}, IO Name: {strIOName}, IO Value: {nIOValue}");

            //Write DB th_pio
            ThPio tPio = ThPio.New();
            tPio.Time = logTime;
            tPio.PortName = strPortName;
            tPio.Direction = strDirection;
            tPio.ConnectModule = strConnectModule;
            tPio.IoName = strIOName;
            tPio.IoValue = nIOValue;
            try
            {
                tPio.Save();
                return true;
            }
            catch (Exception ex)
            {
                string strEx = "Write DB th_pio Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }

        /// <summary>
        /// SC日志
        /// </summary>
        /// <param name="strUnit">SC单元</param>
        /// <param name="strAction">执行的动作</param>
        /// <param name="strOldState">动作执行之前的状态</param>
        /// <param name="strNewState">动作执行之后的状态</param>
        /// <returns></returns>
        public bool SCLog(string strUnit, string strAction, string strOldState, string strNewState)
        {
            DateTime logTime = DateTime.Now;

            //Write Txt
            Logger.Instance.SCLog($"[SC] Unit: {strUnit}, Action: {strAction}, Old State: {strOldState}, New State: {strNewState}");

            //Write DB th_sc
            ThSc tSc = ThSc.New();
            tSc.Time = logTime;
            tSc.Unit = strUnit;
            tSc.Action = strAction;
            tSc.OldState = strOldState;
            tSc.NewState = strNewState;
            try
            {
                tSc.Save();
                return true;
            }
            catch (Exception ex)
            {
                string strEx = "Write DB th_sc Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }

        /// <summary>
        /// PLC读写日志
        /// </summary>
        /// <param name="strUnit">设备单元</param>
        /// <param name="strType">IO操作: ReadSuccess/ReadFailed/WriteSuccess/WriteFailed</param>
        /// <param name="strIOName">IO名称</param>
        /// <param name="dIOValue">IO值</param>
        public bool PLCLog(string strUnit, string strType, string strIOName, string strIOValue)
        {
            DateTime logTime = DateTime.Now;

            //Write Txt
            Logger.Instance.PLCLog($"[PLC] Unit: {strUnit}, IO Type: {strType}, IO Name: {strIOName}, IO Value: {strIOValue}");

            //Write DB th_plc
            //ThPlc tPlc = ThPlc.New();
            //tPlc.Time = logTime;
            //tPlc.Unit = strUnit;
            //tPlc.Type = strType;
            //tPlc.IoName = strIOName;
            //tPlc.IoValue = strIOValue;
            try
            {
                //tPlc.Save();
                return true;
            }
            catch (Exception ex)
            {
                string strEx = "Write DB th_plc Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }

        /// <summary>
        /// 在th_transfer表中，创建新的Command
        /// </summary>
        /// <param name="commandTime">命令创建时间</param>
        /// <param name="strCmdID">命令ID</param>
        /// <param name="strCarrierID">CarrierID</param>
        /// <param name="strCmdSrc">命令来源：Unknown/GUI/MCS</param>
        /// <param name="strCmdType">命令类型：Transfer/Scan</param>
        /// <param name="strTransferType">搬运类型：Transfer/Pick/Place</param>
        /// <param name="nCmdPriority">命令的优先级</param>
        /// <param name="strSrcLoc">源位置信息，可能是排列成，也可能是zone名称</param>
        /// <param name="strDestLoc">目的位置信息，可能是排列成，也可能是zone名称</param>
        /// <param name="nCalPriority">计算后的优先级</param>
        public bool CreateTransferCmd(DateTime commandTime, string strCmdID, string strCarrierID, 
            string strCmdSrc, string strCmdType, string strTransferType, int nCmdPriority,
            string strSrcLoc, string strDestLoc, int nCalPriority, string strComment="")
        {
            //Write Txt
            Logger.Instance.TransferLog("[Create Transfer Command] CmdCreateTime: " + commandTime.ToString("yyyy-MM-dd HH:mm:ss.fff")
                + $", CmdID: {strCmdID}, CarrierID: {strCarrierID}, CmdSrc: {strCmdSrc}, CmdType: {strCmdType}, TransferType: {strTransferType}"
                + $", CmdPriority: {nCmdPriority}, strSrcLoc: {strSrcLoc}, DestLoc: {strDestLoc}, CalPriority: {nCalPriority}, Comment: {strComment}");

            //Write DB th_transfer
            ThTransfer tTransfer = ThTransfer.New();
            tTransfer.CmdTime = commandTime;
            tTransfer.CmdId = strCmdID;
            tTransfer.CarrierId = strCarrierID;
            tTransfer.CmdSource = strCmdSrc;
            tTransfer.CmdType = strCmdType;
            tTransfer.TransferType = strTransferType;
            tTransfer.Priority = nCmdPriority;
            tTransfer.SourceLocation = strSrcLoc;
            tTransfer.DestLocation = strDestLoc;
            tTransfer.CalcPriority = nCalPriority;
            tTransfer.Comment = strComment;
            try
            {
                tTransfer.Save();
                return true;
            }
            catch (Exception ex)
            {
                string strEx = "Write DB th_transfer Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }

        /// <summary>
        /// Crane开始搬运，更新th_transfer表
        /// </summary>
        /// <param name="strCmdID">命令ID</param>
        /// <param name="nFirstSecond">1-第一个搬运子过程，2-第二个搬运子过程(双Crane时使用)</param>
        /// <param name="strCraneID">此搬运子过程的Crane ID</param>
        public bool CraneTransferStart(string strCmdID, int nFirstSecond, string strCraneID, string strComment = "")
        {
            DateTime startTime = DateTime.Now;
            try
            {
                //Write Txt
                Logger.Instance.TransferLog("[Crane Transfer Start] CmdID: " + strCmdID
                    + $", FirstSecond: {nFirstSecond}, CraneID: {strCraneID}, Comment: {strComment}");

                //Write DB th_transfer
                ThTransfer tTransfer = ThTransfer.GetByLambda(x => x.CmdId.Equals(strCmdID));
                tTransfer.CmdState = "Start";
                if (nFirstSecond == 1) //第一个搬运子过程
                {
                    tTransfer.CraneName = strCraneID;
                    tTransfer.CraneStartTime = startTime;
                }
                else if (nFirstSecond == 2) //第二个搬运子过程(双Crane时使用)
                {
                    tTransfer.AltCraneName = strCraneID;
                    tTransfer.AltStartTime = startTime;
                }
                if(strComment.Length > 0)
                {
                    tTransfer.Comment = strComment;
                }
            
                tTransfer.Save();
                return true;
            }
            catch (Exception ex)
            {
                string strEx = "Write DB th_transfer Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }

        /// <summary>
        /// Crane停止搬运，更新th_transfer表
        /// </summary>
        /// <param name="strCmdID">命令ID</param>
        /// <param name="nFirstSecond">1-第一个搬运子过程，2-第二个搬运子过程(双Crane时使用)</param>
        /// <param name="strCmdState">命令执行结果</param>
        /// <param name="strDelayReason">命令超时的原因</param>
        public bool CraneTransferEnd(string strCmdID, int nFirstSecond,
            string strCmdState, string strDelayReason, string strComment = "")
        {
            DateTime endTime = DateTime.Now;
            try
            {
                //Write Txt
                Logger.Instance.TransferLog("[Crane Transfer End] CmdID: " + strCmdID
                    + $", FirstSecond: {nFirstSecond }, CmdState: {strCmdState}"
                    + $", DelayReason: {strDelayReason}, Comment: {strComment}");

                //Write DB th_transfer
                ThTransfer tTransfer = ThTransfer.GetByLambda(x => x.CmdId.Equals(strCmdID));
                if(nFirstSecond == 1) //第一个搬运子过程
                {
                    tTransfer.CraneEndTime = endTime;
                }
                else if (nFirstSecond == 2) //第二个搬运子过程(双Crane时使用)
                {
                    tTransfer.AltEndTime = endTime;
                }
                tTransfer.CmdState = strCmdState;
                tTransfer.DelayReason = strDelayReason;
                if (strComment.Length > 0)
                {
                    tTransfer.Comment = strComment;
                }
            
                tTransfer.Save();
                return true;
            }
            catch (Exception ex)
            {
                string strEx = "Write DB th_transfer Error: " + ex.Message + ", Stack:" + ex.StackTrace;
                Logger.Instance.ExceptionLog(strEx);
                return false;
            }
        }
    }
}
