# SECS/GEM Interface Implementation using Secs4Net

这个项目提供了基于Secs4Net库的SECS/GEM协议接口实现，用于半导体设备通信。

## 文件结构

```
Proj.API/
├── IGemEquipment.cs          # SECS/GEM接口定义
├── GemEquipmentImpl.cs       # Secs4Net实现
├── GemEquipmentExample.cs    # 使用示例
├── config/
│   └── event_def.csv         # 事件定义配置文件
└── README.md                 # 说明文档
```

## 主要功能

### 1. 连接管理
- 支持主动和被动连接模式
- 自动重连机制
- 连接状态监控

### 2. 消息处理
- 异步消息发送和接收
- 主消息流处理
- 消息回复机制

### 3. 状态管理
- 设备控制状态（Local/Remote）
- 通信状态管理
- 在线/离线状态切换

### 4. 数据变量管理
- DV (Data Variables) - 数据变量
- SV (Status Variables) - 状态变量  
- EC (Equipment Constants) - 设备常数

### 5. 事件和报警系统
- 事件发布和订阅
- 报警管理
- 事件报告关联

## 快速开始

### 1. 基本使用

```csharp
// 创建GEM设备实例
var gemEquipment = new GemEquipmentImpl
{
    MDLN = "STKC_Equipment",
    SoftRev = "V2.0.0"
};

// 配置连接参数
gemEquipment.Connect(
    IsActive: true,
    IpAddress: "127.0.0.1", 
    Port: 5000,
    DeviceId: 1
);

// 启动GEM服务
await gemEquipment.StartGEMAsync();
```

### 2. 加载配置数据

```csharp
// 加载数据变量
var dvItems = new List<DVItem>
{
    new() { Id = 1, Name = "Clock", Value = DateTime.Now.ToString("yyyyMMddHHmmss") },
    new() { Id = 2, Name = "EqpName", Value = "STKC" }
};
await gemEquipment.LoadDVAsync(dvItems);

// 从CSV文件加载事件定义
await gemEquipment.LoadEventAsync("config/event_def.csv");
```

### 3. 处理消息

```csharp
// 处理主消息
await foreach (var messageWrapper in gemEquipment.GetPrimaryMessageAsync())
{
    using (messageWrapper)
    {
        var message = messageWrapper.PrimaryMessage;
        Console.WriteLine($"Received: S{message.S}F{message.F}");
        
        // 处理消息并回复
        var reply = ProcessMessage(message);
        if (reply != null)
        {
            await messageWrapper.TryReplyAsync(reply);
        }
    }
}
```

### 4. 发送事件和报警

```csharp
// 发送事件
await gemEquipment.PostEventAsync(100, Item.L(
    Item.A("CARRIER001"),
    Item.A("PORT01")
));

// 发送报警
await gemEquipment.PostAlarmAsync(2, "CRANE01");

// 清除报警
await gemEquipment.ClearAlarmAsync(2, "CRANE01");
```

## 接口说明

### IGemEquipment 主要方法

#### 连接管理
- `Connect()` - 连接到主机
- `Disconnect()` - 断开连接
- `StartGEMAsync()` - 启动GEM服务
- `StopGEMAsync()` - 停止GEM服务

#### 消息处理
- `SendAsync()` - 异步发送消息
- `GetPrimaryMessageAsync()` - 获取主消息流
- `OnReceiveMessageAsync()` - 处理接收消息

#### 状态管理
- `SwitchOnlineAsync()` - 切换到在线状态
- `SwitchOfflineAsync()` - 切换到离线状态
- `SwitchLocalAsync()` - 切换到本地控制
- `SwitchRemoteAsync()` - 切换到远程控制

#### 变量访问
- `OnGetDVAsync()` - 获取数据变量
- `OnGetSVAsync()` - 获取状态变量
- `OnGetECAsync()` - 获取设备常数
- `OnSetECAsync()` - 设置设备常数

#### 事件和报警
- `PostEventAsync()` - 发布事件
- `PostAlarmAsync()` - 发布报警
- `ClearAlarmAsync()` - 清除报警

## 配置文件

### event_def.csv 格式
```csv
EventId,EventName,Enabled,Description
1,"EquipmentOffline",true,"Equipment goes offline"
100,"CarrierArrived",true,"Carrier arrived at port"
```

## 事件处理

```csharp
// 订阅事件
gemEquipment.ConnectionChanged += (sender, state) => 
{
    Console.WriteLine($"Connection: {state}");
};

gemEquipment.EventPosted += (sender, eventId) => 
{
    Console.WriteLine($"Event: {eventId}");
};

gemEquipment.AlarmPosted += (sender, alarmId) => 
{
    Console.WriteLine($"Alarm: {alarmId}");
};
```

## 常见消息处理

### S1F13 - 建立通信
```csharp
case (1, 13):
    return new SecsMessage(1, 14) 
    { 
        SecsItem = Item.L(
            Item.U1(0), // COMMACK - Accept
            Item.L(
                Item.A(MDLN),
                Item.A(SoftRev)
            )
        )
    };
```

### S2F17 - 日期时间请求
```csharp
case (2, 17):
    return new SecsMessage(2, 18) 
    { 
        SecsItem = Item.A(DateTime.Now.ToString("yyyyMMddHHmmss"))
    };
```

## 注意事项

1. **资源管理**: 使用`using`语句确保消息和连接正确释放
2. **异常处理**: 所有异步操作都应包含适当的异常处理
3. **线程安全**: 接口实现是线程安全的，可以在多线程环境中使用
4. **配置验证**: 在连接前确保所有必要的配置参数都已设置

## 依赖项

- Secs4Net (最新版本)
- .NET 6.0 或更高版本
- System.Threading.Tasks
- System.Collections.Generic

## 示例运行

运行完整示例：
```bash
dotnet run --project Proj.API GemEquipmentExample.cs
```

这将启动一个完整的GEM设备模拟，包括连接建立、消息处理、事件发送等功能演示。
