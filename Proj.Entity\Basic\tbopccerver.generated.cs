﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbOpccerver and List
    [Serializable]
    [Description("OPC服务器地址")]
    [LinqToDB.Mapping.Table("TB_OPCCERVER")]
    public partial class TbOpccerver : GEntity<TbOpccerver>, ITimestamp
    {
        #region Contructor(s)

        private TbOpccerver()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CId = RegisterProperty<String>(p => p.CId);
        private static readonly PropertyInfo<String> pty_CIp = RegisterProperty<String>(p => p.CIp);
        private static readonly PropertyInfo<String> pty_CName = RegisterProperty<String>(p => p.CName);
        private static readonly PropertyInfo<String> pty_CConn = RegisterProperty<String>(p => p.CConn);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 服务器地址ID
        /// </summary>
        [Description("服务器地址ID")]
        [LinqToDB.Mapping.Column("C_ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CId
        {
            get { return GetProperty(pty_CId); }
            set { SetProperty(pty_CId, value); }
        }
        /// <summary>
        /// 服务器地址IP
        /// </summary>
        [Description("服务器地址IP")]
        [LinqToDB.Mapping.Column("C_IP")]
        public String CIp
        {
            get { return GetProperty(pty_CIp); }
            set { SetProperty(pty_CIp, value); }
        }
        /// <summary>
        /// 服务器地址名称
        /// </summary>
        [Description("服务器地址名称")]
        [LinqToDB.Mapping.Column("C_NAME")]
        public String CName
        {
            get { return GetProperty(pty_CName); }
            set { SetProperty(pty_CName, value); }
        }
        /// <summary>
        /// 连接字符串
        /// </summary>
        [Description("连接字符串")]
        [LinqToDB.Mapping.Column("C_CONN")]
        public String CConn
        {
            get { return GetProperty(pty_CConn); }
            set { SetProperty(pty_CConn, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CId, "服务器地址ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CId, 40, "服务器地址ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CIp, 50, "服务器地址IP不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CName, 100, "服务器地址名称不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CConn, 1000, "连接字符串不能超过1000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbOpccerverList : GEntityList<TbOpccerverList, TbOpccerver>
    {
        private TbOpccerverList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
