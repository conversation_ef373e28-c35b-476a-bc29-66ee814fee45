﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;


 
namespace Proj.Entity
{
    #region TbClient and List
    [Serializable]
    [Description("客户信息表")]
    [LinqToDB.Mapping.Table("TB_CLIENT")]
    public partial class TbClient : GEntity<TbClient>, ITimestamp
    {
        #region Contructor(s)

        private TbClient()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CClisupId = RegisterProperty<String>(p => p.CClisupId);
        private static readonly PropertyInfo<String> pty_CFullName = RegisterProperty<String>(p => p.CFullName);
        private static readonly PropertyInfo<String> pty_CShortName = RegisterProperty<String>(p => p.CShortName);
        private static readonly PropertyInfo<String> pty_CCountry = RegisterProperty<String>(p => p.CCountry);
        private static readonly PropertyInfo<String> pty_CArea = RegisterProperty<String>(p => p.CArea);
        private static readonly PropertyInfo<String> pty_CAddr = RegisterProperty<String>(p => p.CAddr);
        private static readonly PropertyInfo<String> pty_CTaxNo = RegisterProperty<String>(p => p.CTaxNo);
        private static readonly PropertyInfo<String> pty_CPrimanId = RegisterProperty<String>(p => p.CPrimanId);
        private static readonly PropertyInfo<String> pty_CState = RegisterProperty<String>(p => p.CState);
        private static readonly PropertyInfo<String> pty_CCreaterId = RegisterProperty<String>(p => p.CCreaterId);
        private static readonly PropertyInfo<String> pty_CCreatedate = RegisterProperty<String>(p => p.CCreatedate);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        private static readonly PropertyInfo<String> pty_CZipCode = RegisterProperty<String>(p => p.CZipCode);
        private static readonly PropertyInfo<String> pty_CPrincipal = RegisterProperty<String>(p => p.CPrincipal);
        private static readonly PropertyInfo<String> pty_CLinkphone = RegisterProperty<String>(p => p.CLinkphone);
        private static readonly PropertyInfo<String> pty_CFax = RegisterProperty<String>(p => p.CFax);
        private static readonly PropertyInfo<String> pty_CRegisterCode = RegisterProperty<String>(p => p.CRegisterCode);
        #endregion

        /// <summary>
        /// 客户编码
        /// </summary>
        [Description("客户编码")]
        [LinqToDB.Mapping.Column("C_CLISUPID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CClisupId
        {
            get { return GetProperty(pty_CClisupId); }
            set { SetProperty(pty_CClisupId, value); }
        }
        /// <summary>
        /// 客户全称
        /// </summary>
        [Description("客户全称")]
        [LinqToDB.Mapping.Column("C_FULLNAME")]
        public String CFullName
        {
            get { return GetProperty(pty_CFullName); }
            set { SetProperty(pty_CFullName, value); }
        }
        /// <summary>
        /// 客户简称
        /// </summary>
        [Description("客户简称")]
        [LinqToDB.Mapping.Column("C_SHORTNAME")]
        public String CShortName
        {
            get { return GetProperty(pty_CShortName); }
            set { SetProperty(pty_CShortName, value); }
        }
        /// <summary>
        /// 国家
        /// </summary>
        [Description("国家")]
        [LinqToDB.Mapping.Column("C_COUNTRY")]
        public String CCountry
        {
            get { return GetProperty(pty_CCountry); }
            set { SetProperty(pty_CCountry, value); }
        }
        /// <summary>
        /// 地区
        /// </summary>
        [Description("地区")]
        [LinqToDB.Mapping.Column("C_AREA")]
        public String CArea
        {
            get { return GetProperty(pty_CArea); }
            set { SetProperty(pty_CArea, value); }
        }
        /// <summary>
        /// 地址
        /// </summary>
        [Description("地址")]
        [LinqToDB.Mapping.Column("C_ADDR")]
        public String CAddr
        {
            get { return GetProperty(pty_CAddr); }
            set { SetProperty(pty_CAddr, value); }
        }
        /// <summary>
        /// 税号
        /// </summary>
        [Description("税号")]
        [LinqToDB.Mapping.Column("C_TAXNO")]
        public String CTaxNo
        {
            get { return GetProperty(pty_CTaxNo); }
            set { SetProperty(pty_CTaxNo, value); }
        }
        /// <summary>
        /// 法人
        /// </summary>
        [Description("法人")]
        [LinqToDB.Mapping.Column("C_PRIMANID")]
        public String CPrimanId
        {
            get { return GetProperty(pty_CPrimanId); }
            set { SetProperty(pty_CPrimanId, value); }
        }
        /// <summary>
        /// 有效状态
        /// </summary>
        [Description("有效状态")]
        [LinqToDB.Mapping.Column("C_STATE")]
        public String CState
        {
            get { return GetProperty(pty_CState); }
            set { SetProperty(pty_CState, value); }
        }
        /// <summary>
        /// 建立人
        /// </summary>
        [Description("建立人")]
        [LinqToDB.Mapping.Column("C_CREATERID")]
        public String CCreaterId
        {
            get { return GetProperty(pty_CCreaterId); }
            set { SetProperty(pty_CCreaterId, value); }
        }
        /// <summary>
        /// 建立日期
        /// </summary>
        [Description("建立日期")]
        [LinqToDB.Mapping.Column("C_CREATEDATE")]
        public String CCreatedate
        {
            get { return GetProperty(pty_CCreatedate); }
            set { SetProperty(pty_CCreatedate, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        /// <summary>
        /// 邮政编码
        /// </summary>
        [Description("邮政编码")]
        [LinqToDB.Mapping.Column("C_ZIPCODE")]
        public String CZipCode
        {
            get { return GetProperty(pty_CZipCode); }
            set { SetProperty(pty_CZipCode, value); }
        }
        /// <summary>
        /// 负责人
        /// </summary>
        [Description("负责人")]
        [LinqToDB.Mapping.Column("C_PRINCIPAL")]
        public String CPrincipal
        {
            get { return GetProperty(pty_CPrincipal); }
            set { SetProperty(pty_CPrincipal, value); }
        }
        /// <summary>
        /// 电话
        /// </summary>
        [Description("电话")]
        [LinqToDB.Mapping.Column("C_LINKPHONE")]
        public String CLinkphone
        {
            get { return GetProperty(pty_CLinkphone); }
            set { SetProperty(pty_CLinkphone, value); }
        }
        /// <summary>
        /// 传真
        /// </summary>
        [Description("传真")]
        [LinqToDB.Mapping.Column("C_FAX")]
        public String CFax
        {
            get { return GetProperty(pty_CFax); }
            set { SetProperty(pty_CFax, value); }
        }
        /// <summary>
        /// 纳税登记号
        /// </summary>
        [Description("纳税登记号")]
        [LinqToDB.Mapping.Column("C_REGISTERCODE")]
        public String CRegisterCode
        {
            get { return GetProperty(pty_CRegisterCode); }
            set { SetProperty(pty_CRegisterCode, value); }
        }

      
     
        
        #endregion 
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CClisupId, "客户编码是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CClisupId, 40, "客户编码不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFullName, 160, "客户全称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CShortName, 160, "客户简称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCountry, 40, "国家不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CArea, 160, "地区不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CAddr, 160, "地址不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CTaxNo, 160, "税号不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPrimanId, 40, "法人不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CState, 2, "有效状态不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCreaterId, 60, "建立人不能超过60个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCreatedate, 16, "建立日期不能超过16个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CZipCode, 20, "邮政编码不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPrincipal, 20, "负责人不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLinkphone, 20, "电话不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFax, 20, "传真不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CRegisterCode, 30, "纳税登记号不能超过30个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CClisupId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbClientList : GEntityList<TbClientList, TbClient>
    {
        private TbClientList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
