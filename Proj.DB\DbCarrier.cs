﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Proj.Entity;
using Proj.DataTypeDef;

namespace Proj.DB
{
    public class DbCarrier
    {        
        private static DbCarrier m_Instanse;
        private static readonly object mSyncObject = new object();
        private DbCarrier() { }
        public static DbCarrier Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new DbCarrier();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        //public TpCarrierList GetDbCarrierList()
        //{
        //    try
        //    {
        //        TpCarrierList tpCarrierList = TpCarrierList.GetAll();
        //        return tpCarrierList;
        //    }
        //    catch (Exception ex)
        //    {
        //        //记录程序异常日志
        //    }
        //    return null;
        //}

        public TpCarrierViewList GetDbCarrierList()
        {
            try
            {
                TpCarrierViewList tpCarrierList = TpCarrierViewList.GetAll();
                return tpCarrierList;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCarrier.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return null;
        }

        public TpCarrier GetDbCarrierInfo(string carrierID)
        {
            try
            {
                TpCarrier tpCarrier = TpCarrier.GetById(carrierID);
                return tpCarrier;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCarrier.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return null;
        }

        public bool IsCarrierExist(string carrierID)
        {
            try
            {
                return TpCarrier.Exists(carrierID);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCarrier.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool AddCarrier(string carrierID, string location, IDReadStatus status = IDReadStatus.Success)
        {
            try
            {
                TpCarrier tpCarrier = TpCarrier.New();
                tpCarrier.Id = carrierID;
                tpCarrier.Location = location;
                tpCarrier.State = ((int)CarrierState.Installed).ToString();
                tpCarrier.IdReadStatus = ((int)status).ToString();
                tpCarrier.InstallTime = DateTime.Now;
                tpCarrier.Save();
                //记录Carrier日志：在数据库中创建盒子信息{carrierID, location}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCarrier.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool UpdateCarrierLocation(string carrierID, string location)
        {
            try
            {
                TpCarrier tpCarrier = TpCarrier.GetById(carrierID);
                tpCarrier.Location = location;
                tpCarrier.Save();
                //记录Carrier日志：更新数据库中的盒子位置信息{carrierID, location}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCarrier.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool UpdateCarrierState(string carrierID, CarrierState state)
        {
            try
            {
                TpCarrier tpCarrier = TpCarrier.GetById(carrierID);
                tpCarrier.State = ((int)state).ToString();
                tpCarrier.Save();
                //记录Carrier日志：更新数据库中的盒子状态信息{carrierID, state}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCarrier.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool UpdateCarrierIdReadStatus(string carrierID, IDReadStatus status)
        {
            try
            {
                TpCarrier tpCarrier = TpCarrier.GetById(carrierID);
                tpCarrier.IdReadStatus = ((int)status).ToString();
                tpCarrier.Save();
                //记录Carrier日志：更新数据库中的盒子读码状态信息{carrierID, state}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCarrier.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool UpdateCarrierComment(string carrierID, string comment)
        {
            try
            {
                TpCarrier tpCarrier = TpCarrier.GetById(carrierID);
                tpCarrier.Comment = comment;
                tpCarrier.Save();
                //记录Carrier日志：更新数据库中的盒子备注信息
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCarrier.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public string GetCarrierLocation(string carrierID)
        {
            try
            {
                TpCarrier tpCarrier = TpCarrier.GetById(carrierID);
                return tpCarrier.Location;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCarrier.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return "";
        }

        public LocationType GetCarrierLocType(string carrierID)
        {
            try
            {
                TpCarrier tpCarrier = TpCarrier.GetById(carrierID);
                TpLocation tpLocation = TpLocation.GetById(tpCarrier.Location);
                return (LocationType)uint.Parse(tpLocation.Type);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCarrier.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return LocationType.Unknown;
        }
    }
}
