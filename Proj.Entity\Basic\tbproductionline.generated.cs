﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbProductionline and List
    [Serializable]
    [Description("产线信息表")]
    [LinqToDB.Mapping.Table("TB_PRODUCTIONLINE")]
    public partial class TbProductionline : GEntity<TbProductionline>, ITimestamp
    {
        #region Contructor(s)

        private TbProductionline()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CProductionlineId = RegisterProperty<String>(p => p.CProductionlineId);
        private static readonly PropertyInfo<String> pty_CProductionlineName = RegisterProperty<String>(p => p.CProductionlineName);
        private static readonly PropertyInfo<String> pty_CProductionlineDes = RegisterProperty<String>(p => p.CProductionlineDes);
        private static readonly PropertyInfo<String> pty_CWorkshopId = RegisterProperty<String>(p => p.CWorkshopId);
        private static readonly PropertyInfo<String> pty_CWorkshopName = RegisterProperty<String>(p => p.CWorkshopName);
        private static readonly PropertyInfo<String> pty_CWorkshopDes = RegisterProperty<String>(p => p.CWorkshopDes);
        private static readonly PropertyInfo<String> pty_CFactoryId = RegisterProperty<String>(p => p.CFactoryId);
        private static readonly PropertyInfo<String> pty_CFactoryDes = RegisterProperty<String>(p => p.CFactoryDes);
        private static readonly PropertyInfo<String> pty_CWorkshopType = RegisterProperty<String>(p => p.CWorkshopType);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 产线ID
        /// </summary>
        [Description("产线ID")]
        [LinqToDB.Mapping.Column("C_PRODUCTIONLINEID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CProductionlineId
        {
            get { return GetProperty(pty_CProductionlineId); }
            set { SetProperty(pty_CProductionlineId, value); }
        }
        /// <summary>
        /// 产线名称
        /// </summary>
        [Description("产线名称")]
        [LinqToDB.Mapping.Column("C_PRODUCTIONLINENAME")]
        public String CProductionlineName
        {
            get { return GetProperty(pty_CProductionlineName); }
            set { SetProperty(pty_CProductionlineName, value); }
        }
        /// <summary>
        /// 产线描述
        /// </summary>
        [Description("产线描述")]
        [LinqToDB.Mapping.Column("C_PRODUCTIONLINEDES")]
        public String CProductionlineDes
        {
            get { return GetProperty(pty_CProductionlineDes); }
            set { SetProperty(pty_CProductionlineDes, value); }
        }
        /// <summary>
        /// 车间ID
        /// </summary>
        [Description("车间ID")]
        [LinqToDB.Mapping.Column("C_WORKSHOPID")]
        public String CWorkshopId
        {
            get { return GetProperty(pty_CWorkshopId); }
            set { SetProperty(pty_CWorkshopId, value); }
        }
        /// <summary>
        /// 车间名称
        /// </summary>
        [Description("车间名称")]
        [LinqToDB.Mapping.Column("C_WORKSHOPNAME")]
        public String CWorkshopName
        {
            get { return GetProperty(pty_CWorkshopName); }
            set { SetProperty(pty_CWorkshopName, value); }
        }
        /// <summary>
        /// 车间描述
        /// </summary>
        [Description("车间描述")]
        [LinqToDB.Mapping.Column("C_WORKSHOPDES")]
        public String CWorkshopDes
        {
            get { return GetProperty(pty_CWorkshopDes); }
            set { SetProperty(pty_CWorkshopDes, value); }
        }
        /// <summary>
        /// 工厂ID
        /// </summary>
        [Description("工厂ID")]
        [LinqToDB.Mapping.Column("C_FACTORYID")]
        public String CFactoryId
        {
            get { return GetProperty(pty_CFactoryId); }
            set { SetProperty(pty_CFactoryId, value); }
        }
        /// <summary>
        /// 工厂名称
        /// </summary>
        [Description("工厂名称")]
        [LinqToDB.Mapping.Column("C_FACTORYDES")]
        public String CFactoryDes
        {
            get { return GetProperty(pty_CFactoryDes); }
            set { SetProperty(pty_CFactoryDes, value); }
        }
        /// <summary>
        /// 车间类别
        /// </summary>
        [Description("车间类别")]
        [LinqToDB.Mapping.Column("C_WORKSHOP_TYPE")]
        public String CWorkshopType
        {
            get { return GetProperty(pty_CWorkshopType); }
            set { SetProperty(pty_CWorkshopType, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion


        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CProductionlineId, "产线ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProductionlineId, 40, "产线ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProductionlineName, 160, "产线名称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProductionlineDes, 160, "产线描述不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkshopId, 40, "车间ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkshopName, 160, "车间名称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkshopDes, 400, "车间描述不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryId, 20, "工厂ID不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryDes, 200, "工厂名称不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkshopType, 2, "车间类别不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CProductionlineId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbProductionlineList : GEntityList<TbProductionlineList, TbProductionline>
    {
        private TbProductionlineList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
