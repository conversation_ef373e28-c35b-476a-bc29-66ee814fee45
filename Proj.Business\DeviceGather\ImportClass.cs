﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.OleDb;
using Proj.Entity;
using System.IO;
using System.Text.RegularExpressions;

namespace Proj.Business.DeviceGather
{
    public class ImportClass
    {
        private Dictionary<string, string> dicType;
        public ImportClass()
        {
            dicType = new Dictionary<string, string>();
            dicType.Add("String", "4");
            dicType.Add("Integer", "3");
        }

        private DataTable CsvToDs(String filenameurl)
        {
            DataTable dt = new DataTable();
            FileStream fs = new FileStream(filenameurl, System.IO.FileMode.Open, System.IO.FileAccess.Read);
            StreamReader sr = new StreamReader(fs, System.Text.Encoding.Default);
            //记录每次读取的一行记录
            string strLine = "";
            //记录每行记录中的各字段内容
            string[] aryLine;
            //标示列数
            int columnCount = 0;
            //是否已建立了表的字段
            bool bCreateTableColumns = false;
            //第几行
            int iRow = 1;

            // { ",", ".", "!", "?", ";", ":", " " };
            string[] separators = { "," };
            //逐行读取CSV中的数据
            while ((strLine = sr.ReadLine()) != null)
            {
                strLine = strLine.Trim();
                aryLine = strLine.Split(separators, System.StringSplitOptions.None);

                if (bCreateTableColumns == false)
                {
                    bCreateTableColumns = true;
                    columnCount = aryLine.Length;
                    //创建列
                    for (int i = 0; i < columnCount; i++)
                    {
                        DataColumn dc = new DataColumn(aryLine[i]);
                        dt.Columns.Add(dc);
                    }

                    bCreateTableColumns = true;
                    continue;
                }


                DataRow dr = dt.NewRow();
                for (int j = 0; j < columnCount; j++)
                {
                    dr[j] = aryLine[j];
                }
                dt.Rows.Add(dr);

                iRow = iRow + 1;
            }

            sr.Close();
            fs.Close();
            return dt;
        }

        private DataTable ExcelToDs(String filenameurl)
        {
            string strConn = "Provider=Microsoft.ACE.Oledb.12.0;" + "data source=" + filenameurl + ";Extended Properties='Excel 12.0;HDR=YES; IMEX=1'";
            OleDbConnection conn = new OleDbConnection(strConn);
            conn.Open();
            DataSet ds = new DataSet();
            // 获取Excel表名
            DataTable schemaTable = conn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, null);
            string tableName = schemaTable.Rows[0][2].ToString().Trim();
            tableName = "Sheet$";
            string strSql = string.Format("Select * from [{0}]", tableName);
            OleDbDataAdapter odda = new OleDbDataAdapter(strSql, conn);
            odda.Fill(ds);
            return ds.Tables[0];
        }

        public bool ConvertToEntityList(ref object entityList, InfoType type, string filename)
        {
            DataView dv = filename.Contains(".csv") ? this.CsvToDs(filename).DefaultView : this.ExcelToDs(filename).DefaultView;

            if (type == InfoType.DevInfo)
            {
                for (int i = 0; i < dv.Table.Rows.Count; i++)
                {
                    TntDeviceinfo obj = TntDeviceinfo.New();
                    obj.CDeviceId = SS.BLL.Base.SequenceService.GenerateShortId();
                    obj.CDeviceCode = dv.Table.Rows[i][1].ToString();
                    obj.CDeviceName = dv.Table.Rows[i][1].ToString();
                    obj.CDeviceip = dv.Table.Rows[i][2].ToString();
                    obj.CItemdescription = dv.Table.Rows[i][3].ToString();
                    (entityList as TntDeviceinfoList).Add(obj);
                }
            }
            else
            {
                for (int i = 0; i < dv.Table.Rows.Count; i++)
                {
                    TntParagather obj = TntParagather.New();
                    obj.CParagatherId = SS.BLL.Base.SequenceService.GenerateShortId();
                    obj.CDeviceId = dv.Table.Rows[i][0].ToString();
                    obj.CParameterCode = dv.Table.Rows[i][2].ToString();
                    obj.CParameterName = dv.Table.Rows[i][2].ToString();
                    obj.CParameterType = dv.Table.Rows[i][3].ToString().Length >1? this.dicType[dv.Table.Rows[i][3].ToString()]: dv.Table.Rows[i][3].ToString();
                    obj.CParameterlow = dv.Table.Rows[i][4].ToString();
                    obj.CParameterup = dv.Table.Rows[i][5].ToString();
                    obj.CParameterdef = dv.Table.Rows[i][6].ToString();
                    obj.CDescribe = dv.Table.Rows[i][7].ToString();
                    obj.CAccessType = dv.Table.Rows[i][8].ToString().Replace("Read","R").Replace("Write", "W").Replace("And", "");
                    obj.CTransdate = dv.Table.Rows[i][9].ToString();
                    obj.CNote = dv.Table.Rows[i][10].ToString();

                    (entityList as TntParagatherList).Add(obj);
                }
            }

            return true;
        }
    }

    public enum InfoType
    {
        DevInfo,
        ParamInfo
    }


    /// <summary>
    /// 表达式解析类
    /// </summary>
    public class DateExpression
    {
        public static string GetWeek(string ex)
        {
            string temp = ex.Replace("周", "").Replace("一", "1").Replace("二", "2").Replace("三", "3").Replace("四", "4").Replace("五", "5").Replace("六", "6").Replace("日", "7");
            if (temp == "1, 2, 3, 4, 5, 6, 7" || temp == "")
            {
                temp = "0";
            }
            return temp;
        }

        public static string GetDays(string ex)
        {
            string temp = ex.Replace("号", "").Replace("最后一天", "L*");
            if (temp == "1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, L*" || temp == "")
            {
                temp = "0";
            }
            return temp;
        }

        public static string GetMonth(string ex)
        {
            string temp = ex.Replace("月", "").Replace("十一", "11").Replace("十二", "12").Replace("一", "1").Replace("二", "2").Replace("三", "3").Replace("四", "4").Replace("五", "5").Replace("六", "6").Replace("七", "7").Replace("八", "8").Replace("九", "9").Replace("十", "10");
            if (temp == "" || temp == "1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12")
            {
                temp = "0";
            }
            return temp;
        }

        public static DateTime GetNextExcuteTime(DateTime start, string ex)
        {
            // 存放本年度满足表达式的日期时间
            List<DateTime> list = new List<DateTime>();
            // 返回结果
            DateTime result = DateTime.MinValue;
            // 比对上一次时间
            DateTime time = start.Year == 1 ? DateTime.Now : start;
            string[] str = ex.Replace("E", "").Split('^');
            string strMonths = str[0] == "0" ? "1,2,3,4,5,6,7,8,9,10,11,12" : str[0];
            string strWeeks = str[1] == "0" ? "1,2,3,4,5,6,7" : str[1];
            string strDays = str[2] == "0" ? "1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31" : str[2];
            string strTimes = str[3];

            // 解析月份
            if (strMonths.Contains("-"))
            {
                string[] temp = strMonths.Split(',');
                foreach (string mm in temp)
                {
                    if (mm.Contains("-"))
                    {
                        strMonths = strMonths.Replace(mm, Split(mm));
                    }
                }
            }
            string[] Months = strMonths.Split(',');

            // 解析星期
            if (strWeeks.Contains("-"))
            {
                string[] temp = strWeeks.Split(',');
                foreach (string mm in temp)
                {
                    if (mm.Contains("-"))
                    {
                        strWeeks = strWeeks.Replace(mm, Split(mm));
                    }
                }
            }
            string[] Weeks = strWeeks.Split(',');

            // 解析日期
            if (strDays.Contains("-"))
            {
                string[] temp = strDays.Split(',');
                foreach (string mm in temp)
                {
                    if (mm.Contains("-"))
                    {
                        strDays = strDays.Replace(mm, Split(mm));
                    }
                }
            }
            string[] Days = strDays.Split(',');

            // 解析时间
            TimeSpan[] Times = new TimeSpan[strTimes.Split(',').Length];
            for (int i = 0; i < Times.Length; i++)
            {
                Times[i] = TimeSpan.Parse(strTimes.Split(',')[i]);
            }

            // 计算两年的时间，防止跨年不好用
            for (int year = DateTime.Now.Year; year < DateTime.Now.Year + 2; year++)
            {
                for (int i = 0; i < Months.Length; i++)
                {
                    string strDate = string.Format("{0}-{1}-1", year.ToString(), Months[i]);
                    DateTime date = DateTime.Parse(DateTime.Now.ToString(strDate));
                    while (date.Month.ToString() == Months[i].Trim())
                    {
                        DateTime lastDate = DateTime.Parse(date.AddMonths(1).ToString("yyyy-MM-01")).AddDays(-1);
                        // 根据星期筛选
                        for (int j = 0; j < Weeks.Length; j++)
                        {
                            int w = (int)date.DayOfWeek == 0 ? 7 : (int)date.DayOfWeek;
                            bool isAdd = false;
                            if (Weeks[j].Contains(w.ToString()) && !Weeks[j].Contains("L") && !Weeks[j].Contains("Fo") && !Weeks[j].Contains("T") && !Weeks[j].Contains("S") && !Weeks[j].Contains("F"))
                            {
                                isAdd = true;
                            }
                            // 最后一个
                            if (Weeks[j].Contains(w.ToString()) && Weeks[j].Contains("L"))
                            {
                                if (date.Day > lastDate.Day - 7)
                                {
                                    isAdd = true;
                                }
                            }
                            // 第一个
                            if (Weeks[j].Contains(w.ToString()) && Weeks[j].Contains("F"))
                            {
                                if (date.Day < 8)
                                {
                                    isAdd = true;
                                }
                            }
                            // 第二个
                            if (Weeks[j].Contains(w.ToString()) && Weeks[j].Contains("S"))
                            {
                                if (date.Day > 7 && date.Day < 15)
                                {
                                    isAdd = true;
                                }
                            }
                            // 第三个
                            if (Weeks[j].Contains(w.ToString()) && Weeks[j].Contains("T"))
                            {
                                if (date.Day > 14 && date.Day < 22)
                                {
                                    isAdd = true;
                                }
                            }
                            // 第四个
                            if (Weeks[j].Contains(w.ToString()) && Weeks[j].Contains("Fo"))
                            {
                                if (date.Day > 21 && date.Day < 29)
                                {
                                    isAdd = true;
                                }
                            }
                            if (isAdd)
                            {
                                foreach (var t in Times)
                                {
                                    list.Add(date + t);
                                }
                            }
                        }
                        // 根据日期筛选
                        for (int j = 0; j < Days.Length; j++)
                        {
                            bool isAdd = false;
                            if (Days[j].Contains("L*"))
                            {
                                if (date.Day == lastDate.Day)
                                {
                                    isAdd = true;
                                }
                            }
                            if (Days[j].Contains("LW"))
                            {
                                if (lastDate.DayOfWeek == DayOfWeek.Saturday)
                                {
                                    if (date.Day == lastDate.Day - 1)
                                    {
                                        isAdd = true;
                                    }
                                }
                                else if (lastDate.DayOfWeek == DayOfWeek.Sunday)
                                {
                                    if (date.Day == lastDate.Day - 2)
                                    {
                                        isAdd = true;
                                    }
                                }
                                else
                                {
                                    if (date.Day == lastDate.Day)
                                    {
                                        isAdd = true;
                                    }
                                }
                            }
                            if (Days[j].Contains("FW"))
                            {
                                if (lastDate.AddDays(-lastDate.Day + 1).DayOfWeek == DayOfWeek.Saturday)
                                {
                                    if (date.Day == 3)
                                    {
                                        isAdd = true;
                                    }
                                }
                                else if (lastDate.AddDays(-lastDate.Day + 1).DayOfWeek == DayOfWeek.Sunday)
                                {
                                    if (date.Day == 2)
                                    {
                                        isAdd = true;
                                    }
                                }
                                else
                                {
                                    if (date.Day == 1)
                                    {
                                        isAdd = true;
                                    }
                                }
                            }
                            if (Days[j].Trim() == date.Day.ToString())
                            {
                                isAdd = true;
                            }
                            if (isAdd)
                            {
                                foreach (var t in Times)
                                {
                                    list.Add(date + t);
                                }
                            }
                        }
                        date = date.AddDays(1);
                    }
                }
            }
            // 对比计算下一次执行时间
            foreach (var tt in list)
            {
                if (tt > time)
                {
                    if (result == DateTime.MinValue)
                    {
                        result = tt;
                    }
                    else if (result > tt)
                    {
                        result = tt;
                    }
                }
            }
            return result;
        }

        private static string Split(string str)
        {
            int[] temp = new int[2] { int.Parse(str.Split('-')[0]), int.Parse(str.Split('-')[1]) };
            string res = "";
            for (int i = temp[0]; i <= temp[1]; i++)
            {
                res += i.ToString() + ",";
            }
            return res.Remove(res.LastIndexOf(","), 1);
        }
    }

    /// <summary>
    /// 表达式合法性校验类
    /// </summary>
    public class ExpressionMatch
    {
        public static string Message
        {
            get; set;
        }

        public static bool IsMatch(string text)
        {
            Message = "";
            string month = @"^((1[0-2]|[0-9]|((1[0-2]|[1-9])-(1[0-2]|[1-9]))),)*(1[0-2]|[0-9]|((1[0-2]|[1-9])-(1[0-2]|[1-9])))$";
            string week = @"^([0-7]|L[0-7]|F[0-7]|S[0-7]|T[0-7]|Fo[0-7]|([1-7]-[1-7]),)*([0-7]|L[0-7]|F[0-7]|S[0-7]|T[0-7]|Fo[0-7]|([1-7]-[1-7]))$";
            string day = @"^(([0-9]|[1-2][0-9]|3[0-1]|(([1-9]|[1-2][0-9]|3[0-1])-([1-9]|[1-2][0-9]|3[0-1]))|LW|L\*|FW),)*([0-9]|[1-2][0-9]|3[0-1]|(([1-9]|[1-2][0-9]|3[0-1])-([1-9]|[1-2][0-9]|3[0-1]))|LW|L\*|FW)$";
            string time = @"^((([0-1]?[0-9])|(2[0-3])):([0-5]?[0-9]):([0-5]?[0-9]),)*(([0-1]?[0-9])|(2[0-3])):([0-5]?[0-9]):([0-5]?[0-9])$";
            if (text.Trim().Substring(0, 1) != "E")
            {
                Message = "表达式首字母必须为E";
                return false;
            }
            text = text.Trim().Substring(1);
            string[] strMatch = text.Split('^');
            if (strMatch.Length != 4)
            {
                Message = "表达式不完整";
                return false;
            }
            //验证月份表达式
            bool res = Regex.IsMatch(strMatch[0].Trim(), month);
            {
                if (!res)
                {
                    Message = "月份表达式存在非法字符或超出数值范围，不符合规则";
                    return false;
                }
                string[] matches = Matches(strMatch[0]);
                for (int i = 0; i < matches.Length; i++)
                {
                    string[] str = matches[i].Split('-');
                    if (int.Parse(str[0].Trim()) >= int.Parse(str[1].Trim()))
                    {
                        Message = "月份表达式连接符‘-’前后数值顺序不正确";
                        return false;
                    }
                }
            }
            // 验证星期表达式
            res = Regex.IsMatch(strMatch[1].Trim(), week);
            if (strMatch[1].Trim() != "")
            {
                if (!res)
                {
                    Message = "星期表达式存在非法字符或超出数值范围，不符合规则";
                    return false;
                }
                string[] matches = Matches(strMatch[1]);
                for (int i = 0; i < matches.Length; i++)
                {
                    string[] str = matches[i].Split('-');
                    if (int.Parse(str[0].Trim()) >= int.Parse(str[1].Trim()))
                    {
                        Message = "星期表达式连接符‘-’前后数值顺序不正确";
                        return false;
                    }
                }
            }
            // 验证日期表达式
            if (strMatch[2].Trim() != "")
            {
                res = Regex.IsMatch(strMatch[2].Trim(), day);
                {
                    if (!res)
                    {
                        Message = "日期表达式存在非法字符或超出数值范围，不符合规则";
                        return false;
                    }
                    string[] matches = Matches(strMatch[2]);
                    for (int i = 0; i < matches.Length; i++)
                    {
                        string[] str = matches[i].Split('-');
                        if (int.Parse(str[0].Trim()) >= int.Parse(str[1].Trim()))
                        {
                            Message = "日期表达式连接符‘-’前后数值顺序不正确";
                            return false;
                        }
                    }
                }
            }
            else if (!res)
            {
                Message = "日期表达式、星期表达式不能同时为空，不符合规则";
                return false;
            }
            // 验证时间表达式
            res = Regex.IsMatch(strMatch[3].Trim(), time);
            {
                if (!res)
                {
                    Message = "时间表达式存在非法字符或超出数值范围，不符合规则";
                    return false;
                }
            }
            return true;
        }

        private static string[] Matches(string str)
        {
            Regex regex1 = new Regex(@"[1-3]?[0-9]?-[1-3]?[0-9]?");
            MatchCollection matchs1 = regex1.Matches(str);
            string[] matchs = new string[matchs1.Count];
            for (int i = 0; i < matchs1.Count; i++)
            {
                matchs[i] = matchs1[i].ToString();
            }
            return matchs;
        }
    }
}
