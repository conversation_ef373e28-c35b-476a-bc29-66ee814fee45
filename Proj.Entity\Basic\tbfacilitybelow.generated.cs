﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbFacilityBelow and List
    [Serializable]
    [Description("设备工作不合格情况表")]
    [LinqToDB.Mapping.Table("TB_FACILITY_BELOW")]
    public partial class TbFacilityBelow : GEntity<TbFacilityBelow>, ITimestamp
    {
        #region Contructor(s)

        private TbFacilityBelow()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CFacilitybelowId = RegisterProperty<String>(p => p.CFacilitybelowId);
        private static readonly PropertyInfo<String> pty_CFacilitylistId = RegisterProperty<String>(p => p.CFacilitylistId);
        private static readonly PropertyInfo<String> pty_CFacilitylistName = RegisterProperty<String>(p => p.CFacilitylistName);
        private static readonly PropertyInfo<String> pty_CState = RegisterProperty<String>(p => p.CState);
        private static readonly PropertyInfo<String> pty_CProductId = RegisterProperty<String>(p => p.CProductId);
        private static readonly PropertyInfo<String> pty_CBelowDes = RegisterProperty<String>(p => p.CBelowDes);
        private static readonly PropertyInfo<String> pty_CBelowtime = RegisterProperty<String>(p => p.CBelowtime);
        private static readonly PropertyInfo<String> pty_CFacilityId = RegisterProperty<String>(p => p.CFacilityId);
        private static readonly PropertyInfo<String> pty_CFacilityName = RegisterProperty<String>(p => p.CFacilityName);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 设备工作不合格情况编号
        /// </summary>
        [Description("设备工作不合格情况编号")]
        [LinqToDB.Mapping.Column("C_FACILITYBELOWID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CFacilitybelowId
        {
            get { return GetProperty(pty_CFacilitybelowId); }
            set { SetProperty(pty_CFacilitybelowId, value); }
        }
        /// <summary>
        /// 个体设备编号
        /// </summary>
        [Description("个体设备编号")]
        [LinqToDB.Mapping.Column("C_FACILITYLISTID")]
        public String CFacilitylistId
        {
            get { return GetProperty(pty_CFacilitylistId); }
            set { SetProperty(pty_CFacilitylistId, value); }
        }
        /// <summary>
        /// 个体设备名称
        /// </summary>
        [Description("个体设备名称")]
        [LinqToDB.Mapping.Column("C_FACILITYLISTNAME")]
        public String CFacilitylistName
        {
            get { return GetProperty(pty_CFacilitylistName); }
            set { SetProperty(pty_CFacilitylistName, value); }
        }
        /// <summary>
        /// 个体设备状态
        /// </summary>
        [Description("个体设备状态")]
        [LinqToDB.Mapping.Column("C_STATE")]
        public String CState
        {
            get { return GetProperty(pty_CState); }
            set { SetProperty(pty_CState, value); }
        }
        /// <summary>
        /// 产品编号
        /// </summary>
        [Description("产品编号")]
        [LinqToDB.Mapping.Column("C_PRODUCTID")]
        public String CProductId
        {
            get { return GetProperty(pty_CProductId); }
            set { SetProperty(pty_CProductId, value); }
        }
        /// <summary>
        /// 不合格原因
        /// </summary>
        [Description("不合格原因")]
        [LinqToDB.Mapping.Column("C_BELOWDES")]
        public String CBelowDes
        {
            get { return GetProperty(pty_CBelowDes); }
            set { SetProperty(pty_CBelowDes, value); }
        }
        /// <summary>
        /// 不合格触发时间
        /// </summary>
        [Description("不合格触发时间")]
        [LinqToDB.Mapping.Column("C_BELOWTIME")]
        public String CBelowtime
        {
            get { return GetProperty(pty_CBelowtime); }
            set { SetProperty(pty_CBelowtime, value); }
        }
        /// <summary>
        /// 设备编码
        /// </summary>
        [Description("设备编码")]
        [LinqToDB.Mapping.Column("C_FACILITYID")]
        public String CFacilityId
        {
            get { return GetProperty(pty_CFacilityId); }
            set { SetProperty(pty_CFacilityId, value); }
        }
        /// <summary>
        /// 设备名称
        /// </summary>
        [Description("设备名称")]
        [LinqToDB.Mapping.Column("C_FACILITYNAME")]
        public String CFacilityName
        {
            get { return GetProperty(pty_CFacilityName); }
            set { SetProperty(pty_CFacilityName, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion


        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CFacilitybelowId, "设备工作不合格情况编号是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilitybelowId, 36, "设备工作不合格情况编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilitylistId, 36, "个体设备编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilitylistName, 40, "个体设备名称不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CState, 40, "个体设备状态不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProductId, 36, "产品编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CBelowDes, 100, "不合格原因不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CBelowtime, 76, "不合格触发时间不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityId, 36, "设备编码不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityName, 100, "设备名称不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 40, "扩展字段A不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 40, "扩展字段B不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 40, "扩展字段C不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CFacilitybelowId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbFacilityBelowList : GEntityList<TbFacilityBelowList, TbFacilityBelow>
    {
        private TbFacilityBelowList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
