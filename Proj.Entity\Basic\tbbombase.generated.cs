﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;


 
namespace Proj.Entity
{
    #region TbBomBase and List
    [Serializable]
    [Description("BOM基础表")]
    [LinqToDB.Mapping.Table("TB_BOM_BASE")]
    public partial class TbBomBase : GEntity<TbBomBase>, ITimestamp
    {
        #region Contructor(s)

        private TbBomBase()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CMainId = RegisterProperty<String>(p => p.CMainId);
        private static readonly PropertyInfo<Int64?> pty_CDisplayNo = RegisterProperty<Int64?>(p => p.CDisplayNo);
        private static readonly PropertyInfo<String> pty_CChildCode = RegisterProperty<String>(p => p.CChildCode);
        private static readonly PropertyInfo<String> pty_CChildName = RegisterProperty<String>(p => p.CChildName);
        private static readonly PropertyInfo<String> pty_CChildspec = RegisterProperty<String>(p => p.CChildspec);
        private static readonly PropertyInfo<String> pty_CChilddrawnumber = RegisterProperty<String>(p => p.CChilddrawnumber);
        private static readonly PropertyInfo<String> pty_CParentCode = RegisterProperty<String>(p => p.CParentCode);
        private static readonly PropertyInfo<String> pty_CParentName = RegisterProperty<String>(p => p.CParentName);
        private static readonly PropertyInfo<String> pty_CParentspec = RegisterProperty<String>(p => p.CParentspec);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        private static readonly PropertyInfo<String> pty_CTargetCode = RegisterProperty<String>(p => p.CTargetCode);
        private static readonly PropertyInfo<String> pty_CTargetName = RegisterProperty<String>(p => p.CTargetName);
        private static readonly PropertyInfo<String> pty_CTargetspec = RegisterProperty<String>(p => p.CTargetspec);
        private static readonly PropertyInfo<String> pty_CParentdrawnumber = RegisterProperty<String>(p => p.CParentdrawnumber);
        private static readonly PropertyInfo<Int64?> pty_NChildcount = RegisterProperty<Int64?>(p => p.NChildcount);
        private static readonly PropertyInfo<Double?> pty_NRadix = RegisterProperty<Double?>(p => p.NRadix);
        private static readonly PropertyInfo<Double?> pty_NWaste = RegisterProperty<Double?>(p => p.NWaste);
        private static readonly PropertyInfo<Int64?> pty_NLayer = RegisterProperty<Int64?>(p => p.NLayer);
        private static readonly PropertyInfo<String> pty_CRemark = RegisterProperty<String>(p => p.CRemark);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CTargetnumber = RegisterProperty<String>(p => p.CTargetnumber);
        private static readonly PropertyInfo<String> pty_CFmainId = RegisterProperty<String>(p => p.CFmainId);
        private static readonly PropertyInfo<String> pty_CProductId = RegisterProperty<String>(p => p.CProductId);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("C_MAINID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CMainId
        {
            get { return GetProperty(pty_CMainId); }
            set { SetProperty(pty_CMainId, value); }
        }
        /// <summary>
        /// 序号
        /// </summary>
        [Description("序号")]
        [LinqToDB.Mapping.Column("C_DISPLAYNO")]
        public Int64? CDisplayNo
        {
            get { return GetProperty(pty_CDisplayNo); }
            set { SetProperty(pty_CDisplayNo, value); }
        }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        [LinqToDB.Mapping.Column("C_CHILDCODE")]
        public String CChildCode
        {
            get { return GetProperty(pty_CChildCode); }
            set { SetProperty(pty_CChildCode, value); }
        }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        [LinqToDB.Mapping.Column("C_CHILDNAME")]
        public String CChildName
        {
            get { return GetProperty(pty_CChildName); }
            set { SetProperty(pty_CChildName, value); }
        }
        /// <summary>
        /// 物料规格
        /// </summary>
        [Description("物料规格")]
        [LinqToDB.Mapping.Column("C_CHILDSPEC")]
        public String CChildspec
        {
            get { return GetProperty(pty_CChildspec); }
            set { SetProperty(pty_CChildspec, value); }
        }
        /// <summary>
        /// 物料代号
        /// </summary>
        [Description("物料代号")]
        [LinqToDB.Mapping.Column("C_CHILDDRAWNUMBER")]
        public String CChilddrawnumber
        {
            get { return GetProperty(pty_CChilddrawnumber); }
            set { SetProperty(pty_CChilddrawnumber, value); }
        }
        /// <summary>
        /// 父物料编码
        /// </summary>
        [Description("父物料编码")]
        [LinqToDB.Mapping.Column("C_PARENTCODE")]
        public String CParentCode
        {
            get { return GetProperty(pty_CParentCode); }
            set { SetProperty(pty_CParentCode, value); }
        }
        /// <summary>
        /// 父物料名称
        /// </summary>
        [Description("父物料名称")]
        [LinqToDB.Mapping.Column("C_PARENTNAME")]
        public String CParentName
        {
            get { return GetProperty(pty_CParentName); }
            set { SetProperty(pty_CParentName, value); }
        }
        /// <summary>
        /// 父物料规格
        /// </summary>
        [Description("父物料规格")]
        [LinqToDB.Mapping.Column("C_PARENTSPEC")]
        public String CParentspec
        {
            get { return GetProperty(pty_CParentspec); }
            set { SetProperty(pty_CParentspec, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        /// <summary>
        /// 替换物料编码
        /// </summary>
        [Description("替换物料编码")]
        [LinqToDB.Mapping.Column("C_TARGETCODE")]
        public String CTargetCode
        {
            get { return GetProperty(pty_CTargetCode); }
            set { SetProperty(pty_CTargetCode, value); }
        }
        /// <summary>
        /// 替换物料名称
        /// </summary>
        [Description("替换物料名称")]
        [LinqToDB.Mapping.Column("C_TARGETNAME")]
        public String CTargetName
        {
            get { return GetProperty(pty_CTargetName); }
            set { SetProperty(pty_CTargetName, value); }
        }
        /// <summary>
        /// 替换物料规格
        /// </summary>
        [Description("替换物料规格")]
        [LinqToDB.Mapping.Column("C_TARGETSPEC")]
        public String CTargetspec
        {
            get { return GetProperty(pty_CTargetspec); }
            set { SetProperty(pty_CTargetspec, value); }
        }
        /// <summary>
        /// 父物料代号
        /// </summary>
        [Description("父物料代号")]
        [LinqToDB.Mapping.Column("C_PARENTDRAWNUMBER")]
        public String CParentdrawnumber
        {
            get { return GetProperty(pty_CParentdrawnumber); }
            set { SetProperty(pty_CParentdrawnumber, value); }
        }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        [LinqToDB.Mapping.Column("N_CHILDCOUNT")]
        public Int64? NChildcount
        {
            get { return GetProperty(pty_NChildcount); }
            set { SetProperty(pty_NChildcount, value); }
        }
        /// <summary>
        /// 基数
        /// </summary>
        [Description("基数")]
        [LinqToDB.Mapping.Column("N_RADIX")]
        public Double? NRadix
        {
            get { return GetProperty(pty_NRadix); }
            set { SetProperty(pty_NRadix, value); }
        }
        /// <summary>
        /// 损耗率
        /// </summary>
        [Description("损耗率")]
        [LinqToDB.Mapping.Column("N_WASTE")]
        public Double? NWaste
        {
            get { return GetProperty(pty_NWaste); }
            set { SetProperty(pty_NWaste, value); }
        }
        /// <summary>
        /// 层
        /// </summary>
        [Description("层")]
        [LinqToDB.Mapping.Column("N_LAYER")]
        public Int64? NLayer
        {
            get { return GetProperty(pty_NLayer); }
            set { SetProperty(pty_NLayer, value); }
        }
        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        [LinqToDB.Mapping.Column("C_REMARK")]
        public String CRemark
        {
            get { return GetProperty(pty_CRemark); }
            set { SetProperty(pty_CRemark, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 替换物料代码
        /// </summary>
        [Description("替换物料代码")]
        [LinqToDB.Mapping.Column("C_TARGETNUMBER")]
        public String CTargetnumber
        {
            get { return GetProperty(pty_CTargetnumber); }
            set { SetProperty(pty_CTargetnumber, value); }
        }
        /// <summary>
        /// 父类主键
        /// </summary>
        [Description("父类主键")]
        [LinqToDB.Mapping.Column("C_FMAINID")]
        public String CFmainId
        {
            get { return GetProperty(pty_CFmainId); }
            set { SetProperty(pty_CFmainId, value); }
        }
        /// <summary>
        /// 产品编号
        /// </summary>
        [Description("产品编号")]
        [LinqToDB.Mapping.Column("C_PRODUCTID")]
        public String CProductId
        {
            get { return GetProperty(pty_CProductId); }
            set { SetProperty(pty_CProductId, value); }
        }

        private object tag;
        public object Tag
        {
            get { return tag; }
            set { tag=value; }
        }

        /// <summary>
        /// 序号
        /// </summary>
        private int intNo;
        public int IntNo
        {
            get { return intNo; }
            set { intNo = value; }
        }
        
        #endregion 
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CMainId, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMainId, 40, "主键不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CChildCode, 30, "物料编码不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CChildName, 80, "物料名称不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CChildspec, 80, "物料规格不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CChilddrawnumber, 30, "物料代号不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParentCode, 30, "父物料编码不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParentName, 80, "父物料名称不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParentspec, 80, "父物料规格不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CTargetCode, 30, "替换物料编码不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CTargetName, 80, "替换物料名称不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CTargetspec, 80, "替换物料规格不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParentdrawnumber, 30, "父物料代号不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CRemark, 500, "备注不能超过500个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CTargetnumber, 30, "替换物料代码不能超过30个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFmainId, 40, "父类主键不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CProductId, 40, "产品编号不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CMainId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbBomBaseList : GEntityList<TbBomBaseList, TbBomBase>
    {
        private TbBomBaseList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
