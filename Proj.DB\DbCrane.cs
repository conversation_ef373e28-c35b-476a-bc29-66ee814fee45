﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Proj.Entity;
using Proj.DataTypeDef;

namespace Proj.DB
{
    public class DbCrane
    {        
        private static DbCrane m_Instanse;
        private static readonly object mSyncObject = new object();
        private DbCrane() { }
        public static DbCrane Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new DbCrane();
                        }
                    }
                }
                return m_Instanse;
            }
        }
        public TpCraneList GetDbCraneList()
        {
            try
            {
                TpCraneList tpCraneList = TpCraneList.GetAll();
                return tpCraneList;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return null;
        }

        /// <summary>
        /// 获取Crane在数据库中的信息
        /// </summary>
        /// <param name="craneNo">tpCrane的编号</param>
        /// <returns></returns>
        public TpCrane GetCraneDbInfoByNo(int craneNo)
        {
            TpCrane tpCrane = null;
            try
            {
                tpCrane = TpCrane.GetById(craneNo);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return tpCrane;
        }
        /// <summary>
        /// 获取Crane在数据库中的信息
        /// </summary>
        /// <param name="craneName">crane名称</param>
        /// <returns></returns>
        public TpCrane GetCraneDbInfoByName(string craneName)
        {
            TpCrane tpCrane = null;
            try
            {
                tpCrane = TpCrane.GetById(TpCraneList.GetByLambda(x => x.Name == craneName).First().No);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return tpCrane;
        }       
        /// <summary>
        /// 在数据库中添加一条Crane信息
        /// </summary>
        /// <param name="craneNo">tpCrane编号</param>
        /// <param name="craneName">tpCrane名称</param>
        /// <param name="isHaveIdr">是否有读码器，默认为有</param>
        /// <returns></returns>
        public bool AddCraneInfo(int craneNo, string craneName, bool isHaveIdr = true)
        {
            try
            {
                TpCrane tpC = TpCrane.GetById(craneNo);
                //if (null!= tpC)
                if (TpCrane.Exists(craneNo))
                {
                    //记录Crane日志：向数据库中添加Crane信息失败{craneNo}已存在
                    return false;
                }
                
                TpCrane tpCrane = TpCrane.New();
                tpCrane.No = craneNo;
                tpCrane.Name = craneName;
                tpCrane.IsIdr = isHaveIdr ? 1 : 0;
                tpCrane.OperState = "1";
                //tpCrane.CommandId = "";
                //tpCrane.SpeedLoad = 20;
                //tpCrane.SpeedEmpty = 50;
                //tpCrane.SpeedXAxis = 100;
                //tpCrane.SpeedYAxis = 100;
                //tpCrane.SpeedZAxis = 100;
                //tpCrane.SpeedTAxis = 100;
                //tpCrane.MileageTravel = 1.0;
                //tpCrane.MileageElevation = 1.0;
                //tpCrane.MileageTurn = 1.0;
                //tpCrane.MileageFork = 1.0;
                //tpCrane.Comment = "";

                tpCrane.Save();
                //记录Crane日志：向数据库中添加Crane信息{craneNo,CraneName, isHavaIdr}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中删除一条Crane信息
        /// </summary>
        /// <param name="craneNo">tpCrane编号</param>
        /// <returns></returns>
        public bool DeleteCraneDbInfo(int craneNo)
        {
            try
            {
                TpCraneList.DeleteByCriteria(x => x.No == craneNo);
                //记录Crane日志：从数据库中删除Crane信息{craneNo}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 修改数据库中Crane的名称
        /// </summary>
        /// <param name="craneNo">Crane编号</param>
        /// <param name="craneNewName">Crane新的名称</param>
        /// <returns></returns>
        public bool ChangeCraneName(int craneNo, string craneNewName)
        {
            try
            {
                //更新一条记录的第一种写法
                //TpCraneList craneList = TpCraneList.GetByLambda(x => x.No == craneNo);
                //foreach (TpCrane tpCrane in craneList)
                //{
                //    tpCrane.Name = craneNewName;
                //}
                //craneList.Save();

                //更新一条记录的第二种写法
                //TpCrane tpCrane = TpCrane.GetById(TpCraneList.GetByLambda(x => x.No == craneNo).First().No);
                //tpCrane.Name = craneNewName;
                //tpCrane.Save();

                //更新一条记录的第三种写法
                TpCrane tpCrane = TpCrane.GetById(craneNo);
                tpCrane.Name = craneNewName;
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane名称{craneNo, craneNewName}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 在数据库中设置Crane是否有读码器的信息
        /// </summary>
        /// <param name="craneNo">Crane编号</param>
        /// <param name="flag">是否有读码器</param>
        /// <returns></returns>
        public bool SetCraneIsHaveIDR(int craneNo, bool flag)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(craneNo);
                tpCrane.IsIdr = flag ? 1 : 0;
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane读码器{craneNo, flag}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 查询数据库中Crane是否有读码器
        /// </summary>
        /// <param name="craneNo"></param>
        /// <returns></returns>
        public bool IsCraneHaveIDR(int craneNo)
        {
            bool bRes = false;
            try
            {
                TpCrane tpCrane = TpCrane.GetById(craneNo);
                bRes = tpCrane.IsIdr == 1 ? true : false;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 更新数据库中Crane的工作状态
        /// </summary>
        /// <param name="craneNo">tpCrane编号</param>
        /// <param name="state">工作状态</param>
        /// <returns></returns>
        public bool UpdateCraneOperationState(int craneNo, CraneOperationState state)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(craneNo);
                tpCrane.OperState = ((int)state).ToString();
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane服务状态{craneNo, state}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool UpdateCraneOperationState(string craneName, CraneOperationState state)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(TpCraneList.GetByLambda(x => x.Name == craneName).First().No);
                tpCrane.OperState = ((int)state).ToString();
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane服务状态{craneNo, state}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        /// <summary>
        /// 查询数据库中Crane的工作状态
        /// </summary>
        /// <param name="craneNo"></param>
        /// <returns></returns>
        public CraneOperationState GetCraneOperationState(int craneNo)
        {
            try 
            {
                TpCrane tpCrane = TpCrane.GetById(craneNo);
                return (CraneOperationState)int.Parse(tpCrane.OperState);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return CraneOperationState.None;
        }
        /// <summary>
        /// 更新数据库中Crane的速度设置信息
        /// </summary>
        /// <param name="craneNo">tpCrane编号</param>
        /// <param name="craneSpeed">tpCrane速度信息</param>
        /// <returns></returns>
        public bool UpdateCraneSpeedSetting(int craneNo, CraneSpeed craneSpeed)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(craneNo);
                tpCrane.SpeedEmpty = craneSpeed.emptySpeed;
                tpCrane.SpeedLoad = craneSpeed.loadSpeed;
                tpCrane.SpeedXAxis = craneSpeed.xAxisSpeed;
                tpCrane.SpeedYAxis = craneSpeed.yAxisSpeed;
                tpCrane.SpeedZAxis = craneSpeed.zAxisSpeed;
                tpCrane.SpeedTAxis = craneSpeed.tAxisSpeed;
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane的速度设定值{craneNo, craneSpeed}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        public bool UpdateCraneSpeedSetting(string craneName, CraneSpeed craneSpeed)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(TpCraneList.GetByLambda(x => x.Name == craneName).First().No);
                tpCrane.SpeedEmpty = craneSpeed.emptySpeed;
                tpCrane.SpeedLoad = craneSpeed.loadSpeed;
                tpCrane.SpeedXAxis = craneSpeed.xAxisSpeed;
                tpCrane.SpeedYAxis = craneSpeed.yAxisSpeed;
                tpCrane.SpeedZAxis = craneSpeed.zAxisSpeed;
                tpCrane.SpeedTAxis = craneSpeed.tAxisSpeed;
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane的速度设定值{craneNo, craneSpeed}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 获取数据库中tpCrane的速度设定值
        /// </summary>
        /// <param name="craneNo">tpCrane编号</param>
        /// <returns></returns>
        public CraneSpeed GetCraneSpeedSetting(int craneNo)
        {
            CraneSpeed speed = new CraneSpeed();
            try
            {
                TpCrane tpCrane = TpCrane.GetById(craneNo);
                speed.emptySpeed = (int)tpCrane.SpeedEmpty;
                speed.loadSpeed = (int)tpCrane.SpeedLoad;
                speed.xAxisSpeed = (int)tpCrane.SpeedXAxis;
                speed.yAxisSpeed = (int)tpCrane.SpeedYAxis;
                speed.zAxisSpeed = (int)tpCrane.SpeedZAxis;
                speed.tAxisSpeed = (int)tpCrane.SpeedTAxis;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return speed;
        }
        /// <summary>
        /// 更新数据库中Crane的里程信息
        /// </summary>
        /// <param name="craneNo">tpCrane编号</param>
        /// <param name="mileage">tpCrane里程信息</param>
        /// <returns></returns>
        public bool UpdateCraneMileage(int craneNo, AxisMileage mileage)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(craneNo);
                tpCrane.MileageTravel = mileage.travelMileage;
                tpCrane.MileageElevation = mileage.elevationMileage;
                tpCrane.MileageTurn = mileage.turnMileage;
                tpCrane.MileageFork = mileage.forkMileage;
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane的里程数据{craneNo, mileage}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        public bool UpdateCraneMileage(string craneName, AxisMileage mileage)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(TpCraneList.GetByLambda(x => x.Name == craneName).First().No);
                tpCrane.MileageTravel = mileage.travelMileage;
                tpCrane.MileageElevation = mileage.elevationMileage;
                tpCrane.MileageTurn = mileage.turnMileage;
                tpCrane.MileageFork = mileage.forkMileage;
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane的里程数据{craneNo, mileage}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 更新数据库中Crane的备注信息
        /// </summary>
        /// <param name="craneNo"></param>
        /// <param name="comment"></param>
        /// <returns></returns>
        public bool UpdateCraneComment(int craneNo, string comment)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(craneNo);
                tpCrane.Comment = comment;
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane的备注信息{craneNo, comment}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        public bool UpdateCraneComment(string craneName, string comment)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(TpCraneList.GetByLambda(x => x.Name == craneName).First().No);
                tpCrane.Comment = comment;
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane的备注信息{craneNo, comment}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
        /// <summary>
        /// 获取数据库中tpCrane的备注信息
        /// </summary>
        /// <param name="craneNo"></param>
        /// <returns></returns>
        public string GetCraneCommnet(int craneNo)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(craneNo);
                return tpCrane.Comment;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return "";
        }
        public string GetCraneCommnet(string craneName)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(TpCraneList.GetByLambda(x => x.Name == craneName).First().No);
                return tpCrane.Comment;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return "";
        }


        public bool UpdateCraneCommandID(string craneName, string commandID)
        {
            try
            {
                TpCrane tpCrane = TpCrane.GetById(TpCraneList.GetByLambda(x => x.Name == craneName).First().No);
                tpCrane.CommandId = commandID;
                tpCrane.Save();
                //记录Crane日志：在数据库中修改Crane的备注信息{craneNo, commandID}
                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool UpdateUpdateCraneXAxisDistance(string strValue)
        {
            try
            {
                TpStatistic tpST = TpStatistic.GetByLambda(x => x.Name == "XAxisDistance");
                if (tpST != null)
                {
                    tpST.Value = strValue;
                    tpST.Save();
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("DbCrane.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }
        }
    }
}
