using Secs4Net;

namespace Proj.HostComm
{
    /// <summary>
    /// Host Command Acknowledge (HCACK) 枚举
    /// 用于回复主机命令的确认状态
    /// </summary>
    public enum HCACK : byte
    {
        /// <summary>
        /// 命令被接受并执行
        /// </summary>
        Acknowledge = 0,
        
        /// <summary>
        /// 命令被拒绝，参数无效
        /// </summary>
        InvalidParameter = 1,
        
        /// <summary>
        /// 命令被拒绝，设备无法执行
        /// </summary>
        CannotPerform = 2,
        
        /// <summary>
        /// 命令被拒绝，设备已经在执行中
        /// </summary>
        AlreadyInDesiredCondition = 3,
        
        /// <summary>
        /// 命令不存在
        /// </summary>
        CommandNotExist = 4,
        
        /// <summary>
        /// 命令被拒绝，其他原因
        /// </summary>
        Rejected = 5
    }

    /// <summary>
    /// GEM 操作结果枚举
    /// </summary>
    public enum GemResult
    {
        /// <summary>
        /// 操作成功
        /// </summary>
        Success = 0,
        
        /// <summary>
        /// 操作失败
        /// </summary>
        Failed = 1,
        
        /// <summary>
        /// 功能未实现
        /// </summary>
        NotImplemented = 2,
        
        /// <summary>
        /// 读取设备值错误
        /// </summary>
        ReadEquipmentValError = 3,
        
        /// <summary>
        /// 写入设备值错误
        /// </summary>
        WriteEquipmentValError = 4,
        
        /// <summary>
        /// 参数无效
        /// </summary>
        InvalidParameter = 5,
        
        /// <summary>
        /// 超时
        /// </summary>
        Timeout = 6
    }

    /// <summary>
    /// Online Acknowledge (ONLACK) 枚举
    /// 用于回复在线请求
    /// </summary>
    public enum ONLACK : byte
    {
        /// <summary>
        /// 接受在线请求
        /// </summary>
        Accept = 0,
        
        /// <summary>
        /// 拒绝在线请求，设备不可用
        /// </summary>
        NotReady = 1,
        
        /// <summary>
        /// 拒绝在线请求，已经在线
        /// </summary>
        AlreadyOnline = 2
    }

    /// <summary>
    /// Equipment Acknowledge (EAC) 枚举
    /// 用于设备常量设置确认
    /// </summary>
    public enum EAC : byte
    {
        /// <summary>
        /// 接受设置
        /// </summary>
        Acknowledge = 0,
        
        /// <summary>
        /// 拒绝设置，参数无效
        /// </summary>
        InvalidParameter = 1,
        
        /// <summary>
        /// 拒绝设置，格式错误
        /// </summary>
        InvalidFormat = 2,
        
        /// <summary>
        /// 拒绝设置，常量不存在
        /// </summary>
        ConstantNotExist = 3,
        
        /// <summary>
        /// 拒绝设置，设备忙碌
        /// </summary>
        Busy = 4
    }

    /// <summary>
    /// SECS 错误代码枚举
    /// </summary>
    public enum SecsErrorCode
    {
        /// <summary>
        /// 成功
        /// </summary>
        Success = 0,
        
        /// <summary>
        /// 未知错误
        /// </summary>
        UnknownError = 1,
        
        /// <summary>
        /// 消息格式错误
        /// </summary>
        InvalidMessageFormat = 2,
        
        /// <summary>
        /// 功能不支持
        /// </summary>
        FunctionNotSupported = 3,
        
        /// <summary>
        /// 数据长度错误
        /// </summary>
        InvalidDataLength = 4,
        
        /// <summary>
        /// 超时
        /// </summary>
        Timeout = 5,
        
        /// <summary>
        /// 连接错误
        /// </summary>
        ConnectionError = 6
    }

    /// <summary>
    /// HSMS 会话信息
    /// </summary>
    public class HsmsSessionInfo
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        public uint SessionId { get; set; }
        
        /// <summary>
        /// 远程IP地址
        /// </summary>
        public string RemoteAddress { get; set; } = string.Empty;
        
        /// <summary>
        /// 远程端口
        /// </summary>
        public int RemotePort { get; set; }
        
        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime ConnectedTime { get; set; }
        
        /// <summary>
        /// 是否活动连接
        /// </summary>
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// SECS 数据项包装类
    /// 用于兼容原有的 SecsItem 接口
    /// </summary>
    public class SecsItem
    {
        private Item _item;
        
        public SecsItem(SecsFormat format)
        {
            Format = format;
            _item = format switch
            {
                SecsFormat.List => Item.L(),
                SecsFormat.ASCII => Item.A(""),
                SecsFormat.U1 => Item.U1(0),
                SecsFormat.U2 => Item.U2(0),
                SecsFormat.U4 => Item.U4(0),
                SecsFormat.I1 => Item.I1(0),
                SecsFormat.I2 => Item.I2(0),
                SecsFormat.I4 => Item.I4(0),
                SecsFormat.F4 => Item.F4(0),
                SecsFormat.F8 => Item.F8(0),
                SecsFormat.Binary => Item.B(),
                SecsFormat.Boolean => Item.Boolean(false),
                _ => Item.L()
            };
        }
        
        public SecsItem(Item item)
        {
            _item = item;
            Format = item.Format;
        }
        
        public SecsFormat Format { get; private set; }
        
        public object? Value 
        { 
            get => _item.GetValue();
            set => SetValue(value);
        }
        
        public List<SecsItem> ItemList { get; private set; } = new List<SecsItem>();
        
        public void AppendItem(SecsItem item)
        {
            ItemList.Add(item);
            // 这里需要重新构建 Item
            if (Format == SecsFormat.List)
            {
                var items = ItemList.Select(si => si._item).ToArray();
                _item = Item.L(items);
            }
        }
        
        private void SetValue(object? value)
        {
            if (value == null) return;
            
            _item = Format switch
            {
                SecsFormat.ASCII => Item.A(value.ToString() ?? ""),
                SecsFormat.U1 => Item.U1(Convert.ToByte(value)),
                SecsFormat.U2 => Item.U2(Convert.ToUInt16(value)),
                SecsFormat.U4 => Item.U4(Convert.ToUInt32(value)),
                SecsFormat.I1 => Item.I1(Convert.ToSByte(value)),
                SecsFormat.I2 => Item.I2(Convert.ToInt16(value)),
                SecsFormat.I4 => Item.I4(Convert.ToInt32(value)),
                SecsFormat.F4 => Item.F4(Convert.ToSingle(value)),
                SecsFormat.F8 => Item.F8(Convert.ToDouble(value)),
                SecsFormat.Boolean => Item.Boolean(Convert.ToBoolean(value)),
                _ => _item
            };
        }
        
        public static SecsItem CreateBinaryItem(byte value)
        {
            return new SecsItem(Item.B(value));
        }
        
        public Item ToSecs4NetItem()
        {
            return _item;
        }
    }
}
