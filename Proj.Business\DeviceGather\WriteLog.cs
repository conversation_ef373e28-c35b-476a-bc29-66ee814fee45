﻿using Proj.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Proj.Business.DeviceGather
{
    public class WriteLog
    {
        public static OprationResult WriteJobExeLog(string[] args)
        {
            OprationResult result = new OprationResult { strCode = "0" };
            try
            {
                using (var trans = new Csla.Transaction.TransactionScope())
                {
                    TntJobexcuteresult log = TntJobexcuteresult.New();
                    log.CPk = SS.BLL.Base.SequenceService.GenerateShortId(); ;
                    log.CJobgroupCode = args[0];
                    log.CExcutemethod = args[1];
                    log.CMessageType = args[2];
                    log.CSenddate = DateTime.Now;
                    log.CMessagestate = args[3];
                    log.CMessageerrorrror = args[4];
                    log.Save();
                    trans.Complete();
                }
            }
            catch (Exception ex)
            {
                result.strCode = "1";
                result.strMessage = ex.Message + Environment.NewLine;
                //填写错误日志
                //SS.Base.Log.Default.Error("更新数据字典错误消息：" + result.strMessage + "请手动处理查询原因!");
                // by chengbao
                Log.Logger.Instance.ExceptionLog("WriteLog.WriteJobExeLog Error: " + ex.Message + ", Stack: " + ex.StackTrace);
                Logger.WriteDB(DateTime.Now, SS.Base.LogMananger.LogType.ExceptionLog, 0, "Proj.Business", "一般", "更新数据字典错误消息：" + result.strMessage + "请手动处理查询原因!");

            }
            finally
            {
                if (result.strCode == "0")
                {
                    result.strMessage = "数据存储完成！";
                }
            }
            return result;
        }
    }
}
