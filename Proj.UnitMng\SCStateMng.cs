﻿using Proj.CacheData;
using Proj.DataTypeDef;
using Proj.Entity;
using Proj.WCF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Proj.UnitMng
{
    public class SCStateMng
    {
        private static SCStateMng m_Instanse;
        private static readonly object mSyncObject = new object();
        private SCStateMng() { }
        public static SCStateMng Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new SCStateMng();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public void SetState(SCState newState)
        {
            Log.Logger.Instance.SCLog("New SC State: " + newState.ToString());
            GlobalData.Instance.gbSCState = newState;

            TntKeyvalueconfig tntKeyvalueconfig = TntKeyvalueconfig.GetByLambda(x => x.Key == "SC State");
            if (tntKeyvalueconfig != null)
            {
                tntKeyvalueconfig.Vlaue = newState.ToString();
                tntKeyvalueconfig.Save();
            }
        }
    }
}
