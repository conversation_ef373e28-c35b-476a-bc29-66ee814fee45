﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Proj.Entity;
using Proj.DataTypeDef;
//using Proj.WCF;
using Proj.Service;

namespace Proj.DB
{
    public class DbLocation
    {
        private static DbLocation m_Instanse;
        private static readonly object mSyncObject = new object();
        private DbLocation() { }
        public static DbLocation Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new DbLocation();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public TpLocationList GetDbLocationList()
        {
            try
            {
                TpLocationList tpLocationList = TpLocationList.GetAll();
                return tpLocationList;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbLocation.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return null;
        }

        public LocationType GetLocationType(string address)
        {
            try
            {
                TpLocation tpLocation = TpLocation.GetById(address);
                return (LocationType)uint.Parse(tpLocation.Type);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbLocation.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return LocationType.Unknown;
        }

        public bool ReserveLocation(string address)
        {
            try
            {
                TpLocation tpLocation = TpLocation.GetById(address);
                tpLocation.IsReserved = 1;
                tpLocation.Save();

                //通知界面更新
                //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                //dicParams.Add("ADDRESS", address);
                //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbLocation.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool UnReserveLocation(string address)
        {
            try
            {
                TpLocation tpLocation = TpLocation.GetById(address);
                tpLocation.IsReserved = 0;
                tpLocation.Save();

                ////通知界面更新
                //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                //dicParams.Add("ADDRESS", address);
                //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbLocation.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool EnableLocation(string address)
        {
            try
            {
                TpLocation tpLocation = TpLocation.GetById(address);
                tpLocation.IsProhibited = 0;
                tpLocation.Save();

                //通知界面更新
                //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                //dicParams.Add("ADDRESS", address);
                //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbLocation.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }

        public bool DisableLocation(string address)
        {
            try
            {
                TpLocation tpLocation = TpLocation.GetById(address);
                tpLocation.IsProhibited = 1;
                tpLocation.Save();

                //通知界面更新
                //Dictionary<string, object> dicParams = new Dictionary<string, object>();
                //dicParams.Add("ADDRESS", address);
                //WCFService.Instance.ServerSendMessage("UpdateLocation", dicParams);

                return true;
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("DbLocation.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return false;
        }
    }
}
