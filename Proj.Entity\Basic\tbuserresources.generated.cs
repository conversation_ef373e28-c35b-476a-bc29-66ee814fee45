﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbUserResources and List
    [Serializable]
    [Description("TB_USER_RESOURCES")]
    [LinqToDB.Mapping.Table("TB_USER_RESOURCES")]
    public partial class TbUserResources : GEntity<TbUserResources>
    {
        #region Contructor(s)

        private TbUserResources()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_UniqueId = RegisterProperty<String>(p => p.UniqueId);
        private static readonly PropertyInfo<Int64> pty_ResourceId = RegisterProperty<Int64>(p => p.ResourceId);
        private static readonly PropertyInfo<String> pty_ResourceName = RegisterProperty<String>(p => p.ResourceName);
        private static readonly PropertyInfo<Int64?> pty_Color = RegisterProperty<Int64?>(p => p.Color);
        private static readonly PropertyInfo<String> pty_Image = RegisterProperty<String>(p => p.Image);
        private static readonly PropertyInfo<String> pty_Customfield1 = RegisterProperty<String>(p => p.Customfield1);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("UNIQUEID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String UniqueId
        {
            get { return GetProperty(pty_UniqueId); }
            set { SetProperty(pty_UniqueId, value); }
        }
        /// <summary>
        /// 资源ID
        /// </summary>
        [Description("资源ID")]
        [LinqToDB.Mapping.Column("RESOURCEID")]
        public Int64 ResourceId
        {
            get { return GetProperty(pty_ResourceId); }
            set { SetProperty(pty_ResourceId, value); }
        }
        /// <summary>
        /// 资源名称
        /// </summary>
        [Description("资源名称")]
        [LinqToDB.Mapping.Column("RESOURCENAME")]
        public String ResourceName
        {
            get { return GetProperty(pty_ResourceName); }
            set { SetProperty(pty_ResourceName, value); }
        }
        /// <summary>
        /// 颜色
        /// </summary>
        [Description("颜色")]
        [LinqToDB.Mapping.Column("COLOR")]
        public Int64? Color
        {
            get { return GetProperty(pty_Color); }
            set { SetProperty(pty_Color, value); }
        }
        /// <summary>
        /// 图形
        /// </summary>
        [Description("图形")]
        [LinqToDB.Mapping.Column("IMAGE")]
        public String Image
        {
            get { return GetProperty(pty_Image); }
            set { SetProperty(pty_Image, value); }
        }
        /// <summary>
        /// 自定义
        /// </summary>
        [Description("自定义")]
        [LinqToDB.Mapping.Column("CUSTOMFIELD1")]
        public String Customfield1
        {
            get { return GetProperty(pty_Customfield1); }
            set { SetProperty(pty_Customfield1, value); }
        }
        #endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_UniqueId, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_UniqueId, 36, "主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_ResourceId, "资源ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_ResourceName, 50, "资源名称不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Image, 4000, "图形不能超过4000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Customfield1, 2000, "自定义不能超过2000个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.UniqueId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbUserResourcesList : GEntityList<TbUserResourcesList, TbUserResources>
    {
        private TbUserResourcesList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
