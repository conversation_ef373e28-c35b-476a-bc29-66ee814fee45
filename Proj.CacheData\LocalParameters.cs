﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Proj.CacheData
{
    public class LocalParameters
    {
        private static LocalParameters m_Instanse;
        private static readonly object m_SyncObject = new object();
        private Dictionary<string, object> m_dicParameters = new Dictionary<string, object>();

        public static LocalParameters Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (m_SyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new LocalParameters();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public void SetParameterValue(string strParamName, object objParamValue)
        {
            if (m_dicParameters.ContainsKey(strParamName))
            {
                m_dicParameters[strParamName] = objParamValue;
            }
            else
            {
                m_dicParameters.Add(strParamName, objParamValue);
            }
        }

        public object GetParameterValue(string strParamName)
        {
            if (m_dicParameters.ContainsKey(strParamName))
            {
                return m_dicParameters[strParamName];
            }
            else
            {
                return null;
            }
        }
    }
}
