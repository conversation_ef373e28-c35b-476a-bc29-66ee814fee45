﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="isdemo" value="false" />
    <add key="default.db" value="SSMES_MYSQL" />
    <add key="default.db.encrypt" value="false" />
    <add key="mylog.loglevel" value="Debug" />
    <add key="AutoCloneOnUpdate" value="false" />
    <add key="CslaDataPortalProxy--" value="Csla.Extend.Client.WcfProxy, SS.MidFunction" />
    <add key="CslaDataPortalUrl" value="http://localhost:12847/CslaWsHttpPortal" />
  </appSettings>
  
  <connectionStrings>
    <add name="SSMES" connectionString="Data Source=PC-SY/SSMES;User Id=*****;Password=*****;enlist=dynamic" providerName="Oracle.ManagedDataAccess.Client" />
    <add name="SQMES" connectionString="Data Source=PC-SY;Initial Catalog=SSMES;User ID=sa;Password=***" providerName="System.Data.SqlClient" />
    <add name="SSMES_ZS" providerName="System.Data.SqlClient" connectionString="Data Source=HPSVR;Initial Catalog=FA;Persist Security Info=True;User ID=yjyadmin;Password=`*****100;Pooling=False" />
    <add name="SSMES_MYSQL" providerName="MySql.Data.MySqlClient" connectionString="Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=Aa***456;Charset=utf8;Convert Zero Datetime=True" />
  </connectionStrings>

  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0" />
  </startup>

  <runtime>

    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">

      <dependentAssembly>

        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />

        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />

      </dependentAssembly>

      <dependentAssembly>

        <assemblyIdentity name="MySql.Data" publicKeyToken="c5687fc88969c44d" culture="neutral" />

        <bindingRedirect oldVersion="0.0.0.0-6.9.12.0" newVersion="6.9.12.0" />

      </dependentAssembly>

    </assemblyBinding>

  </runtime>
</configuration>