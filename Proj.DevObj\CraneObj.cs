﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Proj.Common;
using Proj.DataTypeDef;
using Proj.DevComm;
using Proj.HostComm;
using SecsLite.Gem;
using Proj.History;
using Proj.Alarm;
using Proj.Setting;

namespace Proj.DevObj
{
    //将DevObj工程放到UnitMng中，可以不用下面这些委托
    public delegate void deleUpdataTransferState(string commandID, int iFirstSecond, TransferState state, ResultCode code);
    public delegate void deleUpdateTransferCrane(string commandID, string craneID);
    public delegate void deleCranePickCarrier(string craneID, string carrierID, string sourceLocation);
    public delegate void deleCranePlaceCarrier(string craneID, string carrierID, string destLocation);
    public delegate void deleTransferChange2Port(string strPortAddress, EnhancedTransferCommand task);
    public delegate bool deleIsCarrierDuplicate(string carrierID, string carrierLoc, ref string dupCarrierRealLoc);
    public delegate void deleUpdateCarrierState(string carrierID, CarrierState state);
    public delegate bool deleDeleteCarrierInfo(string strCarrierID);
    public delegate bool deleAddCarrierInfo(string strCarrierID, string strCarrierLoc);
    public delegate string deleGenIDRFailCarrierID(string locationName);
    public delegate string deleGenDulplicateCarrierID(string strDulplicatedID);
    public delegate string deleGenDoubleStorageCarrierID(string shelfID);
    public delegate string deleGenEmptyRetrvScanCarrierID(string oldCarrierID);
    public delegate string deleGenOnCraneUnkCarrierID(string strVehicleID);
    public delegate void deleSendCraneActionToUI(string strAction, string strCarrierID, string strSource, string strDest);
    public delegate bool deleIsHaveCraneCommand(string strCraneID, string strCarrierID);
    public delegate void deleAddTransferCommand(Dictionary<string, object> dicParameter);
    public delegate void deleCraneOperationStateChg(string strCraneID, CraneOperationState state);
    public delegate void deleUpdateCraneCommandID(string strCraneID, string strCommandID);

    public class CraneObj
    {
        private ThreadBaseModel taskExecuteThread = null; //

        private int iCraneNo = 0;
        public string strCraneName { get; set; }
        public bool IsHaveIDR { get; set; }
        public CraneOperationState operState { get; set; }
        public CraneModeState modeState { get; set; }

        private EnhancedTransferCommand currCommand = null;
        private int taskStep = 0;
        private string strCarrierID = "";
        private bool bTaskAbortInitiated = false;
        private CraneTaskStep craneTaskStep = new CraneTaskStep();
        private bool bAction1Started = false;
        private bool bAction2Started = false;
        private bool bAction3Started = false;
        private bool bAction4Started = false;
        private bool bAction9Started = false;
        private bool bDoubleStorage = false;
        private bool bEmptyRetrieval = false;
        private IDRResult idrResult = new IDRResult();
        private ResultCode resultCode = ResultCode.None;
        private string dupCarrierRealLoc = "";
        private int iFailCount = 0;
        private bool bAlarmed = false;

        private int iReadDFailCounter = 0;
        private int iAutoPlaceCounter = 0;

        public event deleUpdataTransferState UpdataTransferState = null;
        public event deleUpdateTransferCrane UpdateTransferCrane = null;
        public event deleCranePickCarrier CranePickCarrier = null;
        public event deleCranePlaceCarrier CranePlaceCarrier = null;
        public event deleTransferChange2Port TransferChange2Port = null;
        public event deleIsCarrierDuplicate IsCarrierDuplicate = null;
        public event deleUpdateCarrierState UpdateCarrierState = null;
        public event deleDeleteCarrierInfo DeleteCarrierInfo = null;
        public event deleAddCarrierInfo AddCarrierInfo = null;
        public event deleGenIDRFailCarrierID GenIDRFailCarrierID = null;
        public event deleGenDulplicateCarrierID GenDulplicateCarrierID = null;
        public event deleGenDoubleStorageCarrierID GenDoubleStorageCarrierID = null;
        public event deleGenEmptyRetrvScanCarrierID GenEmptyRetrvScanCarrierID = null;
        public event deleGenOnCraneUnkCarrierID GenOnCraneUnkCarrierID = null;
        public event deleSendCraneActionToUI SendCraneActionToUI = null;
        public event deleIsHaveCraneCommand IsHaveCraneCommand = null;
        public event deleAddTransferCommand AddTransferCommand = null;
        public event deleCraneOperationStateChg CraneOperationStateChg = null;
        public event deleUpdateCraneCommandID UpdateCraneCommandID = null;

        public CraneObj(int no, string name, bool isHaveIDR, CraneOperationState state)
        {
            iCraneNo = no;
            strCraneName = name;
            IsHaveIDR = isHaveIDR;
            operState = state;
            modeState = CraneModeState.Idle;
        }

        public bool IsHaveTask()
        {
            return currCommand != null;
        }

        public bool ResetAlarm()
        {
            return true;
        }

        private void Shelf2ShelfCommand()
        {
            switch (currCommand.transferState)
            {
                case TransferState.Queue:
                    {
                        if (UpdataTransferState != null && UpdateTransferCrane != null)
                        {
                            taskStep = 1;
                            UpdateTransferCrane(currCommand.strCommandID, strCraneName);
                            UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.Transferring, ResultCode.None);
                            HistoryWriter.Instance.CraneTransferStart(currCommand.strCommandID, currCommand.iFirstSecendCrane, strCraneName);
                        }
                        else
                        {
                            endCurrentCommand(TransferState.None, ResultCode.OtherError);
                            modeState = CraneModeState.Idle;
                        }
                    }
                    break;
                case TransferState.Transferring:
                    {
                        switch (taskStep)
                        {
                            case 1:
                                {
                                    //检查Fork上没有盒子，向Crane发生任务数据，启动任务
                                    if (!CraneDev.Instance.IsCraneForkHasCarrier())
                                    {
                                        //如果Shelf2Shelf设置了IDRead bypass，告诉Crane 不读码
                                        if (SysSettings.Instance.CraneIDRReadRules.bShelf2ShelfReadBypass)
                                        {
                                            CraneDev.Instance.IDReadBypass(true);
                                        }
                                        else
                                        {
                                            CraneDev.Instance.IDReadBypass(false);
                                        }

                                        CraneTaskData taskData = new CraneTaskData();
                                        taskData.taskType = CraneTaskType.Transfer;
                                        taskData.strCarrierID = currCommand.strCarrierID;
                                        taskData.strSourceAddr = currCommand.strRealSource;
                                        taskData.strDestAddr = currCommand.strRealDest;
                                        //向Crane发生任务数据
                                        if (CraneDev.Instance.SendCraneCommand(taskData))
                                        {
                                            //记录Crane日志：成功向Crane发生任务数据
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.TransferInitiated, GemEvent.TransferInitiated.ToString()
                                                , $"发送成功:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");

                                            //Crane任务启动
                                            if (CraneDev.Instance.CraneTaskStart())
                                            {
                                                //记录Crane日志：启动Crane任务成功
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.TransferInitiated, GemEvent.TransferInitiated.ToString(), "Crane启动成功");

                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, GemEvent.TransferInitiated);
                                                if (SendCraneActionToUI != null)
                                                {
                                                    SendCraneActionToUI("PickStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                                }
                                                taskStep++;
                                            }
                                            else
                                            {
                                                //记录Crane日志：启动Crane任务失败
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.TransferInitiated, GemEvent.TransferInitiated.ToString(), "Crane启动失败");
                                                CraneDev.Instance.CleanCraneCommand();
                                                UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, TransferState.None, ResultCode.OtherError);
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, GemEvent.TransferCompleted);
                                            }
                                        }
                                        else  //SendCraneCommand失败
                                        {
                                            //记录Crane日志：向Crane发生任务数据失败
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.TransferInitiated, GemEvent.TransferInitiated.ToString()
                                                , $"发送失败:TaskType = {taskData.taskType}, CarrierID = {taskData.strCarrierID}, SourceAddr = {taskData.strSourceAddr}, DestAddr = {taskData.strDestAddr}");
                                            if (++iFailCount == 3)
                                            {
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, GemEvent.TransferCompleted);
                                                endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                                modeState = CraneModeState.Idle;
                                            }
                                        }
                                    }
                                    else //检查Fork上有盒子
                                    {
                                        //记录Crane日志：向Crane发送任务数据前，Fork上检测到盒子
                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.TransferInitiated, GemEvent.TransferInitiated.ToString(), "向Crane发送任务数据前，Fork上检测到盒子");
                                        HostIF.Instance.PostTransferEvent(currCommand.strCommandID, GemEvent.TransferCompleted);
                                        endCurrentCommand(TransferState.None, ResultCode.OtherError);
                                        modeState = CraneModeState.Idle;
                                    }
                                }
                                break;
                            case 2:
                                {
                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep();
                                    //Crane还未向源地址移动
                                    if (craneTaskStep.iStep < 1)
                                    {
                                        return;
                                    }
                                    else if (craneTaskStep.iStep == 1)
                                    {
                                        if (IsHaveIDR && !SysSettings.Instance.CraneIDRReadRules.bShelf2ShelfReadBypass)
                                        {
                                            //重置Crane任务启动信号
                                            CraneDev.Instance.ResetCraneTaskStart();
                                            //Crane开始向源地址移动
                                            modeState = CraneModeState.Active;
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, GemEvent.CraneActive);
                                            //记录Crane日志：Crane开始向源地址移动
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.TransferInitiated, GemEvent.CraneActive.ToString(), "Crane开始向源地址移动");
                                            taskStep++;
                                        }
                                        else //IC Stocker无IDR
                                        {
                                            if (bAction1Started == false && craneTaskStep.iAction == 1)
                                            {
                                                //向目标位检测Shelf上有无Carrier高度移动
                                                bAction1Started = true;
                                                CraneDev.Instance.ResetCraneTaskStart();
                                                //Crane开始向源地址移动
                                                modeState = CraneModeState.Active;
                                                HostIF.Instance.PostTransferEvent(currCommand.strCommandID, GemEvent.CraneActive);
                                                //记录Crane日志：Crane开始向源地址移动
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.TransferInitiated, GemEvent.CraneActive.ToString(), "Crane Step1, Action1");
                                            }
                                            else if (bAction2Started == false && craneTaskStep.iAction == 2)
                                            {
                                                //开始向取Carrier的高度移动
                                                bAction1Started = false;
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.TransferInitiated, GemEvent.CraneActive.ToString(), "Crane Step1, Action2");
                                                if (CraneDev.Instance.IsShelfHaveCarrier())
                                                {
                                                    taskStep++;
                                                }
                                                else
                                                {
                                                    bEmptyRetrieval = true;
                                                    taskStep = 10;  //EmptyRetrieval的处理
                                                }
                                            }
                                        }
                                    }
                                }
                                break;
                            case 3:
                                {
                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep();
                                    if (craneTaskStep.iStep < 2)
                                    {
                                        //向取Carrier的高度移动中
                                        return;
                                    }
                                    //Crane开始取货
                                    if (craneTaskStep.iStep == 2)
                                    {
                                        if (bAction2Started == false && craneTaskStep.iAction == 2)
                                        {
                                            //Crane开始伸Fork
                                            bAction2Started = true;
                                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, GemEvent.ForkingStarted);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(), "Crane Step2, Action2");
                                        }
                                        else if (bAction3Started == false && craneTaskStep.iAction == 3)
                                        {
                                            //Crane去读码
                                            bAction3Started = true;
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(), "Crane Step2, Action3");
                                        }
                                        else if (bAction4Started == false && craneTaskStep.iAction == 4)
                                        {
                                            //Crane开始抬起Fork（读码异常会跳过该步骤）
                                            bAction4Started = true;
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(), "Crane Step2, Action4");
                                        }
                                        else if (bAction9Started == false && craneTaskStep.iAction == 9)
                                        {
                                            //Crane开始缩回Fork
                                            bAction9Started = true;
                                            HostIF.Instance.PostCraneEvent(strCraneName, GemEvent.ForkRised);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(), "Crane Step9, Action9");

                                            if (IsHaveIDR && !SysSettings.Instance.CraneIDRReadRules.bShelf2ShelfReadBypass)
                                            {
                                                //处理读码结果（什么情况不读码，要有可配置的控制逻辑）lzt
                                                idrResult = CraneDev.Instance.GetIDRResult();
                                                switch ((IDRResultCode)idrResult.iResultCode)
                                                {
                                                    case IDRResultCode.Success:
                                                        resultCode = ResultCode.Success;
                                                        break;
                                                    case IDRResultCode.Mismatch: //进行Duplicate的检查
                                                        {
                                                            if (IsCarrierDuplicate != null)
                                                            {
                                                                if (IsCarrierDuplicate(idrResult.strCarrierID, currCommand.strRealSource, ref dupCarrierRealLoc))
                                                                {
                                                                    resultCode = ResultCode.DuplicateID;
                                                                }
                                                                else
                                                                {
                                                                    resultCode = ResultCode.MismatchID;
                                                                }
                                                            }
                                                            else
                                                            {
                                                                resultCode = ResultCode.MismatchID;
                                                            }
                                                        }
                                                        break;
                                                    case IDRResultCode.Failed:
                                                        {
                                                            resultCode = ResultCode.IDReadFail;
                                                            if (++iReadDFailCounter == SysSettings.Instance.CraneIDRReadRules.iMaxReadFailCount)
                                                            {
                                                                iReadDFailCounter = 0;
                                                                if (SysSettings.Instance.CraneIDRReadRules.bShowIDRBreakDown)
                                                                {
                                                                    //报警：IDR Break Down
                                                                    AlarmController.Instance.SetAlarm(1000, "IDR", "Crane IDR Break Down");
                                                                    HostIF.Instance.PostUnitAlarmEvent(strCraneName, 1000, "", GemEvent.UnitAlarmSet);                                                                    
                                                                }
                                                            }
                                                        }
                                                        break;
                                                    case IDRResultCode.NoCarrier:
                                                        {
                                                            resultCode = ResultCode.OtherError;
                                                            bEmptyRetrieval = true;
                                                            taskStep = 10;
                                                        }
                                                        break;
                                                    case IDRResultCode.None:
                                                    default:
                                                        resultCode = ResultCode.OtherError;
                                                        break;
                                                }
                                                if (resultCode != ResultCode.Success)
                                                {
                                                    //读码有问题的情况下才报告读码事件
                                                    HostIF.Instance.PostIDREvent(currCommand.strCarrierID, currCommand.strRealSource, resultCode, GemEvent.CarrierIDRead);
                                                }
                                                else
                                                {
                                                    //任务结束的处理
                                                    taskStep = 6;
                                                }
                                            }
                                            else
                                            {
                                                bool bCranePickCarrier = CraneDev.Instance.IsCraneForkHasCarrier();
                                                if (bCranePickCarrier)
                                                {
                                                    resultCode = ResultCode.Success;
                                                    //更新盒子位置
                                                    if (CranePickCarrier != null)
                                                    {
                                                        CranePickCarrier(strCraneName, currCommand.strCarrierID, currCommand.strRealSource);
                                                        strCarrierID = currCommand.strCarrierID;
                                                        HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(),
                                                            $"CranePickCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strRealSource}");
                                                    }
                                                    taskStep++;
                                                }
                                                else //没有取到盒子
                                                {
                                                    //Source Empty
                                                    bEmptyRetrieval = true;
                                                    taskStep = 10;
                                                }
                                            }
                                            bAction2Started = false;
                                            bAction3Started = false;
                                            bAction4Started = false;
                                            bAction9Started = false;
                                        }
                                    }
                                }
                                break;
                            case 4:
                                {
                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep();
                                    if (craneTaskStep.iStep < 3)
                                    {
                                        //Crane正在缩回Fork
                                        return;
                                    }
                                    if (craneTaskStep.iStep == 3)
                                    {
                                        if (bAction1Started == false && craneTaskStep.iAction == 1)
                                        {
                                            bAction1Started = true;

                                            //Crane缩回Fork完成,Fork开始向目的地检查Carrier有无的高度移动
                                            HostIF.Instance.PostCraneEvent(strCraneName, GemEvent.ForkingCompleted); //此处的该事件可能因为EmptyRetrieval而不上报
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(), "Crane Step3, Action1");

                                            if (CraneDev.Instance.IsCraneForkHasCarrier())
                                            {
                                                if (UpdateCarrierState != null)
                                                {
                                                    UpdateCarrierState(currCommand.strCarrierID, CarrierState.Transferring);
                                                }
                                                HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, GemEvent.CarrierTransferring);
                                                HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CarrierTransferring, GemEvent.CarrierTransferring.ToString(), "Crane开始搬送盒子");
                                            }
                                            if (SendCraneActionToUI != null)
                                            {
                                                SendCraneActionToUI("PickComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                                SendCraneActionToUI("PlaceStart", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);
                                            }
                                        }
                                        else if (bAction2Started == false && craneTaskStep.iAction == 2)
                                        {
                                            bAction1Started = false;
                                            //Fork开始向放Carrier的高度移动
                                            if (bDoubleStorage = CraneDev.Instance.IsShelfHaveCarrier())
                                            {
                                                //检查到目标位置有Carrier
                                                taskStep = 20;
                                            }
                                            else
                                            {
                                                taskStep++;
                                            }
                                        }
                                    }
                                }
                                break;
                            case 5:
                                {
                                    craneTaskStep = CraneDev.Instance.GetCraneTaskStep();
                                    if (craneTaskStep.iStep < 4)
                                    {
                                        //Crane正在向目的地移动
                                        return;
                                    }
                                    //Crane开始放下盒子
                                    if (craneTaskStep.iStep == 4)
                                    {
                                        if (bAction2Started == false && craneTaskStep.iAction == 2)
                                        {
                                            //Crane开始伸Fork
                                            bAction2Started = true;
                                            HostIF.Instance.PostCraneEvent(strCraneName, GemEvent.ForkingStarted);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(), "Crane Step4, Action2");
                                        }
                                        else if (bAction3Started == false && craneTaskStep.iAction == 3)
                                        {
                                            //Crane的Fork开始下降
                                            bAction3Started = true;
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(), "Crane Step4, Action3");

                                        }
                                        else if (bAction4Started == false && craneTaskStep.iAction == 4)
                                        {
                                            //Crane的Fork开始缩回(ForkDowned)
                                            HostIF.Instance.PostCraneEvent(strCraneName, GemEvent.ForkDowned);
                                            HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(), "Crane Step4, Action4");

                                            //DoubleStorage检查
                                            if (bDoubleStorage = CraneDev.Instance.IsCraneForkHasCarrier())
                                            {
                                                //盒子还在货叉上，发生了DoubleStorage
                                                taskStep = 20;
                                            }
                                            else
                                            {
                                                if (CranePlaceCarrier != null)
                                                {
                                                    CranePlaceCarrier(strCraneName, currCommand.strCarrierID, currCommand.strDest);
                                                    strCarrierID = "";
                                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(),
                                                        $"CranePlaceCarrier: CraneName={strCraneName}, CarrierID = {currCommand.strCarrierID}, SourceAddress = {currCommand.strDest} ");
                                                }
                                                if (UpdateCarrierState != null)
                                                {
                                                    UpdateCarrierState(currCommand.strCarrierID, CarrierState.Compeleted);
                                                }

                                                taskStep++;
                                            }

                                            bAction2Started = false;
                                            bAction3Started = false;
                                        }
                                    }
                                }
                                break;
                            case 6:
                                {
                                    //Crane的Fork正在缩回，等待Crane任务完成信号
                                    if (!CraneDev.Instance.IsCraneTaskComplete())
                                    {
                                        return;
                                    }

                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneActive, GemEvent.CraneActive.ToString(), "Crane Task Complete");
                                    //Crane任务完成，答复完成信号
                                    CraneDev.Instance.CraneTaskCompleteAck();
                                    SendCraneActionToUI("PlaceComplete", currCommand.strCarrierID, currCommand.strRealSource, currCommand.strRealDest);

                                    if (bDoubleStorage == false && CraneDev.Instance.IsCraneForkHasCarrier())
                                    {
                                        bDoubleStorage = true;
                                        resultCode = ResultCode.OtherError;
                                    }
                                    else
                                    {
                                        resultCode = ResultCode.Success;
                                    }
                                    taskStep++;
                                }
                                break;
                            case 7:
                                {
                                    //Crane任务完成信号复位
                                    if (CraneDev.Instance.IsCraneTaskComplete())
                                    {
                                        return;
                                    }

                                    //复位答复完成信号
                                    CraneDev.Instance.ResetCraneTaskCompleteAck();

                                    HostIF.Instance.PostCraneEvent(strCraneName, GemEvent.CraneIdle);
                                    HistoryWriter.Instance.EqpEventLog(strCraneName, (int)GemEvent.CraneIdle, GemEvent.CraneIdle.ToString(), "Crane Idle");
                                    HostIF.Instance.PostTransferEvent(currCommand.strCommandID, GemEvent.TransferCompleted);
                                    switch (resultCode)
                                    {
                                        case ResultCode.Success:
                                            {
                                                HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, GemEvent.CarrierStored);
                                                modeState = CraneModeState.Idle;
                                            }
                                            break;
                                        case ResultCode.IDReadFail:
                                            {
                                                //1.删除原CarrierID的Carrier信息
                                                if (DeleteCarrierInfo != null)
                                                {
                                                    DeleteCarrierInfo(currCommand.strCarrierID);
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, GemEvent.CarrierRemoveCompleted);
                                                }
                                                //2.添加UnKnownID的Carrier信息
                                                if (AddCarrierInfo != null && GenIDRFailCarrierID != null)
                                                {
                                                    string strUnKnownID = GenIDRFailCarrierID(currCommand.strRealSource);
                                                    AddCarrierInfo(strUnKnownID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, GemEvent.CarrierInstallCompleted);
                                                }
                                            }
                                            break;
                                        case ResultCode.DuplicateID:
                                            {
                                                //1.删除DuplicateID的盒子信息
                                                if (DeleteCarrierInfo != null)
                                                {
                                                    DeleteCarrierInfo(idrResult.strCarrierID);
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, GemEvent.CarrierRemoveCompleted);
                                                }

                                                //2.在DuplicateID的位置处添加UnKnownID的Carrier信息
                                                if (AddCarrierInfo != null && GenDulplicateCarrierID != null)
                                                {
                                                    string strUnKnownID = GenDulplicateCarrierID(dupCarrierRealLoc);
                                                    AddCarrierInfo(strUnKnownID, dupCarrierRealLoc);
                                                    HostIF.Instance.PostCarrierEvent(strUnKnownID, GemEvent.CarrierInstallCompleted);
                                                }

                                                //3.删除指令中CarrierID位置处的Carrier信息
                                                if (DeleteCarrierInfo != null)
                                                {
                                                    DeleteCarrierInfo(currCommand.strCarrierID);
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, GemEvent.CarrierRemoveCompleted);
                                                }

                                                //4.在指令中的位置处安装读到的盒子信息
                                                if (AddCarrierInfo != null && GenIDRFailCarrierID != null)
                                                {
                                                    AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, GemEvent.CarrierInstallCompleted);
                                                }
                                            }
                                            break;
                                        case ResultCode.MismatchID:
                                            {
                                                //1.删除指令中CarrierID位置处的Carrier信息
                                                if (DeleteCarrierInfo != null)
                                                {
                                                    DeleteCarrierInfo(currCommand.strCarrierID);
                                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, GemEvent.CarrierRemoveCompleted);
                                                }

                                                //2.在指令中的位置处安装读到的盒子信息
                                                if (AddCarrierInfo != null && GenIDRFailCarrierID != null)
                                                {
                                                    AddCarrierInfo(idrResult.strCarrierID, currCommand.strRealSource);
                                                    HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, GemEvent.CarrierInstallCompleted);
                                                }
                                            }
                                            break;
                                        case ResultCode.TypeMismatch:
                                        case ResultCode.ShelfZoneFull:
                                        case ResultCode.OtherError:
                                        default:
                                            break;
                                    }
                                    //结束Crane的当前任务
                                    endCurrentCommand(TransferState.None, resultCode);

                                    //2OutPort时,其余过程放到PortObj中执行
                                    //TransferChange2Port(currCommand.strRealDest, currCommand);
                                }
                                break;
                            case 10:  //EmptyRetrival的处理
                                {
                                    //EmptyRetrival对应的AlarmID
                                    HostIF.Instance.PostAlarm(111);
                                    HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "Source Empty", currCommand.strStockerCraneID, "ABORT", 1110, GemEvent.AlarmSet);
                                    taskStep++;
                                }
                                break;
                            case 11:
                                {
                                    //如果收到了Retry指令,回到Step3重新执行
                                    //if ()
                                    //{
                                    //    CraneDev.Instance.CraneTaskResume();
                                    //    taskStep = 3;
                                    //}
                                }
                                break;
                            case 20:  //DoubleStorage的处理
                                {

                                }
                                break;
                            case 30: //Abort流程的处理
                                break;
                        }
                    }
                    break;
                case TransferState.Aborting:
                    {
                        if (!bTaskAbortInitiated)
                        { 
                            HostIF.Instance.PostAlarmEvent(currCommand.strCommandID, "Source Empty", currCommand.strStockerCraneID, "ABORT", 111, GemEvent.AlarmCleared);
                            HostIF.Instance.ClearAlarm(111);

                            CraneDev.Instance.CraneTaskAbort();
                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, GemEvent.TransferAbortInitiated);
                            bTaskAbortInitiated = true;
                        }
                        else if (CraneDev.Instance.IsCraneAbortComplete()) //还是IsCraneTaskComplete?
                        {
                            CraneDev.Instance.CraneAbortCompleteAck();
                        }
                        else
                        {
                            CraneDev.Instance.ResetCraneAbortCompleteAck();
                            HostIF.Instance.PostTransferEvent(currCommand.strCommandID, GemEvent.TransferAbortCompleted);

                            if (bEmptyRetrieval)
                            {
                                if (DeleteCarrierInfo != null)
                                {
                                    DeleteCarrierInfo(currCommand.strCarrierID);
                                    HostIF.Instance.PostCarrierEvent(currCommand.strCarrierID, GemEvent.CarrierRemoveCompleted);
                                }
                                //是否还要添加一个UnKnown的盒子？
                                if (AddCarrierInfo != null && GenEmptyRetrvScanCarrierID != null)
                                {
                                    string strUnknownID = GenEmptyRetrvScanCarrierID(currCommand.strRealSource);
                                    AddCarrierInfo(strUnknownID, currCommand.strRealSource);
                                    HostIF.Instance.PostCarrierEvent(strUnknownID, GemEvent.CarrierInstallCompleted);
                                }
                            }
                            else if (bDoubleStorage)
                            {
                                if (AddCarrierInfo != null && GenDoubleStorageCarrierID != null)
                                {
                                    string strUnknownID = GenDoubleStorageCarrierID(currCommand.strRealDest);
                                    AddCarrierInfo(strUnknownID, currCommand.strRealDest);
                                    HostIF.Instance.PostCarrierEvent(strUnknownID, GemEvent.CarrierInstallCompleted);
                                }
                            }
                        }
                    }
                    break;
                case TransferState.Canceling:
                    break;
                case TransferState.Paused:
                    break;
                case TransferState.None:   //不应该在此出现
                    break;
            }
        }

        private void Shelf2EqPortCommand()
        {

        }

        private void Shelf2OutPortCommand()
        {

        }

        private void EqPort2EqPortCommand()
        {

        }

        private void EqPort2OutPortCommand()
        {

        }

        private void EqPort2ShlefCommand()
        {

        }

        private void InPort2EqPortCommand()
        {

        }

        private void InPort2OutPortCommand()
        {

        }

        private void InPort2ShelfCommand()
        {

        }

        private void Crane2EqPortCommand()
        {

        }

        private void Crane2OutPortCommand()
        {

        }

        private void Crane2ShelfCommand()
        {

        }

        private void Shelf2CraneCommand()
        {

        }

        private void InPort2CraneCommand()
        {

        }

        private void EqPort2CraneCommand()
        {

        }

        private void executeScanCommand()
        {

        }
        private void endCurrentCommand(TransferState transferState,ResultCode result)
        {
            iFailCount = 0;
            taskStep = 0;
            if (UpdataTransferState != null)
            {
                UpdataTransferState(currCommand.strCommandID, currCommand.iFirstSecendCrane, transferState, result);
            }
            if (UpdateCraneCommandID != null)
            {
                UpdateCraneCommandID(strCraneName, "");
            }
            currCommand = null;
        }
        private void taskExecuteThreadFunc()
        {
            //此处添加从PLC获取Crane状态
            //... lzt

            if (!bAlarmed)
            {
                //Crane Unit Alarm
                if (CraneDev.Instance.IsCraneHasAlarm())
                {
                    bAlarmed = true;
                    List<int> craneAlarms = CraneDev.Instance.GetCraneAlarms();
                    if (craneAlarms != null && craneAlarms.Count > 0)
                    {
                        foreach (int alarmID in craneAlarms)
                        {
                            AlarmController.Instance.SetAlarm(alarmID, "Crane", "");
                        }
                        HostIF.Instance.PostUnitAlarmEvent(strCraneName, craneAlarms[0], "", GemEvent.UnitAlarmSet);
                        if (currCommand != null)
                        {
                            endCurrentCommand(TransferState.None, ResultCode.OtherError);
                        }
                        if (CraneOperationStateChg != null)
                        {
                            CraneOperationStateChg(strCraneName, CraneOperationState.Down);
                            HostIF.Instance.PostCraneEvent(strCraneName, GemEvent.CraneOutOfService);
                        }
                    }
                }
            }
            if (currCommand != null)
            {
                if (currCommand.strCommandName == "TRANSFER")
                {
                    if (iAutoPlaceCounter != 0)
                    {
                        iAutoPlaceCounter = 0;
                    } 
                    switch (currCommand.transferType)
                    {                        
                        case TransferType.Shelf2Shelf:
                            Shelf2ShelfCommand();
                            break;
                        case TransferType.Shelf2EqPort:
                            Shelf2EqPortCommand();
                            break;
                        case TransferType.Shelf2OutPort:
                            Shelf2OutPortCommand();
                            break;
                        case TransferType.EqPort2EqPort:
                            EqPort2EqPortCommand();
                            break;
                        case TransferType.EqPort2OutPort:
                            EqPort2OutPortCommand();
                            break;
                        case TransferType.EqPort2Shlef:
                            EqPort2ShlefCommand();
                            break;
                        case TransferType.InPort2EqPort:
                            InPort2EqPortCommand();
                            break;
                        case TransferType.InPort2OutPort:
                            InPort2OutPortCommand();
                            break;
                        case TransferType.InPort2Shelf:
                            InPort2ShelfCommand();
                            break;
                        case TransferType.Crane2EqPort:
                            Crane2EqPortCommand();
                            break;
                        case TransferType.Crane2OutPort:
                            Crane2OutPortCommand();
                            break;
                        case TransferType.Crane2Shelf:
                            Crane2ShelfCommand();
                            break;
                        case TransferType.Shelf2Crane:
                            Shelf2CraneCommand();
                            break;
                        case TransferType.InPort2Crane:
                            InPort2CraneCommand();
                            break;
                        case TransferType.EqPort2Crane:
                            EqPort2CraneCommand();
                            break;
                    }
                }
                else if (currCommand.strCommandName == "SCAN")
                {
                    executeScanCommand();
                }
            }
            else  //无指令且Fork上有Carrier，自动创建放下Carrier的指令
            {
                if (!bAlarmed && CraneDev.Instance.IsCraneForkHasCarrier())
                {
                    //任务队列中没有该放下Carrier的指令
                    if (IsHaveCraneCommand != null)
                    {
                        if (IsHaveCraneCommand(strCarrierID, strCraneName) 
                            && (++iAutoPlaceCounter) * taskExecuteThread.LoopInterval > SysSettings.Instance.TimeOuts.iCraneOccupied * 1000)
                        {
                            iAutoPlaceCounter = 0;
                            if (AddTransferCommand != null)
                            { 
                                //生成指令
                                Dictionary<string, object> dicParams = new Dictionary<string, object>();
                                dicParams.Add("CARRIERID", strCarrierID);
                                dicParams.Add("SOURCE", strCraneName);

                                AddTransferCommand(dicParams);
                            }
                        }
                    }
                }
            }
        }

        public void Start()
        {
            try
            {
                string strRes = "";

                taskExecuteThread = new ThreadBaseModel(10 + iCraneNo, "TaskExecuteThreadFunc "); //Crane的现场ID从10开始
                taskExecuteThread.LoopInterval = 200;
                taskExecuteThread.SetThreadRoutine(taskExecuteThreadFunc);
                taskExecuteThread.TaskInit(ref strRes);
                taskExecuteThread.Start(ref strRes);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("CraneObj.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }

        public void Stop()
        {
            string strRes = "";
            taskExecuteThread.TaskExit(ref strRes);
        }

        public void AddTask(EnhancedTransferCommand task)
        {
            this.currCommand = task;
        }
        
        private void AbortCurrentTask()
        {

        }

        public bool SetCraneSpeed(int emptySpeed, int storageSpeed, int xSpeed = 100, int ySpeed = 100, int zSpeed = 100, int tSpeed = 100)
        {
            return CraneDev.Instance.SetCraneSpeed(emptySpeed, storageSpeed, xSpeed, ySpeed, zSpeed, tSpeed);
        }
    }
}
