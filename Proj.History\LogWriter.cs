﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;

namespace Proj.History
{
    public enum EnumLogTableName
    {
        th_alarm,
        th_eqp,
        th_host,
        th_operation,
        th_pio,
        th_plc,
        sc,
        transfer
    }

    public class LogWriter
    {
        private static LogWriter m_Instanse;
        private static readonly object mSyncObject = new object();
        public string m_strDBConnectString = "Server=127.0.0.1;Port=3306;Database=stkc_history;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True";
        private ILog alarmLogger;
        private ILog exceptionLogger;
        private ILog eventLogger;
        private ILog hostLoger;
        private ILog operationLogger;
        private ILog pioLogger;
        private ILog plcLogger;
        private ILog scLogger;
        private ILog transferLogger;

        public static LogWriter Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new LogWriter();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public LogWriter()
        {
            //初始化Txt日志
            FileInfo fi = new FileInfo(System.Environment.CurrentDirectory + @"\config\Log4net.config");
            log4net.Config.XmlConfigurator.Configure(fi);
            alarmLogger = LogManager.GetLogger("AlarmLog");
            exceptionLogger = LogManager.GetLogger("ExceptionLog");
            eventLogger = LogManager.GetLogger("EventLog");
            hostLoger = LogManager.GetLogger("HostLog");
            operationLogger = LogManager.GetLogger("OperationLog");
            pioLogger = LogManager.GetLogger("PIOLog");
            plcLogger = LogManager.GetLogger("PLCLog");
            scLogger = LogManager.GetLogger("SCLog");
            transferLogger = LogManager.GetLogger("TransferLog");
        }

        /// <summary>
        /// 向alarm_history表写入新的Alarm
        /// </summary>
        /// <param name="strStartTime">报警第一次发生的时间，格式yyyyMMddHHmmssfff</param>
        /// <param name="strAlarmUnit">模块</param>
        /// <param name="nAlarmCode">报警编码</param>
        /// <param name="strAlarmRemark">报警内容备注</param>
        public bool AddAlarmHistory(string strStartTime, string strAlarmUnit, int nAlarmCode, string strAlarmRemark)
        {
            //Write Txt
            alarmLogger.Debug("[Set Alarm] AlarmStartTime: " + strStartTime + ", AlarmUnit: " + strAlarmUnit + ", AlarmCode: " + nAlarmCode + ", Alarm Text: " + strAlarmRemark);

            ////Write DB alarm_history
            //string strTable = EnumLogTableName.alarm_history.ToString() + "_" + strStartTime.Substring(0, 8);
            //if (m_MySqlCommon.CheckTableExists(strTable) == false)
            //{
            //    string strCreateSQL = "create table " + strTable
            //        + " (start_time BIGINT(17) default 0,"
            //        + " clear_time BIGINT(17) default 0,"
            //        + " alarm_unit VARCHAR(32) default 'Unknown',"
            //        + " alarm_code INT(11) default 0,"
            //        + " alarm_remark VARCHAR(255) default '',"
            //        + " index (start_time asc))";
            //    m_MySqlCommon.ExcuteCommand(strCreateSQL);
            //}
            //string strInsertSQL = "insert into " + strTable + " values("
            //    + strStartTime + ",0,'" + strAlarmUnit + "'," + nAlarmCode + ",'" + strAlarmRemark + "')";
            //return m_MySqlCommon.ExcuteCommand(strInsertSQL);

            return true;
        }

        /// <summary>
        /// 更新alarm_history表中的报警清除时间
        /// </summary>
        /// <param name="strStartTime">报警第一次发生的时间，格式yyyyMMddHHmmssfff</param>
        /// <param name="nAlarmCode">报警编码</param>
        public bool UpdateAlarmClearTime(string strStartTime, string strAlarmUnit, int nAlarmCode)
        {
            string strClearTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");

            //Write Txt
            alarmLogger.Debug("[Clear Alarm] AlarmStartTime: " + strStartTime + ", AlarmUnit: " + strAlarmUnit + ", AlarmCode: " + nAlarmCode);

            ////Write DB alarm_history
            //string strTable = EnumLogTableName.alarm_history.ToString() + "_" + strStartTime.Substring(0, 8);
            //if (m_MySqlCommon.CheckTableExists(strTable) == false)
            //{
            //    ExceptionLog("Can not find the table: " + strTable);
            //    return false;
            //}
            //string strUpdateSQL = "update " + strTable + " set clear_time=" + strClearTime
            //    + " where start_time=" + strStartTime + " and alarm_code=" + nAlarmCode.ToString();
            //return m_MySqlCommon.ExcuteCommand(strUpdateSQL);

            return true;
        }

        private long DateTimeToLong(DateTime dtTime)
        {
            string strTime = dtTime.ToString("yyyyMMddHHmmssfff");
            return Convert.ToInt64(strTime);
        }

        /// <summary>
        /// 异常日志，只写入TXT
        /// </summary>
        /// <param name="strException">异常内容</param>
        public bool ExceptionLog(string strException)
        {
            exceptionLogger.Debug(strException);
            return true;
        }

        /// <summary>
        /// 设备事件日志
        /// </summary>
        /// <param name="strUnit">设备单元名称</param>
        /// <param name="nEventID">事件ID</param>
        /// <param name="strEventName">事件名称</param>
        /// <param name="strEventText">事件具体内容</param>
        /// <returns></returns>
        public bool EventLog(string strUnit, int nEventID, string strEventName, string strEventText)
        {
            string strTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");

            //Write Txt
            eventLogger.Debug("[EQP Event] Unit: " + strUnit + ", Event ID: " + nEventID
                + ", Event Name: " + strEventName + ", Event Text: " + strEventText);

            ////Write DB
            //string strTable = EnumLogTableName.eqp_event.ToString() + "_" + strTime.Substring(0, 8);
            //if (m_MySqlCommon.CheckTableExists(strTable) == false)
            //{
            //    string strCreateSQL = "create table " + strTable
            //        + " (time BIGINT(17) default 0,"
            //        + " eqp_unit VARCHAR(32) default 'Unknown',"
            //        + " event_id INT(11) default 0,"
            //        + " event_name VARCHAR(32) default '',"
            //        + " event_text VARCHAR(255) default '',"
            //        + " index (time asc))";
            //    m_MySqlCommon.ExcuteCommand(strCreateSQL);
            //}
            //string strInsertSQL = "insert into " + strTable + " values("
            //    + strTime + ",'" + strUnit + "'," + nEventID + ",'" + strEventName + "','" + strEventText + "')";
            //return m_MySqlCommon.ExcuteCommand(strInsertSQL);

            return true;
        }

        /// <summary>
        /// 与Host的通信日志
        /// </summary>
        /// <param name="nDirection">数据传输方向：1-发送，2-接收</param>
        /// <param name="strStreamFunction">如S1F1</param>
        /// <param name="strMsgType">消息类型: 1-Request，2-Reply</param>
        /// <param name="strMsgName">消息名称</param>
        /// <param name="strMsgData">消息具体内容</param>
        public bool HostLog(int nDirection, string strStreamFunction, int nMsgType, string strMsgName, string strMsgData)
        {
            string strTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");

            //Write Txt
            hostLoger.Debug("[Host Comm] Direction: " + nDirection + ", Stream Function: " + strStreamFunction
                + ", Msg Type: " + nMsgType + ", Msg Name: " + strMsgName + ", Msg Data: " + strMsgData);

            ////Write DB
            //string strTable = EnumLogTableName.host_comm.ToString() + "_" + strTime.Substring(0, 8);
            //if (m_MySqlCommon.CheckTableExists(strTable) == false)
            //{
            //    string strCreateSQL = "create table " + strTable
            //        + " (time BIGINT(17) default 0,"
            //        + " direction SMALLINT(1) default 0,"
            //        + " stream_function VARCHAR(8) default '',"
            //        + " msg_type SMALLINT(1) default 0,"
            //        + " msg_name VARCHAR(32) default '',"
            //        + " msg_data VARCHAR(4000) default '',"
            //        + " index (time asc))";
            //    m_MySqlCommon.ExcuteCommand(strCreateSQL);
            //}
            //string strInsertSQL = "insert into " + strTable + " values("
            //    + strTime + "," + nDirection + ",'" + strStreamFunction + "'," + nMsgType
            //    + ",'" + strMsgName + "','" + strMsgData + "')";
            //return m_MySqlCommon.ExcuteCommand(strInsertSQL);

            return true;
        }

        /// <summary>
        /// 用户界面操作日志
        /// </summary>
        /// <param name="strUnit">GUI客户端ID</param>
        /// <param name="strAction">用户名</param>
        /// <param name="strOldState">操作的界面名称</param>
        /// <param name="strNewState">界面中的功能(界面中的区域、按钮、菜单项等)</param>
        /// <param name="strFunctionData">功能中的具体数据</param>
        public bool OperationLog(string strClient, string strUser, string strUIName, string strFunctionName, string strFunctionData)
        {
            string strTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");

            //Write Txt
            operationLogger.Debug("[Operation] Client: " + strClient + ", User: " + strUser
                + ", UI Name: " + strUIName + ", Function Name: " + strFunctionName + ", Function Data: " + strFunctionData);

            ////Write DB
            //string strTable = EnumLogTableName.operation.ToString() + "_" + strTime.Substring(0, 8);
            //if (m_MySqlCommon.CheckTableExists(strTable) == false)
            //{
            //    string strCreateSQL = "create table " + strTable
            //        + " (time BIGINT(17) default 0,"
            //        + " client VARCHAR(32) default '',"
            //        + " user VARCHAR(32) default '',"
            //        + " ui_name VARCHAR(32) default '',"
            //        + " function_name VARCHAR(32) default '',"
            //        + " function_data VARCHAR(64) default '',"
            //        + " index (time asc))";
            //    m_MySqlCommon.ExcuteCommand(strCreateSQL);
            //}
            //string strInsertSQL = "insert into " + strTable + " values("
            //    + strTime + ",'" + strClient + "','" + strUser + "','"
            //    + strUIName + "','" + strFunctionName + "','" + strFunctionData + "')";
            //return m_MySqlCommon.ExcuteCommand(strInsertSQL);

            return true;
        }

        /// <summary>
        /// PIO日志(E84)
        /// </summary>
        /// <param name="nPortID">Port编号</param>
        /// <param name="nDirection">数据传输方向：1-发送，2-接收</param>
        /// <param name="strConnectModule">与Port通讯的模块名称</param>
        /// <param name="strIOName">IO名称</param>
        /// <param name="nIOValue">IO值</param>
        public bool PIOLog(int nPortID, int nDirection, string strConnectModule, string strIOName, int nIOValue)
        {
            string strTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");

            //Write Txt
            pioLogger.Debug("[PIO] Port ID: " + nPortID + ", Direction: " + nDirection
                + ", Connect Module: " + strConnectModule + ", IO Name: " + strIOName + ", IO Value: " + nIOValue);

            ////Write DB
            //string strTable = EnumLogTableName.pio.ToString() + "_" + strTime.Substring(0, 8);
            //if (m_MySqlCommon.CheckTableExists(strTable) == false)
            //{
            //    string strCreateSQL = "create table " + strTable
            //        + " (time BIGINT(17) default 0,"
            //        + " port_id INT(11) default 0,"
            //        + " direction SMALLINT(1) default 0,"
            //        + " connect_module VARCHAR(32) default '',"
            //        + " io_name VARCHAR(32) default '',"
            //        + " io_value INT(11) default 0,"
            //        + " index (time asc))";
            //    m_MySqlCommon.ExcuteCommand(strCreateSQL);
            //}
            //string strInsertSQL = "insert into " + strTable + " values("
            //    + strTime + "," + nPortID + "," + nDirection + ",'"
            //    + strConnectModule + "','" + strIOName + "'," + nIOValue + ")";
            //return m_MySqlCommon.ExcuteCommand(strInsertSQL);

            return true;
        }

        /// <summary>
        /// SC日志
        /// </summary>
        /// <param name="strUnit">SC单元</param>
        /// <param name="strAction">执行的动作</param>
        /// <param name="strOldState">动作执行之前的状态</param>
        /// <param name="strNewState">动作执行之后的状态</param>
        /// <returns></returns>
        public bool SCLog(string strUnit, string strAction, string strOldState, string strNewState)
        {
            string strTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");

            //Write Txt
            scLogger.Debug("[SC] Unit: " + strUnit + ", Action: " + strAction
                + ", Old State: " + strOldState + ", New State: " + strNewState);

            ////Write DB
            //string strTable = EnumLogTableName.sc.ToString() + "_" + strTime.Substring(0, 8);
            //if (m_MySqlCommon.CheckTableExists(strTable) == false)
            //{
            //    string strCreateSQL = "create table " + strTable
            //        + " (time BIGINT(17) default 0,"
            //        + " unit_name VARCHAR(32) default '',"
            //        + " action VARCHAR(64) default '',"
            //        + " old_state VARCHAR(64) default '',"
            //        + " new_state VARCHAR(64) default '',"
            //        + " index (time asc))";
            //    m_MySqlCommon.ExcuteCommand(strCreateSQL);
            //}
            //string strInsertSQL = "insert into " + strTable + " values("
            //    + strTime + ",'" + strUnit + "','" + strAction + "','"
            //    + strOldState + "','" + strNewState + "')";
            //return m_MySqlCommon.ExcuteCommand(strInsertSQL);

            return true;
        }

        /// <summary>
        /// PLC读写日志
        /// </summary>
        /// <param name="strUnit">设备单元</param>
        /// <param name="nIOResult">IO操作结果: 1-读成功 2-写成功 3-读失败 4-写失败</param>
        /// <param name="strIOName">IO名称</param>
        /// <param name="dIOValue">IO值</param>
        public bool PLCLog(string strUnit, int nIOResult, string strIOName, double dIOValue)
        {
            string strTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");

            //Write Txt
            plcLogger.Debug("[PLC] Unit: " + strUnit + ", IO Result: " + nIOResult
                + ", IO Name: " + strIOName + ", IO Value: " + dIOValue.ToString("f4"));

            ////Write DB
            //string strTable = EnumLogTableName.plc.ToString() + "_" + strTime.Substring(0, 8);
            //if (m_MySqlCommon.CheckTableExists(strTable) == false)
            //{
            //    string strCreateSQL = "create table " + strTable
            //        + " (time BIGINT(17) default 0,"
            //        + " eqp_unit SMALLINT(1) default 0,"
            //        + " io_type SMALLINT(1) default 0,"
            //        + " io_id INT(11) default 0,"
            //        + " io_value FLOAT default 0,"
            //        + " index (time asc))";
            //    m_MySqlCommon.ExcuteCommand(strCreateSQL);
            //}
            //string strInsertSQL = "insert into " + strTable + " values("
            //    + strTime + "," + nUnitID + "," + nIOResult + ","
            //    + nIOID + "," + dIOValue.ToString("f4") + ")";
            //return m_MySqlCommon.ExcuteCommand(strInsertSQL);

            return true;
        }

        /// <summary>
        /// 在transfer_history表中，创建新的Command
        /// </summary>
        /// <param name="strCreateTime">命令创建时间</param>
        /// <param name="strCmdID">命令ID</param>
        /// <param name="nCmdSrc">命令来源：0-Unknown 1-GUI 2-MCS</param>
        /// <param name="nCmdPriority">命令的优先级</param>
        /// <param name="strSrcLoc">源位置信息，可能是排列成，也可能是zone名称</param>
        /// <param name="strDestLoc">目的位置信息，可能是排列成，也可能是zone名称</param>
        /// <param name="nCalPriority">计算后的优先级</param>
        public bool CreateTransferCmd(string strCreateTime, string strCmdID, int nCmdSrc, int nCmdPriority, 
            string strSrcLoc, string strDestLoc, int nCalPriority)
        {
            //Write Txt
            transferLogger.Debug("[Create Command] CmdCreateTime: " + strCreateTime + ", CmdID: " + strCmdID
                + ", CmdSrc: " + nCmdSrc + ", CmdPriority: " + nCmdPriority + ", strSrcLoc: " + strSrcLoc
                + ", DestLoc: " + strDestLoc + ", CalPriority: " + nCalPriority);

            ////Write DB
            //string strTable = EnumLogTableName.transfer.ToString() + "_" + strCreateTime.Substring(0, 8);
            //if (m_MySqlCommon.CheckTableExists(strTable) == false)
            //{
            //    string strCreateSQL = "create table " + strTable
            //        + " (command_time BIGINT(17) default 0,"
            //        + " command_id VARCHAR(64) default '',"
            //        + " command_src SMALLINT(1) default 0,"
            //        + " command_priority SMALLINT(6) default 0,"
            //        + " command_src_loc VARCHAR(64) default '',"
            //        + " command_dest_loc VARCHAR(64) default '',"
            //        + " calculation_priority SMALLINT(6) default 0,"
            //        + " first_crane_id VARCHAR(64) default '',"
            //        + " first_start_time BIGINT(17) default 0,"
            //        + " first_end_time BIGINT(17) default 0,"
            //        + " second_crane_id VARCHAR(64) default '',"
            //        + " second_start_time BIGINT(17) default 0,"
            //        + " second_end_time BIGINT(17) default 0,"
            //        + " command_state VARCHAR(32) default '',"
            //        + " delay_reason VARCHAR(255) default '',"
            //        + " command_remark VARCHAR(255) default '',"
            //        + " index (command_create_time asc))";
            //    m_MySqlCommon.ExcuteCommand(strCreateSQL);
            //}
            //string strInsertSQL = "insert into " + strTable + " values("
            //    + strCreateTime + ",'" + strCmdID + "'," + nCmdSrc + "," + nCmdPriority
            //    + ",'" + strSrcLoc + "','" + strDestLoc + "',"+ nCalPriority
            //    + ",'',0,0,'',0,0,'Queued','','')";
            //return m_MySqlCommon.ExcuteCommand(strInsertSQL);

            return true;
        }

        /// <summary>
        /// Crane开始搬运，更新transfer_history表
        /// </summary>
        /// <param name="strCreateTime">命令创建时间</param>
        /// <param name="strCmdID">命令ID</param>
        /// <param name="nFirstSecond">1-第一个搬运子过程，2-第二个搬运子过程(双Crane时使用)</param>
        /// <param name="strCraneID">此搬运子过程的Crane ID</param>
        public bool CraneTransferStart(string strCreateTime, string strCmdID, int nFirstSecond, string strCraneID)
        {
            string strStartTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            
            //Write Txt
            transferLogger.Debug("[Crane Transfer Start] CmdCreateTime: " + strCreateTime + ", CmdID: " + strCmdID
                + ", FirstSecond: " + nFirstSecond + ", CraneID: " + strCraneID);

            ////Write DB
            //string strTable = EnumLogTableName.transfer.ToString() + "_" + strCreateTime.Substring(0, 8);
            //string strUpdateSQL = "update " + strTable;
            //if (nFirstSecond == 1) //第一个搬运子过程
            //{
            //    strUpdateSQL = strUpdateSQL + " set first_crane_id='" + strCraneID
            //        + "', first_start_time=" + strStartTime;
            //}
            //else if (nFirstSecond == 2) //第二个搬运子过程(双Crane时使用)
            //{
            //    strUpdateSQL = strUpdateSQL + " set second_crane_id='" + strCraneID
            //        + "', second_start_time=" + strStartTime;
            //}
            //strUpdateSQL = strUpdateSQL + ", command_state='Transferring' where command_create_time=" + strCreateTime
            //    + " and command_id='" + strCmdID + "'";
            //return m_MySqlCommon.ExcuteCommand(strUpdateSQL);

            return true;
        }

        /// <summary>
        /// Crane停止搬运，更新transfer_history表
        /// </summary>
        /// <param name="strCreateTime">命令创建时间</param>
        /// <param name="strCmdID">命令ID</param>
        /// <param name="nFirstSecond">1-第一个搬运子过程，2-第二个搬运子过程(双Crane时使用)</param>
        /// <param name="strCmdState">命令执行结果</param>
        /// <param name="strDelayReason">命令超时的原因</param>
        public bool CraneTransferStop(string strCreateTime, string strCmdID, int nFirstSecond,
            string strCmdState, string strDelayReason)
        {
            string strStopTime = DateTime.Now.ToString("yyyyMMddHHmmssfff");

            //Write Txt
            transferLogger.Debug("[Crane Transfer Start] CmdCreateTime: " + strCreateTime + ", CmdID: " + strCmdID
                + ", FirstSecond: " + nFirstSecond + ", CmdState: " + strCmdState + ", DelayReason: " + strDelayReason);

            ////Write DB
            //string strTable = EnumLogTableName.transfer.ToString() + "_" + strCreateTime.Substring(0, 8);
            //string strUpdateSQL = "update " + strTable;
            //if (nFirstSecond == 1) //第一个搬运子过程
            //{
            //    strUpdateSQL = strUpdateSQL + " set first_end_time=" + strStopTime;
            //}
            //else if (nFirstSecond == 2) //第二个搬运子过程(双Crane时使用)
            //{
            //    strUpdateSQL = strUpdateSQL + " set second_end_time=" + strStopTime;                 ;
            //}
            //strUpdateSQL = strUpdateSQL + ", command_state='" + strCmdState + "', delay_reason='" + strDelayReason + "'"
            //    + " where command_create_time=" + strCreateTime + " and command_id='" + strCmdID + "'";
            //return m_MySqlCommon.ExcuteCommand(strUpdateSQL);

            return true;
        }
    }
}
