﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Proj.DB;
using Proj.Entity.PointConfig;
using IoTClient.Clients.PLC;
using IoTClient.Enums;

namespace Proj.DevComm
{
    public class PLCTools
    {
        
        public static List<tp_point_config> plcPointList;//点位信息
        public static List<tp_equipment_t> equipmentList;//设备信息

        public static Dictionary<string, OmronFinsClient> plcDictionary;

        /// <summary>
        /// 加载PLC点位数据
        /// </summary>
        public object LoadPLCData()
        {
            try
            {
                plcPointList = new DbContext<tp_point_config>().Db.Queryable<tp_point_config>().ToList();
                if (plcPointList != null)
                {
                    return "success";
                }
                else
                {
                    return "点位数据加载失败!数据表或为空！";
                }
            }
            catch (Exception ex)
            {

                return ex;
            }
        }
        /// <summary>
        /// 加载设备信息并连接
        /// 使用 IoTClient
        /// </summary>
        /// <returns></returns>
        public object ConnectEquipment()
        {
            PLCResult result = new();
            try
            {
                // 初始化字典
                plcDictionary = new Dictionary<string, OmronFinsClient>();

                equipmentList = new DbContext<tp_equipment_t>().Db.Queryable<tp_equipment_t>().ToList();
                if (equipmentList != null)
                {
                    foreach (tp_equipment_t equipment in equipmentList)
                    {
                        if (equipment.IsEnable == true)
                        {
                            try
                            {
                                OmronFinsClient omronPLC = new(equipment.EQUIPMENT_IP, int.Parse(equipment.EQUIPMENT_PORT));
                                omronPLC.Open(); // IoTClient 使用 Open() 方法连接
                                plcDictionary.Add(equipment.EQUIPMENT_NAME, omronPLC);
                            }
                            catch (Exception connectEx)
                            {
                                result.msg = connectEx.Message;
                                return false;
                            }
                        }
                    }
                }
                else
                {
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                return ex;
            }
        }
        /// <summary>
        /// 读取PLC数据
        /// </summary>
        /// <param name="tagName"></param>
        /// <returns></returns>
        public string PLCRead(string tagName)
        {
            PLCResult result = new();
            string tagValue = null;

            string address = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_ADDRESS;
            string eQUIPMENT_NAME = plcPointList.Find(x => x.POINT_NAME == tagName).EQUIPMENT_NAME;
            string count = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_LENGTH;
            string pointType = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_TYPE;
            OmronFinsClient newOmronPLC = plcDictionary[eQUIPMENT_NAME];

            switch (pointType)
            {
                case "short":
                    var readResultInt16 = newOmronPLC.ReadInt16(address);
                    if (readResultInt16.IsSucceed)
                    {
                        tagValue = readResultInt16.Value.ToString();
                        result.code = true;
                    }
                    else
                    {
                        result.msg = readResultInt16.Err;
                        result.code = false;
                    }
                    break;
                case "int":
                    var readResultInt32 = newOmronPLC.ReadInt32(address);
                    if (readResultInt32.IsSucceed)
                    {
                        tagValue = readResultInt32.Value.ToString();
                        result.code = true;
                    }
                    else
                    {
                        result.msg = readResultInt32.Err;
                        result.code = false;
                    }
                    break;
                case "float":
                    var readResultFloat = newOmronPLC.ReadFloat(address);
                    if (readResultFloat.IsSucceed)
                    {
                        tagValue = readResultFloat.Value.ToString();
                        result.code = true;
                    }
                    else
                    {
                        result.msg = readResultFloat.Err;
                        result.code = false;
                    }
                    break;
                case "bool":
                    var readResultBool = newOmronPLC.ReadBoolean(address);
                    if (readResultBool.IsSucceed)
                    {
                        tagValue = readResultBool.Value.ToString();
                        result.code = true;
                    }
                    else
                    {
                        result.msg = readResultBool.Err;
                        result.code = false;
                    }
                    break;
                case "string":
                    var readResultString = newOmronPLC.ReadString(address, ushort.Parse(count));
                    if (readResultString.IsSucceed)
                    {
                        tagValue = readResultString.Value;
                        result.code = true;
                    }
                    else
                    {
                        result.msg = readResultString.Err;
                        result.code = false;
                    }
                    break;
                case "long":
                    var readResultLong = newOmronPLC.ReadInt64(address);
                    if (readResultLong.IsSucceed)
                    {
                        tagValue = readResultLong.Value.ToString();
                        result.code = true;
                    }
                    else
                    {
                        result.msg = readResultLong.Err;
                        result.code = false;
                    }
                    break;
                case "byte":
                    var readResultByte = newOmronPLC.ReadByte(address);
                    if (readResultByte.IsSucceed)
                    {
                        tagValue = readResultByte.Value.ToString();
                        result.code = true;
                    }
                    else
                    {
                        result.msg = readResultByte.Err;
                        result.code = false;
                    }
                    break;
                default:
                    result.code = false;
                    result.msg = "不支持的数据类型";
                    break;
            }

            return tagValue;
            //short[] rd;
            //var value = plcDictionary[eQUIPMENT_NAME].ReadWords(PlcMemory.DM,short.Parse(address),short.Parse(count),out rd);
            //if (value!=-1)
            //{
            //   tagValue = string.Concat(rd.Select(r => r.ToString()));
            //}
            //return tagValue;
        }
        public string PLCWrite(string tagName, string data)
        {
            PLCResult result = new() { data = "" };
            string res = string.Empty;
            string address = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_ADDRESS;
            string eQUIPMENT_NAME = plcPointList.Find(x => x.POINT_NAME == tagName).EQUIPMENT_NAME;
            string pointType = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_TYPE;
            OmronFinsClient newOmronPLC = plcDictionary[eQUIPMENT_NAME];

            switch (pointType)
            {
                case "short":
                    var writeResultInt16 = newOmronPLC.Write(address, short.Parse(data));
                    if (writeResultInt16.IsSucceed)
                    {
                        res = "success";
                        result.code = true;
                    }
                    else
                    {
                        res = writeResultInt16.Err;
                        result.code = false;
                        result.msg = res;
                    }
                    break;
                case "int":
                    var writeResultInt32 = newOmronPLC.Write(address, int.Parse(data));
                    if (writeResultInt32.IsSucceed)
                    {
                        res = "success";
                        result.code = true;
                    }
                    else
                    {
                        res = writeResultInt32.Err;
                        result.code = false;
                        result.msg = res;
                    }
                    break;
                case "float":
                    var writeResultFloat = newOmronPLC.Write(address, float.Parse(data));
                    if (writeResultFloat.IsSucceed)
                    {
                        res = "success";
                        result.code = true;
                    }
                    else
                    {
                        res = writeResultFloat.Err;
                        result.code = false;
                        result.msg = res;
                    }
                    break;
                case "bool":
                    var writeResultBool = newOmronPLC.Write(address, bool.Parse(data));
                    if (writeResultBool.IsSucceed)
                    {
                        res = "success";
                        result.code = true;
                    }
                    else
                    {
                        res = writeResultBool.Err;
                        result.code = false;
                        result.msg = res;
                    }
                    break;
                case "string":
                    var writeResultString = newOmronPLC.Write(address, data);
                    if (writeResultString.IsSucceed)
                    {
                        res = "success";
                        result.code = true;
                    }
                    else
                    {
                        res = writeResultString.Err;
                        result.code = false;
                        result.msg = res;
                    }
                    break;
                case "byte":
                    var writeResultByte = newOmronPLC.Write(address, byte.Parse(data));
                    if (writeResultByte.IsSucceed)
                    {
                        res = "success";
                        result.code = true;
                    }
                    else
                    {
                        res = writeResultByte.Err;
                        result.code = false;
                        result.msg = res;
                    }
                    break;
                default:
                    result.msg = "无此数据类型";
                    result.code = false;
                    break;
            }

            //short[] wt;
            //var value = plcDictionary[eQUIPMENT_NAME].WriteWords(PlcMemory.DM, short.Parse(address), short.Parse(count), wt);
            return res;
        }
        public class PLCResult
        {
            /// <summary>
            /// 读取数据
            /// </summary>
            public string data { get; set; }
            /// <summary>
            /// 报错信息
            /// </summary>
            public string msg { get; set; }
            /// <summary>
            /// 结果代码 成功:true 失败:false
            /// </summary>
            public bool code { get; set; }
        }


    }
}
