﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Proj.DB;
using Proj.Entity.PointConfig;
using HslCommunication.Profinet.Omron;
using HslCommunication;
using ControlEase.AI.Messages;

namespace Proj.DevComm
{
    public class PLCTools
    {
        
        public static List<tp_point_config> plcPointList;//点位信息
        public static List<tp_equipment_t> equipmentList;//设备信息

        public static Dictionary<string, OmronFinsNet> plcDictionary;

        /// <summary>
        /// 加载PLC点位数据
        /// </summary>
        public object LoadPLCData()
        {
            try
            {
                plcPointList = new DbContext<tp_point_config>().Db.Queryable<tp_point_config>().ToList();
                if (plcPointList != null)
                {
                    return "success";
                }
                else
                {
                    return "点位数据加载失败!数据表或为空！";
                }
            }
            catch (Exception ex)
            {

                return ex;
            }
        }
        /// <summary>
        /// 加载设备信息并连接
        /// hsl
        /// </summary>
        /// <returns></returns>
        public object ConnectEquipment()
        {
            PLCResult result = new PLCResult();
            try
            {
                equipmentList = new DbContext<tp_equipment_t>().Db.Queryable<tp_equipment_t>().ToList();
                if (equipmentList != null)
                {
                    foreach (tp_equipment_t equipment in equipmentList)
                    {
                        if (equipment.IsEnable==true)
                        {
                            OmronFinsNet omronPLC = new OmronFinsNet(equipment.EQUIPMENT_IP,int.Parse(equipment.EQUIPMENT_PORT));
                            OperateResult connectResult =  omronPLC.ConnectServer();
                            if (connectResult.IsSuccess)
                            {
                                plcDictionary.Add(equipment.EQUIPMENT_NAME, omronPLC);
                            }
                            else
                            {
                                result.msg = connectResult.Message;
                                return false;
                            }
                            //EtherNetPLC plc = new EtherNetPLC();
                            //short retCode= plc.Link(equipment.EQUIPMENT_IP, short.Parse(equipment.EQUIPMENT_PORT), 500);
                            //if (retCode==0)
                            //{
                            //    plcDictionary.Add(equipment.EQUIPMENT_NAME, plc);
                            //}
                            //else
                            //{
                            //    return false;
                            //    //retCode=-1代表连接失败，OmronFinsTCP作者并未解析错误原因
                            //}
                        }
                        
                    }
                    
                }
                else
                {
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                return ex;
            }
           
        }
        /// <summary>
        /// 读取PLC数据
        /// </summary>
        /// <param name="tagName"></param>
        /// <returns></returns>
        public string PLCRead(string tagName)
        {
            PLCResult result = new PLCResult();
            string tagValue = null;
            string redErr = null;
            
            string address = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_ADDRESS;
            string eQUIPMENT_NAME = plcPointList.Find(x => x.POINT_NAME == tagName).EQUIPMENT_NAME;
            string count = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_LENGTH;
            string pointType = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_TYPE;
            OmronFinsNet newOmronPLC = plcDictionary[eQUIPMENT_NAME];
            switch (pointType)
            {
                case "short":
                    OperateResult<short[]> readResultInt16 = newOmronPLC.ReadInt16(address, ushort.Parse(count));
                    if (readResultInt16.IsSuccess)
                    {
                        tagValue = string.Join(", ", readResultInt16.Content);
                        result.code = true;
                        //Console.WriteLine("Read Int16 result: " + string.Join(", ", readResultInt16.Content));
                        //Console.ReadLine();
                    }
                    else
                    {
                        redErr = string.Join(", ", readResultInt16.Message);
                        result.code = false;
                    }
                    break;
                case "int":
                    OperateResult<int[]> readResultInt32 = newOmronPLC.ReadInt32(address, ushort.Parse(count));
                    if (readResultInt32.IsSuccess)
                    {
                        tagValue = string.Join(", ", readResultInt32.Content);
                        result.code = true;
                        //Console.WriteLine("Read Int32 result: " + string.Join(", ", readResultInt32.Content));
                        //Console.ReadLine();
                    }
                    else
                    {
                        redErr = string.Join(", ", readResultInt32.Message);
                        result.code = false;
                    }
                    break;
                case "float":
                    OperateResult<float[]> readResultFloat = newOmronPLC.ReadFloat(address, ushort.Parse(count));
                    if (readResultFloat.IsSuccess)
                    {
                        tagValue = string.Join(", ", readResultFloat.Content);
                        result.code = true;
                        //Console.WriteLine("Read Float result: " + string.Join(", ", readResultFloat.Content));
                        //Console.ReadLine();
                    }
                    else
                    {
                        redErr = string.Join(", ", readResultFloat.Message);
                        result.code = false;
                    }
                    break;
                case "bool":
                    OperateResult<bool[]> readResultBool = newOmronPLC.ReadBool(address, ushort.Parse(count));
                    if (readResultBool.IsSuccess)
                    {
                        tagValue = string.Join(", ", readResultBool.Content);
                        result.code = true;
                        //Console.WriteLine("Read Bool result: " + string.Join(", ", readResultBool.Content));
                        //Console.ReadLine();
                    }
                    else
                    {
                        redErr = string.Join(", ", readResultBool.Message);
                        result.code = false;
                    }
                    break;
                case "string":
                    OperateResult<byte[]> readResultBytes = newOmronPLC.Read(address, ushort.Parse(count));
                    if (readResultBytes.IsSuccess)
                    {
                        // 假设字符串编码为UTF-8
                        tagValue = Encoding.UTF8.GetString(readResultBytes.Content);
                        result.code = true;
                        //Console.WriteLine("Read String result: " + stringValue2);
                    }
                    else
                    {
                        redErr = string.Join(", ", readResultBytes.Message);
                        result.code = false;
                    }
                    break;
                case "long":
                    OperateResult<long[]> readResultLong = newOmronPLC.ReadInt64(address, ushort.Parse(count));

                    if (readResultLong.IsSuccess)
                    {
                        tagValue = string.Join(", ", readResultLong.Content);
                        result.code = true;
                        //Console.WriteLine("Read Long result: " + string.Join(", ", readResultLong.Content));
                        //Console.ReadLine();
                    }
                    else
                    {
                        redErr = string.Join(", ", readResultLong.Message);
                        result.code = false;
                    }
                    break;
                case "byte":
                    OperateResult<byte[]> readResult = newOmronPLC.Read(address, ushort.Parse(count));
                    if (readResult.IsSuccess)
                    {
                        tagValue = BitConverter.ToString(readResult.Content);
                        result.code = true;
                        //Console.WriteLine("Read result: " + BitConverter.ToString(readResult.Content));
                        //Console.ReadLine();
                    }
                    else
                    {
                        redErr = string.Join(", ", readResult.Message);
                        result.code = false;
                    }
                    break;
                default:
                    result.code = false;
                    break;
            }

            return tagValue;
            //short[] rd;
            //var value = plcDictionary[eQUIPMENT_NAME].ReadWords(PlcMemory.DM,short.Parse(address),short.Parse(count),out rd);
            //if (value!=-1)
            //{
            //   tagValue = string.Concat(rd.Select(r => r.ToString()));
            //}
            //return tagValue;
        }
        public string PLCWrite(string tagName,string data)
        {
            PLCResult result = new PLCResult();
            result.data = "";
            string res=string.Empty;
            string address = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_ADDRESS;
            string eQUIPMENT_NAME = plcPointList.Find(x => x.POINT_NAME == tagName).EQUIPMENT_NAME;
            string count = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_LENGTH;
            string pointType = plcPointList.Find(x => x.POINT_NAME == tagName).POINT_TYPE;
            OmronFinsNet newOmronPLC = plcDictionary[eQUIPMENT_NAME];
            switch (pointType)
            {
                case "short":
                    short[] int16Data = {short.Parse(data)};
                    OperateResult writeResultInt16 = newOmronPLC.Write(address, int16Data);
                    if (writeResultInt16.IsSuccess)
                    {
                        res = "succsee";
                        result.code = true;
                        //Console.WriteLine("Write Int16 data success");
                    }
                    else
                    {
                        res = writeResultInt16.Message;
                        result.code = false;
                        result.msg = res;
                    }
                    break;
                case "int":
                    int[] int32Data = { int.Parse(data) };
                    OperateResult writeResultInt32 = newOmronPLC.Write(address, int32Data);
                    if (writeResultInt32.IsSuccess)
                    {
                        res = "succsee";
                        result.code = true;
                        //Console.WriteLine("Write Int32 data success");
                    }
                    else
                    {
                        res = writeResultInt32.Message;
                        result.code = false;
                        result.msg = res;
                        //Console.WriteLine("Write Int32 data failed: " + writeResultInt32.Message);
                    }
                    break;
                case "float":
                    float[] floatData = {float.Parse(data) };
                    OperateResult writeResultFloat = newOmronPLC.Write(address, floatData);
                    if (writeResultFloat.IsSuccess)
                    {
                        res = "succsee";
                        result.code = true;
                        //Console.WriteLine("Write Float data success");
                    }
                    else
                    {
                        res = writeResultFloat.Message;
                        result.code = false;
                        result.msg = res;
                        //Console.WriteLine("Write Float data failed: " + writeResultFloat.Message);
                    }
                    break;
                case "bool":
                    bool[] boolData = { bool.Parse(data)};
                    OperateResult writeResultBool = newOmronPLC.Write(address, boolData);
                    if (writeResultBool.IsSuccess)
                    {
                        res = "succsee";
                        result.code = true;

                        //Console.WriteLine("Write Bool data success");
                    }
                    else
                    {
                        res = writeResultBool.Message;
                        result.code = false;
                        result.msg = res;
                        //Console.WriteLine("Write Bool data failed: " + writeResultBool.Message);
                    }
                    break;
                case "string":
                    string stringValue = data;
                    byte[] stringBytes = Encoding.UTF8.GetBytes(stringValue);
                    OperateResult writeResultString = newOmronPLC.Write(address, stringBytes);
                    if (writeResultString.IsSuccess)
                    {
                        res = "succsee";
                        result.code = true;
                        //Console.WriteLine("Write String data success");
                    }
                    else
                    {
                        res = writeResultString.Message;
                        result.code = false;
                        result.msg = res;
                        //Console.WriteLine("Write String data failed: " + writeResultString.Message);
                    }
                    break;
                case "byte":
                    byte byteValue = byte.Parse(data);
                    OperateResult writeResultByte = newOmronPLC.Write("D600", new byte[] { byteValue });
                    if (writeResultByte.IsSuccess)
                    {
                        res = "succsee";
                        result.code = true;
                        //Console.WriteLine("Write Byte data success");
                    }
                    else
                    {
                        res = writeResultByte.Message;
                        result.code = false;
                        result.msg = res;
                        //Console.WriteLine("Write Byte data failed: " + writeResultByte.Message);
                    }
                    break;
                default:
                    result.msg = "无此数据类型";
                    result.code = false;
                    break;
            }

            //short[] wt;
            //var value = plcDictionary[eQUIPMENT_NAME].WriteWords(PlcMemory.DM, short.Parse(address), short.Parse(count), wt);
            return res;
        }
        public class PLCResult
        {
            /// <summary>
            /// 读取数据
            /// </summary>
            public string data { get; set; }
            /// <summary>
            /// 报错信息
            /// </summary>
            public string msg { get; set; }
            /// <summary>
            /// 结果代码 成功:true 失败:false
            /// </summary>
            public bool code { get; set; }
        }


    }
}
