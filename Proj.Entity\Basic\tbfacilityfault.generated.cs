﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbFacilityFault and List
    [Serializable]
    [Description("设备故障情况表")]
    [LinqToDB.Mapping.Table("TB_FACILITY_FAULT")]
    public partial class TbFacilityFault : GEntity<TbFacilityFault>, ITimestamp
    {
        #region Contructor(s)

        private TbFacilityFault()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CFacilityfaultId = RegisterProperty<String>(p => p.CFacilityfaultId);
        private static readonly PropertyInfo<String> pty_CFacilitylistId = RegisterProperty<String>(p => p.CFacilitylistId);
        private static readonly PropertyInfo<String> pty_CFacilitylistName = RegisterProperty<String>(p => p.CFacilitylistName);
        private static readonly PropertyInfo<String> pty_CState = RegisterProperty<String>(p => p.CState);
        private static readonly PropertyInfo<String> pty_CFaultId = RegisterProperty<String>(p => p.CFaultId);
        private static readonly PropertyInfo<String> pty_CFaultidDes = RegisterProperty<String>(p => p.CFaultidDes);
        private static readonly PropertyInfo<String> pty_CFacilityfaultvalue = RegisterProperty<String>(p => p.CFacilityfaultvalue);
        private static readonly PropertyInfo<String> pty_CFacilityfaultstart = RegisterProperty<String>(p => p.CFacilityfaultstart);
        private static readonly PropertyInfo<String> pty_CFacilityfaultend = RegisterProperty<String>(p => p.CFacilityfaultend);
        private static readonly PropertyInfo<String> pty_CFacilityId = RegisterProperty<String>(p => p.CFacilityId);
        private static readonly PropertyInfo<String> pty_CFacilityName = RegisterProperty<String>(p => p.CFacilityName);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 设备故障情况编号
        /// </summary>
        [Description("设备故障情况编号")]
        [LinqToDB.Mapping.Column("C_FACILITYFAULTID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CFacilityfaultId
        {
            get { return GetProperty(pty_CFacilityfaultId); }
            set { SetProperty(pty_CFacilityfaultId, value); }
        }
        /// <summary>
        /// 个体设备编号
        /// </summary>
        [Description("个体设备编号")]
        [LinqToDB.Mapping.Column("C_FACILITYLISTID")]
        public String CFacilitylistId
        {
            get { return GetProperty(pty_CFacilitylistId); }
            set { SetProperty(pty_CFacilitylistId, value); }
        }
        /// <summary>
        /// 个体设备名称
        /// </summary>
        [Description("个体设备名称")]
        [LinqToDB.Mapping.Column("C_FACILITYLISTNAME")]
        public String CFacilitylistName
        {
            get { return GetProperty(pty_CFacilitylistName); }
            set { SetProperty(pty_CFacilitylistName, value); }
        }
        /// <summary>
        /// 个体设备状态
        /// </summary>
        [Description("个体设备状态")]
        [LinqToDB.Mapping.Column("C_STATE")]
        public String CState
        {
            get { return GetProperty(pty_CState); }
            set { SetProperty(pty_CState, value); }
        }
        /// <summary>
        /// 故障编号
        /// </summary>
        [Description("故障编号")]
        [LinqToDB.Mapping.Column("C_FAULTID")]
        public String CFaultId
        {
            get { return GetProperty(pty_CFaultId); }
            set { SetProperty(pty_CFaultId, value); }
        }
        /// <summary>
        /// 故障内容
        /// </summary>
        [Description("故障内容")]
        [LinqToDB.Mapping.Column("C_FAULTIDDES")]
        public String CFaultidDes
        {
            get { return GetProperty(pty_CFaultidDes); }
            set { SetProperty(pty_CFaultidDes, value); }
        }
        /// <summary>
        /// 故障触发次数
        /// </summary>
        [Description("故障触发次数")]
        [LinqToDB.Mapping.Column("C_FACILITYFAULTVALUE")]
        public String CFacilityfaultvalue
        {
            get { return GetProperty(pty_CFacilityfaultvalue); }
            set { SetProperty(pty_CFacilityfaultvalue, value); }
        }
        /// <summary>
        /// 故障统计开始时间
        /// </summary>
        [Description("故障统计开始时间")]
        [LinqToDB.Mapping.Column("C_FACILITYFAULTSTART")]
        public String CFacilityfaultstart
        {
            get { return GetProperty(pty_CFacilityfaultstart); }
            set { SetProperty(pty_CFacilityfaultstart, value); }
        }
        /// <summary>
        /// 故障统计完成时间
        /// </summary>
        [Description("故障统计完成时间")]
        [LinqToDB.Mapping.Column("C_FACILITYFAULTEND")]
        public String CFacilityfaultend
        {
            get { return GetProperty(pty_CFacilityfaultend); }
            set { SetProperty(pty_CFacilityfaultend, value); }
        }
        /// <summary>
        /// 设备编码
        /// </summary>
        [Description("设备编码")]
        [LinqToDB.Mapping.Column("C_FACILITYID")]
        public String CFacilityId
        {
            get { return GetProperty(pty_CFacilityId); }
            set { SetProperty(pty_CFacilityId, value); }
        }
        /// <summary>
        /// 设备名称
        /// </summary>
        [Description("设备名称")]
        [LinqToDB.Mapping.Column("C_FACILITYNAME")]
        public String CFacilityName
        {
            get { return GetProperty(pty_CFacilityName); }
            set { SetProperty(pty_CFacilityName, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion


        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CFacilityfaultId, "设备故障情况编号是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityfaultId, 36, "设备故障情况编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilitylistId, 36, "个体设备编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilitylistName, 40, "个体设备名称不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CState, 40, "个体设备状态不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFaultId, 36, "故障编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFaultidDes, 100, "故障内容不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityfaultvalue, 5, "故障触发次数不能超过5个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityfaultstart, 76, "故障统计开始时间不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityfaultend, 76, "故障统计完成时间不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityId, 36, "设备编码不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityName, 100, "设备名称不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 40, "扩展字段A不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 40, "扩展字段B不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 40, "扩展字段C不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CFacilityfaultId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbFacilityFaultList : GEntityList<TbFacilityFaultList, TbFacilityFault>
    {
        private TbFacilityFaultList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
