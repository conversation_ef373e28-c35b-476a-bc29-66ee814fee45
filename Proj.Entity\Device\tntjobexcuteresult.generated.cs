﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntJobexcuteresult and List
    [Serializable]
    [Description("任务执行结果表")]
    [LinqToDB.Mapping.Table("Tnt_JobExcuteResult")]
    public partial class TntJobexcuteresult : GEntity<TntJobexcuteresult>, ITimestamp
    {
        #region Contructor(s)

        private TntJobexcuteresult()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CJobgroupCode = RegisterProperty<String>(p => p.CJobgroupCode);
        private static readonly PropertyInfo<String> pty_CMessageType = RegisterProperty<String>(p => p.CMessageType);
        private static readonly PropertyInfo<DateTime?> pty_CSenddate = RegisterProperty<DateTime?>(p => p.CSenddate);
        private static readonly PropertyInfo<String> pty_CMessagestate = RegisterProperty<String>(p => p.CMessagestate);
        private static readonly PropertyInfo<String> pty_CMessageerrorrror = RegisterProperty<String>(p => p.CMessageerrorrror);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        private static readonly PropertyInfo<String> pty_CExcutemethod = RegisterProperty<String>(p => p.CExcutemethod);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_Pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 作业编码
        /// </summary>
        [Description("作业编码")]
        [LinqToDB.Mapping.Column("c_JobGroupCode")]
        public String CJobgroupCode
        {
            get { return GetProperty(pty_CJobgroupCode); }
            set { SetProperty(pty_CJobgroupCode, value); }
        }
        /// <summary>
        /// 执行类别
        /// </summary>
        [Description("执行类别")]
        [LinqToDB.Mapping.Column("C_MessageType")]
        public String CMessageType
        {
            get { return GetProperty(pty_CMessageType); }
            set { SetProperty(pty_CMessageType, value); }
        }
        /// <summary>
        /// 执行时间
        /// </summary>
        [Description("执行时间")]
        [LinqToDB.Mapping.Column("C_SendDate")]
        public DateTime? CSenddate
        {
            get { return GetProperty(pty_CSenddate); }
            set { SetProperty(pty_CSenddate, value); }
        }
        /// <summary>
        /// 执行结果
        /// </summary>
        [Description("执行结果")]
        [LinqToDB.Mapping.Column("C_MessageState")]
        public String CMessagestate
        {
            get { return GetProperty(pty_CMessagestate); }
            set { SetProperty(pty_CMessagestate, value); }
        }
        /// <summary>
        /// 错误消息
        /// </summary>
        [Description("错误消息")]
        [LinqToDB.Mapping.Column("C_MessageERRORrror")]
        public String CMessageerrorrror
        {
            get { return GetProperty(pty_CMessageerrorrror); }
            set { SetProperty(pty_CMessageerrorrror, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
        /// <summary>
        /// 执行方法
        /// </summary>
        [Description("执行方法")]
        [LinqToDB.Mapping.Column("c_ExcuteMethod")]
        public String CExcutemethod
        {
            get { return GetProperty(pty_CExcutemethod); }
            set { SetProperty(pty_CExcutemethod, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 80, "主键不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CJobgroupCode, 40, "作业编码不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMessageType, 2, "执行类别不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMessagestate, 2, "执行结果不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMessageerrorrror, 400, "错误消息不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExcutemethod, 40, "执行方法不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntJobexcuteresultList : GEntityList<TntJobexcuteresultList, TntJobexcuteresult>
    {
        private TntJobexcuteresultList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
