# Stocker Service (.NET 8.0)

这是从.NET Framework 4.0 WCF服务移植到.NET 8.0的Stocker服务。新服务使用ASP.NET Core Web API和SignalR来替代原有的WCF功能。

## 功能特性

- **Web API**: 提供RESTful API接口，替代WCF的操作契约
- **SignalR Hub**: 提供实时双向通信，替代WCF的回调功能
- **兼容性**: 保持与原WCF服务相同的业务逻辑和接口
- **现代化**: 使用.NET 8.0的最新特性和性能优化

## 架构对比

### 原WCF服务 (.NET Framework 4.0)
- `IWCFService`: 服务契约接口
- `WCFService`: 服务实现类
- `WCFHost`: 服务宿主
- TCP绑定 + 回调机制

### 新服务 (.NET 8.0)
- `IStockerService`: 业务服务接口
- `StockerController`: Web API控制器
- `StockerHub`: SignalR Hub
- HTTP/HTTPS + WebSocket

## 项目结构

```
Proj.Service/
├── Controllers/           # Web API控制器
│   └── StockerController.cs
├── Hubs/                 # SignalR Hub
│   └── StockerHub.cs
├── Interfaces/           # 服务接口
│   └── IStockerService.cs
├── Models/               # 数据模型
│   └── ServiceModels.cs
├── Services/             # 服务实现
│   ├── StockerService.cs
│   ├── ClientManagerService.cs
│   └── WCFCompatibilityService.cs
├── Events/               # 事件定义
│   └── ServiceEvents.cs
├── Core/                 # 核心组件
│   └── ServiceHost.cs
├── Tests/                # 测试代码
│   ├── ServiceTest.cs
│   ├── SignalRTestClient.cs
│   └── TestRunner.cs
└── config/               # 配置文件
    └── wcf-config.xml
```

## 配置说明

### appsettings.json
```json
{
  "StockerService": {
    "ServerPort": 9900,
    "DefaultIP": "",
    "EnableHttps": true,
    "ConfigPath": "config/wcf-config.xml"
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:9900"
      },
      "Https": {
        "Url": "https://localhost:9901"
      }
    }
  }
}
```

### wcf-config.xml
```xml
<?xml version="1.0" encoding="utf-8"?>
<WCFConfig>
  <Host ip="127.0.0.1" />
  <Host ip="*************" />
</WCFConfig>
```

## API接口

### Web API端点

- `GET /health` - 健康检查
- `GET /api/stocker/test` - 连接测试
- `POST /api/stocker/message` - 发送消息
- `POST /api/stocker/state` - 获取状态
- `GET /api/stocker/states` - 获取所有状态
- `POST /api/stocker/tag` - 获取标签值
- `GET /api/stocker/clients` - 获取连接的客户端
- `POST /api/stocker/broadcast` - 广播消息

### SignalR Hub方法

- `Register()` - 客户端注册
- `ClientSendMessage(function, parameters)` - 发送消息
- `GetState(stateName)` - 获取状态
- `GetAllStates()` - 获取所有状态
- `GetTagValue(tagName)` - 获取标签值
- `ConnectTest()` - 连接测试

### SignalR客户端事件

- `ServerSendMessage(function, parameters)` - 接收服务端消息
- `OnRegistered(data)` - 注册成功通知
- `OnError(data)` - 错误通知

## 使用方法

### 1. 启动服务

```bash
cd Proj.Service
dotnet run
```

服务将在以下地址启动：
- HTTP: http://localhost:9900
- HTTPS: https://localhost:9901
- SignalR Hub: http://localhost:9900/stockerhub
- Swagger UI: http://localhost:9900

### 2. Web API客户端示例

```csharp
using System.Text;
using System.Text.Json;

var client = new HttpClient();
client.BaseAddress = new Uri("http://localhost:9900");

// 连接测试
var response = await client.GetAsync("/api/stocker/test");

// 发送消息
var request = new {
    Function = "ConnectTest",
    Parameters = new Dictionary<string, object>()
};
var json = JsonSerializer.Serialize(request);
var content = new StringContent(json, Encoding.UTF8, "application/json");
response = await client.PostAsync("/api/stocker/message", content);
```

### 3. SignalR客户端示例

```csharp
using Microsoft.AspNetCore.SignalR.Client;

var connection = new HubConnectionBuilder()
    .WithUrl("http://localhost:9900/stockerhub")
    .Build();

// 注册消息处理器
connection.On<string, Dictionary<string, object>>("ServerSendMessage", 
    (function, parameters) => {
        Console.WriteLine($"Received: {function}");
    });

// 连接并注册
await connection.StartAsync();
await connection.InvokeAsync("Register");

// 发送消息
var result = await connection.InvokeAsync<object>("ClientSendMessage", 
    "ConnectTest", new Dictionary<string, object>());
```

## 与原WCF服务的兼容性

### 事件集成
新服务提供了`WCFCompatibilityService`类来保持与原有代码的兼容性：

```csharp
// 在原有代码中
WCFCompatibilityService.Instance.ClientSendMessage += (function, parameters) => {
    // 处理客户端消息
    return ProcessMessage(function, parameters);
};

WCFCompatibilityService.Instance.ClientGetTagValue += (tagName) => {
    // 获取标签值
    return GetTagValue(tagName);
};

// 发送消息给客户端
await WCFCompatibilityService.Instance.ServerSendMessageAsync(function, parameters);
```

## 测试

### 运行自动化测试
```bash
cd Proj.Service
dotnet run --project Tests/TestRunner.cs
```

### 手动测试
1. 启动服务
2. 访问 http://localhost:9900 查看Swagger UI
3. 使用Swagger UI测试各个API端点
4. 使用SignalR测试客户端测试实时通信

## 部署

### 开发环境
```bash
dotnet run
```

### 生产环境
```bash
dotnet publish -c Release -o ./publish
cd publish
dotnet Proj.Service.dll
```

### Windows服务
可以使用`Microsoft.Extensions.Hosting.WindowsServices`包将应用程序部署为Windows服务。

## 注意事项

1. **端口配置**: 确保端口9900和9901没有被其他应用程序占用
2. **防火墙**: 如果需要外部访问，请配置防火墙规则
3. **HTTPS证书**: 生产环境中需要配置有效的SSL证书
4. **日志配置**: 确保Log4net配置文件路径正确
5. **依赖项目**: 确保所有依赖的项目（Proj.CacheData、Proj.Log等）已正确引用

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 检查配置文件是否正确
   - 查看日志文件获取详细错误信息

2. **SignalR连接失败**
   - 检查CORS配置
   - 确认Hub URL正确
   - 检查网络连接

3. **API调用失败**
   - 检查请求格式是否正确
   - 确认Content-Type为application/json
   - 查看服务端日志

### 日志位置
- 应用程序日志: `D:/STK_Log/Server_Log/[日期]/`
- WCF日志: `D:/STK_Log/Server_Log/[日期]/Wcf/`
- 异常日志: `D:/STK_Log/Server_Log/[日期]/Exception/`
