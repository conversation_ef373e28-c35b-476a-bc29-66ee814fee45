﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;


 
namespace Proj.Entity
{
    #region TbLocation and List
    [Serializable]
    [Description("库位信息表")]
    [LinqToDB.Mapping.Table("TB_LOCATION")]
    public partial class TbLocation : GEntity<TbLocation>, ITimestamp
    {
        #region Contructor(s)

        private TbLocation()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CLocationId = RegisterProperty<String>(p => p.CLocationId);
        private static readonly PropertyInfo<String> pty_CLocationDes = RegisterProperty<String>(p => p.CLocationDes);
        private static readonly PropertyInfo<String> pty_CStorageId = RegisterProperty<String>(p => p.CStorageId);
        private static readonly PropertyInfo<String> pty_CStorageDes = RegisterProperty<String>(p => p.CStorageDes);
        private static readonly PropertyInfo<String> pty_CFactoryId = RegisterProperty<String>(p => p.CFactoryId);
        private static readonly PropertyInfo<String> pty_CFactoryDes = RegisterProperty<String>(p => p.CFactoryDes);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 库位ID
        /// </summary>
        [Description("库位ID")]
        [LinqToDB.Mapping.Column("C_LOCATIONID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CLocationId
        {
            get { return GetProperty(pty_CLocationId); }
            set { SetProperty(pty_CLocationId, value); }
        }
        /// <summary>
        /// 库位描述
        /// </summary>
        [Description("库位描述")]
        [LinqToDB.Mapping.Column("C_LOCATIONDES")]
        public String CLocationDes
        {
            get { return GetProperty(pty_CLocationDes); }
            set { SetProperty(pty_CLocationDes, value); }
        }
        /// <summary>
        /// 库房ID
        /// </summary>
        [Description("库房ID")]
        [LinqToDB.Mapping.Column("C_STORAGEID")]
        public String CStorageId
        {
            get { return GetProperty(pty_CStorageId); }
            set { SetProperty(pty_CStorageId, value); }
        }
        /// <summary>
        /// 库房描述
        /// </summary>
        [Description("库房描述")]
        [LinqToDB.Mapping.Column("C_STORAGEDES")]
        public String CStorageDes
        {
            get { return GetProperty(pty_CStorageDes); }
            set { SetProperty(pty_CStorageDes, value); }
        }
        /// <summary>
        /// 工厂ID
        /// </summary>
        [Description("工厂ID")]
        [LinqToDB.Mapping.Column("C_FACTORYID")]
        public String CFactoryId
        {
            get { return GetProperty(pty_CFactoryId); }
            set { SetProperty(pty_CFactoryId, value); }
        }
        /// <summary>
        /// 工厂描述
        /// </summary>
        [Description("工厂描述")]
        [LinqToDB.Mapping.Column("C_FACTORYDES")]
        public String CFactoryDes
        {
            get { return GetProperty(pty_CFactoryDes); }
            set { SetProperty(pty_CFactoryDes, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }

      
     
        
        #endregion 
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CLocationId, "库位ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLocationId, 40, "库位ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLocationDes, 160, "库位描述不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CStorageId, 40, "库房ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CStorageDes, 160, "库房描述不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryId, 20, "工厂ID不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryDes, 20, "工厂描述不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CLocationId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbLocationList : GEntityList<TbLocationList, TbLocation>
    {
        private TbLocationList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
