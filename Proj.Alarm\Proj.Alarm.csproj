﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Proj.API\Proj.API.csproj" />
    <ProjectReference Include="..\Proj.DevComm\Proj.DevComm.csproj" />
    <ProjectReference Include="..\Proj.Entity\Proj.Entity.csproj" />
    <ProjectReference Include="..\Proj.History\Proj.History.csproj" />
    <ProjectReference Include="..\Proj.HostComm\Proj.HostComm.csproj" />
    <ProjectReference Include="..\Proj.Log\Proj.Log.csproj" />
    <ProjectReference Include="..\Proj.Service\Proj.Service.csproj" />
  </ItemGroup>

</Project>
