﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntJobsexcute and List
    [Serializable]
    [Description("作业执行表")]
    [LinqToDB.Mapping.Table("Tnt_JobsExcute")]
    public partial class TntJobsexcute : GEntity<TntJobsexcute>, ITimestamp
    {
        #region Contructor(s)

        private TntJobsexcute()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CJobgroupCode = RegisterProperty<String>(p => p.CJobgroupCode);
        private static readonly PropertyInfo<DateTime> pty_CNexttime = RegisterProperty<DateTime>(p => p.CNexttime);
        private static readonly PropertyInfo<DateTime> pty_CExcutetime = RegisterProperty<DateTime>(p => p.CExcutetime);
        private static readonly PropertyInfo<Int64> pty_CExcutecount = RegisterProperty<Int64>(p => p.CExcutecount);
        private static readonly PropertyInfo<String> pty_CEnable = RegisterProperty<String>(p => p.CEnable);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_Pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 作业组代码
        /// </summary>
        [Description("作业组代码")]
        [LinqToDB.Mapping.Column("c_JobGroupCode")]
        public String CJobgroupCode
        {
            get { return GetProperty(pty_CJobgroupCode); }
            set { SetProperty(pty_CJobgroupCode, value); }
        }
        /// <summary>
        /// 下次执行时间
        /// </summary>
        [Description("下次执行时间")]
        [LinqToDB.Mapping.Column("c_NextTime")]
        public DateTime CNexttime
        {
            get { return GetProperty(pty_CNexttime); }
            set { SetProperty(pty_CNexttime, value); }
        }
        /// <summary>
        /// 本次执行时间
        /// </summary>
        [Description("本次执行时间")]
        [LinqToDB.Mapping.Column("c_ExcuteTime")]
        public DateTime CExcutetime
        {
            get { return GetProperty(pty_CExcutetime); }
            set { SetProperty(pty_CExcutetime, value); }
        }
        /// <summary>
        /// 执行次数
        /// </summary>
        [Description("执行次数")]
        [LinqToDB.Mapping.Column("c_ExcuteCount")]
        public Int64 CExcutecount
        {
            get { return GetProperty(pty_CExcutecount); }
            set { SetProperty(pty_CExcutecount, value); }
        }
        /// <summary>
        /// 有效状态
        /// </summary>
        [Description("有效状态")]
        [LinqToDB.Mapping.Column("c_Enable")]
        public String CEnable
        {
            get { return GetProperty(pty_CEnable); }
            set { SetProperty(pty_CEnable, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 80, "主键不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CJobgroupCode, 80, "作业组代码不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CEnable, 2, "有效状态不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntJobsexcuteList : GEntityList<TntJobsexcuteList, TntJobsexcute>
    {
        private TntJobsexcuteList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
