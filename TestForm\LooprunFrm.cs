﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Proj.DataTypeDef;
using Proj.CacheData;
using Proj.UnitMng;
using Proj.Log;
using System.Threading;
using Proj.DevComm;
using Proj.DB;
using Proj.Entity;

namespace TestForm
{
    public partial class LooprunFrm : Form
    {
        public LooprunFrm()
        {
            InitializeComponent();
        }

        private void LooprunFrm_Load(object sender, EventArgs e)
        {
            if (!PlcComm.Instance.Start())
            {
                MessageBox.Show("plc start failed");
            }
                InitData();

            Thread th = new Thread(looprun);
            th.IsBackground = true;
            th.Start();
        }

        private void InitData()
        {
            try
            {
            List<string> carrierList = new List<string>() { "c001", "c002", "c003" };

            List<string> locationList = new List<string>() { "20201", "20202", "20203", "20204", "20205" };

            this.comboBox1.DataSource = carrierList;
            //this.comboBox1.ValueMember = "CarrierId";
            //this.listBox2.DataSource = locationList;
            TpCyclecarrier cycCarrier = TpCyclecarrierList.GetAll().FirstOrDefault();
            if (cycCarrier != null)
            {
                this.comboBox1.SelectedIndex = this.comboBox1.Items.IndexOf(cycCarrier.CarrierId);
                this.numericUpDown1.Value = (decimal)cycCarrier.CycleTimes;

                if (cycCarrier.UsePort == 1)
                {
                    this.checkBox1.Checked = true;
                    radioButton2.Checked = radioButton2.Text == cycCarrier.PortName;
                    radioButton1.Enabled = true;
                    radioButton2.Enabled = true;
                }
            }

            List<string> loopList = DbTrans.Instance.GetCycleLocationList();
            //初始化loopList
            foreach (string strItem in loopList)
            {
                this.listBox2.Items.Add(strItem);
            }

            //初始化commandList
            foreach (string strItem in locationList)
            {
                if(!loopList.Contains(strItem))
                    this.listBox1.Items.Add(strItem);
            }


            UpdatebtnStatus();
            }
            catch (Exception ex)
            {
                Proj.Log.Logger.Instance.ExceptionLog("LooprunFrm.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }

        private void looprun()
        {
            while (true)
            {
                if (CycleTestMng.Instance.IsCycleStart() )
                {
                    Thread.Sleep(500);
                    EnhancedTransferCommand enhancedTransferCommand = CycleTestMng.Instance.GenNextCycleCommand();
                    if (enhancedTransferCommand != null)
                    {
                        string strLog = enhancedTransferCommand.strCarrierLoc + " to " + enhancedTransferCommand.strDest;
                        Logger.Instance.TransferLog(strLog);
                    }
                    //开始looprun后判断是否停止
                    if (!CycleTestMng.Instance.IsCycleStart())
                    {
                        MessageBox.Show("loop end!");
                        this.Invoke(new Action(() =>
                        {
                            UpdatebtnStatus();
                        }));
                    }
                }
            }
        }

        private void btnStart_Click(object sender, EventArgs e)
        {
            if (!SaveInfo())
            {
                MessageBox.Show("save failed!");
                return;
            }

            if (this.numericUpDown1.Value < 1)
            {
                MessageBox.Show("please set the CycleTimes!");
                return;
            }
            if (this.listBox2.Items.Count < 1)
            {
                MessageBox.Show("please set the LoopList!");
                return;
            }

            //测试时注释
            //if (!CraneDev.Instance.CraneTaskStart())
            //{
            //    MessageBox.Show("启动失败");
            //    return;
            //}

            CycleTestMng.Instance.StartCyleTest();

            UpdatebtnStatus();
        }

        private void btnStop_Click(object sender, EventArgs e)
        {
            CycleTestMng.Instance.StopCycleTest();

            UpdatebtnStatus();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (SaveInfo())
            {
                MessageBox.Show("save sucessed!");
            }
            else
            {
                MessageBox.Show("save failed!");
            }
        }

        private bool SaveInfo()
        {
            CycleTestInfo cycInfo = new CycleTestInfo();

            cycInfo.strCarrierID = this.comboBox1.SelectedValue.ToString();
            cycInfo.bUsePort = this.checkBox1.Checked;
            if (this.checkBox1.Checked)
            {
                cycInfo.strPortName = this.radioButton1.Checked ? this.radioButton1.Text : this.radioButton2.Text;
            }

            cycInfo.iCycleTimes = int.Parse(this.numericUpDown1.Value.ToString());

            cycInfo.Locations = getListSource(this.listBox2);

            //GlobalData.Instance.gbCycleTestInfo = cycInfo;
            return CycleTestMng.Instance.SaveCycleTestInfo(cycInfo);
        }

        private List<string> getListSource(ListBox listBox)
        {
            List<string> strList = new List<string>();
            foreach (var item in listBox.Items)
            {
                strList.Add(item.ToString());
            }
            return strList;
        }
        private void btnMoveRight_Click(object sender, EventArgs e)
        {
            MoveRight();
        }

        private void btnMoveLeft_Click(object sender, EventArgs e)
        {
            MoveLeft();
        }

        private void MoveRight()
        {
            var item = this.listBox1.SelectedItem;
            if (item != null)
            {
                this.listBox2.Items.Add(item);
                this.listBox1.Items.Remove(item);
            }
        }

        private void MoveLeft()
        {
            var item = this.listBox2.SelectedItem;
            if (item != null)
            {
                this.listBox1.Items.Add(item);
                this.listBox2.Items.Remove(item);
            }
        }

        private void listBox1_DoubleClick(object sender, EventArgs e)
        {
            MoveRight();
        }

        private void listBox2_DoubleClick(object sender, EventArgs e)
        {
            MoveLeft();
        }

        private void UpdatebtnStatus()
        {
            if (CycleTestMng.Instance.IsCycleStart())
            {
                this.btnSave.Enabled = false;
                this.btnStart.Enabled = false;
                this.btnStop.Enabled = true;

                //this.comboBox1.Enabled = false;
                //this.numericUpDown1.Enabled = false;
                //this.listBox1.Enabled = false;
                //this.listBox2.Enabled = false;
                //this.btnMoveLeft.Enabled = false;
                //this.btnMoveRight.Enabled = false;
                //this.checkBox1.Enabled = false;

            }
            else
            {
                this.btnSave.Enabled = true;
                this.btnStart.Enabled = true;
                this.btnStop.Enabled = false;

                this.comboBox1.Enabled = true;
                this.numericUpDown1.Enabled = true;
                this.listBox1.Enabled = true;
                this.listBox2.Enabled = true;
                this.btnMoveLeft.Enabled = true;
                this.btnMoveRight.Enabled = true;
            }
        }

        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            this.radioButton1.Enabled = !this.radioButton1.Enabled;
            this.radioButton2.Enabled = !this.radioButton2.Enabled;
        }
    }
}
