﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntParaalarm and List
    [Serializable]
    [Description("变量报警表")]
    [LinqToDB.Mapping.Table("Tnt_ParaAlarm")]
    public partial class TntParaalarm : GEntity<TntParaalarm>, ITimestamp
    {
        #region Contructor(s)

        private TntParaalarm()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CParagatherId = RegisterProperty<String>(p => p.CParagatherId);
        private static readonly PropertyInfo<String> pty_CParagatherName = RegisterProperty<String>(p => p.CParagatherName);
        private static readonly PropertyInfo<String> pty_CAlarmCode = RegisterProperty<String>(p => p.CAlarmCode);
        private static readonly PropertyInfo<String> pty_CAlarmName = RegisterProperty<String>(p => p.CAlarmName);
        private static readonly PropertyInfo<String> pty_CAlarmevent = RegisterProperty<String>(p => p.CAlarmevent);
        private static readonly PropertyInfo<String> pty_CAlarmlevel = RegisterProperty<String>(p => p.CAlarmlevel);
        private static readonly PropertyInfo<Int64?> pty_CDelay = RegisterProperty<Int64?>(p => p.CDelay);
        private static readonly PropertyInfo<String> pty_CDescription = RegisterProperty<String>(p => p.CDescription);
        private static readonly PropertyInfo<String> pty_CAlarmType = RegisterProperty<String>(p => p.CAlarmType);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 变量主键
        /// </summary>
        [Description("变量主键")]
        [LinqToDB.Mapping.Column("c_ParaGatherId")]
        public String CParagatherId
        {
            get { return GetProperty(pty_CParagatherId); }
            set { SetProperty(pty_CParagatherId, value); }
        }
        /// <summary>
        /// 变量名称
        /// </summary>
        [Description("变量名称")]
        [LinqToDB.Mapping.Column("c_ParaGatherName")]
        public String CParagatherName
        {
            get { return GetProperty(pty_CParagatherName); }
            set { SetProperty(pty_CParagatherName, value); }
        }
        /// <summary>
        /// 报警编码
        /// </summary>
        [Description("报警编码")]
        [LinqToDB.Mapping.Column("c_AlarmCode")]
        public String CAlarmCode
        {
            get { return GetProperty(pty_CAlarmCode); }
            set { SetProperty(pty_CAlarmCode, value); }
        }
        /// <summary>
        /// 报警类型
        /// </summary>
        [Description("报警类型")]
        [LinqToDB.Mapping.Column("c_AlarmType")]
        public String CAlarmType
        {
            get { return GetProperty(pty_CAlarmType); }
            set { SetProperty(pty_CAlarmType, value); }
        }
        /// <summary>
        /// 触发条件
        /// </summary>
        [Description("触发条件")]
        [LinqToDB.Mapping.Column("c_AlarmEvent")]
        public String CAlarmevent
        {
            get { return GetProperty(pty_CAlarmevent); }
            set { SetProperty(pty_CAlarmevent, value); }
        }
        /// <summary>
        /// 报警级别
        /// </summary>
        [Description("报警级别")]
        [LinqToDB.Mapping.Column("c_AlarmLevel")]
        public String CAlarmlevel
        {
            get { return GetProperty(pty_CAlarmlevel); }
            set { SetProperty(pty_CAlarmlevel, value); }
        }
        /// <summary>
        /// 延时时间
        /// </summary>
        [Description("延时时间")]
        [LinqToDB.Mapping.Column("c_Delay")]
        public Int64? CDelay
        {
            get { return GetProperty(pty_CDelay); }
            set { SetProperty(pty_CDelay, value); }
        }
        /// <summary>
        /// 报警描述
        /// </summary>
        [Description("报警描述")]
        [LinqToDB.Mapping.Column("c_Description")]
        public String CDescription
        {
            get { return GetProperty(pty_CDescription); }
            set { SetProperty(pty_CDescription, value); }
        }
        /// <summary>
        /// 是否有效
        /// </summary>
        [Description("是否有效")]
        [LinqToDB.Mapping.Column("c_AlarmName")]
        public String CAlarmName
        {
            get { return GetProperty(pty_CAlarmName); }
            set { SetProperty(pty_CAlarmName, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 36, "主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CParagatherId, "变量主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParagatherId, 36, "变量主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CParagatherName, 50, "变量名称不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CAlarmCode, 20, "报警编码不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CAlarmType, 2, "报警类型不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CAlarmevent, 200, "触发条件不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CAlarmlevel, 2, "报警级别不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDescription, 400, "报警描述不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CAlarmName, 20, "是否有效不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntParaalarmList : GEntityList<TntParaalarmList, TntParaalarm>
    {
        private TntParaalarmList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
