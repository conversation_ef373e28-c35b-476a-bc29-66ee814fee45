﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbClasses and List
    [Serializable]
    [Description("班次设定表")]
    [LinqToDB.Mapping.Table("TB_CLASSES")]
    public partial class TbClasses : GEntity<TbClasses>, ITimestamp
    {
        #region Contructor(s)

        private TbClasses()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPkId = RegisterProperty<String>(p => p.CPkId);
        private static readonly PropertyInfo<String> pty_CClassesId = RegisterProperty<String>(p => p.CClassesId);
        private static readonly PropertyInfo<String> pty_CClassesName = RegisterProperty<String>(p => p.CClassesName);
        private static readonly PropertyInfo<String> pty_CClassesDes = RegisterProperty<String>(p => p.CClassesDes);
        private static readonly PropertyInfo<String> pty_CFactoryId = RegisterProperty<String>(p => p.CFactoryId);
        private static readonly PropertyInfo<String> pty_CFactoryDes = RegisterProperty<String>(p => p.CFactoryDes);
        private static readonly PropertyInfo<String> pty_COrganizationId = RegisterProperty<String>(p => p.COrganizationId);
        private static readonly PropertyInfo<String> pty_COrganizationDes = RegisterProperty<String>(p => p.COrganizationDes);
        private static readonly PropertyInfo<String> pty_CClassesStatus = RegisterProperty<String>(p => p.CClassesStatus);
        private static readonly PropertyInfo<String> pty_CDataStatus = RegisterProperty<String>(p => p.CDataStatus);
        private static readonly PropertyInfo<String> pty_CClassesstart = RegisterProperty<String>(p => p.CClassesstart);
        private static readonly PropertyInfo<String> pty_CClassesend = RegisterProperty<String>(p => p.CClassesend);
        private static readonly PropertyInfo<String> pty_CWorktime = RegisterProperty<String>(p => p.CWorktime);
        private static readonly PropertyInfo<String> pty_CWorkhour = RegisterProperty<String>(p => p.CWorkhour);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 系统ID
        /// </summary>
        [Description("系统ID")]
        [LinqToDB.Mapping.Column("C_PKID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPkId
        {
            get { return GetProperty(pty_CPkId); }
            set { SetProperty(pty_CPkId, value); }
        }
        /// <summary>
        /// 班次编码
        /// </summary>
        [Description("班次编码")]
        [LinqToDB.Mapping.Column("C_CLASSESID")]
        public String CClassesId
        {
            get { return GetProperty(pty_CClassesId); }
            set { SetProperty(pty_CClassesId, value); }
        }
        /// <summary>
        /// 班次名称
        /// </summary>
        [Description("班次名称")]
        [LinqToDB.Mapping.Column("C_CLASSESNAME")]
        public String CClassesName
        {
            get { return GetProperty(pty_CClassesName); }
            set { SetProperty(pty_CClassesName, value); }
        }
        /// <summary>
        /// 班次描述
        /// </summary>
        [Description("班次描述")]
        [LinqToDB.Mapping.Column("C_CLASSESDES")]
        public String CClassesDes
        {
            get { return GetProperty(pty_CClassesDes); }
            set { SetProperty(pty_CClassesDes, value); }
        }
        /// <summary>
        /// 创建组织ID
        /// </summary>
        [Description("创建组织ID")]
        [LinqToDB.Mapping.Column("C_FACTORYID")]
        public String CFactoryId
        {
            get { return GetProperty(pty_CFactoryId); }
            set { SetProperty(pty_CFactoryId, value); }
        }
        /// <summary>
        /// 创建组织名称
        /// </summary>
        [Description("创建组织名称")]
        [LinqToDB.Mapping.Column("C_FACTORYDES")]
        public String CFactoryDes
        {
            get { return GetProperty(pty_CFactoryDes); }
            set { SetProperty(pty_CFactoryDes, value); }
        }
        /// <summary>
        /// 使用组织ID
        /// </summary>
        [Description("使用组织ID")]
        [LinqToDB.Mapping.Column("C_ORGANIZATIONID")]
        public String COrganizationId
        {
            get { return GetProperty(pty_COrganizationId); }
            set { SetProperty(pty_COrganizationId, value); }
        }
        /// <summary>
        /// 使用组织名称
        /// </summary>
        [Description("使用组织名称")]
        [LinqToDB.Mapping.Column("C_ORGANIZATIONDES")]
        public String COrganizationDes
        {
            get { return GetProperty(pty_COrganizationDes); }
            set { SetProperty(pty_COrganizationDes, value); }
        }
        /// <summary>
        /// 禁用状态
        /// </summary>
        [Description("禁用状态")]
        [LinqToDB.Mapping.Column("C_CLASSES_STATUS")]
        public String CClassesStatus
        {
            get { return GetProperty(pty_CClassesStatus); }
            set { SetProperty(pty_CClassesStatus, value); }
        }
        /// <summary>
        /// 数据状态
        /// </summary>
        [Description("数据状态")]
        [LinqToDB.Mapping.Column("C_DATA_STATUS")]
        public String CDataStatus
        {
            get { return GetProperty(pty_CDataStatus); }
            set { SetProperty(pty_CDataStatus, value); }
        }
        /// <summary>
        /// 班次开始时间
        /// </summary>
        [Description("班次开始时间")]
        [LinqToDB.Mapping.Column("C_CLASSESSTART")]
        public String CClassesstart
        {
            get { return GetProperty(pty_CClassesstart); }
            set { SetProperty(pty_CClassesstart, value); }
        }
        /// <summary>
        /// 班次结束时间
        /// </summary>
        [Description("班次结束时间")]
        [LinqToDB.Mapping.Column("C_CLASSESEND")]
        public String CClassesend
        {
            get { return GetProperty(pty_CClassesend); }
            set { SetProperty(pty_CClassesend, value); }
        }
        /// <summary>
        /// 工作时间
        /// </summary>
        [Description("工作时间")]
        [LinqToDB.Mapping.Column("C_WORKTIME")]
        public String CWorktime
        {
            get { return GetProperty(pty_CWorktime); }
            set { SetProperty(pty_CWorktime, value); }
        }
        /// <summary>
        /// 工作小时数
        /// </summary>
        [Description("工作小时数")]
        [LinqToDB.Mapping.Column("C_WORKHOUR")]
        public String CWorkhour
        {
            get { return GetProperty(pty_CWorkhour); }
            set { SetProperty(pty_CWorkhour, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPkId, "系统ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPkId, 40, "系统ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CClassesId, 40, "班次编码不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CClassesName, 160, "班次名称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CClassesDes, 400, "班次描述不能超过400个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryId, 20, "创建组织ID不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryDes, 200, "创建组织名称不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_COrganizationId, 20, "使用组织ID不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_COrganizationDes, 200, "使用组织名称不能超过200个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CClassesStatus, 2, "禁用状态不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDataStatus, 2, "数据状态不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CClassesstart, 76, "班次开始时间不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CClassesend, 76, "班次结束时间不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorktime, 10, "工作时间不能超过10个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWorkhour, 10, "工作小时数不能超过10个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPkId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbClassesList : GEntityList<TbClassesList, TbClasses>
    {
        private TbClassesList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
