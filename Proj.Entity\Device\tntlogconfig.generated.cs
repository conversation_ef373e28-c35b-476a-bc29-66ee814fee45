﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntLogconfig and List
    [Serializable]
    [Description("日志配置表")]
    [LinqToDB.Mapping.Table("Tnt_LogConfig")]
    public partial class TntLogconfig : GEntity<TntLogconfig>, ITimestamp
    {
        #region Contructor(s)

        private TntLogconfig()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CLogId = RegisterProperty<String>(p => p.CLogId);
        private static readonly PropertyInfo<String> pty_CLogType = RegisterProperty<String>(p => p.CLogType);
        private static readonly PropertyInfo<String> pty_CIssavefile = RegisterProperty<String>(p => p.CIssavefile);
        private static readonly PropertyInfo<String> pty_CIssavedb = RegisterProperty<String>(p => p.CIssavedb);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion

        /// <summary>
        /// 日志id
        /// </summary>
        [Description("日志id")]
        [LinqToDB.Mapping.Column("c_LogId")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CLogId
        {
            get { return GetProperty(pty_CLogId); }
            set { SetProperty(pty_CLogId, value); }
        }
        /// <summary>
        /// 日志类型
        /// </summary>
        [Description("日志类型")]
        [LinqToDB.Mapping.Column("c_LogType")]
        public String CLogType
        {
            get { return GetProperty(pty_CLogType); }
            set { SetProperty(pty_CLogType, value); }
        }
        /// <summary>
        /// 存文件
        /// </summary>
        [Description("存文件")]
        [LinqToDB.Mapping.Column("c_IsSaveFile")]
        public String CIssavefile
        {
            get { return GetProperty(pty_CIssavefile); }
            set { SetProperty(pty_CIssavefile, value); }
        }
        /// <summary>
        /// 存数据库
        /// </summary>
        [Description("存数据库")]
        [LinqToDB.Mapping.Column("c_IsSaveDB")]
        public String CIssavedb
        {
            get { return GetProperty(pty_CIssavedb); }
            set { SetProperty(pty_CIssavedb, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CLogId, "日志id是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLogId, 36, "日志id不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLogType, 2, "日志类型不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CIssavefile, 2, "存文件不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CIssavedb, 2, "存数据库不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CLogId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntLogconfigList : GEntityList<TntLogconfigList, TntLogconfig>
    {
        private TntLogconfigList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
