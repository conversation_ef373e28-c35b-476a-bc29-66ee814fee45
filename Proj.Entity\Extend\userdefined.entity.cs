﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SS.Base;

namespace Proj.Entity
{
    #region OprationResult 操作结果返回消息
    /// <summary>
    /// 返回结果类
    /// </summary> 
    [Serializable]
    public class OprationResult
    {
        /// <summary>
        /// 0:成功  1:失败  
        /// </summary>
        public string strCode { get; set; }
        /// <summary>
        /// 错误消息
        /// </summary>
        public string strMessage { get; set; }
        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Successed { get { return strCode != "1"; } }

        public bool HasError
        {
            get { return !Successed; }
        }

    }
    /// <summary>
    /// 返回结果类
    /// </summary> 
    [Serializable]
    public class OprationResult<T> : OprationResult
    {
        public T Data { get; set; }
    }
    #endregion

    
}
