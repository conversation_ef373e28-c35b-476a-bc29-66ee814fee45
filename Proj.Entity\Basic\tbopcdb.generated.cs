﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbOpcdb and List
    [Serializable]
    [Description("数据采集点")]
    [LinqToDB.Mapping.Table("TB_OPCDB")]
    public partial class TbOpcdb : GEntity<TbOpcdb>, ITimestamp
    {
        #region Contructor(s)

        private TbOpcdb()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CDbId = RegisterProperty<String>(p => p.CDbId);
        private static readonly PropertyInfo<String> pty_CIp = RegisterProperty<String>(p => p.CIp);
        private static readonly PropertyInfo<String> pty_CName = RegisterProperty<String>(p => p.CName);
        private static readonly PropertyInfo<String> pty_CGroup = RegisterProperty<String>(p => p.CGroup);
        private static readonly PropertyInfo<String> pty_CDbcheck = RegisterProperty<String>(p => p.CDbcheck);
        private static readonly PropertyInfo<String> pty_CDbtask = RegisterProperty<String>(p => p.CDbtask);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 数据采集ID
        /// </summary>
        [Description("数据采集ID")]
        [LinqToDB.Mapping.Column("C_DBID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CDbId
        {
            get { return GetProperty(pty_CDbId); }
            set { SetProperty(pty_CDbId, value); }
        }
        /// <summary>
        /// 服务器地址IP
        /// </summary>
        [Description("服务器地址IP")]
        [LinqToDB.Mapping.Column("C_IP")]
        public String CIp
        {
            get { return GetProperty(pty_CIp); }
            set { SetProperty(pty_CIp, value); }
        }
        /// <summary>
        /// 服务器地址名称
        /// </summary>
        [Description("服务器地址名称")]
        [LinqToDB.Mapping.Column("C_NAME")]
        public String CName
        {
            get { return GetProperty(pty_CName); }
            set { SetProperty(pty_CName, value); }
        }
        /// <summary>
        /// 数据采集组
        /// </summary>
        [Description("数据采集组")]
        [LinqToDB.Mapping.Column("C_GROUP")]
        public String CGroup
        {
            get { return GetProperty(pty_CGroup); }
            set { SetProperty(pty_CGroup, value); }
        }
        /// <summary>
        /// 是否采集
        /// </summary>
        [Description("是否采集")]
        [LinqToDB.Mapping.Column("C_DBCHECK")]
        public String CDbcheck
        {
            get { return GetProperty(pty_CDbcheck); }
            set { SetProperty(pty_CDbcheck, value); }
        }
        /// <summary>
        /// 所属任务
        /// </summary>
        [Description("所属任务")]
        [LinqToDB.Mapping.Column("C_DBTASK")]
        public String CDbtask
        {
            get { return GetProperty(pty_CDbtask); }
            set { SetProperty(pty_CDbtask, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CDbId, "数据采集ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDbId, 40, "数据采集ID不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CIp, 50, "服务器地址IP不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CName, 100, "服务器地址名称不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CGroup, 100, "数据采集组不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDbcheck, 100, "是否采集不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CDbtask, 100, "所属任务不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 80, "扩展字段A不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 80, "扩展字段B不能超过80个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 80, "扩展字段C不能超过80个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CDbId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbOpcdbList : GEntityList<TbOpcdbList, TbOpcdb>
    {
        private TbOpcdbList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
