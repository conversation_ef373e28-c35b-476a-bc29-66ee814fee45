﻿using System;

using Proj.DataTypeDef;
using System.Collections.Generic;

namespace Proj.DevComm
{
    public class StockerDev
    {
        private static StockerDev m_Instanse;
        private static readonly object mSyncObject = new object();
        public static StockerDev Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new StockerDev();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public StockerDev() { }
        private string preSTKSet = "Global.h2sSTKSet";
        private string preSTKStatus = "Global.s2hSTKStatus";
        private string preFFUSet = "h2sFFUSet";

        public bool STKAlarmClear()
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".AlarmReset", 1);
            //记录Stocker日志：Is STKAlarmClear Succeeded：{bRes}
            return bRes;
        }
        public bool ResetSTKAlarmClear()
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".AlarmReset", 0);
            //记录Stocker日志：Is ResetSTKAlarmClear Succeeded：{bRes}
            return bRes;
        }

        public bool STKBuzzerDisable()
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".BuzzerSilence", 1);
            //记录Stocker日志：Is STKBuzzerDisable Succeeded：{bRes}
            return bRes;
        }

        public bool STKBuzzerEnable()
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".BuzzerSilence", 0);
            //记录Stocker日志：Is STKBuzzerEnable Succeeded：{bRes}
            return bRes;
        }
        

        public bool STKServoOn()
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".ServoOn", 1);
            //记录Stocker日志：Is STKServoOn Succeeded：{bRes}
            return bRes;
        }
        public bool STKServoOff()
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".ServoOn", 0);
            //记录Stocker日志：Is STKServoOff Succeeded：{bRes}
            return bRes;
        }

        public bool STKLightMode()
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".LightMode", 0);
            //记录Stocker日志：Is STKServoOff Succeeded：{bRes}
            return bRes;
        }

        public bool STKLightColor()
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".LightColor", 0);
            //记录Stocker日志：Is STKServoOff Succeeded：{bRes}
            return bRes;
        }

        public bool SetSTKLightTime(int seconds)
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".LightTime", seconds);
            //记录Stocker日志：Is STKServoOff Succeeded：{bRes}
            return bRes;
        }

        public bool SetSTKLightOn()
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".LightOn", 1);
            //记录Stocker日志：Is STKServoOff Succeeded：{bRes}
            return bRes;
        }

        public bool SetSTKLightOff()
        {
            bool bRes = PlcComm.Instance.WriteTagValue(preSTKSet + ".LightOff", 1);
            //记录Stocker日志：Is STKServoOff Succeeded：{bRes}
            return bRes;
        }

        public bool IsSTKReady()
        {
            bool bRes = false;
            object objReady = PlcComm.Instance.GetTagValue(preSTKStatus + ".Ready");
            try
            {
                bRes = Convert.ToBoolean(objReady);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsSTKReady Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public bool IsSTKAlarm()
        {
            bool bRes = false;
            object objAlarm = PlcComm.Instance.GetTagValue(preSTKStatus + ".Alarm");
            try
            {
                bRes = Convert.ToBoolean(objAlarm);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsSTKAlarm Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public bool IsSTKSafetyOK()
        {
            bool bRes = false;
            object objSafetyOK = PlcComm.Instance.GetTagValue(preSTKStatus + ".SafetyOK");
            try
            {
                bRes = Convert.ToBoolean(objSafetyOK);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsSTKSafetyOK Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public bool IsSTKBuzzerSilence()
        {
            bool bRes = false;
            object objBuzzerSilence = PlcComm.Instance.GetTagValue(preSTKStatus + ".BuzzerSilence");
            try
            {
                bRes = Convert.ToBoolean(objBuzzerSilence);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:IsSTKBuzzerSilence Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public STKLightMode GetSTKLightMode()
        {
            STKLightMode lightMode = DataTypeDef.STKLightMode.NoTiming;
            object objLightMode = PlcComm.Instance.GetTagValue(preSTKStatus + ".LightMode");
            try
            {
                lightMode = (STKLightMode)Convert.ToInt32(objLightMode);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetSTKLightMode Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return lightMode;
        }

        public STKLightColor GetSTKLightColor()
        {
            STKLightColor lightColor = DataTypeDef.STKLightColor.Yellow;
            object objLightColor = PlcComm.Instance.GetTagValue(preSTKStatus + ".LightColor");
            try
            {
                lightColor = (STKLightColor)Convert.ToInt32(objLightColor);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetSTKLightMode Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return lightColor;
        }

        public bool IsSTKLightOn()
        {
            bool bRes = false;
            object objLightOn = PlcComm.Instance.GetTagValue(preSTKStatus + ".LightOn");
            try
            {
                bRes = Convert.ToBoolean(objLightOn);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetSTKLightMode Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public int GetSTKLightSetTime()
        {
            int iSetTime = 0;
            object objLightSetTime = PlcComm.Instance.GetTagValue(preSTKStatus + ".LightSetTime");
            try
            {
                iSetTime = Convert.ToInt32(objLightSetTime);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetSTKLightMode Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return iSetTime;
        }

        public bool SetFFUSpeed(string strFFUID, int iSpeed)
        {
            bool bRes = false;
            try
            {
                bRes = PlcComm.Instance.WriteIOValue(preFFUSet + strFFUID + ".SpeedChange", true);
                if(bRes == false)
                {
                    return false;
                }
                bRes = PlcComm.Instance.WriteIOValue(preFFUSet + strFFUID + ".SpeedSet", iSpeed);
            }
            catch (Exception ex)
            {
                //记录程序异常日志:GetSTKLightMode Failed: {ex.Message}
                Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        /// <summary>
        /// 获取STK的报警ID
        /// </summary>
        /// <returns></returns>
        public List<int> GetSTKAlarms()
        {
            string alarmsTag = "s2hSTKAlarms";
            List<int> alarmIDs = new List<int>();
            object objAlarm = null;
            bool bAlarmed = false;
            for (int i = 0; i < 96; i++)
            {
                objAlarm = PlcComm.Instance.GetIOValue(alarmsTag + "[" + i + "]");
                try
                {
                    bAlarmed = Convert.ToBoolean(objAlarm);
                    if (bAlarmed)
                    {
                        alarmIDs.Add(1 + i);
                    }
                }
                catch (Exception ex)
                {
                    //记录程序异常日志:GetCraneAlarms Failed: {ex.Message}
                    Log.Logger.Instance.ExceptionLog("CraneDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                    return null;
                }
            }
            return alarmIDs;
        }

        public int GetCPSConverterErrorCode()
        {
            int iErrorCode = 0;
            object objErrorCode = PlcComm.Instance.GetIOValue("s2hConverter.ErrorCode_Conveter");
            try
            {
                iErrorCode = Convert.ToInt32(objErrorCode);
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return iErrorCode;
        }

        public int GetCPSRegulatorErrorCode()
        {
            int iErrorCode = 0;
            object objErrorCode = PlcComm.Instance.GetIOValue("s2hRegulatorECO.ErrorCode_Regulator");
            try
            {
                iErrorCode = Convert.ToInt32(objErrorCode);
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return iErrorCode;
        }

        public int GetCPSECOErrorCode()
        {
            int iErrorCode = 0;
            object objErrorCode = PlcComm.Instance.GetIOValue("s2hRegulatorECO.ErrorCode_ECO");
            try
            {
                iErrorCode = Convert.ToInt32(objErrorCode);
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("StockerDev.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return iErrorCode;
        }

        public bool SetMCSLinkType(bool bIsLink)
        {
            string strPath = "Global.h2sMCSLinkType";
            Log.Logger.Instance.EventLog("StockerDev" + "MCS连接状态写PLC" + "bIsLink=" + bIsLink.ToString());
            bool bRes = PlcComm.Instance.WriteTagValue(strPath, bIsLink);
           
            return bRes;
        }

        public bool SetSTKCIsFull(bool bIsFull)
        {
            string strPath = "Global.h2sShelfFull";
            Log.Logger.Instance.EventLog("StockerDev" + "满库灯状态写PLC" + "bLastShelfFullFlag=" + bIsFull.ToString());
            bool bRes = PlcComm.Instance.WriteTagValue(strPath, bIsFull);

            return bRes;
        }
    }
}
