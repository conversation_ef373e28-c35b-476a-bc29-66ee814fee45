using Proj.Log;

namespace Proj.WCF
{
    /// <summary>
    /// WCF服务兼容性类，提供与原WCF服务相同的静态接口
    /// 这个类是为了兼容现有代码中对 Proj.WCF.WCFService.Instance 的调用
    /// </summary>
    public class WCFService
    {
        private static WCFService? _instance;
        private static readonly object _lock = new();

        /// <summary>
        /// 单例实例
        /// </summary>
        public static WCFService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new WCFService();
                    }
                }
                return _instance;
            }
        }

        private WCFService()
        {
        }

        /// <summary>
        /// 服务器向客户端发送消息（兼容原WCF接口）
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>发送是否成功</returns>
        public bool ServerSendMessage(string function, Dictionary<string, object> parameters)
        {
            try
            {
                // 委托给 WCFCompatibilityService 处理
                return Proj.Service.Services.WCFCompatibilityService.Instance.ServerSendMessage(function, parameters);
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"WCFService.ServerSendMessage error: {ex.Message}, Stack: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 服务器向客户端发送消息（异步版本）
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> ServerSendMessageAsync(string function, Dictionary<string, object> parameters)
        {
            try
            {
                // 委托给 WCFCompatibilityService 处理
                return await Proj.Service.Services.WCFCompatibilityService.Instance.ServerSendMessageAsync(function, parameters);
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"WCFService.ServerSendMessageAsync error: {ex.Message}, Stack: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 获取连接的客户端数量
        /// </summary>
        /// <returns>客户端数量</returns>
        public int GetConnectedClientCount()
        {
            return Proj.Service.Services.WCFCompatibilityService.Instance.GetConnectedClientCount();
        }

        /// <summary>
        /// 检查是否有客户端连接
        /// </summary>
        /// <returns>是否有客户端连接</returns>
        public bool HasConnectedClients()
        {
            return Proj.Service.Services.WCFCompatibilityService.Instance.HasConnectedClients();
        }
    }
}
