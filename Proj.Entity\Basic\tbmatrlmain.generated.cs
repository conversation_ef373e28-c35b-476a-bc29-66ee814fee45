﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TbMatrlMain and List
    [Serializable]
    [Description("物料主数据表")]
    [LinqToDB.Mapping.Table("TB_MATRL_MAIN")]
    public partial class TbMatrlMain : GEntity<TbMatrlMain>, ITimestamp
    {
        #region Contructor(s)

        private TbMatrlMain()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CMaterialId = RegisterProperty<String>(p => p.CMaterialId);
        private static readonly PropertyInfo<String> pty_CMaterialDes = RegisterProperty<String>(p => p.CMaterialDes);
        private static readonly PropertyInfo<String> pty_CFactoryId = RegisterProperty<String>(p => p.CFactoryId);
        private static readonly PropertyInfo<String> pty_CMaterialgroup = RegisterProperty<String>(p => p.CMaterialgroup);
        private static readonly PropertyInfo<String> pty_CMaterialgroupName = RegisterProperty<String>(p => p.CMaterialgroupName);
        private static readonly PropertyInfo<String> pty_CGuige = RegisterProperty<String>(p => p.CGuige);
        private static readonly PropertyInfo<String> pty_CMeasurementunits = RegisterProperty<String>(p => p.CMeasurementunits);
        private static readonly PropertyInfo<String> pty_CManageway = RegisterProperty<String>(p => p.CManageway);
        private static readonly PropertyInfo<String> pty_CValidstate = RegisterProperty<String>(p => p.CValidstate);
        private static readonly PropertyInfo<String> pty_CKeyflag = RegisterProperty<String>(p => p.CKeyflag);
        private static readonly PropertyInfo<String> pty_CAttribute = RegisterProperty<String>(p => p.CAttribute);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        private static readonly PropertyInfo<String> pty_CModel = RegisterProperty<String>(p => p.CModel);
        private static readonly PropertyInfo<String> pty_CManufacturer = RegisterProperty<String>(p => p.CManufacturer);
        private static readonly PropertyInfo<String> pty_CHarnessflag = RegisterProperty<String>(p => p.CHarnessflag);
        private static readonly PropertyInfo<String> pty_CMatType = RegisterProperty<String>(p => p.CMatType);
        private static readonly PropertyInfo<String> pty_CCommonType = RegisterProperty<String>(p => p.CCommonType);
        private static readonly PropertyInfo<String> pty_CCommonlevel = RegisterProperty<String>(p => p.CCommonlevel);
        private static readonly PropertyInfo<String> pty_CBusinessType = RegisterProperty<String>(p => p.CBusinessType);
        private static readonly PropertyInfo<Double?> pty_NGrossweight = RegisterProperty<Double?>(p => p.NGrossweight);
        private static readonly PropertyInfo<Double?> pty_NTareweight = RegisterProperty<Double?>(p => p.NTareweight);
        private static readonly PropertyInfo<String> pty_CWeightunit = RegisterProperty<String>(p => p.CWeightunit);
        private static readonly PropertyInfo<Double?> pty_NLength = RegisterProperty<Double?>(p => p.NLength);
        private static readonly PropertyInfo<Double?> pty_NWide = RegisterProperty<Double?>(p => p.NWide);
        private static readonly PropertyInfo<Double?> pty_NHigh = RegisterProperty<Double?>(p => p.NHigh);
        private static readonly PropertyInfo<Double?> pty_NVolume = RegisterProperty<Double?>(p => p.NVolume);
        private static readonly PropertyInfo<String> pty_CSizeunit = RegisterProperty<String>(p => p.CSizeunit);
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        #endregion

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        [LinqToDB.Mapping.Column("C_MATERIALID")]
        public String CMaterialId
        {
            get { return GetProperty(pty_CMaterialId); }
            set { SetProperty(pty_CMaterialId, value); }
        }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        [LinqToDB.Mapping.Column("C_MATERIALDES")]
        public String CMaterialDes
        {
            get { return GetProperty(pty_CMaterialDes); }
            set { SetProperty(pty_CMaterialDes, value); }
        }
        /// <summary>
        /// 工厂编码
        /// </summary>
        [Description("工厂编码")]
        [LinqToDB.Mapping.Column("C_FACTORYID")]
        public String CFactoryId
        {
            get { return GetProperty(pty_CFactoryId); }
            set { SetProperty(pty_CFactoryId, value); }
        }
        /// <summary>
        /// 物料组
        /// </summary>
        [Description("物料组")]
        [LinqToDB.Mapping.Column("C_MATERIALGROUP")]
        public String CMaterialgroup
        {
            get { return GetProperty(pty_CMaterialgroup); }
            set { SetProperty(pty_CMaterialgroup, value); }
        }
        /// <summary>
        /// 物料组名称
        /// </summary>
        [Description("物料组名称")]
        [LinqToDB.Mapping.Column("C_MATERIALGROUPNAME")]
        public String CMaterialgroupName
        {
            get { return GetProperty(pty_CMaterialgroupName); }
            set { SetProperty(pty_CMaterialgroupName, value); }
        }
        /// <summary>
        /// 规格描述
        /// </summary>
        [Description("规格描述")]
        [LinqToDB.Mapping.Column("C_GUIGE")]
        public String CGuige
        {
            get { return GetProperty(pty_CGuige); }
            set { SetProperty(pty_CGuige, value); }
        }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        [LinqToDB.Mapping.Column("C_MEASUREMENTUNITS")]
        public String CMeasurementunits
        {
            get { return GetProperty(pty_CMeasurementunits); }
            set { SetProperty(pty_CMeasurementunits, value); }
        }
        /// <summary>
        /// 管理方式
        /// </summary>
        [Description("管理方式")]
        [LinqToDB.Mapping.Column("C_MANAGEWAY")]
        public String CManageway
        {
            get { return GetProperty(pty_CManageway); }
            set { SetProperty(pty_CManageway, value); }
        }
        /// <summary>
        /// 有效状态
        /// </summary>
        [Description("有效状态")]
        [LinqToDB.Mapping.Column("C_VALIDSTATE")]
        public String CValidstate
        {
            get { return GetProperty(pty_CValidstate); }
            set { SetProperty(pty_CValidstate, value); }
        }
        /// <summary>
        /// 关键件标识
        /// </summary>
        [Description("关键件标识")]
        [LinqToDB.Mapping.Column("C_KEYFLAG")]
        public String CKeyflag
        {
            get { return GetProperty(pty_CKeyflag); }
            set { SetProperty(pty_CKeyflag, value); }
        }
        /// <summary>
        /// 生产属性
        /// </summary>
        [Description("生产属性")]
        [LinqToDB.Mapping.Column("C_ATTRIBUTE")]
        public String CAttribute
        {
            get { return GetProperty(pty_CAttribute); }
            set { SetProperty(pty_CAttribute, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        /// <summary>
        /// 型号
        /// </summary>
        [Description("型号")]
        [LinqToDB.Mapping.Column("C_MODEL")]
        public String CModel
        {
            get { return GetProperty(pty_CModel); }
            set { SetProperty(pty_CModel, value); }
        }
        /// <summary>
        /// 制作商
        /// </summary>
        [Description("制作商")]
        [LinqToDB.Mapping.Column("C_MANUFACTURER")]
        public String CManufacturer
        {
            get { return GetProperty(pty_CManufacturer); }
            set { SetProperty(pty_CManufacturer, value); }
        }
        /// <summary>
        /// 齐配套标识
        /// </summary>
        [Description("齐配套标识")]
        [LinqToDB.Mapping.Column("C_HARNESSFLAG")]
        public String CHarnessflag
        {
            get { return GetProperty(pty_CHarnessflag); }
            set { SetProperty(pty_CHarnessflag, value); }
        }
        /// <summary>
        /// 物料类别
        /// </summary>
        [Description("物料类别")]
        [LinqToDB.Mapping.Column("C_MATTYPE")]
        public String CMatType
        {
            get { return GetProperty(pty_CMatType); }
            set { SetProperty(pty_CMatType, value); }
        }
        /// <summary>
        /// 通用类别
        /// </summary>
        [Description("通用类别")]
        [LinqToDB.Mapping.Column("C_COMMONTYPE")]
        public String CCommonType
        {
            get { return GetProperty(pty_CCommonType); }
            set { SetProperty(pty_CCommonType, value); }
        }
        /// <summary>
        /// 通用级别
        /// </summary>
        [Description("通用级别")]
        [LinqToDB.Mapping.Column("C_COMMONLEVEL")]
        public String CCommonlevel
        {
            get { return GetProperty(pty_CCommonlevel); }
            set { SetProperty(pty_CCommonlevel, value); }
        }
        /// <summary>
        /// 检验方式
        /// </summary>
        [Description("检验方式")]
        [LinqToDB.Mapping.Column("C_BUSINESSTYPE")]
        public String CBusinessType
        {
            get { return GetProperty(pty_CBusinessType); }
            set { SetProperty(pty_CBusinessType, value); }
        }
        /// <summary>
        /// 毛重
        /// </summary>
        [Description("毛重")]
        [LinqToDB.Mapping.Column("N_GROSSWEIGHT")]
        public Double? NGrossweight
        {
            get { return GetProperty(pty_NGrossweight); }
            set { SetProperty(pty_NGrossweight, value); }
        }
        /// <summary>
        /// 皮重
        /// </summary>
        [Description("皮重")]
        [LinqToDB.Mapping.Column("N_TAREWEIGHT")]
        public Double? NTareweight
        {
            get { return GetProperty(pty_NTareweight); }
            set { SetProperty(pty_NTareweight, value); }
        }
        /// <summary>
        /// 重量单位
        /// </summary>
        [Description("重量单位")]
        [LinqToDB.Mapping.Column("C_WEIGHTUNIT")]
        public String CWeightunit
        {
            get { return GetProperty(pty_CWeightunit); }
            set { SetProperty(pty_CWeightunit, value); }
        }
        /// <summary>
        /// 长
        /// </summary>
        [Description("长")]
        [LinqToDB.Mapping.Column("N_LENGTH")]
        public Double? NLength
        {
            get { return GetProperty(pty_NLength); }
            set { SetProperty(pty_NLength, value); }
        }
        /// <summary>
        /// 宽
        /// </summary>
        [Description("宽")]
        [LinqToDB.Mapping.Column("N_WIDE")]
        public Double? NWide
        {
            get { return GetProperty(pty_NWide); }
            set { SetProperty(pty_NWide, value); }
        }
        /// <summary>
        /// 高
        /// </summary>
        [Description("高")]
        [LinqToDB.Mapping.Column("N_HIGH")]
        public Double? NHigh
        {
            get { return GetProperty(pty_NHigh); }
            set { SetProperty(pty_NHigh, value); }
        }
        /// <summary>
        /// 体积
        /// </summary>
        [Description("体积")]
        [LinqToDB.Mapping.Column("N_VOLUME")]
        public Double? NVolume
        {
            get { return GetProperty(pty_NVolume); }
            set { SetProperty(pty_NVolume, value); }
        }
        /// <summary>
        /// 体积单位
        /// </summary>
        [Description("体积单位")]
        [LinqToDB.Mapping.Column("C_SIZEUNIT")]
        public String CSizeunit
        {
            get { return GetProperty(pty_CSizeunit); }
            set { SetProperty(pty_CSizeunit, value); }
        }
        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("C_PK")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        #endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CMaterialId, "物料编码是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialId, 36, "物料编码不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialDes, 500, "物料名称不能超过500个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFactoryId, 8, "工厂编码不能超过8个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialgroup, 40, "物料组不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMaterialgroupName, 160, "物料组名称不能超过160个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CGuige, 500, "规格描述不能超过500个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMeasurementunits, 6, "单位不能超过6个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CManageway, 2, "管理方式不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CValidstate, 1, "有效状态不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CKeyflag, 1, "关键件标识不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CAttribute, 1, "生产属性不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 40, "扩展字段A不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 40, "扩展字段B不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 40, "扩展字段C不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CModel, 40, "型号不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CManufacturer, 6, "制作商不能超过6个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CHarnessflag, 1, "齐配套标识不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CMatType, 1, "物料类别不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCommonType, 1, "通用类别不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CCommonlevel, 1, "通用级别不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CBusinessType, 1, "检验方式不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CWeightunit, 20, "重量单位不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSizeunit, 20, "体积单位不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 36, "主键不能超过36个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbMatrlMainList : GEntityList<TbMatrlMainList, TbMatrlMain>
    {
        private TbMatrlMainList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
