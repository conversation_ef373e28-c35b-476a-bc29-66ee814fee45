﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Robot;

namespace Proj.DevComm
{
    public class PLCTest
    {
        private static PLCTest m_Instanse;
        private static readonly object mSyncObject = new object();
        private string deviceName = "MelsecQE711";
        private PLCTest() { }
        public static PLCTest Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new PLCTest();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        private List<string> listDeviceNames = new List<string>();
        private int iHistoryDays = 0;

        /// <summary>
        /// 加载设备名称
        /// </summary>
        private void loadDeviceConfigInfo()
        {
            listDeviceNames.Clear();
            listDeviceNames.Add("MelsecQE711");
            iHistoryDays = 90;
        }

        /// <summary>
        /// 启动PLC通信
        /// </summary>
        /// <returns>是否成功</returns>
        public bool Start()
        {
            loadDeviceConfigInfo();
            int iRes = ProjectAPI.Run();
            if (0 != iRes)
            {
                //记录PLC日志:PLC通信启动失败  PLC Communication Start Failed
                return false;
            }
            int iDevCount = listDeviceNames.Count();
            if (iDevCount == 0)
            {
                //记录PLC日志:未获取到设备名称 Not Get Device Names
                ProjectAPI.Exit();
                return false;
            }
            try
            {
                IOAPI.StartIO();
            }
            catch (Exception ex)
            {
                //记录PLC日志:IO启动失败  Start IO Failed
                //记录程序异常日志：Start IO Failed {ex.Message}
                Log.Logger.Instance.ExceptionLog("PlcTest.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                ProjectAPI.Exit();
                return false;
            }
            for (int i = 0; i < iDevCount; i++)
            {
                iRes = IOAPI.StartDevice(listDeviceNames[i]);
                if (0 != iRes)
                {
                    //记录PLC日志:设备启动失败{设备名,结果码}  Device Start Failed{DevceName,ResultCode}
                    IOAPI.StopIO();
                    ProjectAPI.Exit();
                    return false;
                }
            }
            //记录PLC日志:PLC通信启动成功  PLC Communication Started

            return true;
        }

        /// <summary>
        /// 停止PLC通信
        /// </summary>
        public void Stop()
        {
            IOAPI.StopIO();
            ProjectAPI.Exit();
            //记录PLC日志:PLC通信停止  PLC Communication Stoped
        }


        /// <summary>
        /// 从变量数据库中（内存中）读取变量值
        /// </summary>
        /// <param name="paramName">变量名</param>
        /// <returns></returns>
        public object GetTagValue(string paramName)
        {
            object objValue = TagAPI.GetValue("Tag." + paramName);
            //记录PLC日志:获取Tag变量值{paramName=,value=}   Get Tag Variable Value {paramName=,value=}
            return objValue;
        }
        
        /// <summary>
        /// 写变量数据库中（内存中）的变量
        /// </summary>
        /// <param name="paramName">变量名</param>
        /// <param name="value">变量值</param>
        /// <returns></returns>
        public bool WriteTagValue(string paramName, object value)
        {
            bool bRes = false;
            bRes = TagAPI.SetValue("Tag." + paramName, value);
            //记录PLC日志:向Tag写值{ParamName=,value=,Result=} Write Value to PLC {DeviceName=,value=,Result=}
            return bRes;
        }
        /// <summary>
        /// 从设备变量（IO）中读取变量值
        /// </summary>
        /// <param name="tagName"></param>
        /// <returns></returns>
        public object GetIOValue(string tagName)
        {
            object objValue = IOAPI.GetValue(this.deviceName + "." + tagName);
            //object objValue = TagAPI.GetValue("Tag." + tagName);
            //记录PLC日志:获取PLC变量值{DeviceName=,value=}   Get PLC Variable Value {DeviceName=,value=}
            return objValue;
        }
        /// <summary>
        /// 写设备变量（IO）中的变量
        /// </summary>
        /// <param name="paramName"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public bool WriteIOValue(string paramName, object value)
        {
            bool bRes = false;
            //bRes = IOAPI.WriteTag(this.deviceName + "." + paramName, value);
            bRes = TagAPI.SetValue("Tag." + paramName, value);

            //记录PLC日志:向Ta写值{ParamName=,value=,Result=} Write Value to PLC {DeviceName=,value=,Result=}
            return bRes;
        }

    }
}
