﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Proj.CacheData\Proj.CacheData.csproj" />
    <ProjectReference Include="..\Proj.Common\Proj.Common.csproj" />
    <ProjectReference Include="..\Proj.DataTypeDef\Proj.DataTypeDef.csproj" />
    <ProjectReference Include="..\Proj.DevComm\Proj.DevComm.csproj" />
    <ProjectReference Include="..\Proj.Entity\Proj.Entity.csproj" />
    <ProjectReference Include="..\Proj.History\Proj.History.csproj" />
    <ProjectReference Include="..\Proj.HostComm\Proj.HostComm.csproj" />
    <ProjectReference Include="..\Proj.Service\Proj.Service.csproj" />
    <ProjectReference Include="..\Proj.UnitMng\Proj.UnitMng.csproj" />
  </ItemGroup>

</Project>
