﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.ServiceModel;

namespace Proj.WCF
{
    [ServiceContract(SessionMode = SessionMode.Required, CallbackContract = typeof(ICallBackServices))]
    public interface IWCFService
    {
        /// <summary>
        /// 客户端注册上线
        /// </summary>
        /// <param name="message"></param>
        [OperationContract(IsOneWay = true)]
        void Register();

        /// <summary>
        /// 客户端发送消息
        /// </summary>
        /// <param name="message">消息内容</param>
        [OperationContract(IsOneWay=false)]
        object ClientSendMessage(string strFunction, Dictionary<string, object> dicParameter);
    }

    public interface ICallBackServices
    {
        /// <summary>
        /// 服务端向客户端发送信息(异步)
        /// </summary>
        /// <param name="Message"></param>
        [OperationContract(IsOneWay = true)]
        void ServerSendMessage(string strFunction, Dictionary<string, object> dicParameter);
    }
}
