﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region Transfer20191026 and List
    [Serializable]
    [Description("TRANSFER_20191026")]
    [LinqToDB.Mapping.Table("TRANSFER_20191026")]
    public partial class Transfer20191026 : GEntity<Transfer20191026>
    {
        #region Contructor(s)

        private Transfer20191026()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<Int64> pty_CommandTime = RegisterProperty<Int64>(p => p.CommandTime);
        private static readonly PropertyInfo<String> pty_CommandId = RegisterProperty<String>(p => p.CommandId);
        private static readonly PropertyInfo<String> pty_CommandSrc = RegisterProperty<String>(p => p.CommandSrc);
        private static readonly PropertyInfo<String> pty_CommandPriority = RegisterProperty<String>(p => p.CommandPriority);
        private static readonly PropertyInfo<String> pty_CommandSrcLoc = RegisterProperty<String>(p => p.CommandSrcLoc);
        private static readonly PropertyInfo<String> pty_CommandDestLoc = RegisterProperty<String>(p => p.CommandDestLoc);
        private static readonly PropertyInfo<String> pty_CalculationPriority = RegisterProperty<String>(p => p.CalculationPriority);
        private static readonly PropertyInfo<String> pty_FirstCraneId = RegisterProperty<String>(p => p.FirstCraneId);
        private static readonly PropertyInfo<Int64> pty_FirstStartTime = RegisterProperty<Int64>(p => p.FirstStartTime);
        private static readonly PropertyInfo<Int64> pty_FirstEndTime = RegisterProperty<Int64>(p => p.FirstEndTime);
        private static readonly PropertyInfo<String> pty_SecondCraneId = RegisterProperty<String>(p => p.SecondCraneId);
        private static readonly PropertyInfo<Int64> pty_SecondStartTime = RegisterProperty<Int64>(p => p.SecondStartTime);
        private static readonly PropertyInfo<Int64> pty_SecondEndTime = RegisterProperty<Int64>(p => p.SecondEndTime);
        private static readonly PropertyInfo<String> pty_CommandState = RegisterProperty<String>(p => p.CommandState);
        private static readonly PropertyInfo<String> pty_DelayReason = RegisterProperty<String>(p => p.DelayReason);
        private static readonly PropertyInfo<String> pty_CommandRemark = RegisterProperty<String>(p => p.CommandRemark);
        #endregion

        /// <summary>
        /// command_time
        /// </summary>
        [Description("command_time")]
        [LinqToDB.Mapping.Column("command_time")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int64 CommandTime
        {
            get { return GetProperty(pty_CommandTime); }
            set { SetProperty(pty_CommandTime, value); }
        }
        /// <summary>
        /// command_id
        /// </summary>
        [Description("command_id")]
        [LinqToDB.Mapping.Column("command_id")]
        public String CommandId
        {
            get { return GetProperty(pty_CommandId); }
            set { SetProperty(pty_CommandId, value); }
        }
        /// <summary>
        /// command_src
        /// </summary>
        [Description("command_src")]
        [LinqToDB.Mapping.Column("command_src")]
        public String CommandSrc
        {
            get { return GetProperty(pty_CommandSrc); }
            set { SetProperty(pty_CommandSrc, value); }
        }
        /// <summary>
        /// command_priority
        /// </summary>
        [Description("command_priority")]
        [LinqToDB.Mapping.Column("command_priority")]
        public String CommandPriority
        {
            get { return GetProperty(pty_CommandPriority); }
            set { SetProperty(pty_CommandPriority, value); }
        }
        /// <summary>
        /// command_src_loc
        /// </summary>
        [Description("command_src_loc")]
        [LinqToDB.Mapping.Column("command_src_loc")]
        public String CommandSrcLoc
        {
            get { return GetProperty(pty_CommandSrcLoc); }
            set { SetProperty(pty_CommandSrcLoc, value); }
        }
        /// <summary>
        /// command_dest_loc
        /// </summary>
        [Description("command_dest_loc")]
        [LinqToDB.Mapping.Column("command_dest_loc")]
        public String CommandDestLoc
        {
            get { return GetProperty(pty_CommandDestLoc); }
            set { SetProperty(pty_CommandDestLoc, value); }
        }
        /// <summary>
        /// calculation_priority
        /// </summary>
        [Description("calculation_priority")]
        [LinqToDB.Mapping.Column("calculation_priority")]
        public String CalculationPriority
        {
            get { return GetProperty(pty_CalculationPriority); }
            set { SetProperty(pty_CalculationPriority, value); }
        }
        /// <summary>
        /// first_crane_id
        /// </summary>
        [Description("first_crane_id")]
        [LinqToDB.Mapping.Column("first_crane_id")]
        public String FirstCraneId
        {
            get { return GetProperty(pty_FirstCraneId); }
            set { SetProperty(pty_FirstCraneId, value); }
        }
        /// <summary>
        /// first_start_time
        /// </summary>
        [Description("first_start_time")]
        [LinqToDB.Mapping.Column("first_start_time")]
        public Int64 FirstStartTime
        {
            get { return GetProperty(pty_FirstStartTime); }
            set { SetProperty(pty_FirstStartTime, value); }
        }
        /// <summary>
        /// first_end_time
        /// </summary>
        [Description("first_end_time")]
        [LinqToDB.Mapping.Column("first_end_time")]
        public Int64 FirstEndTime
        {
            get { return GetProperty(pty_FirstEndTime); }
            set { SetProperty(pty_FirstEndTime, value); }
        }
        /// <summary>
        /// second_crane_id
        /// </summary>
        [Description("second_crane_id")]
        [LinqToDB.Mapping.Column("second_crane_id")]
        public String SecondCraneId
        {
            get { return GetProperty(pty_SecondCraneId); }
            set { SetProperty(pty_SecondCraneId, value); }
        }
        /// <summary>
        /// second_start_time
        /// </summary>
        [Description("second_start_time")]
        [LinqToDB.Mapping.Column("second_start_time")]
        public Int64 SecondStartTime
        {
            get { return GetProperty(pty_SecondStartTime); }
            set { SetProperty(pty_SecondStartTime, value); }
        }
        /// <summary>
        /// second_end_time
        /// </summary>
        [Description("second_end_time")]
        [LinqToDB.Mapping.Column("second_end_time")]
        public Int64 SecondEndTime
        {
            get { return GetProperty(pty_SecondEndTime); }
            set { SetProperty(pty_SecondEndTime, value); }
        }
        /// <summary>
        /// command_state
        /// </summary>
        [Description("command_state")]
        [LinqToDB.Mapping.Column("command_state")]
        public String CommandState
        {
            get { return GetProperty(pty_CommandState); }
            set { SetProperty(pty_CommandState, value); }
        }
        /// <summary>
        /// delay_reason
        /// </summary>
        [Description("delay_reason")]
        [LinqToDB.Mapping.Column("delay_reason")]
        public String DelayReason
        {
            get { return GetProperty(pty_DelayReason); }
            set { SetProperty(pty_DelayReason, value); }
        }
        /// <summary>
        /// command_remark
        /// </summary>
        [Description("command_remark")]
        [LinqToDB.Mapping.Column("command_remark")]
        public String CommandRemark
        {
            get { return GetProperty(pty_CommandRemark); }
            set { SetProperty(pty_CommandRemark, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CommandTime, "command_time是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CommandId, 64, "command_id不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CommandSrc, 50, "command_src不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CommandPriority, 50, "command_priority不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CommandSrcLoc, 64, "command_src_loc不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CommandDestLoc, 64, "command_dest_loc不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CalculationPriority, 50, "calculation_priority不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_FirstCraneId, 64, "first_crane_id不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_SecondCraneId, 64, "second_crane_id不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CommandState, 32, "command_state不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_DelayReason, 255, "delay_reason不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CommandRemark, 255, "command_remark不能超过255个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CommandTime.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class Transfer20191026List : GEntityList<Transfer20191026List, Transfer20191026>
    {
        private Transfer20191026List() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
