﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SS.Base;
using SS.CslaBase;
using SS.BLL.Base;
using Csla.Transaction;
using Proj.Entity;
using System.Data;
using Csla.Repository;
namespace Proj.Business
{
    [Serializable]
    public class GetFactoryService : SS.CslaBase.ServiceV2<GetFactoryService>
    {
        /// <summary>
        /// 按编码获取工厂信息
        /// </summary>
        /// <param name="strFactoryID"></param>
        /// <returns></returns>
        public virtual TbFactoryList GetFactory(string strFactoryID)
        {
            //定义事务
            using (var trans = new Csla.Transaction.TransactionScope())
            {

                TbFactory tf = TbFactory.GetById(strFactoryID);
                if (tf != null)
                {
                    tf.CFactoryName += "1";
                    tf.Save();
                }
                //事物提交
                trans.Complete();


            }
            TbFactoryList lstFactory = TbFactoryList.GetByLambda(x => x.CFactoryId == strFactoryID);
            return lstFactory;
        }


        /// <summary>
        /// 通过SQL查询工厂信息
        /// </summary>
        /// <param name="strFactoryID"></param>
        /// <returns></returns>
        public virtual List<TbFactory> GetFactoryBySql(string strFactoryID)
        {
            var sql = @"select t.c_factory_id CFactoryId,t.c_factory_name CFactoryName,t.c_timestamp CTimestamp from tb_factory t where t.c_factory_id='{0}'";

            List <TbFactory>  table = SqlService.ExecuteList<TbFactory>(string.Format(sql, strFactoryID)).ToList();

            return table;
        }


        /// <summary>
        /// 通过存储过程查询工厂信息
        /// </summary>
        /// <param name="strFactoryID"></param>
        /// <returns></returns>
        public virtual IEnumerable<TbFactory> GetFactoryByProc(string strFactoryID)
        {
            var sql = @"PROC_GetFactory ";
            List<Csla.Repository.SqlParamInfo> Params=new List<Csla.Repository.SqlParamInfo>();

            SqlParamInfo para=new SqlParamInfo();
            para.DbType=Csla.Repository.GDbType.NVarchar2;
            para.Direction=GParameterDirection.Input;
            para.ParameterName=@"P_FACTORYID";
            para.Size=20;
            para.Value = strFactoryID;
            Params.Add(para);

            SqlParamInfo para1=new SqlParamInfo();
            para1.DbType=Csla.Repository.GDbType.RefCursor;
            para1.Direction=GParameterDirection.Output;
            para1.ParameterName=@"P_CUR";
            Params.Add(para1);
           
            IEnumerable <TbFactory> table = SqlService.ExecuteProcQuery(sql, Params,null).ToList<TbFactory>();

            return table;
        }
    }
}
