﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Proj.Common;
using Proj.DataTypeDef;
using Proj.DevComm;
using Proj.Alarm;
using Proj.HostComm;
using SecsLite.Gem;
using Proj.History;
using System.Threading;
using Proj.CacheData;
using Proj.Entity;
using Proj.Log;

namespace Proj.UnitMng
{
    public class PortObj
    {
        private ThreadBaseModel taskExecuteThread = null; //
        private ThreadBaseModel taskExecuteThreadToPortLP = null; //
        private ThreadBaseModel taskExecutePrintPortStage = null;
        private PortTypeChgCommand chgCommand = null; //Port出入库切换指令

        private int iPortNo = 0;
       // private int iFailedCount = 0; //出入库切换失败次数
        private string strPortName = "";
        private string strPortLocation = "";
        private string strZoneName = "";
        private PortUnitType portUnitTypeSetting = PortUnitType.Bidirection;
        private PortUnitType lastPortUnitType = PortUnitType.None;  //上一次读取的Port出入库模式
        private bool isMPort = false;
        private bool isHaveIDR = false;
        private int iPriority = 20;
        private int iEqNum = 0;    //EqNum值为0，代表此Port为IOPort
        private string portTag = "";
        private IOPortWorkingStatus lastPortWorkingState = IOPortWorkingStatus.Init;
        private IOPortWorkingStatus currPortWorkingState = IOPortWorkingStatus.Ready;
        private IOPortWorkingStatus lastBufferWorkingState = IOPortWorkingStatus.Init;
        private IOPortWorkingStatus currBufferWorkingState = IOPortWorkingStatus.Ready;
        private EqReqStatus lastEqReqStatus = EqReqStatus.ReqOff;
        private EqPresenceStatus lastEqPresenceStatus = EqPresenceStatus.NoPresence;
        private int bThreadFuncCount = 0;
        private PortTransferState portState = PortTransferState.None;

        //PIO存储上一次变量值
        string portPath;
        PortPIOStatus m_stLastPortPIOStatus;


       // private bool bAlarmed = false;
        private string dupCarrierRealLoc = "";

        public InPortCSTIDTime stInPortCSTIDTime;
        private string strSaveCarrierID = "";

        string strFailedCSTID = "";

        private int iCheckListCount = 0;

        private bool bIsClean = false;

        public string Name
        {
            get
            {
                return strPortName;
            }
        }
        public string Location
        {
            get
            {
                return strPortLocation;
            }
        }
        public string TagName
        {
            get
            {
                return portTag;
            }
        }

        public IOPortWorkingStatus PortWorkingState
        {
            get
            {
                return currPortWorkingState;
            }
        }

        public PortUnitType PortUnitType
        {
            get
            {
                return portUnitTypeSetting;
            }
        }

        public PortObj(int no, string name, string zoneName, PortUnitType unitType, bool isMPort, bool isHaveIDR, int priority, int EqNum, string tagName)
        {
            iPortNo = no;
            strPortName = name;
            strZoneName = zoneName;
            portUnitTypeSetting = unitType;
            this.isMPort = isMPort;
            this.isHaveIDR = isHaveIDR;
            iPriority = priority;
            iEqNum = EqNum;
            portTag = tagName;

            stInPortCSTIDTime.timeInPut = DateTime.Now;
            stInPortCSTIDTime.bInputFlag = false;


            portPath = null;
            if (1 == EqNum)
            {
                portPath = "s2hEQPortStatus"; 
            }
            else if (portTag.Contains("CV"))
            {
                portPath = "s2hCVPortStatus";
            }
            else if (portTag.Contains("MGV"))
            {
                portPath = "s2hMGVPortStatus";
            }

            if (!string.IsNullOrEmpty(portPath))
            {
                m_stLastPortPIOStatus = PortDev.Instance.GetPortAllPIO(portPath, portTag);
            }
        }

        public void AddPortTypeChgTask(string portID, PortUnitType unitType)
        {
            PortDev.Instance.IOPortServoOn(portTag);

            chgCommand = new PortTypeChgCommand();
            chgCommand.strPortID = portID;
            chgCommand.unitType = unitType;
        }

        public void Start()
        {
            try
            { 
                string strRes = "";

                taskExecuteThread = new ThreadBaseModel(20 + iPortNo, "TaskExecuteThreadFunc "); //Port的线程ID从20开始
                taskExecuteThread.LoopInterval = 300;
                bThreadFuncCount = 0;
                taskExecuteThread.SetThreadRoutine(taskExecuteThreadFunc);
                taskExecuteThread.TaskInit(ref strRes);
                taskExecuteThread.Start(ref strRes);

                taskExecuteThreadToPortLP = new ThreadBaseModel(30 + iPortNo, "TaskExecuteThreadFuncToPortLP ");
                taskExecuteThreadToPortLP.LoopInterval = 300;
                taskExecuteThreadToPortLP.SetThreadRoutine(TaskExecuteThreadFuncToPortLP);
                taskExecuteThreadToPortLP.TaskInit(ref strRes);
                taskExecuteThreadToPortLP.Start(ref strRes);
                
#if false
                taskExecutePrintPortStage = new ThreadBaseModel(90 + iPortNo, "TaskExecuteThreadFuncPrintPortStage ");
                taskExecutePrintPortStage.LoopInterval = 200;
                taskExecutePrintPortStage.SetThreadRoutine(TaskExecuteThreadFuncPrintPortStage);
                taskExecutePrintPortStage.TaskInit(ref strRes);
                taskExecutePrintPortStage.Start(ref strRes);
#endif
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("PortObj.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }

        public PortTransferState GetPortTransferState()
        {
            return portState;
        }

        /// <summary>
        /// 修改内存中的TransferState，不修改数据库
        /// </summary>
        public void UpdatePortTransferState(PortTransferState state)
        {
            portState = state;
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (PortInfo port in GlobalData.Instance.gbCurrentPortStates)
                {
                    if (strPortName == port.strPortID)
                    {
                        port.transferState = state;
                        return;
                    }
                }
            }
        }

        private bool JudgeIsCanChangeIOPortType(PortUnitType targetPortUnitType)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedTransferCommand TransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    LocationType CarrierLocaType = LocationMng.Instance.GetLocationType(TransferCommand.strRealSource);
                    LocationType DestLocaType = LocationMng.Instance.GetLocationType(TransferCommand.strRealDest);

                    if (TransferCommand.transferState == TransferState.Transferring &&
                        (CarrierLocaType == LocationType.IoPort || DestLocaType == LocationType.IoPort))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        private void taskExecuteThreadFunc()
        {
            if (bThreadFuncCount < 4)
            {
                bThreadFuncCount++;
                return;
            }

            //PIO 变化记录
            //if(!portTag.Contains("MR") && !portTag.Contains("OHT"))
            //{
            //    CompareAndOutPutPioChange();
            //}

            PortDev.Instance.IOPortServoOn(portTag);

            //IOPort
            if (iEqNum == 0)
            {
               
                if (PortDev.Instance.IsIOPortHasAlarm(portTag)) //Port是否有报警
                {
                    List<int> ioPortAlarms = PortDev.Instance.GetIOPortAlarms(portTag);
                    if (ioPortAlarms != null && ioPortAlarms.Count > 0)
                    {
                        foreach (int alarmID in ioPortAlarms)
                        {
                            AlarmController.Instance.SetAlarm(alarmID, strPortName, "");
                        }
                        if (portState != PortTransferState.OutOfService)
                        {
                            PortOutOfService();
                        }
                        else
                        {
                            UpdatePortTransferState(PortTransferState.OutOfService);
                        }
                    }
                }
                else if (LocationMng.Instance.IsLocationProhibited(strPortName))
                {
                    if (portState != PortTransferState.OutOfService)
                    {
                        PortOutOfService();
                    }
                    else
                    {
                        UpdatePortTransferState(PortTransferState.OutOfService);
                    }
                }
                else if (!PortDev.Instance.IsIOPortAuto(portTag)) //Port无报警，Port模式不是Auto
                {
                    AlarmController.Instance.ToMCSClearAlarm("", strPortName);
                    AlarmController.Instance.ClearAlarmByUnit(strPortName);
                    if (portState != PortTransferState.OutOfService)
                    {
                        PortOutOfService();
                    }
                    else
                    {
                        UpdatePortTransferState(PortTransferState.OutOfService);
                    }
                }
                else if (AlarmController.Instance.bIsAlarmWithSTKorCrane())
                {
                    if (portState != PortTransferState.OutOfService)
                    {
                        PortOutOfService();
                    }
                    else
                    {
                        UpdatePortTransferState(PortTransferState.OutOfService);
                    }
                }
                else //Port无报警，Port模式是Auto，Port没有被禁用
                {
                    AlarmController.Instance.ToMCSClearAlarm("", strPortName);
                    AlarmController.Instance.ClearAlarmByUnit(strPortName);
                    if (portState != PortTransferState.InService)
                    {
                        PortInService();
                    }
                    else
                    {
                        UpdatePortTransferState(PortTransferState.InService);
                    }
                }

                try
                {
                    if (portTag.Contains("MR2"))
                    {
                        if (PortDev.Instance.IsIOPortHasFoupErrorAlarm(portTag))
                        {
                            string strCarrier = PortDev.Instance.GetIOPortVehicleCSTID(portTag);
                            if (strCarrier.Length > 0)
                            {
                                bIsClean = true;
                                CarrierMng.Instance.DeleteCarrierInfo(strCarrier);
                                PortDev.Instance.setPortFoupErrorFlag(portTag, 1);
                            }
                        }
                        else if (bIsClean)
                        {
                            bIsClean = false;
                            PortDev.Instance.setPortFoupErrorFlag(portTag, 0);
                        }
                    }

                    //靠近Crane一侧
                    currPortWorkingState = PortDev.Instance.GetIOPortWorkingState(portTag);
                    if (lastPortWorkingState != currPortWorkingState)
                    {
                        HistoryWriter.Instance.EqpEventLog(strPortName, 10002
                                , "PortStateChange", $"靠近Crane一侧 Port New State: {currPortWorkingState}");
                        lastPortWorkingState = currPortWorkingState;
                        switch (currPortWorkingState)
                        {
                            case IOPortWorkingStatus.WaitCVPick://出库时表示等待从stkc侧取cst
                                {
                                    if (PortUnitType.Output == PortUnitType)//出库
                                    {
                                        string strID = LocationMng.Instance.GetLocationCarrierByLocName(strPortName);
                                        if(strID.Length <= 0)
                                        {
                                            lastPortWorkingState = IOPortWorkingStatus.Init;
                                            break;
                                        }

                                        GlobalData.Instance.gbPortType = "OP";
                                        HostIF.Instance.PostCarrierEvent(strID, EqpEvent.CarrierWaitOut);
                                    }
                                }
                                break;
                            case IOPortWorkingStatus.WaitCranePick://等待入库
                                {
                                    string strCSTID = PortDev.Instance.GetIOPortStageCSTID(portTag);
                                    stInPortCSTIDTime.bIsReadSuccess = true;
                                    if (strCSTID.Length == 0)
                                    {
                                        HistoryWriter.Instance.EqpEventLog(portTag + "WaitCranePick入库", 0, "OP侧读取PLC传递Carrier Error", "");
                                        if (strFailedCSTID.Length != 0)
                                        {
                                            strCSTID = strFailedCSTID;
                                        }
                                        else
                                        {
                                            strFailedCSTID = CarrierMng.Instance.GetIDRFailCarrierID(strPortName);
                                            strCSTID = strFailedCSTID;
                                        }

                                        strFailedCSTID = "";
                                        CarrierMng.Instance.AddCarrierInfo(strCSTID, strPortName);
                                    }
                                    else
                                    {
                                        TpCarrier tpCarrier = TpCarrier.GetById(strCSTID);
                                        if (tpCarrier == null)
                                        {
                                            CarrierMng.Instance.AddCarrierInfo(strCSTID, strPortName);
                                        }
                                    }

                                    //CarrierMng.Instance.AddCarrierInfo(strCSTID, strPortName);
                                    HostIF.Instance.PostCarrierEvent(strCSTID, EqpEvent.CarrierWaitIn);
                                    stInPortCSTIDTime.strCasstileID = strCSTID;
                                    stInPortCSTIDTime.timeInPut = DateTime.Now;
                                    stInPortCSTIDTime.bInputFlag = true;

                                }
                                break;
                            case IOPortWorkingStatus.Init:
                                {
                                    if (!PortDev.Instance.IsOPHasCST(portTag))//OP无
                                    {
                                        TpLocation tpPortLocation = TpLocation.GetByLambda(x => x.Name == strZoneName);
                                        TpCarrierList tpCarrierList = TpCarrierList.GetAll();
                                        foreach (TpCarrier tpCarrier in tpCarrierList)
                                        {
                                            if (tpCarrier.Location == tpPortLocation.Address && tpCarrier.Id != "")
                                            {
                                                HostIF.Instance.PostCarrierEvent(tpCarrier.Id, EqpEvent.CarrierRemoveCompleted);
                                                HistoryWriter.Instance.EqpEventLog(strPortName, 10003, "异常处理", $"删除残帐CarrierID: {tpCarrier.Id}");
                                                CarrierMng.Instance.DeleteCarrierInfo(tpCarrier.Id);
                                            }
                                        }
                                    }
                                    else//OP有
                                    {
                                        TpLocation tpPortLocation = TpLocation.GetByLambda(x => x.Name == strZoneName);
                                        TpCarrierList tpCarrierList = TpCarrierList.GetAll();
                                        foreach (TpCarrier tpCarrier in tpCarrierList)
                                        {
                                            if (tpCarrier.Location == tpPortLocation.Address && tpCarrier.Id != "")
                                            {
                                                if (tpCarrier.Id != tpPortLocation.CarrierId)
                                                {
                                                    HostIF.Instance.PostCarrierEvent(tpCarrier.Id, EqpEvent.CarrierRemoveCompleted);
                                                    HistoryWriter.Instance.EqpEventLog(strPortName, 10003, "异常处理", $"删除残帐CarrierID: {tpCarrier.Id}");
                                                    CarrierMng.Instance.DeleteCarrierInfo(tpCarrier.Id);
                                                }
                                            }
                                        }
                                    }
                                }
                                break;
                            default:
                                {
                                    break;
                                }
                        }
                    }
                }
                catch (Exception ex)
                {
                    //记录程序异常日志
                    Log.Logger.Instance.ExceptionLog("PortObj.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                }
            }
        }

        private void TaskExecuteThreadFuncToPortLP()
        {
            if (iEqNum == 0)
            {
                try
                {
                    //外侧
                    if (portTag.Contains("MR") || portTag.Contains("OHT"))
                    {
                        currBufferWorkingState = PortDev.Instance.GetBufferWorkingState(portTag);
                        if (lastBufferWorkingState != currBufferWorkingState)
                        {
                            HistoryWriter.Instance.EqpEventLog(strPortName, 10002
                                , "PortStateChange", $"靠近Crane外侧 Port New State: {currBufferWorkingState}");
                            lastBufferWorkingState = currBufferWorkingState;
                            switch (currBufferWorkingState)
                            {
                                case IOPortWorkingStatus.VehiclePlaceCompleted:
                                    {
                                        bool bflag = PortDev.Instance.HandoffCompleteAck(portTag);
                                        if (!bflag)
                                        {
                                            lastBufferWorkingState = IOPortWorkingStatus.Ready;
                                            HistoryWriter.Instance.EqpEventLog("入库时外侧VehiclePlaceCompleted =CompleteAck写入1失败", 0, "VehiclePlaceCompleted", "");
                                        }
                                    }
                                    break;
                                case IOPortWorkingStatus.WaitVehiclePlace:
                                    {
                                        PortDev.Instance.ResetHandoffCompleteAck(portTag);
                                    }
                                    break;
                                case IOPortWorkingStatus.WaitCVPick://入库时表示读码完成；
                                    {
                                        if (PortUnitType.Input == PortUnitType)//入库
                                        {
                                            IDRResult idrResult = PortDev.Instance.GetIOPortIDRResult(portTag); ;
                                            int iLoop = 0;
                                            while (iLoop < 10)
                                            {
                                                iLoop++;
                                                if (1 == idrResult.iResultCode && 0 == idrResult.strCarrierID.Length)
                                                {
                                                    Thread.Sleep(200);
                                                    idrResult = PortDev.Instance.GetIOPortIDRResult(portTag);
                                                    HistoryWriter.Instance.EqpEventLog(strPortName, 0, "重读扫码结果", $"重读次数:{iLoop}");
                                                    if (10 == iLoop)
                                                    {
                                                        idrResult.iResultCode = 3;
                                                    }
                                                }
                                                else
                                                {
                                                    break;
                                                }
                                            }
                                            
                                            if (Proj.CacheData.GlobalData.Instance.gbSimulation)
                                            {
                                                idrResult.iResultCode = 1;
                                                idrResult.strCarrierID = "123456";
                                            }

                                            string strPortAddress = LocationMng.Instance.GetLocationAddress(strPortName);
                                            switch (idrResult.iResultCode)
                                            {
                                                case 1:    //成功（可能Duplicate）
                                                    {
                                                        //Duplicate
                                                        if (CarrierMng.Instance.IsCarrierDuplicate(idrResult.strCarrierID, strPortAddress, ref dupCarrierRealLoc))
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog(strPortName, (int)EqpEvent.CarrierIDRead
                                                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead DuplicateID:{idrResult.strCarrierID}");

                                                            //1.删除DuplicateID的盒子信息
                                                            HostIF.Instance.PostCarrierEvent(idrResult.strCarrierID, EqpEvent.CarrierRemoveCompleted);
                                                            CarrierMng.Instance.DeleteCarrierInfo(idrResult.strCarrierID);

                                                            //2.在DuplicateID的位置处添加UnKnownID的Carrier信息
                                                            string strUnKnownID = CarrierMng.Instance.GenDulplicateCarrierID(idrResult.strCarrierID);
                                                            CarrierMng.Instance.AddCarrierInfo(strUnKnownID, dupCarrierRealLoc);
                                                            HostIF.Instance.PostCarrierEvent(strUnKnownID, EqpEvent.CarrierInstallCompleted);

                                                            HostIF.Instance.PostIDREvent(idrResult.strCarrierID, strPortName, IDReadStatus.Duplicate, EqpEvent.CarrierIDRead);
                                                        }
                                                        else //Success
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog(strPortName, (int)EqpEvent.CarrierIDRead
                                                                , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead Success:{idrResult.strCarrierID}");

                                                            string strPortLocation = LocationMng.Instance.GetLocationAddress(strPortName);
                                                            HostIF.Instance.PostIDREvent(idrResult.strCarrierID, strPortLocation, IDReadStatus.Success, EqpEvent.CarrierIDRead);
                                                        }
                                                    }
                                                    break;
                                                case 3:    //失败
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog(strPortName, (int)EqpEvent.CarrierIDRead
                                                            , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead Failure: {idrResult.strCarrierID}, ResultCode: {idrResult.iResultCode}");

                                                        strFailedCSTID = CarrierMng.Instance.GetIDRFailCarrierID(strPortName);
                                                        if (PortDev.Instance.WritePortLPInCarrierID(strFailedCSTID, portTag))
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog(portTag + "入库", 0, "WritePortLPInCarrierID", strFailedCSTID);
                                                        }
                                                        HostIF.Instance.PostIDREvent(strFailedCSTID, strPortName, IDReadStatus.Failure, EqpEvent.CarrierIDRead);
                                                    }
                                                    break;
                                                default:    //不该出现
                                                    {
                                                        HistoryWriter.Instance.EqpEventLog(strPortName, (int)EqpEvent.CarrierIDRead
                                                            , EqpEvent.CarrierIDRead.ToString(), $"CarrierIDRead Read error: {idrResult.strCarrierID}, ResultCode: {idrResult.iResultCode}");

                                                        strFailedCSTID = CarrierMng.Instance.GetIDRFailCarrierID(strPortName);
                                                        if (PortDev.Instance.WritePortLPInCarrierID(strFailedCSTID, portTag))
                                                        {
                                                            HistoryWriter.Instance.EqpEventLog(portTag + "入库", 0, "WritePortLPInCarrierID", strFailedCSTID);
                                                        }
                                                        HostIF.Instance.PostIDREvent(strFailedCSTID, strPortName, IDReadStatus.Failure, EqpEvent.CarrierIDRead);
                                                    }
                                                    break;
                                            }
                                        }
                                    }
                                    break;
                                case IOPortWorkingStatus.WaitVehiclePick:
                                    {
                                        PortDev.Instance.ResetHandoffCompleteAck(portTag);

                                        strSaveCarrierID = PortDev.Instance.GetIOPortBufferCSTID(portTag);
                                        if (strSaveCarrierID.Length == 0)
                                        {
                                            lastBufferWorkingState = IOPortWorkingStatus.Ready;
                                        }
                                        else
                                        {
                                            GlobalData.Instance.gbPortType = "LP";
                                            HostIF.Instance.PostCarrierEvent(strSaveCarrierID, EqpEvent.CarrierWaitOut);

                                            bool bFlag = PortDev.Instance.ResetHandoffCompleteAck(portTag);
                                            if (!bFlag)
                                            {
                                                lastBufferWorkingState = IOPortWorkingStatus.Ready;
                                                HistoryWriter.Instance.EqpEventLog(strPortName,  0, "出库时外侧等待取走Foup=CompleteAck写入0失败", "WaitVehiclePick");
                                            }
                                        }
                                    }
                                    break;
                                case IOPortWorkingStatus.VehiclePickCompleted://出库时外侧取走货
                                    {
                                        GlobalData.Instance.gbCarrierLoc = strZoneName;
                                        if (portTag.Contains("MR"))
                                        {
                                            GlobalData.Instance.gbHandoffType64 = HandoffType.Manual;
                                        }
                                        else
                                        {
                                            GlobalData.Instance.gbHandoffType64 = HandoffType.Automated;
                                        }

                                        //GlobalData.Instance.gbCarrierLoc = strZoneName;
                                        HostIF.Instance.PostCarrierEvent(strSaveCarrierID, EqpEvent.CarrierRemoved, strZoneName);
                                        Thread.Sleep(50);
                                        HistoryWriter.Instance.EqpEventLog(strPortName,  0, "出库时外侧取走货:VehiclePickCompleted", "ID=" + strSaveCarrierID);
                                        CarrierMng.Instance.DeleteCarrierInfo(strSaveCarrierID);
                                        strSaveCarrierID = "";

                                        PortDev.Instance.HandoffCompleteAck(portTag);
                                    }
                                    break;
                                default:
                                    {
                                        bool bFlag = PortDev.Instance.ResetHandoffCompleteAck(portTag);
                                        if (!bFlag)
                                        {
                                            HistoryWriter.Instance.EqpEventLog(strPortName, 0, "出库时外侧等待取走Foup=CompleteAck写入0失败", "default");
                                        }
                                        break;
                                    }
                            }
                        }
                    }
                }
                catch(Exception ex)
                {
                    Logger.Instance.ExceptionLog("PortObj.cs:TaskExecuteThreadFuncToPortLP " + ex.Message + ", Stack: " + ex.StackTrace);
                }
            }
        }

        private void TaskExecuteThreadFuncPrintPortStage()
        {
            IOPortWorkingStatus currBufferWorkingState = PortDev.Instance.GetBufferWorkingState(portTag);
            //if (lastBufferWorkingState != currBufferWorkingState)
            {
                HistoryWriter.Instance.PIOLog(strPortName, "", $"靠近Crane外侧 Port New State: {currBufferWorkingState}", "", 1);
            }
          
            IOPortWorkingStatus currPortWorkingState = PortDev.Instance.GetIOPortWorkingState(portTag);
            //if (lastPortWorkingState != currPortWorkingState)
            {
                HistoryWriter.Instance.PIOLog(strPortName, "", $"靠近Crane一侧 Port New State: {currPortWorkingState}", "", 0);
            }
        }
       
        public void CompareAndOutPutPioChange()
        {
            PortPIOStatus curPortPIOStatus = PortDev.Instance.GetPortAllPIO(portPath, portTag);

            if (m_stLastPortPIOStatus.P_L_REQ != curPortPIOStatus.P_L_REQ)
            {
                m_stLastPortPIOStatus.P_L_REQ = curPortPIOStatus.P_L_REQ;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "P_L_REQ", Convert.ToInt32(curPortPIOStatus.P_L_REQ));
            }
            if (m_stLastPortPIOStatus.P_U_REQ != curPortPIOStatus.P_U_REQ)
            {
                m_stLastPortPIOStatus.P_U_REQ = curPortPIOStatus.P_U_REQ;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "P_U_REQ", Convert.ToInt32(curPortPIOStatus.P_U_REQ));
            }
            if (m_stLastPortPIOStatus.P_Ready != curPortPIOStatus.P_Ready)
            {
                m_stLastPortPIOStatus.P_Ready = curPortPIOStatus.P_Ready;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "P_Ready", Convert.ToInt32(curPortPIOStatus.P_Ready));
            }
            if (m_stLastPortPIOStatus.P_Abnormal != curPortPIOStatus.P_Abnormal)
            {
                m_stLastPortPIOStatus.P_Abnormal = curPortPIOStatus.P_Abnormal;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "P_Abnormal", Convert.ToInt32(curPortPIOStatus.P_Abnormal));
            }
            if (m_stLastPortPIOStatus.P_InterLock != curPortPIOStatus.P_InterLock)
            {
                m_stLastPortPIOStatus.P_InterLock = curPortPIOStatus.P_InterLock;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "P_InterLock", Convert.ToInt32(curPortPIOStatus.P_InterLock));
            }
            if (m_stLastPortPIOStatus.P_CSTContain != curPortPIOStatus.P_CSTContain)
            {
                m_stLastPortPIOStatus.P_CSTContain = curPortPIOStatus.P_CSTContain;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "P_CSTContain", Convert.ToInt32(curPortPIOStatus.P_CSTContain));
            }

            if (m_stLastPortPIOStatus.A_TR_REQ != curPortPIOStatus.A_TR_REQ)
            {
                m_stLastPortPIOStatus.A_TR_REQ = curPortPIOStatus.A_TR_REQ;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "A_TR_REQ", Convert.ToInt32(curPortPIOStatus.A_TR_REQ));
            }
            if (m_stLastPortPIOStatus.A_Busy != curPortPIOStatus.A_Busy)
            {
                m_stLastPortPIOStatus.A_Busy = curPortPIOStatus.A_Busy;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "A_Busyb", Convert.ToInt32(curPortPIOStatus.A_Busy));
            }
            if (m_stLastPortPIOStatus.A_COMPT != curPortPIOStatus.A_COMPT)
            {
                m_stLastPortPIOStatus.A_COMPT = curPortPIOStatus.A_COMPT;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "A_COMPT", Convert.ToInt32(curPortPIOStatus.A_COMPT));
            }
            if (m_stLastPortPIOStatus.A_Abnormal != curPortPIOStatus.A_Abnormal)
            {
                m_stLastPortPIOStatus.A_Abnormal = curPortPIOStatus.A_Abnormal;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "A_Abnormal", Convert.ToInt32(curPortPIOStatus.A_Abnormal));
            }
            if (m_stLastPortPIOStatus.A_InterLock != curPortPIOStatus.A_InterLock)
            {
                m_stLastPortPIOStatus.A_InterLock = curPortPIOStatus.A_InterLock;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "A_InterLock", Convert.ToInt32(curPortPIOStatus.A_InterLock));
            }
            if (m_stLastPortPIOStatus.A_CSTContain != curPortPIOStatus.A_CSTContain)
            {
                m_stLastPortPIOStatus.A_CSTContain = curPortPIOStatus.A_CSTContain;
                HistoryWriter.Instance.PIOLog(strPortName, "", "", "A_CSTContain", Convert.ToInt32(curPortPIOStatus.P_L_REQ));
            }
        }

        public void UpdateEqPortStatus()
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EqPortInfo eqPort in GlobalData.Instance.gbCurrentEqPortStatus)
                {
                    if (strPortName == eqPort.strPortID)
                    {
                        eqPort.eqReqStatus = lastEqReqStatus;
                        eqPort.eqPresenceStatus = lastEqPresenceStatus;
                        return;
                    }
                }
            }
        }


        /// <summary>
        /// 更新lastPortUnitType，向MCS发送Event
        /// </summary>
        private void UpdatePortUnitType(PortUnitType unitType)
        {
            lastPortUnitType = unitType;
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (PortTypeInfo portTypeInfo in GlobalData.Instance.gbPortTypes)
                {
                    if (portTypeInfo.strPortID == strPortName)
                    {
                        portTypeInfo.portUnitType = unitType;
                        GlobalData.Instance.gbPortUnitType = unitType;
                        break;
                    }
                }
            }
            HostIF.Instance.PostPortEvent(strPortName, EqpEvent.PortTypeChange);
        }

        public void Stop()
        {
            string strRes = "";
            taskExecuteThread.TaskExit(ref strRes);
        }

        public IDRResult GetPortIDRResult()
        {
            return PortDev.Instance.GetIOPortIDRResult(portTag);
        }

        public bool SendPortSpeed(int emptySpeed, int storageSpeed)
        { 
            return PortDev.Instance.SetIOPortSpeed(portTag, emptySpeed, storageSpeed);
        }

        public bool ResetAlarm()
        {
            PortDev.Instance.PortAlarmClear(portTag);
            Thread.Sleep(200);
            PortDev.Instance.ResetPortAlarmClear(portTag);
            return true;
        }

        public EqPortInfo GetEqPortInfo()
        {
            EqPortInfo eqInfo = new EqPortInfo();
            eqInfo.strPortID = strPortName;

            EqPortStatus eqStatus = PortDev.Instance.GetEqPortRequest(portTag);
            if(eqStatus.pLReq == false && eqStatus.pUReq == false)
            {
                eqInfo.eqReqStatus = EqReqStatus.ReqOff;
            }
            else if (eqStatus.pLReq)
            {
                eqInfo.eqReqStatus = EqReqStatus.LReq;
            }
            else if (eqStatus.pUReq)
            {
                eqInfo.eqReqStatus = EqReqStatus.UReq;
            }

            if(PortDev.Instance.EqPortHasCST(portTag))
            {
                eqInfo.eqPresenceStatus = EqPresenceStatus.Presence;
            }
            else
            {
                eqInfo.eqPresenceStatus = EqPresenceStatus.Presence;
            }

            return eqInfo;
        }

        public PortUnitType GetIOPortUnitType()
        {
            return PortDev.Instance.GetIOPortUnitType(portTag);
        }

        public void PortOutOfService()
        {
            portState = PortTransferState.OutOfService;
            HostIF.Instance.PostPortEvent(strPortName, EqpEvent.PortOutofService);
            PortMng.Instance.UpdateIOPortTransferState(strPortName, PortTransferState.OutOfService);
        }

        public void PortInService()
        {
            portState = PortTransferState.InService;
            HostIF.Instance.PostPortEvent(strPortName, EqpEvent.PortInService);
            PortMng.Instance.UpdateIOPortTransferState(strPortName, PortTransferState.InService);
        }

        public bool WriteInPortCarrierID(string strCarrierID, string strOHT)
        {
            string strPath = "Port.s2hIOPortStatus" + strOHT + ".BufferCSTID";
            bool bRes = PlcComm.Instance.WriteTagValue(strPath, strCarrierID);

            return bRes;
        }
    }
}
