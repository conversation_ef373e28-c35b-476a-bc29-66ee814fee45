﻿using Proj.API;
using Proj.CacheData;
using Proj.DataTypeDef;
using Proj.DB;
using Proj.Entity;
using Proj.History;
using Proj.Log;
using Secs4Net;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices.ComTypes;
using System.Threading;
using System.Threading.Tasks;

namespace Proj.HostComm
{
    public delegate HCACK deleHostCommand(string RCMD, Dictionary<string, object> dicParameter);
    public delegate void deleStateChange(string stateName, string stateVlue);

    public class HostIF : GemEquipmentImpl
    {
        private static HostIF m_Instanse;
        private static readonly object mSyncObject = new object();
        private bool bIsFirstLink = true;
        private HostIF() { }
        public static HostIF Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new HostIF();
                        }
                    }
                }
                return m_Instanse;
            }
        }
        public event deleHostCommand EventHostCommand = null;
        public event deleStateChange EventStateChange = null;

        //先代码写死，以后要放到数据库或配置文件中
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:验证平台兼容性", Justification = "<挂起>")]
        private List<DVItem> DefineDVItems()
        {
#pragma warning disable IDE0028 // 简化集合初始化
            List<DVItem> dvItemList = new List<DVItem>()
            {
                new DVItem((uint)EqpVariable.Clock,SecsFormat.ASCII.ToString(), EqpVariable.Clock.ToString()),
                new DVItem((uint)EqpVariable.AlarmEnabled,SecsFormat.List.ToString(), EqpVariable.AlarmEnabled.ToString()),
                new DVItem((uint)EqpVariable.AlarmSet, SecsFormat.ASCII.ToString(), EqpVariable.AlarmSet.ToString()),
                new DVItem((uint)EqpVariable.EventEnabled, SecsFormat.ASCII.ToString(), EqpVariable.EventEnabled.ToString()),
                new DVItem((uint)EqpVariable.SpecVersion, SecsFormat.ASCII.ToString(), EqpVariable.SpecVersion.ToString()),
                new DVItem((uint)EqpVariable.ControlState, SecsFormat.U2.ToString(), EqpVariable.ControlState.ToString()),
                new DVItem((uint)EqpVariable.CassetteStockerState, SecsFormat.U2.ToString(), EqpVariable.CassetteStockerState.ToString()),
                new DVItem((uint)EqpVariable.SCState, SecsFormat.U2.ToString(), EqpVariable.SCState.ToString()),
                new DVItem((uint)EqpVariable.CurrentPortStates, SecsFormat.List.ToString(), EqpVariable.CurrentPortStates.ToString()),
                new DVItem((uint)EqpVariable.PortTypes, SecsFormat.List.ToString(), EqpVariable.PortTypes.ToString()),
                new DVItem((uint)EqpVariable.PortTypeInfo_i, SecsFormat.List.ToString(), EqpVariable.PortTypeInfo_i.ToString()),
                new DVItem((uint)EqpVariable.PortUnitType, SecsFormat.U2.ToString(), EqpVariable.PortUnitType.ToString()),
                new DVItem((uint)EqpVariable.ActiveZones, SecsFormat.List.ToString(), EqpVariable.ActiveZones.ToString()),
                new DVItem((uint)EqpVariable.EnhancedActiveZones, SecsFormat.List.ToString(), EqpVariable.EnhancedActiveZones.ToString()),
                new DVItem((uint)EqpVariable.ExtendedActiveZones, SecsFormat.List.ToString(), EqpVariable.ExtendedActiveZones.ToString()),
                new DVItem((uint)EqpVariable.ExtendedZoneData, SecsFormat.List.ToString(), EqpVariable.ExtendedZoneData.ToString()),
                new DVItem((uint)EqpVariable.CraneOperationInformation, SecsFormat.List.ToString(), EqpVariable.CraneOperationInformation.ToString()),
                new DVItem((uint)EqpVariable.CraneModeStateInformation, SecsFormat.List.ToString(), EqpVariable.CraneModeStateInformation.ToString()),
                new DVItem((uint)EqpVariable.ActiveCarriers, SecsFormat.List.ToString(), EqpVariable.ActiveCarriers.ToString()),
                new DVItem((uint)EqpVariable.EnhancedCarriers, SecsFormat.List.ToString(), EqpVariable.EnhancedCarriers.ToString()),
                new DVItem((uint)EqpVariable.ActiveTransfers, SecsFormat.List.ToString(), EqpVariable.ActiveTransfers.ToString()),
                new DVItem((uint)EqpVariable.EnhancedTransfers, SecsFormat.List.ToString(), EqpVariable.EnhancedTransfers.ToString()),
                new DVItem((uint)EqpVariable.TransferState, SecsFormat.U2.ToString(), EqpVariable.TransferState.ToString()),
                new DVItem((uint)EqpVariable.CarrierState, SecsFormat.U2.ToString(), EqpVariable.CarrierState.ToString()),
                new DVItem((uint)EqpVariable.PortTransferState_i, SecsFormat.U2.ToString(), EqpVariable.PortTransferState_i.ToString()),
                new DVItem((uint)EqpVariable.ResultCode, SecsFormat.U2.ToString(), EqpVariable.ResultCode.ToString()),
                new DVItem((uint)EqpVariable.IDReadStatus, SecsFormat.U2.ToString(), EqpVariable.IDReadStatus.ToString()),
                new DVItem((uint)EqpVariable.CraneState, SecsFormat.U2.ToString(), EqpVariable.CraneState.ToString()),
                new DVItem((uint)EqpVariable.EqReqStatus, SecsFormat.U2.ToString(), EqpVariable.EqReqStatus.ToString()),
                new DVItem((uint)EqpVariable.EqPresenceStatus, SecsFormat.ASCII.ToString(), EqpVariable.EqPresenceStatus.ToString()), //应该是U2吧？lzt
                new DVItem((uint)EqpVariable.TransferCommand, SecsFormat.List.ToString(), EqpVariable.TransferCommand.ToString()),
                new DVItem((uint)EqpVariable.CommandInfo, SecsFormat.List.ToString(), EqpVariable.CommandInfo.ToString()),
                new DVItem((uint)EqpVariable.TransferInfo, SecsFormat.List.ToString(), EqpVariable.TransferInfo.ToString()),
                new DVItem((uint)EqpVariable.CarrierInfo, SecsFormat.List.ToString(), EqpVariable.CarrierInfo.ToString()),
                new DVItem((uint)EqpVariable.ZoneData, SecsFormat.List.ToString(), EqpVariable.ZoneData.ToString()),
                new DVItem((uint)EqpVariable.EnhancedZoneData, SecsFormat.List.ToString(), EqpVariable.EnhancedZoneData.ToString()),
                new DVItem((uint)EqpVariable.StockerUnitInfo, SecsFormat.List.ToString(), EqpVariable.StockerUnitInfo.ToString()),
                new DVItem((uint)EqpVariable.CommandID, SecsFormat.ASCII.ToString().ToString(), EqpVariable.CommandID.ToString()),
                new DVItem((uint)EqpVariable.CommandType, SecsFormat.ASCII.ToString(), EqpVariable.CommandType.ToString()),
                new DVItem((uint)EqpVariable.Priority, SecsFormat.U2.ToString(), EqpVariable.Priority.ToString()),
                new DVItem((uint)EqpVariable.CarrierID, SecsFormat.ASCII.ToString(), EqpVariable.CarrierID.ToString()),
                new DVItem((uint)EqpVariable.CarrierLoc, SecsFormat.ASCII.ToString(), EqpVariable.CarrierLoc.ToString()),
                new DVItem((uint)EqpVariable.Dest, SecsFormat.ASCII.ToString(), EqpVariable.Dest.ToString()),
                new DVItem((uint)EqpVariable.Source, SecsFormat.ASCII.ToString(), EqpVariable.Source.ToString()),
                new DVItem((uint)EqpVariable.CarrierZoneName, SecsFormat.ASCII.ToString(), EqpVariable.CarrierZoneName.ToString()),
                new DVItem((uint)EqpVariable.CarrierLocations, SecsFormat.List.ToString(), EqpVariable.CarrierLocations.ToString()),
                new DVItem((uint)EqpVariable.ZoneName, SecsFormat.U2.ToString(), EqpVariable.ZoneName.ToString()),
                new DVItem((uint)EqpVariable.ZoneCapacity, SecsFormat.U2.ToString(), EqpVariable.ZoneCapacity.ToString()),
                new DVItem((uint)EqpVariable.ZoneType, SecsFormat.U2.ToString(), EqpVariable.ZoneType.ToString()),
                new DVItem((uint)EqpVariable.StockerCraneID, SecsFormat.ASCII.ToString(), EqpVariable.StockerCraneID.ToString()),
                new DVItem((uint)EqpVariable.StockerUnitID, SecsFormat.ASCII.ToString(), EqpVariable.StockerUnitID.ToString()),
                new DVItem((uint)EqpVariable.RecoveryOptions, SecsFormat.ASCII.ToString(), EqpVariable.RecoveryOptions.ToString()),
                new DVItem((uint)EqpVariable.PortID, SecsFormat.ASCII.ToString(), EqpVariable.PortID.ToString()),
                new DVItem((uint)EqpVariable.HandoffType, SecsFormat.U2.ToString(), EqpVariable.HandoffType.ToString()),
                new DVItem((uint)EqpVariable.PortType, SecsFormat.ASCII.ToString(), EqpVariable.PortType.ToString()),
                new DVItem((uint)EqpVariable.ErrorID, SecsFormat.ASCII.ToString(), EqpVariable.ErrorID.ToString()),
                new DVItem((uint)EqpVariable.EmptyCarrier, SecsFormat.U2.ToString(), EqpVariable.EmptyCarrier.ToString()),
                new DVItem((uint)EqpVariable.UnitID, SecsFormat.ASCII.ToString(), EqpVariable.UnitID.ToString()),
                new DVItem((uint)EqpVariable.AlarmID, SecsFormat.U4.ToString(), EqpVariable.AlarmID.ToString()),
                new DVItem((uint)EqpVariable.AlarmText, SecsFormat.ASCII.ToString(), EqpVariable.AlarmText.ToString()),
                new DVItem((uint)EqpVariable.DisabledLocations, SecsFormat.List.ToString(), EqpVariable.DisabledLocations.ToString()),
                new DVItem((uint)EqpVariable.DisabledLoc_i, SecsFormat.List.ToString(), EqpVariable.DisabledLoc_i.ToString()),
                new DVItem((uint)EqpVariable.GlassExist, SecsFormat.U2.ToString(), EqpVariable.GlassExist.ToString()),
                new DVItem((uint)EqpVariable.EqpName, SecsFormat.ASCII.ToString(), EqpVariable.EqpName.ToString()),
                new DVItem((uint)EqpVariable.EstablishCommunicationTimeOut, SecsFormat.U2.ToString(), EqpVariable.EstablishCommunicationTimeOut.ToString()),
                new DVItem((uint)EqpVariable.IDReadDuplicateOption, SecsFormat.U1.ToString(), EqpVariable.IDReadDuplicateOption.ToString()),
                new DVItem((uint)EqpVariable.IDReadFailureOption, SecsFormat.U1.ToString(), EqpVariable.IDReadFailureOption.ToString()),
                new DVItem((uint)EqpVariable.IDReadMismatchOption, SecsFormat.U1.ToString(), EqpVariable.IDReadMismatchOption.ToString()),
                new DVItem((uint)EqpVariable.EnhancedTransferCommand, SecsFormat.List.ToString(), EqpVariable.EnhancedTransferCommand.ToString()),
                new DVItem((uint)EqpVariable.ZoneTotalSize, SecsFormat.U2.ToString(), EqpVariable.ZoneTotalSize.ToString()),
                new DVItem((uint)EqpVariable.T3TimeOut, SecsFormat.U2.ToString(), EqpVariable.T3TimeOut.ToString()),
                new DVItem((uint)EqpVariable.T5TimeOut, SecsFormat.U2.ToString(), EqpVariable.T5TimeOut.ToString()),
                new DVItem((uint)EqpVariable.T6TimeOut, SecsFormat.U2.ToString(), EqpVariable.T6TimeOut.ToString()),
                new DVItem((uint)EqpVariable.T7TimeOut, SecsFormat.U2.ToString(), EqpVariable.T7TimeOut.ToString()),
                new DVItem((uint)EqpVariable.T8TimeOut, SecsFormat.U2.ToString(), EqpVariable.T8TimeOut.ToString()),
                new DVItem((uint)EqpVariable.RetryCount, SecsFormat.U2.ToString(), EqpVariable.RetryCount.ToString()),
                new DVItem((uint)EqpVariable.MainteState, SecsFormat.U2.ToString(), EqpVariable.RetryCount.ToString()),
                new DVItem((uint)EqpVariable.AllEnhancedDisableLocations, SecsFormat.List.ToString().ToString(), EqpVariable.RetryCount.ToString())
            };
#pragma warning restore IDE0028 // 简化集合初始化
            return dvItemList;
        }
        //SVID设备状态变量
        private List<SVItem> DefineSVItems()
        {
#pragma warning disable IDE0028 // 简化集合初始化
#pragma warning disable CA1416 // 验证平台兼容性
            List<SVItem> svItemList = new List<SVItem>()
            {
                new SVItem((uint)EqpVariable.Clock, SecsFormat.ASCII.ToString(), EqpVariable.Clock.ToString()),
                new SVItem((uint)EqpVariable.AlarmEnabled, SecsFormat.List.ToString(), EqpVariable.AlarmEnabled.ToString()),
                new SVItem((uint)EqpVariable.AlarmSet, SecsFormat.List.ToString(), EqpVariable.AlarmSet.ToString()),
                new SVItem((uint)EqpVariable.EventEnabled, SecsFormat.List.ToString(), EqpVariable.EventEnabled.ToString()),
                new SVItem((uint)EqpVariable.SpecVersion, SecsFormat.ASCII.ToString(), EqpVariable.SpecVersion.ToString()),
                new SVItem((uint)EqpVariable.ControlState, SecsFormat.U2.ToString(), EqpVariable.ControlState.ToString()),
                new SVItem((uint)EqpVariable.CassetteStockerState, SecsFormat.U2.ToString(), EqpVariable.CassetteStockerState.ToString()),
                new SVItem((uint)EqpVariable.SCState, SecsFormat.U2.ToString(), EqpVariable.SCState.ToString()),
                new SVItem((uint)EqpVariable.CurrentPortStates, SecsFormat.List.ToString(), EqpVariable.CurrentPortStates.ToString()),
                new SVItem((uint)EqpVariable.CurrentEqPortStatus, SecsFormat.List.ToString(), EqpVariable.CurrentEqPortStatus.ToString()),
                new SVItem((uint)EqpVariable.PortTypes, SecsFormat.List.ToString(), EqpVariable.PortTypes.ToString()),
                new SVItem((uint)EqpVariable.PortTypeInfo_i, SecsFormat.List.ToString(), EqpVariable.PortTypeInfo_i.ToString()),
                new SVItem((uint)EqpVariable.PortUnitType, SecsFormat.U2.ToString(), EqpVariable.PortUnitType.ToString()),
                new SVItem((uint)EqpVariable.ActiveZones, SecsFormat.List.ToString(), EqpVariable.ActiveZones.ToString()),
                new SVItem((uint)EqpVariable.EnhancedActiveZones, SecsFormat.List.ToString(), EqpVariable.EnhancedActiveZones.ToString()),
                new SVItem((uint)EqpVariable.ExtendedActiveZones, SecsFormat.List.ToString(), EqpVariable.ExtendedActiveZones.ToString()),
                new SVItem((uint)EqpVariable.ExtendedZoneData, SecsFormat.List.ToString(), EqpVariable.ExtendedZoneData.ToString()),
                new SVItem((uint)EqpVariable.CraneOperationInformation, SecsFormat.List.ToString(), EqpVariable.CraneOperationInformation.ToString()),
                new SVItem((uint)EqpVariable.CraneModeStateInformation, SecsFormat.List.ToString(), EqpVariable.CraneModeStateInformation.ToString()),
                new SVItem((uint)EqpVariable.ActiveCarriers, SecsFormat.List.ToString(), EqpVariable.ActiveCarriers.ToString()),
                new SVItem((uint)EqpVariable.EnhancedCarriers, SecsFormat.List.ToString(), EqpVariable.EnhancedCarriers.ToString()),
                new SVItem((uint)EqpVariable.ActiveTransfers, SecsFormat.List.ToString(), EqpVariable.ActiveTransfers.ToString()),
                 //add begin
                new SVItem((uint)EqpVariable.ShelfAllStats, SecsFormat.List.ToString(), EqpVariable.ShelfAllStats.ToString()),
                new SVItem((uint)EqpVariable.ActiveZones_2, SecsFormat.List.ToString(), EqpVariable.ActiveZones_2.ToString()),
                //add end  
                new SVItem((uint)EqpVariable.EnhancedTransfers, SecsFormat.List.ToString(), EqpVariable.EnhancedTransfers.ToString()),
                new SVItem((uint)EqpVariable.UnitAlarmStatList, SecsFormat.List.ToString(), EqpVariable.UnitAlarmStatList.ToString())
            };
#pragma warning restore CA1416 // 验证平台兼容性
#pragma warning restore IDE0028 // 简化集合初始化
            return svItemList;
        }
        //常量ECID
        private List<ECItem> DefineECItems()
        {

#pragma warning disable IDE0028 // 简化集合初始化
#pragma warning disable CA1416 // 验证平台兼容性
            List<ECItem> ecItemList = new List<ECItem>()
            {
                new ECItem((uint)EqpVariable.EqpName, SecsFormat.ASCII.ToString(), EqpVariable.EqpName.ToString()),
                new ECItem((uint)EqpVariable.EstablishCommunicationTimeOut, SecsFormat.U2.ToString(), EqpVariable.EstablishCommunicationTimeOut.ToString()),
                new ECItem((uint)EqpVariable.IDReadDuplicateOption, SecsFormat.U1.ToString(), EqpVariable.IDReadDuplicateOption.ToString()),
                new ECItem((uint)EqpVariable.IDReadFailureOption, SecsFormat.U1.ToString(), EqpVariable.IDReadFailureOption.ToString()),
                new ECItem((uint)EqpVariable.IDReadMismatchOption, SecsFormat.U1.ToString(), EqpVariable.IDReadMismatchOption.ToString()),
                new ECItem((uint)EqpVariable.T3TimeOut, SecsFormat.U2.ToString(), EqpVariable.T3TimeOut.ToString()),
                new ECItem((uint)EqpVariable.T5TimeOut, SecsFormat.U2.ToString(), EqpVariable.T5TimeOut.ToString()),
                new ECItem((uint)EqpVariable.T6TimeOut, SecsFormat.U2.ToString(), EqpVariable.T6TimeOut.ToString()),
                new ECItem((uint)EqpVariable.T7TimeOut, SecsFormat.U2.ToString(), EqpVariable.T7TimeOut.ToString()),
                new ECItem((uint)EqpVariable.T8TimeOut, SecsFormat.U2.ToString(), EqpVariable.T8TimeOut.ToString()),
                new ECItem((uint)EqpVariable.RetryCount, SecsFormat.U2.ToString(), EqpVariable.RetryCount.ToString())
            };
#pragma warning restore CA1416 // 验证平台兼容性
#pragma warning restore IDE0028 // 简化集合初始化
            return ecItemList;
        }
        //CEID事件ID
        private List<EventItem> DefineEventItems()
        {
#pragma warning disable IDE0028 // 简化集合初始化
#pragma warning disable CA1416 // 验证平台兼容性
            List<EventItem> eventItemList = new List<EventItem>()
            {
                new EventItem((uint)EqpEvent.SCAutoComplete, EqpEvent.SCAutoComplete.ToString(), true, "Controller received Remote Command and shifted to Auto State"),
                new EventItem((uint)EqpEvent.SCAutoInitialized, EqpEvent.SCAutoInitialized.ToString(), true, "Initialization of Controller was completed and it shifted to SC INIT State"),
                new EventItem((uint)EqpEvent.SCPauseCompleted, EqpEvent.SCPauseCompleted.ToString(), true, "All Carrier operation was completed by Pausing State, and it shifted to Paused State"),
                new EventItem((uint)EqpEvent.SCPaused, EqpEvent.SCPaused.ToString(), true, "All of an end and Carrier stopped and initialization of Controller shifted to Paused State"),
                new EventItem((uint)EqpEvent.SCPausedInitialized, EqpEvent.SCPausedInitialized.ToString(), true, "Pause Command was received and it shifted to Pausing State"),
                new EventItem((uint)EqpEvent.TransferAbortCompleted, EqpEvent.TransferAbortCompleted.ToString(), true, "The abort procedure for the TRANSFER command has completed by the stocker and SC"),
                new EventItem((uint)EqpEvent.TransferAbortFailed, EqpEvent.TransferAbortFailed.ToString(), true, "TRANSFER command cannot be aborted"),
                new EventItem((uint)EqpEvent.TransferAbortInitiated, EqpEvent.TransferAbortInitiated.ToString(), true, "Host sends ABORT command for a specified TRANSFER"),
                new EventItem((uint)EqpEvent.TransferCancelCompleted, EqpEvent.TransferCancelCompleted.ToString(), true, "The cancel procedure for the TRANSFER command has completed by the stocker and the SC"),
                new EventItem((uint)EqpEvent.TransferCancelFailed, EqpEvent.TransferCancelFailed.ToString(), true, "Transport system is unable to cancel the TRANSFER command and it is still"),
                new EventItem((uint)EqpEvent.TransferCancelInitiated, EqpEvent.TransferCancelInitiated.ToString(), true, "Host sends CANCEL command for a specified TRANSFER"),
                new EventItem((uint)EqpEvent.TransferCompleted, EqpEvent.TransferCompleted.ToString(), true, "The TRANSFER command has completed by the stocker and SC"),
                new EventItem((uint)EqpEvent.TransferInitiated, EqpEvent.TransferInitiated.ToString(), true, "Host sends TRANSFER command for a specified TRANSFER"),
                new EventItem((uint)EqpEvent.TransferPaused, EqpEvent.TransferPaused.ToString(), true, " stocker changes into a stop state"),
                new EventItem((uint)EqpEvent.TransferResumed, EqpEvent.TransferResumed.ToString(), true, "resuming Transfer command"),
                new EventItem((uint)EqpEvent.PriorityUpdateCompleted, EqpEvent.PriorityUpdateCompleted.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.PriorityUpdateFailed, EqpEvent.PriorityUpdateFailed.ToString(), false, ""),   //DFK
                new EventItem((uint)EqpEvent.ScanInitiated, EqpEvent.ScanInitiated.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.ScanCompleted, EqpEvent.ScanCompleted.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.UnitAlarmCleared, EqpEvent.UnitAlarmCleared.ToString(), true, "unit alarm is cleared"),
                new EventItem((uint)EqpEvent.UnitAlarmSet, EqpEvent.UnitAlarmSet.ToString(), true, "unit alarm is generated"),
                new EventItem((uint)EqpEvent.CarrierInstallCompleted, EqpEvent.CarrierInstallCompleted.ToString(), true, "Carrier entry is created or modified in the SC database."),
                new EventItem((uint)EqpEvent.CarrierRemoveCompleted, EqpEvent.CarrierRemoveCompleted.ToString(), true, "Carrier entry is removed from the SC database."),
                new EventItem((uint)EqpEvent.CarrierRemoved, EqpEvent.CarrierRemoved.ToString(), true, "Carrier is removed form the stocker domain(removed from the output LP)"),
                new EventItem((uint)EqpEvent.CarrierResumed, EqpEvent.CarrierResumed.ToString(), true, "The port becomes available and transfer command is first in queue"), //? Carrier go on Transfer at after stored alt
                new EventItem((uint)EqpEvent.CarrierStored, EqpEvent.CarrierStored.ToString(), true, "Completion of a TRANSFER command with a DEST of internal stocker storage"),
                new EventItem((uint)EqpEvent.CarrierStoredAlt, EqpEvent.CarrierStoredAlt.ToString(), true, "The destination of the move command is occupied"),
                new EventItem((uint)EqpEvent.CarrierTransferring, EqpEvent.CarrierTransferring.ToString(), true, "Stocker is executing a TRANSFER command for the carrier"),
                new EventItem((uint)EqpEvent.CarrierWaitIn, EqpEvent.CarrierWaitIn.ToString(), true, "Carrier is registered into stocker"),
                new EventItem((uint)EqpEvent.CarrierWaitOut, EqpEvent.CarrierWaitOut.ToString(), true, "Carrier has arrived at the output port"),
                new EventItem((uint)EqpEvent.ZoneCapacityChange, EqpEvent.ZoneCapacityChange.ToString(), true, "Zone Capacity was Changed"),
                new EventItem((uint)EqpEvent.CraneActive, EqpEvent.CraneActive.ToString(), true, "The stocker crane is busy performing Host or SC initiated work"),
                new EventItem((uint)EqpEvent.CraneIdle, EqpEvent.CraneIdle.ToString(), true, "The stocker crane is not performing Host or SC initiated work"),
                new EventItem((uint)EqpEvent.CraneOutOfService, EqpEvent.CraneOutOfService.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.CraneInService, EqpEvent.CraneInService.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.ForkingStarted, EqpEvent.ForkingStarted.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.ForkingCompleted, EqpEvent.ForkingCompleted.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.ForkRised, EqpEvent.ForkRised.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.ForkDowned, EqpEvent.ForkDowned.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.CraneArrived, EqpEvent.CraneArrived.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.CarrierIDRead, EqpEvent.CarrierIDRead.ToString(), true, "Reading of Carrier-ID was completed"),
                new EventItem((uint)EqpEvent.CarrierLocateCompleted, EqpEvent.CarrierLocateCompleted.ToString(), true, "Carrier location is reported to LOCATE Remote Command"),
                new EventItem((uint)EqpEvent.IDReaderError, EqpEvent.IDReaderError.ToString(), true, "Reading of Carrier-ID went wrong"),
                new EventItem((uint)EqpEvent.OperatorInitiatedAction, EqpEvent.OperatorInitiatedAction.ToString(), true, "It is reported when an operator performs transfer operation"),
                new EventItem((uint)EqpEvent.PortOutofService, EqpEvent.PortOutofService.ToString(), true, "Transfer to/from this port is disabled and the port should not be used in any Transfer command issued by the host"),
                new EventItem((uint)EqpEvent.PortInService, EqpEvent.PortInService.ToString(), true, "Transfer to/from this port enabled"),
                new EventItem((uint)EqpEvent.PortTypeInput, EqpEvent.PortTypeInput.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.PortTypeOutput, EqpEvent.PortTypeOutput.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.PortTypeChanging, EqpEvent.PortTypeChanging.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.EqNoRequest, EqpEvent.EqNoRequest.ToString(), true, "Load request from Eq port and Unload request turn off"),
                new EventItem((uint)EqpEvent.EqLoadRequest, EqpEvent.EqLoadRequest.ToString(), true, "Load request from Eq port turns on"),
                new EventItem((uint)EqpEvent.EqUnloadRequest, EqpEvent.EqUnloadRequest.ToString(), true, "Unload request from Eq port turns on"),
                new EventItem((uint)EqpEvent.EqPresence, EqpEvent.EqPresence.ToString(), true, "Carrier presence in Eq port"),
                new EventItem((uint)EqpEvent.EqNoPresence, EqpEvent.EqNoPresence.ToString(), true, "Carrier disappears from Eq port"),
                new EventItem((uint)EqpEvent.OperatorChangeZoneData, EqpEvent.OperatorChangeZoneData.ToString(), true, "The data of a zone changed when an operator changed a zone"),
                new EventItem((uint)EqpEvent.SCState, EqpEvent.SCState.ToString(), true, "SC Status Changed"),
                new EventItem((uint)EqpEvent.PortAutoMode, EqpEvent.PortAutoMode.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.PortManualMode, EqpEvent.PortManualMode.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.PortModeChanging, EqpEvent.PortModeChanging.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.ZoneShelfStateChanged, EqpEvent.ZoneShelfStateChanged.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.CassetteStockerStateChange, EqpEvent.CassetteStockerStateChange.ToString(), false, ""), //DFK
                new EventItem((uint)EqpEvent.ShelfStateChanged, EqpEvent.ShelfStateChanged.ToString(), true, "disable or enable Unit"),
                new EventItem((uint)EqpEvent.PortTypeChange, EqpEvent.PortTypeChange.ToString(), true, "changing port directions"),
            };
#pragma warning restore CA1416 // 验证平台兼容性
#pragma warning restore IDE0028 // 简化集合初始化

            return eventItemList;
        }
        //ALID报警编号
        private List<AlarmItem> DefineAlarmItems()
        {
            lock(GlobalData.Instance.objRWLock)
            {
                //数据库中加载Alarm定义信息
                GlobalData.Instance.gbAlarmItems.Clear();
                try
                {
                    //应该在Alarm定义表中加载Alarm定义
                    TpAlarmConfigList tpAlarmList = TpAlarmConfigList.GetAll();
                    foreach (TpAlarmConfig tpAlarm in tpAlarmList)
                    {
                        AlarmItem alarmItem = new AlarmItem((uint)tpAlarm.Code, tpAlarm.Text, (int)tpAlarm.Enabled == 1);
                        GlobalData.Instance.gbAlarmItems.Add(alarmItem);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.ExceptionLog("AlarmController.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                }
            }
            return GlobalData.Instance.gbAlarmItems;
        }

        public async void InitializeAsync()
        {
            await InitializeDefaultVariablesAsync();

            #region 以前的代码
            /*
            this.MDLN = "AMHS";
            //this.SoftRev = "V1.00";

            GemResult gemResult = GemResult.NotImplemented;

            List<DVItem> dvList = this.DefineDVItems();
            if (dvList == null || dvList.Count == 0)
            {
                //LogWriter.Instance.ExceptionLog($"GemEquipment DefineDVItems failed: {gemResult}");
                return GemResult.ReadEquipmentValError;
            }
            gemResult = LoadDV(dvList);
            if (gemResult != GemResult.Success)
            {
                //LogWriter.Instance.ExceptionLog($"GemEquipment LoadDV failed: {gemResult}");
                return gemResult;
            }

            List<SVItem> svList = DefineSVItems();
            if (svList == null || svList.Count == 0)
            {
                //LogWriter.Instance.ExceptionLog($"GemEquipment DefineSVItems failed: {gemResult}");
                return GemResult.ReadEquipmentValError;
            }
            gemResult = this.LoadSV(svList);
            if (gemResult != GemResult.Success)
            {
                //LogWriter.Instance.ExceptionLog($"GemEquipment LoadSV failed: {gemResult}");
                return gemResult;
            }

            List<ECItem> ecList = DefineECItems();
            if (ecList == null || ecList.Count == 0)
            {
                //LogWriter.Instance.ExceptionLog($"GemEquipment DefineECItems failed: {gemResult}");
                return GemResult.ReadEquipmentValError;
            }
            gemResult = LoadEC(ecList);
            if (gemResult != GemResult.Success)
            {
                //LogWriter.Instance.ExceptionLog($"GemEquipment LoadEC failed: {gemResult}");
                return gemResult;
            }

            ////禁用GEM中预定义Event
            //EnableEvent((uint)GemEvent.EquipmentOffline, false);
            //EnableEvent((uint)GemEvent.ControlStateToLocal, false);
            //EnableEvent((uint)GemEvent.ControlStateToRemote, false);
            //EnableEvent((uint)GemEvent.AlarmCleared, false);
            //EnableEvent((uint)GemEvent.AlarmSet, false);
            //EnableEvent((uint)GemEvent.PPChanged, false);

            //加载GEM库中预定义Event
            string strEventCsvPath = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetEntryAssembly().Location)
                + "\\config\\event_def.csv";
            gemResult = LoadEvent(strEventCsvPath);

            //加载自定义Event
            List<EventItem> eventList = DefineEventItems();
            gemResult = LoadEvent(eventList);

            //Report中添加VID
            ReportAppendVID((uint)EqpReport.Report1, (uint)EqpVariable.EqpName);

            //Link初始Event
            LinkEventReport((uint)EqpEvent.EquipmentOffLine, (uint)EqpReport.Report1);
            LinkEventReport((uint)EqpEvent.ControlStatusLocal, (uint)EqpReport.Report1);
            LinkEventReport((uint)EqpEvent.ControlStatusRemote, (uint)EqpReport.Report1);
            LinkEventReport((uint)EqpEvent.SCAutoInitialized, (uint)EqpReport.Report1);
            LinkEventReport((uint)EqpEvent.SCPaused, (uint)EqpReport.Report1);

            //加载默认Alarm列表
            List<AlarmItem> alarmList = DefineAlarmItems();
            gemResult = LoadAlarm(alarmList);

            return GemResult.Success; 
            */
            #endregion
        }
        
        /// <summary>
        /// Communication Requiest, S1F13,host 建立连接
        /// </summary>
        public override async Task<bool> OnCommEstablishedAsync()
        {
            Enum.TryParse(base.CommunicationState.ToString(), out GlobalData.Instance.gbMCSCommunication);
            if (EventStateChange != null)
            {
                EventStateChange("MCS Communication", base.CommunicationState.ToString());
            }
            return await base.OnCommEstablishedAsync();
        }

        /// <summary>
        /// 通信失败，进入NotCommunicating状态状态
        /// </summary>
        public override async Task OnCommDisabledAsync()
        {
            Enum.TryParse(base.CommunicationState.ToString(), out GlobalData.Instance.gbMCSCommunication);
            GlobalData.Instance.gbMCSCommunication = MCSCommunication.NotCommunicating;
            if (EventStateChange != null)
            {
                EventStateChange("MCS Communication", base.CommunicationState.ToString());
            }
            await base.OnCommDisabledAsync();
        }

        /// <summary>
        /// 接收到来自MCS的S1F17 OnlineRequest,回复ONLACK=2?1
        /// </summary>
        public override async Task<bool> OnHostRequestOnlineAsync()
        {
            return await base.OnHostRequestOnlineAsync();
        }

        public override async Task OnSwitchOfflineAsync()
        {
            Enum.TryParse(base.ControlState.ToString(), out GlobalData.Instance.gbControlState);
            if (EventStateChange != null)
            {
                EventStateChange("Control State", base.ControlState.ToString());
            }
            await base.OnSwitchOfflineAsync();
        }

        public override async Task OnSwitchLocalAsync()
        {
            Enum.TryParse(base.ControlState.ToString(), out GlobalData.Instance.gbControlState);
            if (EventStateChange != null)
            {
                EventStateChange("Control State", base.ControlState.ToString());
            }
            if (bIsFirstLink)
            {
                bIsFirstLink = false;
                Thread OnlineInitThread = new Thread(OnlineInitFunction);
                OnlineInitThread.Start();
            }

            await base.OnSwitchLocalAsync();
        }

        public override async Task OnSwitchRemoteAsync()
        {
            Enum.TryParse(base.ControlState.ToString(), out GlobalData.Instance.gbControlState);
            if (EventStateChange != null)
            {
                EventStateChange("Control State", base.ControlState.ToString());
            }

            if (bIsFirstLink)
            {
                bIsFirstLink = false;
                Thread OnlineInitThread = new Thread(OnlineInitFunction);
                OnlineInitThread.Start();
            }
            await base.OnSwitchRemoteAsync();
        }

        private void OnlineInitFunction()
        {
            Thread.Sleep(50);
            SendDateTimeRequest();
            PostScEvent(EqpEvent.SCAutoInitialized);
            GlobalData.Instance.gbSCState = SCState.Paused;
            if (EventStateChange != null)
            {
                EventStateChange("SC State", GlobalData.Instance.gbSCState.ToString());
            }
            
            PostScEvent(EqpEvent.SCPaused);
        }

        public override async Task<Item> OnGetDVAsync(uint vid)
        {
            await Task.CompletedTask;
            var secsItem = GetVariableValue(vid);
            if (secsItem != null)
            {
                return secsItem.ToSecs4NetItem();
            }
            else
            {
                return Item.L();
            }
        }

        public override async Task<Item> OnGetSVAsync(uint svid)
        {
            await Task.CompletedTask;
            var secsItem = this.GetVariableValue(svid);
            if (secsItem != null)
            {
                return secsItem.ToSecs4NetItem();
            }
            else
            {
                return Item.L();
            }
        }

        public override async Task<Item> OnGetECAsync(uint ecid)
        {
            await Task.CompletedTask;
            var secsItem = GetVariableValue(ecid);
            if (secsItem != null)
            {
                return secsItem.ToSecs4NetItem();
            }
            else
            {
                return Item.L();
            }
        }

        public override async Task<bool> OnSetECAsync(uint ecid, Item value)
        {
            await Task.CompletedTask;
            //设置EC值
            return true;
        }

        public override bool OnConnect()
        {
            GlobalData.Instance.gbHSMSState = HSMSState.Connected;
            if (EventStateChange != null)
            {
                EventStateChange("HSMS State", HSMSState.Connected.ToString());
            }
            return base.OnConnect();
        }

        public override void OnDisconnect()
        {
            GlobalData.Instance.gbHSMSState = HSMSState.NotConnected;
            if (EventStateChange != null)
            {
                EventStateChange("HSMS State", HSMSState.NotConnected.ToString());
            }
            base.OnDisconnect();
        }

        public override async Task<Item> OnHostCommandAsync(string command, Item parameters)
        {
            await Task.CompletedTask;
            HCACK res = HCACK.CommandNotExist;
            try
            {
                if (this.EventHostCommand != null)
                {
                    // 将 Item 转换为 SecsItem 以兼容原有逻辑
                    var param = new SecsItem(parameters);
                    int iCount = param.ItemList.Count;
                    Dictionary<string, object> paramDic = new Dictionary<string, object>();
                    if (iCount != 0)
                    {
                        for (int i = 0; i < iCount; i++)
                        {
                            if(param.ItemList[i].ItemList != null && param.ItemList[i].ItemList.Count > 0)
                            {
                                foreach(SecsItem subItem in param.ItemList[i].ItemList)
                                {
                                    if (subItem.ItemList != null && subItem.ItemList.Count > 0)
                                    {
                                        paramDic.Add(subItem.ItemList[0].Value?.ToString() ?? "", subItem.ItemList[1].Value);
                                    }
                                }
                            }
                        }
                    }
                    paramDic.Add("COMMANDSOURCE", 0);  //指令来源：MCS
                    res = EventHostCommand(command, paramDic);
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("HostIF.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            // 返回 HCACK 结果
            return Item.U1((byte)res);
        }
        //S2F49TRANSFER
        public override async Task<Item> OnHostEnhancedCommandAsync(string command, string objType, Item parameters)
        {
            await Task.CompletedTask;
            HCACK res = HCACK.CommandNotExist;
            try
            {
                if (this.EventHostCommand != null)
                {
                    // 将 Item 转换为 SecsItem 以兼容原有逻辑
                    var param = new SecsItem(parameters);
                    Dictionary<string, object> paramDic = new Dictionary<string, object>();
                    //目前只处理固定格式的Transfer指令
                    //CommandInfo
                    paramDic.Add(param.ItemList[0].ItemList[1].ItemList[0].ItemList[0].Value?.ToString() ?? "", //COMMANDID
                                 param.ItemList[0].ItemList[1].ItemList[0].ItemList[1].Value); //Command-ID
                    paramDic.Add(param.ItemList[0].ItemList[1].ItemList[1].ItemList[0].Value?.ToString() ?? "", //PRIORITY
                                 param.ItemList[0].ItemList[1].ItemList[1].ItemList[1].Value); //Priority

                    //TransferInfo
                    paramDic.Add(param.ItemList[1].ItemList[1].ItemList[0].ItemList[0].Value?.ToString() ?? "", //CARRIERID
                                 param.ItemList[1].ItemList[1].ItemList[0].ItemList[1].Value); //Carrier-ID
                    paramDic.Add(param.ItemList[1].ItemList[1].ItemList[1].ItemList[0].Value?.ToString() ?? "", //SOURCE
                                 param.ItemList[1].ItemList[1].ItemList[1].ItemList[1].Value); //Transfer Source ID
                    paramDic.Add(param.ItemList[1].ItemList[1].ItemList[2].ItemList[0].Value?.ToString() ?? "", //DEST
                                 param.ItemList[1].ItemList[1].ItemList[2].ItemList[1].Value); //Transfer Destination  ID

                    paramDic.Add("COMMANDSOURCE", (int)CommandSource.MCS);  //指令来源：MCS
                    res = EventHostCommand(command, paramDic);
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("HostIF.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            // 返回 HCACK 结果
            return Item.U1((byte)res);
        }
        //经过S1F13后FSMCommunicationState.State.Communicating状态收到S2F31回复S2F32success
        // Online RequestS1F17也一样，包括此状态后面的交互都在processMessage里面实现了
        public override async Task<SecsMessage> OnReceiveMessageAsync(SecsMessage message)
        {
            await Task.CompletedTask;
            if (message.S == 2 && message.F == 18)
            {
                return new SecsMessage(2, 18) { SecsItem = Item.L() };
            }
            //ID READ FAILURE & DUPLICATE
            if (message.S == 2 && message.F == 65)
            {
                int retCode = 0;
                return new SecsMessage(2, 66) { SecsItem = Item.U1((byte)retCode) };
            }
            else
            {
                //ONLACK==1 send S1F1
                bool onlineResult = await OnHostRequestOnlineAsync();
                if (message.S == 1 && message.F == 17 && onlineResult)
                {
                    // 发送 S1F1 消息
                    try
                    {
                        await SendAsync(new SecsMessage(1, 1) { SecsItem = Item.L() });
                    }
                    catch (Exception ex)
                    {
                        Log.Logger.Instance.ExceptionLog("HostIF.cs SendAreYouThereRequest: " + ex.Message);
                    }
                }
                return await base.OnReceiveMessageAsync(message);
            }
        }

        private SecsItem GetVariableValue(uint vid)
        {
            SecsItem variableItem = null;
            switch ((EqpVariable)vid)
            {
                case EqpVariable.Clock:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbClock;
                    }
                    break;
                case EqpVariable.AlarmEnabled:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (uint alarmID in GlobalData.Instance.gbAlarmEnabled)
                        {
                            SecsItem alarmIDItem = new SecsItem(SecsFormat.U2);
                            alarmIDItem.Value = alarmID;
                            variableItem.AppendItem(alarmIDItem);
                        }
                    }
                    break;
                case EqpVariable.AlarmSet:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        GlobalData.Instance.gbAlarmSet.Clear();
                        List<AlarmItem> alarmItems = new List<AlarmItem>();
                        try
                        {
                            TpAlarmList tpAlarmList = TpAlarmList.GetAll();
                            foreach (TpAlarm tpAlarm in tpAlarmList)
                            {
                                TpPort portInfo = TpPort.GetByLambda(x => x.Name == tpAlarm.Unit);
                                if (portInfo == null || portInfo.Name == "")  //报警单元不是PORT
                                {
                                    GlobalData.Instance.gbAlarmSet.Add(Convert.ToUInt32(tpAlarm.Code));
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.ExceptionLog("HostIF.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                        }
                        foreach (uint alarmID in GlobalData.Instance.gbAlarmSet)
                        {
                            SecsItem alarmIDItem = new SecsItem(SecsFormat.U2);
                            alarmIDItem.Value = alarmID;
                            variableItem.AppendItem(alarmIDItem);
                        }
                    }
                    break;
                case EqpVariable.EventEnabled:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (uint ceidID in GlobalData.Instance.gbEventEnabled)
                        {
                            SecsItem alarmIDItem = new SecsItem(SecsFormat.U2);
                            alarmIDItem.Value = ceidID;
                            variableItem.AppendItem(alarmIDItem);
                        }
                    }
                    break;
                case EqpVariable.SpecVersion:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbSpecVersion;
                    }
                    break;
                case EqpVariable.ControlState:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbControlState;
                    }
                    break;
                case EqpVariable.CassetteStockerState:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbCassetteStockerState;
                    }
                    break;
                case EqpVariable.SCState:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbSCState;
                    }
                    break;
                case EqpVariable.CurrentPortStates:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (PortInfo portInfo in GlobalData.Instance.gbCurrentPortStates)
                        {
                            SecsItem portInfoItem = new SecsItem(SecsFormat.List);

                            SecsItem portIDItem = new SecsItem(SecsFormat.Ascii);
                            portIDItem.Value = portInfo.strPortID;
                            SecsItem portTransferStateItem = new SecsItem(SecsFormat.U2);
                            portTransferStateItem.Value = (uint)portInfo.transferState;

                            portInfoItem.AppendItem(portIDItem);
                            portInfoItem.AppendItem(portTransferStateItem);

                            variableItem.AppendItem(portInfoItem);
                        }
                    }
                    break;
                case EqpVariable.PortTypes:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (PortTypeInfo portTypeInfo in GlobalData.Instance.gbPortTypes)
                        {
                            SecsItem portTypeInfoItem = new SecsItem(SecsFormat.List);

                            SecsItem portIDItem = new SecsItem(SecsFormat.Ascii);
                            portIDItem.Value = portTypeInfo.strPortID;
                            SecsItem portUnitTypeItem = new SecsItem(SecsFormat.U2);
                            portUnitTypeItem.Value = (uint)portTypeInfo.portUnitType;
                            portTypeInfoItem.AppendItem(portIDItem);
                            portTypeInfoItem.AppendItem(portUnitTypeItem);

                            variableItem.AppendItem(portTypeInfoItem);
                        }
                    }
                    break;
                case EqpVariable.PortTypeInfo_i:
                    {
                        variableItem = new SecsItem(SecsFormat.List);

                        SecsItem portIDItem = new SecsItem(SecsFormat.Ascii);
                        portIDItem.Value = GlobalData.Instance.gbPortTypeInfo_i.strPortID;
                        SecsItem portUnitTypeItem = new SecsItem(SecsFormat.U2);
                        portUnitTypeItem.Value = (uint)GlobalData.Instance.gbPortTypeInfo_i.portUnitType;

                        variableItem.AppendItem(portIDItem);
                        variableItem.AppendItem(portUnitTypeItem);
                    }
                    break;
                case EqpVariable.PortUnitType:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbPortUnitType;
                    }
                    break;
                case EqpVariable.ActiveZones:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (ZoneData zoneData in GlobalData.Instance.gbActiveZones)
                        {
                            SecsItem zoneDataItem = new SecsItem(SecsFormat.List);

                            SecsItem zoneNameItem = new SecsItem(SecsFormat.Ascii);
                            zoneNameItem.Value = zoneData.strZoneName;
                            SecsItem zoneCapacity = new SecsItem(SecsFormat.U2);
                            zoneCapacity.Value = zoneData.u2ZoneCapacity;
                            zoneDataItem.AppendItem(zoneNameItem);
                            zoneDataItem.AppendItem(zoneCapacity);

                            variableItem.AppendItem(zoneDataItem);
                        }
                    }
                    break;
                case EqpVariable.EnhancedActiveZones:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (EnhancedZoneData enhancedZoneData in GlobalData.Instance.gbEnhancedActiveZones)
                        {
                            SecsItem enhancedZoneDataItem = new SecsItem(SecsFormat.List);

                            SecsItem zoneNameItem = new SecsItem(SecsFormat.Ascii);
                            zoneNameItem.Value = enhancedZoneData.strZoneName;
                            SecsItem zoneCapacityItem = new SecsItem(SecsFormat.U2);
                            zoneCapacityItem.Value = enhancedZoneData.u2ZoneCapacity;
                            SecsItem zoneSizeItem = new SecsItem(SecsFormat.U2);
                            zoneSizeItem.Value = enhancedZoneData.u2ZoneSize;
                            SecsItem zoneTypeItem = new SecsItem(SecsFormat.U2);
                            zoneTypeItem.Value = (uint)enhancedZoneData.zoneType;

                            enhancedZoneDataItem.AppendItem(zoneNameItem);
                            enhancedZoneDataItem.AppendItem(zoneCapacityItem);
                            enhancedZoneDataItem.AppendItem(zoneSizeItem);
                            enhancedZoneDataItem.AppendItem(zoneTypeItem);

                            variableItem.AppendItem(enhancedZoneDataItem);
                        }
                    }
                    break;
                case EqpVariable.ExtendedActiveZones:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (ExtendedZoneData extendedZoneData in GlobalData.Instance.gbExtendedActiveZones)
                        {
                            SecsItem extendedZoneDataItem = new SecsItem(SecsFormat.List);

                            SecsItem zoneNameItem = new SecsItem(SecsFormat.Ascii);
                            zoneNameItem.Value = extendedZoneData.strZoneName;
                            SecsItem zoneCapacityItem = new SecsItem(SecsFormat.U2);
                            zoneCapacityItem.Value = extendedZoneData.u2ZoneCapacity;
                            SecsItem zoneSizeItem = new SecsItem(SecsFormat.U2);
                            zoneSizeItem.Value = extendedZoneData.u2ZoneTotalSize;
                            SecsItem zoneTypeItem = new SecsItem(SecsFormat.U2);
                            zoneTypeItem.Value = (uint)extendedZoneData.zoneType;

                            SecsItem DisabledLocsItemList = new SecsItem(SecsFormat.List);
                            foreach (DisabledLoc disabledLoc in extendedZoneData.disableLocations)
                            {
                                SecsItem DisabledLocsItem = new SecsItem(SecsFormat.List);
                                SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                                carrierLocItem.Value = disabledLoc.strCarrierLoc;
                                SecsItem carrierIdItem = new SecsItem(SecsFormat.Ascii);
                                carrierIdItem.Value = disabledLoc.strCarrierId;
                                DisabledLocsItem.AppendItem(carrierLocItem);
                                DisabledLocsItem.AppendItem(carrierIdItem);
                                DisabledLocsItemList.AppendItem(DisabledLocsItem);
                            }

                            extendedZoneDataItem.AppendItem(zoneNameItem);
                            extendedZoneDataItem.AppendItem(zoneCapacityItem);
                            extendedZoneDataItem.AppendItem(zoneSizeItem);
                            extendedZoneDataItem.AppendItem(zoneTypeItem);
                            extendedZoneDataItem.AppendItem(DisabledLocsItemList);

                            variableItem.AppendItem(extendedZoneDataItem);
                        }
                    }
                    break;
                case EqpVariable.ExtendedZoneData:
                    {
                        variableItem = new SecsItem(SecsFormat.List);

                        SecsItem extendedZoneDataItem = new SecsItem(SecsFormat.List);

                        SecsItem zoneNameItem = new SecsItem(SecsFormat.Ascii);
                        zoneNameItem.Value = GlobalData.Instance.gbExtendedZoneData.strZoneName;
                        SecsItem zoneCapacityItem = new SecsItem(SecsFormat.U2);
                        zoneCapacityItem.Value = GlobalData.Instance.gbExtendedZoneData.u2ZoneCapacity;
                        SecsItem zoneSizeItem = new SecsItem(SecsFormat.U2);
                        zoneSizeItem.Value = GlobalData.Instance.gbExtendedZoneData.u2ZoneTotalSize;
                        SecsItem zoneTypeItem = new SecsItem(SecsFormat.U2);
                        zoneTypeItem.Value = (uint)GlobalData.Instance.gbExtendedZoneData.zoneType;

                        SecsItem DisabledLocsItem = new SecsItem(SecsFormat.List);
                        foreach (DisabledLoc disabledLoc in GlobalData.Instance.gbExtendedZoneData.disableLocations)
                        {
                            SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                            carrierLocItem.Value = disabledLoc.strCarrierLoc;
                            SecsItem carrierIdItem = new SecsItem(SecsFormat.Ascii);
                            carrierIdItem.Value = disabledLoc.strCarrierId;

                            DisabledLocsItem.AppendItem(carrierLocItem);
                            DisabledLocsItem.AppendItem(carrierIdItem);
                        }

                        extendedZoneDataItem.AppendItem(zoneNameItem);
                        extendedZoneDataItem.AppendItem(zoneCapacityItem);
                        extendedZoneDataItem.AppendItem(zoneSizeItem);
                        extendedZoneDataItem.AppendItem(zoneTypeItem);
                        extendedZoneDataItem.AppendItem(DisabledLocsItem);

                        variableItem.AppendItem(extendedZoneDataItem);
                    }
                    break;
                case EqpVariable.AllEnhancedDisableLocations:
                    {
                        variableItem = new SecsItem(SecsFormat.List);

                        TpLocationList tpLocationList = TpLocationList.GetByLambda(x => x.ZoneName == "Shelf" && x.IsProhibited == 1);
                        foreach (TpLocation tpLocation in tpLocationList)
                        {
                            SecsItem DisabledLocsItem = new SecsItem(SecsFormat.List);
                            SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                            carrierLocItem.Value = tpLocation.Address;
                            SecsItem carrierIdItem = new SecsItem(SecsFormat.Ascii);
                            carrierIdItem.Value = tpLocation.CarrierId;
                            DisabledLocsItem.AppendItem(carrierLocItem);
                            DisabledLocsItem.AppendItem(carrierIdItem);

                            variableItem.AppendItem(DisabledLocsItem);
                        }
                    }
                    break;
                case EqpVariable.CraneOperationInformation:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (CraneOperationInfo craneOperationInfo in GlobalData.Instance.gbCraneOperationInformation)
                        {
                            SecsItem craneOperationInfoItem = new SecsItem(SecsFormat.List);
                            SecsItem stockerCraneIDItem = new SecsItem(SecsFormat.Ascii);
                            stockerCraneIDItem.Value = craneOperationInfo.strStockerCraneID;
                            SecsItem craneOperationStateItem = new SecsItem(SecsFormat.U2);
                            craneOperationStateItem.Value = (uint)craneOperationInfo.craneOperationState;

                            craneOperationInfoItem.AppendItem(stockerCraneIDItem);
                            craneOperationInfoItem.AppendItem(craneOperationStateItem);

                            variableItem.AppendItem(craneOperationInfoItem);
                        }
                    }
                    break;
                case EqpVariable.CraneModeStateInformation:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (CraneModeStateInfo craneModeStateInfo in GlobalData.Instance.gbCraneModeStateInformation)
                        {
                            SecsItem craneModeStateInfoItem = new SecsItem(SecsFormat.List);
                            SecsItem stockerCraneIDItem = new SecsItem(SecsFormat.Ascii);
                            stockerCraneIDItem.Value = craneModeStateInfo.strStockerCraneID;
                            SecsItem craneModeState = new SecsItem(SecsFormat.U2);
                            craneModeState.Value = (uint)craneModeStateInfo.craneModeState;

                            craneModeStateInfoItem.AppendItem(stockerCraneIDItem);
                            craneModeStateInfoItem.AppendItem(craneModeState);

                            variableItem.AppendItem(craneModeStateInfoItem);
                        }
                    }
                    break;
                case EqpVariable.ActiveCarriers:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (CarrierInfo carrierInfo in GlobalData.Instance.gbActiveCarriers)
                        {
                            SecsItem carrierInfoItem = new SecsItem(SecsFormat.List);

                            SecsItem carrierIDItem = new SecsItem(SecsFormat.Ascii);
                            carrierIDItem.Value = carrierInfo.strCarrierID;
                            SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                            carrierLocItem.Value = carrierInfo.strCarrierLoc;
                            carrierIDItem.AppendItem(carrierIDItem);
                            carrierIDItem.AppendItem(carrierLocItem);

                            variableItem.AppendItem(carrierIDItem);
                        }
                    }
                    break;
                case EqpVariable.EnhancedCarriers:
                    {
                        //同步数据库与缓存信息
                        lock (GlobalData.Instance.objRWLock)
                        {
                            TpCarrierViewList carrierList = DbCarrier.Instance.GetDbCarrierList();
                            GlobalData.Instance.gbEnhancedCarriers.Clear();
                            foreach (TpCarrierView carrier in carrierList)
                            {
                                EnhancedCarrierInfo enCarrier = new EnhancedCarrierInfo();
                                enCarrier.strCarrierID = carrier.Id;
                                enCarrier.strCarrierLoc = carrier.Location;
                                if (carrier.LocName == null || carrier.LocName == "")
                                {
                                    TpCarrierList.DeleteByCriteria(x => x.Id == carrier.Id);
                                }
                                enCarrier.strLocationName = carrier.LocName;
                                enCarrier.strCarrierZoneName = carrier.ZoneName;
                                enCarrier.strInstallTime = carrier.InstallTime?.ToString("yyyyMMddHHmmssff");
                                enCarrier.carrierState = (CarrierState)uint.Parse(carrier.State);
                                enCarrier.locType = (LocationType)Convert.ToInt32(carrier.Type);
                                GlobalData.Instance.gbEnhancedCarriers.Add(enCarrier);
                            }
                        }

                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (EnhancedCarrierInfo enhancedCarrierInfo in GlobalData.Instance.gbEnhancedCarriers)
                        {
                            SecsItem enhancedCarrierInfoItem = new SecsItem(SecsFormat.List);

                            SecsItem carrierIDItem = new SecsItem(SecsFormat.Ascii);
                            carrierIDItem.Value = enhancedCarrierInfo.strCarrierID;
                            SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                            if(enhancedCarrierInfo.locType == LocationType.Shelf)
                            {
                                carrierLocItem.Value = enhancedCarrierInfo.strCarrierLoc;
                            }
                            else
                            {
                                carrierLocItem.Value = enhancedCarrierInfo.strCarrierZoneName;
                            }
                            SecsItem carrierZoneNameItem = new SecsItem(SecsFormat.Ascii);
                            carrierZoneNameItem.Value = enhancedCarrierInfo.strCarrierZoneName;
                            SecsItem installTimeItem = new SecsItem(SecsFormat.Ascii);
                            installTimeItem.Value = enhancedCarrierInfo.strInstallTime;
                            SecsItem carrierStateItem = new SecsItem(SecsFormat.U2);
                            carrierStateItem.Value = (uint)enhancedCarrierInfo.carrierState;

                            enhancedCarrierInfoItem.AppendItem(carrierIDItem);
                            enhancedCarrierInfoItem.AppendItem(carrierLocItem);
                            enhancedCarrierInfoItem.AppendItem(carrierZoneNameItem);
                            enhancedCarrierInfoItem.AppendItem(installTimeItem);
                            enhancedCarrierInfoItem.AppendItem(carrierStateItem);

                            variableItem.AppendItem(enhancedCarrierInfoItem);
                        }
                    }
                    break;
                case EqpVariable.ActiveTransfers:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (TransferCommand transferCommand in GlobalData.Instance.gbActiveTransfers)
                        {
                            SecsItem transferCommandItem = new SecsItem(SecsFormat.List);

                            SecsItem commandInfoItem = new SecsItem(SecsFormat.List);
                            SecsItem transferInfoItem = new SecsItem(SecsFormat.List);

                            SecsItem commandIDItem = new SecsItem(SecsFormat.Ascii);
                            commandIDItem.Value = transferCommand.commandInfo.strCommandID;
                            SecsItem priorityItem = new SecsItem(SecsFormat.U2);
                            priorityItem.Value = transferCommand.commandInfo.u2Priority;
                            commandInfoItem.AppendItem(commandIDItem);
                            commandInfoItem.AppendItem(priorityItem);

                            SecsItem carrierIDItem = new SecsItem(SecsFormat.Ascii);
                            carrierIDItem.Value = transferCommand.transferInfo.strCarrierID;
                            SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                            carrierLocItem.Value = transferCommand.transferInfo.strCarrierLoc;
                            SecsItem destItem = new SecsItem(SecsFormat.Ascii);
                            destItem.Value = transferCommand.transferInfo.strDest;
                            //SecsItem stockerCraneIDItem = new SecsItem(SecsFormat.Ascii);
                            //stockerCraneIDItem.Value = transferCommand.transferInfo.strStockerCraneID;
                            transferInfoItem.AppendItem(carrierIDItem);
                            transferInfoItem.AppendItem(carrierLocItem);
                            transferInfoItem.AppendItem(destItem);
                            //transferInfoItem.AppendItem(stockerCraneIDItem);

                            transferCommandItem.AppendItem(commandInfoItem);
                            transferCommandItem.AppendItem(transferInfoItem);

                            variableItem.AppendItem(transferCommandItem);
                        }

                    }
                    break;
                case EqpVariable.EnhancedTransfers:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                        {
                            SecsItem enhancedTransferCommandItem = new SecsItem(SecsFormat.List);

                            SecsItem transferStateItem = new SecsItem(SecsFormat.U2);
                            SecsItem commandInfoItem = new SecsItem(SecsFormat.List);
                            SecsItem transferInfoItem = new SecsItem(SecsFormat.List);

                            transferStateItem.Value = (uint)enhancedTransferCommand.transferState;

                            SecsItem commandIDItem = new SecsItem(SecsFormat.Ascii);
                            commandIDItem.Value = enhancedTransferCommand.strCommandID;
                            SecsItem priorityItem = new SecsItem(SecsFormat.U2);
                            priorityItem.Value = enhancedTransferCommand.u2Priority;
                            commandInfoItem.AppendItem(commandIDItem);
                            commandInfoItem.AppendItem(priorityItem);

                            SecsItem carrierIDItem = new SecsItem(SecsFormat.Ascii);
                            carrierIDItem.Value = enhancedTransferCommand.strCarrierID;
                            SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                            carrierLocItem.Value = enhancedTransferCommand.strSourceZone;
                            SecsItem destItem = new SecsItem(SecsFormat.Ascii);
                            destItem.Value = enhancedTransferCommand.strDestZone;
                            //SecsItem stockerCraneIDItem = new SecsItem(SecsFormat.Ascii);
                            //stockerCraneIDItem.Value = enhancedTransferCommand.strStockerCraneID;

                            transferInfoItem.AppendItem(carrierIDItem);
                            transferInfoItem.AppendItem(carrierLocItem);
                            transferInfoItem.AppendItem(destItem);
                            //transferInfoItem.AppendItem(stockerCraneIDItem);

                            enhancedTransferCommandItem.AppendItem(transferStateItem);
                            enhancedTransferCommandItem.AppendItem(commandInfoItem);
                            enhancedTransferCommandItem.AppendItem(transferInfoItem);

                            variableItem.AppendItem(enhancedTransferCommandItem);
                        }
                    }
                    break;
                case EqpVariable.TransferState:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbTransferState;
                    }
                    break;
                case EqpVariable.CarrierState:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbCarrierState;
                    }
                    break;
                case EqpVariable.PortTransferState_i:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbPortTransferState;
                    }
                    break;
                case EqpVariable.ResultCode:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbResultCode;
                    }
                    break;
                case EqpVariable.IDReadStatus:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbIDReadStatus;
                    }
                    break;
                case EqpVariable.CraneState:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbCraneState;
                    }
                    break;
                case EqpVariable.EqReqStatus:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbEqReqStatus;
                    }
                    break;
                case EqpVariable.EqPresenceStatus:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbEqPresenceStatus;
                    }
                    break;
                case EqpVariable.TransferCommand:
                    {
                        variableItem = new SecsItem(SecsFormat.List);

                        SecsItem commandInfoItem = new SecsItem(SecsFormat.List);
                        SecsItem transferInfoItem = new SecsItem(SecsFormat.List);

                        SecsItem commandIDItem = new SecsItem(SecsFormat.Ascii);
                        commandIDItem.Value = GlobalData.Instance.gbCommandID;
                        SecsItem priorityItem = new SecsItem(SecsFormat.U2);
                        priorityItem.Value = GlobalData.Instance.gbPriority;
                        commandInfoItem.AppendItem(commandIDItem);
                        commandInfoItem.AppendItem(priorityItem);

                        SecsItem carrierIDItem = new SecsItem(SecsFormat.Ascii);
                        carrierIDItem.Value = GlobalData.Instance.gbCarrierID;
                        SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                        carrierLocItem.Value = GlobalData.Instance.gbCarrierLoc;
                        SecsItem destItem = new SecsItem(SecsFormat.Ascii);
                        destItem.Value = GlobalData.Instance.gbDest;
                        //SecsItem stockerCraneIDItem = new SecsItem(SecsFormat.Ascii);
                        //stockerCraneIDItem.Value = GlobalData.Instance.gbTransferCommand.transferInfo.strStockerCraneID;
                        transferInfoItem.AppendItem(carrierIDItem);
                        transferInfoItem.AppendItem(carrierLocItem);
                        transferInfoItem.AppendItem(destItem);
                       //transferInfoItem.AppendItem(stockerCraneIDItem);

                        variableItem.AppendItem(commandInfoItem);
                        variableItem.AppendItem(transferInfoItem);
                    }
                    break;
                case EqpVariable.CommandInfo:
                    {
                        variableItem = new SecsItem(SecsFormat.List);

                        SecsItem commandInfoItem = new SecsItem(SecsFormat.List);

                        SecsItem commandIDItem = new SecsItem(SecsFormat.Ascii);
                        commandIDItem.Value = GlobalData.Instance.gbCommandID;
                        SecsItem priorityItem = new SecsItem(SecsFormat.U2);
                        priorityItem.Value = GlobalData.Instance.gbPriority;
                        commandInfoItem.AppendItem(commandIDItem);
                        commandInfoItem.AppendItem(priorityItem);

                        variableItem.AppendItem(commandIDItem);
                    }
                    break;
                case EqpVariable.TransferInfo:
                    {
                        variableItem = new SecsItem(SecsFormat.List);

                        SecsItem transferInfoItem = new SecsItem(SecsFormat.List);

                        SecsItem carrierIDItem = new SecsItem(SecsFormat.Ascii);
                        carrierIDItem.Value = GlobalData.Instance.gbCarrierID;
                        SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                        carrierLocItem.Value = GlobalData.Instance.gbCarrierLoc;
                        SecsItem destItem = new SecsItem(SecsFormat.Ascii);
                        destItem.Value = GlobalData.Instance.gbDest;
                        //SecsItem stockerCraneIDItem = new SecsItem(SecsFormat.Ascii);
                        //stockerCraneIDItem.Value = GlobalData.Instance.gbTransferCommand.transferInfo.strStockerCraneID;
                        transferInfoItem.AppendItem(carrierIDItem);
                        transferInfoItem.AppendItem(carrierLocItem);
                        transferInfoItem.AppendItem(destItem);
                        //transferInfoItem.AppendItem(stockerCraneIDItem);

                        variableItem.AppendItem(transferInfoItem);
                    }
                    break;
                case EqpVariable.CarrierInfo:
                    {
                        variableItem = new SecsItem(SecsFormat.List);

                        SecsItem carrierInfoItem = new SecsItem(SecsFormat.List);

                        SecsItem carrierIDItem = new SecsItem(SecsFormat.Ascii);
                        carrierIDItem.Value = GlobalData.Instance.gbCarrierInfo.strCarrierID;
                        SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                        carrierLocItem.Value = GlobalData.Instance.gbCarrierInfo.strCarrierLoc;
                        carrierInfoItem.AppendItem(carrierIDItem);
                        carrierInfoItem.AppendItem(carrierLocItem);

                        variableItem.AppendItem(carrierInfoItem);
                    }
                    break;
                case EqpVariable.ZoneData:
                    {
                        variableItem = new SecsItem(SecsFormat.List);

                        SecsItem zoneNameItem = new SecsItem(SecsFormat.Ascii);
                        zoneNameItem.Value = GlobalData.Instance.gbZoneData.strZoneName;

                        SecsItem zoneCapacity = new SecsItem(SecsFormat.U2);
                        zoneCapacity.Value = GlobalData.Instance.gbZoneData.u2ZoneCapacity;
                           
                        variableItem.AppendItem(zoneNameItem);
                        variableItem.AppendItem(zoneCapacity);
                    }
                    break;
                case EqpVariable.EnhancedZoneData:
                    {
                        variableItem = new SecsItem(SecsFormat.List);

                        SecsItem zoneNameItem = new SecsItem(SecsFormat.Ascii);
                        zoneNameItem.Value = GlobalData.Instance.gbEnhancedZoneData.strZoneName;
                        SecsItem zoneCapacityItem = new SecsItem(SecsFormat.U2);
                        zoneCapacityItem.Value = GlobalData.Instance.gbEnhancedZoneData.u2ZoneCapacity;
                        uint uiZoneCapacity = 0;
                        TpLocationList tpLocationList = TpLocationList.GetAll();
                        foreach (TpLocation tpLocation in tpLocationList)
                        {
                            if (tpLocation.ZoneName.Equals(GlobalData.Instance.gbZoneData.strZoneName)
                                && tpLocation.IsOccupied != 1)//没有Foup
                            {
                                uiZoneCapacity += 1;
                            }
                        }
                        if (uiZoneCapacity > 0)
                        {
                            zoneCapacityItem.Value = uiZoneCapacity;
                        }

                        SecsItem zoneSizeItem = new SecsItem(SecsFormat.U2);
                        zoneSizeItem.Value = GlobalData.Instance.gbEnhancedZoneData.u2ZoneSize;
                        SecsItem zoneTypeItem = new SecsItem(SecsFormat.U2);
                        zoneTypeItem.Value = (uint)GlobalData.Instance.gbEnhancedZoneData.zoneType;

                        variableItem.AppendItem(zoneNameItem);
                        variableItem.AppendItem(zoneCapacityItem);
                        variableItem.AppendItem(zoneSizeItem);
                        variableItem.AppendItem(zoneTypeItem);
                    }
                    break;
                case EqpVariable.StockerUnitInfo:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        SecsItem stockerUnitIDItem = new SecsItem(SecsFormat.Ascii);
                        stockerUnitIDItem.Value = GlobalData.Instance.gbStockerUnitID;
                        SecsItem stockerUnitStateItem = new SecsItem(SecsFormat.U2);
                        stockerUnitStateItem.Value = (uint)GlobalData.Instance.gbStockerUnitState;
                        variableItem.AppendItem(stockerUnitIDItem);
                        variableItem.AppendItem(stockerUnitStateItem);
                    }
                    break;
                case EqpVariable.CommandID:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbCommandID;
                    }
                    break;
                case EqpVariable.CommandType:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbCommandType;
                    }
                    break;
                case EqpVariable.Priority:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = GlobalData.Instance.gbPriority;
                    }
                    break;
                case EqpVariable.CarrierID:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbCarrierID;
                    }
                    break;
                case EqpVariable.CarrierLoc:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbCarrierLoc;
                    }
                    break;
                case EqpVariable.Dest:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbDest;
                    }
                    break;
                case EqpVariable.Source:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbSource;
                    }
                    break;
                case EqpVariable.CarrierLocations:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (EnhancedCarrierInfo enhancedCarrierInfo in GlobalData.Instance.gbEnhancedCarriers)
                        {
                            SecsItem enhancedCarrierInfoItem = new SecsItem(SecsFormat.List);

                            SecsItem carrierIDItem = new SecsItem(SecsFormat.Ascii);
                            carrierIDItem.Value = enhancedCarrierInfo.strCarrierID;
                            SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                            if (enhancedCarrierInfo.locType == LocationType.Shelf)
                            {
                                carrierLocItem.Value = enhancedCarrierInfo.strCarrierLoc;
                            }
                            else
                            {
                                carrierLocItem.Value = enhancedCarrierInfo.strCarrierZoneName;
                            }
                            SecsItem carrierZoneNameItem = new SecsItem(SecsFormat.Ascii);
                            carrierZoneNameItem.Value = enhancedCarrierInfo.strCarrierZoneName;

                            enhancedCarrierInfoItem.AppendItem(carrierIDItem);
                            enhancedCarrierInfoItem.AppendItem(carrierLocItem);
                            enhancedCarrierInfoItem.AppendItem(carrierZoneNameItem);

                            variableItem.AppendItem(enhancedCarrierInfoItem);
                        }
                    }
                    break;
                case EqpVariable.CarrierZoneName:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbCarrierZoneName;
                    }
                    break;
                case EqpVariable.ZoneName:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbZoneName;
                    }
                    break;
                case EqpVariable.ZoneCapacity:
                    {
                        Logger.Instance.ExceptionLog("************HostIF.cs: EqpVariable.ZoneCapacity == 76");
                        variableItem = new SecsItem(SecsFormat.U2);

                        uint uiZoneCapacity = 0;
                        TpLocationList tpLocationList = TpLocationList.GetAll();
                        foreach (TpLocation tpLocation in tpLocationList)
                        {
                            if (tpLocation.Type.Contains("1") 
                                && tpLocation.IsOccupied != 1)//没有Foup
                            {
                                uiZoneCapacity += 1;
                            }
                        }
                        if (GlobalData.Instance.gbZoneCapacity != uiZoneCapacity)
                        {
                            Logger.Instance.ExceptionLog("HostIF.cs: " + "GlobalData.Instance.gbZoneCapacity= "+ GlobalData.Instance.gbZoneCapacity.ToString() + "uiZoneCapacity="+ uiZoneCapacity.ToString());
                        }
                       
                        variableItem.Value = uiZoneCapacity;
                    }
                    break;
                case EqpVariable.ZoneType:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbZoneType;
                    }
                    break;
                case EqpVariable.StockerCraneID:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbStockerCraneID;
                    }
                    break;
                case EqpVariable.StockerUnitID:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbStockerUnitID;
                    }
                    break;
                case EqpVariable.StockerUnitState:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = (uint)GlobalData.Instance.gbStockerUnitState;
                    }
                    break;
                case EqpVariable.RecoveryOptions:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbRecoveryOptions;
                    }
                    break;
                case EqpVariable.PortID:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbPortID;
                    }
                    break;
                case EqpVariable.HandoffType:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = GlobalData.Instance.gbHandoffType64;
                    }
                    break;
                case EqpVariable.PortType:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbPortType;
                        //variableItem.Value = "LP";  //固定LP
                    }
                    break;
                case EqpVariable.ErrorID:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbErrorID;
                    }
                    break;
                case EqpVariable.EmptyCarrier:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbEmptyCarrier;
                    }
                    break;
                case EqpVariable.UnitID:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbUnitID;
                    }
                    break;
                case EqpVariable.AlarmID:
                    {
                        variableItem = new SecsItem(SecsFormat.U4);
                        variableItem.Value = (uint)GlobalData.Instance.gbAlarmID;
                    }
                    break;
                case EqpVariable.AlarmText:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbAlarmText;
                    }
                    break;
                case EqpVariable.DisabledLocations:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (DisabledLoc disabledLoc in GlobalData.Instance.gbDisabledLocations)
                        {
                            SecsItem disabledLocItem = new SecsItem(SecsFormat.List);

                            SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                            carrierLocItem.Value = disabledLoc.strCarrierLoc;
                            disabledLocItem.AppendItem(carrierLocItem);

                            SecsItem carrierIDItem = new SecsItem(SecsFormat.Ascii);
                            carrierIDItem.Value = disabledLoc.strCarrierId;
                            disabledLocItem.AppendItem(carrierIDItem);

                            variableItem.AppendItem(disabledLocItem);
                        }
                    }
                    break;
                case EqpVariable.DisabledLoc_i:
                    {
                        variableItem = new SecsItem(SecsFormat.List);

                        SecsItem carrierIDItem = new SecsItem(SecsFormat.Ascii);
                        carrierIDItem.Value = GlobalData.Instance.gbDisabledLoc.strCarrierId;
                        SecsItem carrierLocItem = new SecsItem(SecsFormat.Ascii);
                        carrierLocItem.Value = GlobalData.Instance.gbDisabledLoc.strCarrierLoc;

                        variableItem.AppendItem(carrierIDItem);
                        variableItem.AppendItem(carrierLocItem);
                    }
                    break;
                case EqpVariable.GlassExist:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = (uint)GlobalData.Instance.gbGlassExist;
                    }
                    break;
                case EqpVariable.EqpName:
                    {
                        variableItem = new SecsItem(SecsFormat.Ascii);
                        variableItem.Value = GlobalData.Instance.gbEqpName;
                    }
                    break;
                case EqpVariable.EstablishCommunicationTimeOut:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = GlobalData.Instance.gbEstablishCommunicationTimeOut;
                    }
                    break;
                case EqpVariable.IDReadDuplicateOption:
                    {
                        variableItem = new SecsItem(SecsFormat.U1);
                        variableItem.Value = (uint)GlobalData.Instance.gbIDReadDuplicateOption;
                    }
                    break;
                case EqpVariable.IDReadFailureOption:
                    {
                        variableItem = new SecsItem(SecsFormat.U1);
                        variableItem.Value = (uint)GlobalData.Instance.gbIDReadFailureOption;
                    }
                    break;
                case EqpVariable.IDReadMismatchOption:
                    {
                        variableItem = new SecsItem(SecsFormat.U1);
                        variableItem.Value = (uint)GlobalData.Instance.gbIDReadMismatchOption;
                    }
                    break;
                case EqpVariable.T3TimeOut:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = GlobalData.Instance.gbT3TimeOut;
                    }
                    break;
                case EqpVariable.T5TimeOut:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = GlobalData.Instance.gbT5TimeOut;
                    }
                    break;
                case EqpVariable.T6TimeOut:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = GlobalData.Instance.gbT6TimeOut;
                    }
                    break;
                case EqpVariable.T7TimeOut:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = GlobalData.Instance.gbT7TimeOut;
                    }
                    break;
                case EqpVariable.T8TimeOut:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = GlobalData.Instance.gbT8TimeOut;
                    }
                    break;
                case EqpVariable.RetryCount:
                    {
                        variableItem = new SecsItem(SecsFormat.U2);
                        variableItem.Value = GlobalData.Instance.gbRetryCount;
                    }
                    break;
                case EqpVariable.ShelfAllStats:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        try
                        {
                            TpLocationList tpLocationList = TpLocationList.GetAll();
                            foreach (TpLocation tpLocation in tpLocationList)
                            {
                                if (tpLocation.Type.Contains("1"))
                                {
                                    SecsItem TempList = new SecsItem(SecsFormat.List);

                                    SecsItem ShelfAddress = new SecsItem(SecsFormat.Ascii);
                                    ShelfAddress.Value = tpLocation.Address;

                                    SecsItem ShelfZonename = new SecsItem(SecsFormat.Ascii);
                                    ShelfZonename.Value = tpLocation.ZoneName;

                                    SecsItem ShelfState = new SecsItem(SecsFormat.U2);

                                    if(tpLocation.IsProhibited == 1)
                                    {
                                        ShelfState.Value = 2;
                                    }
                                    else if (tpLocation.IsReserved == 1)
                                    {
                                        ShelfState.Value = 3;
                                    }
                                    else if (tpLocation.IsOccupied == 1)
                                    {
                                        ShelfState.Value = 1;
                                    }
                                    else
                                    {
                                        ShelfState.Value = 0;
                                    }

                                    SecsItem ShelfCarried = new SecsItem(SecsFormat.Ascii);
                                    ShelfCarried.Value = tpLocation.CarrierId;

                                    TempList.AppendItem(ShelfAddress);
                                    TempList.AppendItem(ShelfZonename);
                                    TempList.AppendItem(ShelfState);
                                    TempList.AppendItem(ShelfCarried);

                                    variableItem.AppendItem(TempList);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.ExceptionLog("HostIF.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                        }

                    }
                    break;
                case EqpVariable.ActiveZones_2:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        try
                        {
                            TpZoneList tpZoneList = TpZoneList.GetAll();
                            foreach (TpZone tpZone in tpZoneList)
                            {
                                SecsItem ZoneList = new SecsItem(SecsFormat.List);

                                SecsItem ZoneName = new SecsItem(SecsFormat.Ascii);
                                ZoneName.Value = tpZone.Name;

                                SecsItem ZoneCapacity = new SecsItem(SecsFormat.U2);
                                ZoneCapacity.Value = tpZone.Capacity;

                                SecsItem ZoneTotalSize = new SecsItem(SecsFormat.U2);
                                ZoneTotalSize.Value = tpZone.TotalSize;

                                SecsItem ZoneType = new SecsItem(SecsFormat.U2);
                                ZoneType.Value = Convert.ToInt32(tpZone.Type);
                                if (tpZone.Type == "0")
                                {
                                    ZoneType.Value = 3;
                                }
                                
                                SecsItem DistableLocationList = new SecsItem(SecsFormat.List);

                                TpLocationList tpLocationList = TpLocationList.GetAll();
                                uint uiZoneCapacity = 0;
                                foreach (TpLocation tpLocation in tpLocationList)
                                {
                                    SecsItem DistableLocation = new SecsItem(SecsFormat.List);

                                    if (tpLocation.ZoneName == tpZone.Name
                                        && tpLocation.IsProhibited == 1)//禁用
                                    {
                                        SecsItem ShelfLocation = new SecsItem(SecsFormat.Ascii);
                                        ShelfLocation.Value = tpLocation.Address;

                                        SecsItem ShelfCarriedID = new SecsItem(SecsFormat.Ascii);
                                        ShelfCarriedID.Value = tpLocation.CarrierId;

                                        DistableLocation.AppendItem(ShelfLocation);
                                        DistableLocation.AppendItem(ShelfCarriedID);

                                        DistableLocationList.AppendItem(DistableLocation);
                                    }

                                    if (tpLocation.Type.Contains("1")
                                       && tpLocation.IsOccupied != 1)//没有Foup
                                    {
                                        uiZoneCapacity += 1;  
                                    }    
                                }

                                if (tpZone.Type.Contains("1") && tpZone.TotalSize > 1)
                                {
                                    ZoneCapacity.Value = uiZoneCapacity;
                                }

                                ZoneList.AppendItem(ZoneName);
                                ZoneList.AppendItem(ZoneCapacity);
                                ZoneList.AppendItem(ZoneTotalSize);
                                ZoneList.AppendItem(ZoneType);
                                ZoneList.AppendItem(DistableLocationList);
                                variableItem.AppendItem(ZoneList);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.ExceptionLog("HostIF.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                        }

                    }
                    break;
                case EqpVariable.ZoneTotalSize:
                    {
                        try
                        {
                            uint uiZoneTotalSize = 0;
                            TpLocationList tpLocationList = TpLocationList.GetAll();
                            foreach (TpLocation tpLocation in tpLocationList)
                            {
                                if (tpLocation.Type.Contains("1"))
                                {
                                    uiZoneTotalSize += 1;
                                }
                            }
                               
                            variableItem = new SecsItem(SecsFormat.U2);
                            variableItem.Value = uiZoneTotalSize;
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.ExceptionLog("HostIF.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                        }

                    }
                    break;
                case EqpVariable.CurrentEqPortStatus:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        foreach (EqPortInfo eqPortInfo in GlobalData.Instance.gbCurrentEqPortStatus)
                        {
                            SecsItem eqPortInfoItem = new SecsItem(SecsFormat.List);

                            SecsItem portIDItem = new SecsItem(SecsFormat.Ascii);
                            portIDItem.Value = eqPortInfo.strPortID;
                            SecsItem EqReqStatusItem = new SecsItem(SecsFormat.U2);
                            EqReqStatusItem.Value = (uint)eqPortInfo.eqReqStatus;
                            SecsItem EqPresenceStatusItem = new SecsItem(SecsFormat.U2);
                            EqPresenceStatusItem.Value = (uint)eqPortInfo.eqPresenceStatus;

                            eqPortInfoItem.AppendItem(portIDItem);
                            eqPortInfoItem.AppendItem(EqReqStatusItem);
                            eqPortInfoItem.AppendItem(EqPresenceStatusItem);

                            variableItem.AppendItem(eqPortInfoItem);
                        }
                    }
                    break;
                case EqpVariable.MainteState:
                    {
                        SecsItem iMainteState = new SecsItem(SecsFormat.U2);
                        iMainteState.Value = 2;
                    }
                    break;
                case EqpVariable.UnitAlarmStatList:
                    {
                        variableItem = new SecsItem(SecsFormat.List);
                        TpAlarmList tpAlarmList = TpAlarmList.GetAll();
                        foreach (TpAlarm tpAlarm in tpAlarmList)
                        {
                            SecsItem alarm = new SecsItem(SecsFormat.List);
                            SecsItem eqpName = new SecsItem(SecsFormat.Ascii);
                            eqpName.Value = GlobalData.Instance.gbEqpName;
                            SecsItem unitID = new SecsItem(SecsFormat.Ascii);
                            unitID.Value = tpAlarm.Unit;
                            SecsItem alarmID = new SecsItem(SecsFormat.U4);
                            alarmID.Value = tpAlarm.Code;
                            SecsItem alarmText = new SecsItem(SecsFormat.Ascii);
                            alarmText.Value = tpAlarm.Comment;
                            SecsItem maint = new SecsItem(SecsFormat.U2);
                            maint.Value = 2;

                            alarm.AppendItem(eqpName);
                            alarm.AppendItem(unitID);
                            alarm.AppendItem(alarmID);
                            alarm.AppendItem(alarmText);
                            alarm.AppendItem(maint);

                            variableItem.AppendItem(alarm);
                        }
                    }
                    break;
            }
            return variableItem;
        }

        public void PostControlEvent()
        {

        }
        //online后-->host
        public void PostScEvent(EqpEvent ceid)
        {
            this.PostEvent((uint)ceid);
        }

        public void PostAlarmEvent(string commandID, string errorID, string unitID, string recoveryOption, int alarmID, EqpEvent ceid)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                GlobalData.Instance.gbCommandID = commandID;
                GlobalData.Instance.gbErrorID = errorID;
                GlobalData.Instance.gbStockerUnitID = unitID;

                StockerUnitState unitState = StockerUnitState.Normal;
                CurrentCraneState craneState = GlobalData.Instance.gbCurrentCraneState.Where(x => x.strStockerCraneID == unitID).FirstOrDefault();
                if (craneState != null)
                {
                    switch (craneState.craneOperationState)
                    {
                        case CraneOperationState.Normal:
                            unitState = StockerUnitState.Normal;
                            break;
                        case CraneOperationState.Down:
                        case CraneOperationState.None:
                            unitState = StockerUnitState.ErrorUnit;
                            break;
                    }
                }
                else //Port
                {
                    PortInfo portInfo = GlobalData.Instance.gbCurrentPortStates.Where(x => x.strPortID == unitID).FirstOrDefault();
                    if (portInfo != null)
                    {
                        switch (portInfo.transferState)
                        {
                            case PortTransferState.InService:
                                unitState = StockerUnitState.Normal;
                                break;
                            case PortTransferState.OutOfService:
                            case PortTransferState.None:
                                unitState = StockerUnitState.Normal;
                                break;
                        }
                    }
                }
                if(errorID.Contains("SourceEmpty"))
                {
                    unitState = StockerUnitState.EmptyRetrieval;
                }
                else if (errorID.Contains("DestOccupied"))
                {
                    unitState = StockerUnitState.DoubleStorage;
                }

                GlobalData.Instance.gbStockerUnitState = unitState;
                GlobalData.Instance.gbRecoveryOptions = recoveryOption;
                GlobalData.Instance.gbAlarmID = (uint)alarmID;

                PostEvent((uint)ceid);
            }
        }
                
        public void PostUnitAlarmEvent(string unitID, int alarmID, string alarmText, EqpEvent ceid)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                GlobalData.Instance.gbUnitID = unitID;
                GlobalData.Instance.gbStockerUnitID = unitID;
                GlobalData.Instance.gbAlarmID = (uint)alarmID;
                GlobalData.Instance.gbAlarmText = alarmText;
                PostEvent((uint)ceid);
            }
        }

        public void PostManualTransferEvent(string strCommandID)
        {
            lock(GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    if (enhancedTransferCommand.strCommandID == strCommandID)
                    {
                        GlobalData.Instance.gbCommandID = enhancedTransferCommand.strCommandID;
                        GlobalData.Instance.gbCommandType = "TRANSFER";
                        GlobalData.Instance.gbCarrierID = enhancedTransferCommand.strCarrierID;
                        GlobalData.Instance.gbSource = enhancedTransferCommand.strSourceZone;
                        GlobalData.Instance.gbDest = enhancedTransferCommand.strDestZone;
                        PostEvent((uint)EqpEvent.OperatorInitiatedAction);//发布时间由底层SECS库发送数据给上位系统
                        break;
                    }
                }
            }
        }

        public void PostManualAbortEvent(string strCommandID)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    if (enhancedTransferCommand.strCommandID == strCommandID)
                    {
                        GlobalData.Instance.gbCommandID = enhancedTransferCommand.strCommandID;
                        GlobalData.Instance.gbCommandType = "ABORT";
                        GlobalData.Instance.gbCarrierID = enhancedTransferCommand.strCarrierID;
                        GlobalData.Instance.gbSource = enhancedTransferCommand.strSourceZone;
                        GlobalData.Instance.gbDest = enhancedTransferCommand.strDestZone;
                        PostEvent((uint)EqpEvent.OperatorInitiatedAction);
                        break;
                    }
                }
            }
        }

        public void PostManualCancelEvent(string strCommandID)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    if (enhancedTransferCommand.strCommandID == strCommandID)
                    {
                        GlobalData.Instance.gbCommandID = enhancedTransferCommand.strCommandID;
                        GlobalData.Instance.gbCommandType = "CANCEL";
                        GlobalData.Instance.gbCarrierID = enhancedTransferCommand.strCarrierID;
                        GlobalData.Instance.gbSource = enhancedTransferCommand.strSourceZone;
                        GlobalData.Instance.gbDest = enhancedTransferCommand.strDestZone;
                        PostEvent((uint)EqpEvent.OperatorInitiatedAction);
                        break;
                    }
                }
            }
        }

        public void PostTransferEvent(string strCommandID, EqpEvent ceid)
        {
            int iFirstSecond = 1;

            ResultCode resultCode = ResultCode.None;
            lock (GlobalData.Instance.objRWLock)
            {
                foreach(EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    if (enhancedTransferCommand.strCommandID == strCommandID)
                    {
                        GlobalData.Instance.gbCommandID = enhancedTransferCommand.strCommandID;
                        GlobalData.Instance.gbCarrierID = enhancedTransferCommand.strCarrierID;
                        GlobalData.Instance.gbPriority = enhancedTransferCommand.u2Priority;
                        //GlobalData.Instance.gbCarrierLoc = enhancedTransferCommand.strRealSource; //Carrier所在的实际位置
                        //储位和出入口配置在同一张表中，所以需要把shelf检索出来
                        foreach (EnhancedCarrierInfo enhancedCarrierInfo in GlobalData.Instance.gbEnhancedCarriers)
                        {
                            if (enhancedCarrierInfo.strCarrierID == enhancedTransferCommand.strCarrierID)
                            {
                                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                                {
                                    if (loc.strAddress == enhancedCarrierInfo.strCarrierLoc)
                                    {
                                        if(loc.locType == LocationType.Shelf)
                                        {
                                            GlobalData.Instance.gbCarrierLoc = enhancedCarrierInfo.strCarrierLoc;
                                        }
                                        else
                                        {
                                            GlobalData.Instance.gbCarrierLoc = enhancedCarrierInfo.strCarrierZoneName;
                                        }
                                        break;
                                    }
                                }
                                //任务完重置shelf状态
                                if (ceid == EqpEvent.TransferAbortCompleted
                                    || ceid == EqpEvent.TransferCancelCompleted || ceid == EqpEvent.ScanCompleted)
                                {
                                    UnReserveLocation(enhancedTransferCommand.strRealSource);
                                    UnReserveLocation(enhancedTransferCommand.strRealDest);
                                }
                                else if (ceid == EqpEvent.TransferCompleted )
                                {
                                    UnReserveLocation(enhancedTransferCommand.strRealDest);
                                }
                                break;
                            }
                        }

                        //if (enhancedTransferCommand.destLocType == LocationType.Shelf)
                        //{
                        //    GlobalData.Instance.gbDest = enhancedTransferCommand.strRealDest;
                        //}
                        //else
                        //{
                            GlobalData.Instance.gbDest = enhancedTransferCommand.strDestZone;
                        //}

                        GlobalData.Instance.gbResultCode = resultCode = enhancedTransferCommand.resultCode;

                        iFirstSecond = enhancedTransferCommand.iFirstSecendCrane;
                        break;
                    }
                }
                PostEvent((uint)ceid);
                if (ceid == EqpEvent.TransferCompleted || ceid == EqpEvent.TransferAbortCompleted 
                    || ceid == EqpEvent.TransferCancelCompleted || ceid == EqpEvent.ScanCompleted)
                {
                    string strCmdState = "TransferSuccess";
                    if(ceid == EqpEvent.TransferAbortCompleted)
                    {
                        strCmdState = "AbortCompleted";
                    }
                    else if (ceid == EqpEvent.TransferCancelCompleted)
                    {
                        strCmdState = "CancelCompleted";
                    }
                    else if (ceid == EqpEvent.ScanCompleted)
                    {
                        strCmdState = "ScanCompleted";
                    }

                    HistoryWriter.Instance.CraneTransferEnd(strCommandID, iFirstSecond, strCmdState, "");
                    //任务完成清除命令
                    if (DbTransfer.Instance.DeleteTransfer(strCommandID))
                    {
                        lock (GlobalData.Instance.objRWLock)
                        {
                            int iCount = GlobalData.Instance.gbEnhancedTransfers.Count;
                            for (int i = 0; i < iCount; i++)
                            {
                                if (GlobalData.Instance.gbEnhancedTransfers[i].strCommandID == strCommandID)
                                {
                                    GlobalData.Instance.gbEnhancedTransfers.RemoveAt(i);
                                    return;
                                }
                            }
                        }
                    }
                }
            }
        }

        public void UnReserveLocation(string strAddress)
        {
            if (DbLocation.Instance.UnReserveLocation(strAddress))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                    {
                        if (loc.strAddress == strAddress)
                        {
                            loc.IsReserved = false;
                            History.HistoryWriter.Instance.EqpEventLog(strAddress, 0, "UnReserveLocation", $"UnReserveLocation: Location {strAddress}");
                            break;
                        }
                    }
                }
            }
        }

        public void PostCarrierEvent(string strCarrierID, EqpEvent ceid, string portLoc = "")
        {
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    //此处改为直接从数据库读取Carrier信息
                    GlobalData.Instance.gbCarrierID = strCarrierID;
                    GlobalData.Instance.gbCarrierLoc = portLoc;
                    //TpLocation loc = TpLocation.GetByLambda(x => x.CarrierId == strCarrierID);
                    TpCarrier car = TpCarrier.GetByLambda(x => x.Id == strCarrierID);
                    if (car != null)
                    {
                        TpLocation loc = TpLocation.GetByLambda(x => x.Address == car.Location);
                        if (loc != null)
                        {
                            if (Convert.ToInt32(loc.Type) == (int)LocationType.Shelf)//Shelf==1
                            {
                                GlobalData.Instance.gbCarrierLoc = loc.Address;
                            }
                            else
                            {
                                GlobalData.Instance.gbCarrierLoc = loc.ZoneName;
                            }
                            GlobalData.Instance.gbCarrierZoneName = loc.ZoneName;
                            GlobalData.Instance.gbZoneData.strZoneName = loc.ZoneName;

                            PostEvent((uint)ceid);
                        }
                        else
                        {
                            Logger.Instance.ExceptionLog("@@HostIF.cs: PostCarrierEvent" + ", Error: " + ceid);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                HistoryWriter.Instance.ExceptionLog("HostIF.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }

        public void PostZoneCapacityChangeEvent(string strCarrierID, EqpEvent ceid)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                //此处改为直接从数据库读取Carrier信息
                GlobalData.Instance.gbCarrierID = strCarrierID;
                GlobalData.Instance.gbZoneData.strZoneName = "Shelf";

                uint uiZoneCapacitySize = 0;
                TpLocationList tpLocationList = TpLocationList.GetAll();
                foreach (TpLocation tpLocation in tpLocationList)
                {
                    if (tpLocation.ZoneName == GlobalData.Instance.gbZoneData.strZoneName
                        && tpLocation.IsOccupied != 1)//没有Foup
                    {
                        uiZoneCapacitySize += 1;
                    }
                }
                GlobalData.Instance.gbZoneData.u2ZoneCapacity = uiZoneCapacitySize;

                PostEvent((uint)ceid);
            }
        }

        /// <summary>
        /// 当CST已经被删除时，需要调用此函数上报事件
        /// </summary>
        public void PostCarrierEvent(string strCarrierID, string strCarrierLoc, EqpEvent ceid, string portLoc = "")
        {
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    GlobalData.Instance.gbCarrierID = strCarrierID;
                    TpLocation loc = TpLocation.GetByLambda(x => x.Address == strCarrierLoc);
                    if (loc == null)
                    {
                        GlobalData.Instance.gbCarrierLoc = strCarrierLoc;
                        GlobalData.Instance.gbCarrierZoneName = strCarrierLoc;
                    }
                    else
                    {
                        if (Convert.ToInt32(loc.Type) == (int)LocationType.Shelf)
                        {
                            GlobalData.Instance.gbCarrierLoc = loc.Address;
                        }
                        else
                        {
                            GlobalData.Instance.gbCarrierLoc = loc.ZoneName;
                        }
                        GlobalData.Instance.gbCarrierZoneName = loc.ZoneName;
                    }
                    PostEvent((uint)ceid);
                }
            }
            catch (Exception ex)
            {
                HistoryWriter.Instance.ExceptionLog("HostIF.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }

        public void PostCraneEvent(string strCraneID, EqpEvent ceid)
        {
            lock (GlobalData.Instance.objRWLock)
            {

                PostEvent((uint)ceid);
            }
        }

        public void PostIDREvent(string strCarrierID, string strCarrierLoc, IDReadStatus idReadStatus, EqpEvent ceid)
        {
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    GlobalData.Instance.gbCarrierID = strCarrierID;
                    TpLocation loc = TpLocation.GetByLambda(x => x.Address == strCarrierLoc);
                    if (loc == null)
                    {
                        GlobalData.Instance.gbCarrierLoc = strCarrierLoc;
                    }
                    else
                    {
                        if (Convert.ToInt32(loc.Type) == (int)LocationType.Shelf)
                        {
                            GlobalData.Instance.gbCarrierLoc = loc.Address;
                        }
                        else
                        {
                            GlobalData.Instance.gbCarrierLoc = loc.ZoneName;
                        }
                    }
                    GlobalData.Instance.gbIDReadStatus = idReadStatus;
                    PostEvent((uint)ceid);
                }
            }
            catch (Exception ex)
            {
                HistoryWriter.Instance.ExceptionLog("HostIF.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
        }

        public void PostPortEvent(string portID, EqpEvent ceid)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                GlobalData.Instance.gbPortID = portID;
                PostEvent((uint)ceid);
            }
        }

        public void PostShelfEvent()
        {

        }

        private void SendDateTimeRequest()
        {
            SecsItem itemS2F17 = new SecsItem(SecsFormat.List);
            SendMessage(2, 17, itemS2F17, true);
        }
        private void SendAreYouThereRequest()
        {
            //toggled to online report S1F1  SC--> H
            SecsItem s1f2RootItem = new SecsItem(SecsFormat.List);
            SendMessage(1, 1, s1f2RootItem, true);
        }
    }
}
