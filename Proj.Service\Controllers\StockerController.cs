using Microsoft.AspNetCore.Mvc;
using Proj.Service.Interfaces;
using Proj.Service.Models;
using Proj.Log;

namespace Proj.Service.Controllers
{
    /// <summary>
    /// Stocker Web API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class StockerController : ControllerBase
    {
        private readonly IStockerService _stockerService;
        private readonly IClientManagerService _clientManager;
        private readonly Logger _logger;

        public StockerController(IStockerService stockerService, IClientManagerService clientManager)
        {
            _stockerService = stockerService;
            _clientManager = clientManager;
            _logger = Logger.Instance;
        }

        /// <summary>
        /// 连接测试
        /// </summary>
        /// <returns>连接测试结果</returns>
        [HttpGet("test")]
        public async Task<ActionResult<ServiceResponse<bool>>> TestConnection()
        {
            try
            {
                var result = await _stockerService.TestConnectionAsync();
                return Ok(ServiceResponse<bool>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"TestConnection API error: {ex.Message}");
                return StatusCode(500, ServiceResponse<bool>.CreateError("Internal server error"));
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="request">消息请求</param>
        /// <returns>处理结果</returns>
        [HttpPost("message")]
        public async Task<ActionResult<ServiceResponse<object>>> SendMessage([FromBody] ClientMessageRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ServiceResponse<object>.CreateError("Invalid request"));
                }

                var clientIp = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                
                // 记录日志
                var logMessage = $"API Receive From {clientIp}: {request.Function}";
                foreach (var kvp in request.Parameters)
                {
                    logMessage += $", {kvp.Key}: {kvp.Value}";
                }
                _logger.WcfLog(logMessage);

                var result = await _stockerService.ProcessClientMessageAsync(request.Function, request.Parameters);
                return Ok(ServiceResponse<object>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"SendMessage API error: {ex.Message}");
                return StatusCode(500, ServiceResponse<object>.CreateError("Internal server error"));
            }
        }

        /// <summary>
        /// 获取系统状态
        /// </summary>
        /// <param name="request">状态请求</param>
        /// <returns>状态值</returns>
        [HttpPost("state")]
        public async Task<ActionResult<ServiceResponse<string>>> GetState([FromBody] StateRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ServiceResponse<string>.CreateError("Invalid request"));
                }

                var result = await _stockerService.GetStateAsync(request.Name);
                return Ok(ServiceResponse<string>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetState API error: {ex.Message}");
                return StatusCode(500, ServiceResponse<string>.CreateError("Internal server error"));
            }
        }

        /// <summary>
        /// 获取所有系统状态
        /// </summary>
        /// <returns>所有状态信息</returns>
        [HttpGet("states")]
        public async Task<ActionResult<ServiceResponse<SystemStateInfo>>> GetAllStates()
        {
            try
            {
                var result = await _stockerService.GetAllStatesAsync();
                return Ok(ServiceResponse<SystemStateInfo>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetAllStates API error: {ex.Message}");
                return StatusCode(500, ServiceResponse<SystemStateInfo>.CreateError("Internal server error"));
            }
        }

        /// <summary>
        /// 获取标签值
        /// </summary>
        /// <param name="request">标签值请求</param>
        /// <returns>标签值</returns>
        [HttpPost("tag")]
        public async Task<ActionResult<ServiceResponse<object>>> GetTagValue([FromBody] TagValueRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ServiceResponse<object>.CreateError("Invalid request"));
                }

                var result = await _stockerService.GetTagValueAsync(request.TagName);
                return Ok(ServiceResponse<object>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetTagValue API error: {ex.Message}");
                return StatusCode(500, ServiceResponse<object>.CreateError("Internal server error"));
            }
        }

        /// <summary>
        /// 获取连接的客户端列表
        /// </summary>
        /// <returns>客户端连接信息列表</returns>
        [HttpGet("clients")]
        public ActionResult<ServiceResponse<IEnumerable<ClientConnectionInfo>>> GetConnectedClients()
        {
            try
            {
                var clients = _clientManager.GetConnectedClients();
                return Ok(ServiceResponse<IEnumerable<ClientConnectionInfo>>.CreateSuccess(clients));
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetConnectedClients API error: {ex.Message}");
                return StatusCode(500, ServiceResponse<IEnumerable<ClientConnectionInfo>>.CreateError("Internal server error"));
            }
        }

        /// <summary>
        /// 向所有客户端发送消息
        /// </summary>
        /// <param name="message">服务端消息</param>
        /// <returns>发送结果</returns>
        [HttpPost("broadcast")]
        public async Task<ActionResult<ServiceResponse<bool>>> BroadcastMessage([FromBody] ServerMessage message)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ServiceResponse<bool>.CreateError("Invalid request"));
                }

                var result = await _clientManager.SendMessageToAllClientsAsync(message.Function, message.Parameters);
                return Ok(ServiceResponse<bool>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"BroadcastMessage API error: {ex.Message}");
                return StatusCode(500, ServiceResponse<bool>.CreateError("Internal server error"));
            }
        }
    }
}
