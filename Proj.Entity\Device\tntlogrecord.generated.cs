﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TntLogrecord and List
    [Serializable]
    [Description("日志记录")]
    [LinqToDB.Mapping.Table("Tnt_LogRecord")]
    public partial class TntLogrecord : GEntity<TntLogrecord>, ITimestamp
    {
        #region Contructor(s)

        private TntLogrecord()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CPk = RegisterProperty<String>(p => p.CPk);
        private static readonly PropertyInfo<String> pty_CLogtime = RegisterProperty<String>(p => p.CLogtime);
        private static readonly PropertyInfo<String> pty_CLogType = RegisterProperty<String>(p => p.CLogType);
        private static readonly PropertyInfo<String> pty_CLogmodel = RegisterProperty<String>(p => p.CLogmodel);
        private static readonly PropertyInfo<int> pty_CLogId = RegisterProperty<int>(p => p.CLogId);
        private static readonly PropertyInfo<String> pty_CLoglevel = RegisterProperty<String>(p => p.CLoglevel);
        private static readonly PropertyInfo<String> pty_CLogtext = RegisterProperty<String>(p => p.CLogtext);
        private static readonly PropertyInfo<String> pty_CUserId = RegisterProperty<String>(p => p.CUserId);
        private static readonly PropertyInfo<String> pty_CSw01 = RegisterProperty<String>(p => p.CSw01);
        private static readonly PropertyInfo<String> pty_CSw02 = RegisterProperty<String>(p => p.CSw02);
        private static readonly PropertyInfo<String> pty_CSw03 = RegisterProperty<String>(p => p.CSw03);
        #endregion

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [LinqToDB.Mapping.Column("c_pk")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CPk
        {
            get { return GetProperty(pty_CPk); }
            set { SetProperty(pty_CPk, value); }
        }
        /// <summary>
        /// 日志时间
        /// </summary>
        [Description("日志时间")]
        [LinqToDB.Mapping.Column("c_LogTime")]
        public String CLogtime
        {
            get { return GetProperty(pty_CLogtime); }
            set { SetProperty(pty_CLogtime, value); }
        }
        /// <summary>
        /// 日志类型
        /// </summary>
        [Description("日志类型")]
        [LinqToDB.Mapping.Column("c_LogType")]
        public String CLogType
        {
            get { return GetProperty(pty_CLogType); }
            set { SetProperty(pty_CLogType, value); }
        }
        /// <summary>
        /// 日志模块
        /// </summary>
        [Description("日志模块")]
        [LinqToDB.Mapping.Column("c_LogModel")]
        public String CLogmodel
        {
            get { return GetProperty(pty_CLogmodel); }
            set { SetProperty(pty_CLogmodel, value); }
        }
        /// <summary>
        /// 日志Id
        /// </summary>
        [Description("日志Id")]
        [LinqToDB.Mapping.Column("c_LogId")]
        public int CLogId
        {
            get { return GetProperty(pty_CLogId); }
            set { SetProperty(pty_CLogId, value); }
        }
        /// <summary>
        /// 日志级别
        /// </summary>
        [Description("日志级别")]
        [LinqToDB.Mapping.Column("c_LogLevel")]
        public String CLoglevel
        {
            get { return GetProperty(pty_CLoglevel); }
            set { SetProperty(pty_CLoglevel, value); }
        }
        /// <summary>
        /// 日志内容
        /// </summary>
        [Description("日志内容")]
        [LinqToDB.Mapping.Column("c_LogText")]
        public String CLogtext
        {
            get { return GetProperty(pty_CLogtext); }
            set { SetProperty(pty_CLogtext, value); }
        }
        /// <summary>
        /// 用户信息
        /// </summary>
        [Description("用户信息")]
        [LinqToDB.Mapping.Column("c_UserId")]
        public String CUserId
        {
            get { return GetProperty(pty_CUserId); }
            set { SetProperty(pty_CUserId, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("c_TimeStamp")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展1
        /// </summary>
        [Description("扩展1")]
        [LinqToDB.Mapping.Column("c_Sw01")]
        public String CSw01
        {
            get { return GetProperty(pty_CSw01); }
            set { SetProperty(pty_CSw01, value); }
        }
        /// <summary>
        /// 扩展2
        /// </summary>
        [Description("扩展2")]
        [LinqToDB.Mapping.Column("c_Sw02")]
        public String CSw02
        {
            get { return GetProperty(pty_CSw02); }
            set { SetProperty(pty_CSw02, value); }
        }
        /// <summary>
        /// 扩展3
        /// </summary>
        [Description("扩展3")]
        [LinqToDB.Mapping.Column("c_Sw03")]
        public String CSw03
        {
            get { return GetProperty(pty_CSw03); }
            set { SetProperty(pty_CSw03, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CPk, "主键是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CPk, 36, "主键不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLogtime, 40, "日志时间不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLogType, 2, "日志类型不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLogmodel, 50, "日志模块不能超过50个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLoglevel, 2, "日志级别不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CLogtext, 400, "日志内容不能超过4000个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CUserId, 36, "用户信息不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw01, 40, "扩展1不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw02, 40, "扩展2不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CSw03, 40, "扩展3不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CPk; }
        #endregion
    }       
        
    [Serializable]
    public partial class TntLogrecordList : GEntityList<TntLogrecordList, TntLogrecord>
    {
        private TntLogrecordList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
