using System;
using System.Collections.Generic;
using System.ComponentModel;
using Csla;

namespace SS.CslaBase
{
    /// <summary>
    /// 时间戳接口
    /// </summary>
    public interface ITimestamp
    {
        DateTime? LastModified { get; set; }
    }

    /// <summary>
    /// 泛型实体基类
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    [Serializable]
    public abstract class GEntity<T> : BusinessBase<T>, ITimestamp
        where T : GEntity<T>
    {
        #region ITimestamp Implementation
        
        private static readonly PropertyInfo<DateTime?> LastModifiedProperty = 
            RegisterProperty<DateTime?>(nameof(LastModified));
        
        public DateTime? LastModified
        {
            get => GetProperty(LastModifiedProperty);
            set => SetProperty(LastModifiedProperty, value);
        }
        
        #endregion

        // Factory Methods removed - use DataPortal directly in consuming code

        #region Business Rules

        protected override void AddBusinessRules()
        {
            base.AddBusinessRules();
            // Add common business rules here
            AddCustomBusinessRules();
        }

        /// <summary>
        /// 添加自定义业务规则 - 子类可以重写此方法
        /// </summary>
        protected virtual void AddCustomBusinessRules()
        {
            // 默认实现为空，子类可以重写
        }

        #endregion

        #region Primary Key Support

        /// <summary>
        /// 获取主键值 - 子类需要重写此方法
        /// </summary>
        /// <returns>主键值</returns>
        public virtual object GetPrimaryKeyValue()
        {
            // 默认实现，子类应该重写
            return null;
        }

        #endregion
    }

    /// <summary>
    /// 泛型实体列表基类
    /// </summary>
    /// <typeparam name="T">列表类型</typeparam>
    /// <typeparam name="C">子项类型</typeparam>
    [Serializable]
    public abstract class GEntityList<T, C> : BusinessListBase<T, C>
        where T : GEntityList<T, C>
        where C : class, Csla.Core.IEditableBusinessObject
    {
        // Factory Methods removed - use DataPortal directly in consuming code
    }
}
