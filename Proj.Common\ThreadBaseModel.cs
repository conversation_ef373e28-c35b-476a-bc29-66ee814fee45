﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
//using Interface;

namespace Proj.Common
{
    public delegate void DelegateThreadRoutine();
     
    public class ThreadBaseModel
    {
        #region 内部数据
        protected int threadID = 0;
        protected string taskName = "";
        protected Thread threadHandler = null; //线程句柄
        protected bool exitRunning = false; //退出标志
        protected bool pauseFlag = true;//暂停标志
        protected int loopInterval = 100; //任务循环周期

        private DelegateThreadRoutine threadRoutine = null;
        #endregion
        #region 属性
        public int ThreadID { get { return threadID; } }
        public int LoopInterval { get { return loopInterval; } set { this.loopInterval = value; } }
        public bool IsPause { get { return pauseFlag; } }
        #endregion
        #region 公开接口
        public  void Sleep(int ms)
        {
            Thread.Sleep(ms); 
        }
        public ThreadBaseModel(int id, string taskName)
        {
            this.threadID = id;
            this.taskName = taskName;
        }
        public void SetThreadRoutine(DelegateThreadRoutine routine)
        {
            this.threadRoutine = routine;
        }
        public void SetPriority(ThreadPriority priority)
        {
            if(this.threadHandler!= null)
            {
                this.threadHandler.Priority = priority;
            }
        }

        public bool TaskInit(ref string reStr)
        {
            this.threadHandler = new Thread(new ThreadStart(TaskloopProc));
            this.threadHandler.IsBackground = true;
            this.threadHandler.Name = this.taskName;
            this.pauseFlag = false;
            this.exitRunning = false;
            return true;
        }
        public bool TaskExit(ref string reStr)
        {
            this.exitRunning = true;
            if (threadHandler.ThreadState == (ThreadState.Running | ThreadState.Background))
            {
                if (!threadHandler.Join(500))
                {
                    threadHandler.Abort();
                }
            }

            return true;
        }
        public bool Start(ref string reStr)
        {
            this.pauseFlag = false;
            if (this.threadHandler.ThreadState == (ThreadState.Unstarted | ThreadState.Background))
            {
                //this.threadHandler.Apartment = ApartmentState.STA;
                //this.threadHandler.SetApartmentState(ApartmentState.STA); //线程单元模型
                this.threadHandler.Start();
            }

            return true;
        }
        public bool Pause(ref string reStr)
        {
            this.pauseFlag = true;
            return true;
        }
        #endregion
        #region 内部接口
        protected virtual void TaskloopProc()
        {
            while (!exitRunning)
            {
                Thread.Sleep(loopInterval);
                if (pauseFlag)
                {
                    continue;
                }
                if (this.threadRoutine != null)
                {
                    this.threadRoutine();
                }
            }
        }
        #endregion
    }
}
