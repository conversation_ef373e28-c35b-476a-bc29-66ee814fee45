﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Proj.Entity;
using Proj.DB;
using Proj.CacheData;
using Proj.DataTypeDef;

namespace Proj.UnitMng
{
    public class ZoneMng
    {
        private static ZoneMng m_Instanse;
        private static readonly object mSyncObject = new object();
        private ZoneMng() { }
        public static ZoneMng Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new ZoneMng();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        public bool Initialize()
        {
            bool bRes = false;
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    TpZoneList tpZoneList = DbZone.Instance.GetDbZoneList();
                    GlobalData.Instance.gbExtendedActiveZones.Clear();
                    foreach (TpZone tpZone in tpZoneList)
                    {
                        ExtendedZoneData exZoneData = new ExtendedZoneData();
                        exZoneData.strZoneName = tpZone.Name;
                        exZoneData.u2ZoneCapacity = (uint)tpZone.Capacity;
                        exZoneData.u2ZoneTotalSize = (uint)tpZone.TotalSize;
                        exZoneData.zoneType = (ZoneType)uint.Parse(tpZone.Type);
                        TpLocationList tpLocationList = TpLocationList.GetByLambda(x => x.ZoneName == exZoneData.strZoneName && x.IsProhibited == 1);
                        foreach (TpLocation tpLocation in tpLocationList)
                        {
                            DisabledLoc disableLoc = new DisabledLoc();
                            disableLoc.strCarrierLoc = tpLocation.Address;
                            disableLoc.strCarrierId = tpLocation.CarrierId;
                            exZoneData.disableLocations.Add(disableLoc);
                        }
                        GlobalData.Instance.gbExtendedActiveZones.Add(exZoneData);

                        ZoneData zoneData = new ZoneData();
                        zoneData.strZoneName = tpZone.Name;
                        zoneData.u2ZoneCapacity = (uint)tpZone.Capacity;
                        GlobalData.Instance.gbActiveZones.Add(zoneData);

                        EnhancedZoneData ehZoneData = new EnhancedZoneData();
                        ehZoneData.strZoneName = tpZone.Name;
                        ehZoneData.u2ZoneCapacity = (uint)tpZone.Capacity;
                        ehZoneData.u2ZoneSize = (uint)tpZone.TotalSize;
                        ehZoneData.zoneType = (ZoneType)uint.Parse(tpZone.Type);
                        GlobalData.Instance.gbEnhancedActiveZones.Add(ehZoneData);
                    }
                    bRes = true;
                }

            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("ZoneMng.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }
            return bRes;
        }

        public bool Exists(string zoneName)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (ExtendedZoneData exZoneData in GlobalData.Instance.gbExtendedActiveZones)
                {
                    if (exZoneData.strZoneName == zoneName)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public uint GetZoneCapacity(string zoneName)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                uint uiCount = 0;
                TpLocationList tpLocationList = TpLocationList.GetByLambda(x => x.ZoneName == zoneName);
                if(tpLocationList != null)
                {
                    foreach (TpLocation tpLocation in tpLocationList)
                    {
                        if (tpLocation.IsOccupied != 1 && tpLocation.IsProhibited != 1 && tpLocation.IsReserved != 1)
                        {
                            uiCount += 1;
                        }
                    }
                }
               
                return uiCount;
            }
        }
    }
}
