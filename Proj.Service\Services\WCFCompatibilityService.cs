using Proj.Service.Interfaces;
using Proj.Service.Events;
using Proj.Log;

namespace Proj.Service.Services
{
    /// <summary>
    /// WCF兼容性服务，提供与原WCF服务相同的接口
    /// </summary>
    public class WCFCompatibilityService
    {
        private static WCFCompatibilityService? _instance;
        private static readonly object _lock = new();

        private readonly ServiceEventManager _eventManager;
        private readonly Logger _logger;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static WCFCompatibilityService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new WCFCompatibilityService();
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 客户端发送消息事件
        /// </summary>
        public event Func<string, Dictionary<string, object>, object?>? ClientSendMessage;

        /// <summary>
        /// 客户端获取标签值事件
        /// </summary>
        public event Func<string, object?>? ClientGetTagValue;

        private IClientManagerService? _clientManagerInstance;

        private WCFCompatibilityService()
        {
            _eventManager = ServiceEventManager.Instance;
            _logger = Logger.Instance;
        }

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="clientManager">客户端管理服务</param>
        public void Initialize(IClientManagerService clientManager)
        {
            if (_clientManagerInstance == null)
            {
                _clientManagerInstance = clientManager;

                // 注册事件处理器
                _eventManager.ClientSendMessage += OnClientSendMessage;
                _eventManager.ClientGetTagValue += OnClientGetTagValue;
            }
        }

        /// <summary>
        /// 处理客户端发送消息事件
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>处理结果</returns>
        private object? OnClientSendMessage(string function, Dictionary<string, object> parameters)
        {
            return ClientSendMessage?.Invoke(function, parameters);
        }

        /// <summary>
        /// 处理客户端获取标签值事件
        /// </summary>
        /// <param name="tagName">标签名称</param>
        /// <returns>标签值</returns>
        private object? OnClientGetTagValue(string tagName)
        {
            return ClientGetTagValue?.Invoke(tagName);
        }

        /// <summary>
        /// 服务器向客户端发送消息（同步版本，兼容原WCF接口）
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>发送是否成功</returns>
        public bool ServerSendMessage(string function, Dictionary<string, object> parameters)
        {
            try
            {
                // 记录日志，与原WCF服务保持一致
                string logMessage = $"Send: {function}";
                foreach (var kvp in parameters)
                {
                    logMessage += $", {kvp.Key}: {kvp.Value}";
                }
                _logger.WcfLog(logMessage);

                if (_clientManagerInstance != null)
                {
                    // 使用 Task.Run 来同步调用异步方法，避免死锁
                    var task = Task.Run(async () => await _clientManagerInstance.SendMessageToAllClientsAsync(function, parameters));
                    return task.Result;
                }
                return false;
            }
            catch (Exception ex)
            {
                string errorMessage = $"Server Send Message Error: {ex.Message}, Stack: {ex.StackTrace}";
                _logger.ExceptionLog(errorMessage);
                return false;
            }
        }

        /// <summary>
        /// 服务器向客户端发送消息（异步版本）
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> ServerSendMessageAsync(string function, Dictionary<string, object> parameters)
        {
            try
            {
                if (_clientManagerInstance != null)
                {
                    return await _clientManagerInstance.SendMessageToAllClientsAsync(function, parameters);
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"ServerSendMessageAsync error: {ex.Message}, Stack: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 获取连接的客户端数量
        /// </summary>
        /// <returns>客户端数量</returns>
        public int GetConnectedClientCount()
        {
            try
            {
                return _clientManagerInstance?.GetConnectedClients().Count() ?? 0;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetConnectedClientCount error: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 检查是否有客户端连接
        /// </summary>
        /// <returns>是否有客户端连接</returns>
        public bool HasConnectedClients()
        {
            return GetConnectedClientCount() > 0;
        }
    }
}
