using Proj.Service.Interfaces;
using Proj.Service.Events;
using Proj.Log;

namespace Proj.Service.Services
{
    /// <summary>
    /// WCF兼容性服务，提供与原WCF服务相同的接口
    /// </summary>
    public class WCFCompatibilityService
    {
        private static WCFCompatibilityService? _instance;
        private static readonly object _lock = new();

        private readonly IClientManagerService _clientManager;
        private readonly ServiceEventManager _eventManager;
        private readonly Logger _logger;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static WCFCompatibilityService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new WCFCompatibilityService();
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 客户端发送消息事件
        /// </summary>
        public event Func<string, Dictionary<string, object>, object?>? ClientSendMessage;

        /// <summary>
        /// 客户端获取标签值事件
        /// </summary>
        public event Func<string, object?>? ClientGetTagValue;

        private WCFCompatibilityService()
        {
            _eventManager = ServiceEventManager.Instance;
            _logger = Logger.Instance;
            _clientManager = null!; // 将在Initialize方法中设置
        }

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="clientManager">客户端管理服务</param>
        public void Initialize(IClientManagerService clientManager)
        {
            if (_clientManager == null)
            {
                // 使用反射设置私有字段
                var field = typeof(WCFCompatibilityService).GetField("_clientManager", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                field?.SetValue(this, clientManager);

                // 注册事件处理器
                _eventManager.ClientSendMessage += OnClientSendMessage;
                _eventManager.ClientGetTagValue += OnClientGetTagValue;
            }
        }

        /// <summary>
        /// 处理客户端发送消息事件
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>处理结果</returns>
        private object? OnClientSendMessage(string function, Dictionary<string, object> parameters)
        {
            return ClientSendMessage?.Invoke(function, parameters);
        }

        /// <summary>
        /// 处理客户端获取标签值事件
        /// </summary>
        /// <param name="tagName">标签名称</param>
        /// <returns>标签值</returns>
        private object? OnClientGetTagValue(string tagName)
        {
            return ClientGetTagValue?.Invoke(tagName);
        }

        /// <summary>
        /// 服务器向客户端发送消息
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> ServerSendMessageAsync(string function, Dictionary<string, object> parameters)
        {
            try
            {
                if (_clientManager != null)
                {
                    return await _clientManager.SendMessageToAllClientsAsync(function, parameters);
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"ServerSendMessageAsync error: {ex.Message}, Stack: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 获取连接的客户端数量
        /// </summary>
        /// <returns>客户端数量</returns>
        public int GetConnectedClientCount()
        {
            try
            {
                return _clientManager?.GetConnectedClients().Count() ?? 0;
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetConnectedClientCount error: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 检查是否有客户端连接
        /// </summary>
        /// <returns>是否有客户端连接</returns>
        public bool HasConnectedClients()
        {
            return GetConnectedClientCount() > 0;
        }
    }
}
