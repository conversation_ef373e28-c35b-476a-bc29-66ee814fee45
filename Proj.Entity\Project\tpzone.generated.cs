﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpZone and List
    [Serializable]
    [Description("TP_ZONE")]
    [LinqToDB.Mapping.Table("TP_ZONE")]
    public partial class TpZone : GEntity<TpZone>
    {
        #region Contructor(s)

        private TpZone()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<Int32> pty_Capacity = RegisterProperty<Int32>(p => p.Capacity);
        private static readonly PropertyInfo<String> pty_Comment = RegisterProperty<String>(p => p.Comment);
        private static readonly PropertyInfo<String> pty_HardType = RegisterProperty<String>(p => p.HardType);
        private static readonly PropertyInfo<String> pty_Name = RegisterProperty<String>(p => p.Name);
        private static readonly PropertyInfo<Int32> pty_TotalSize = RegisterProperty<Int32>(p => p.TotalSize);
        private static readonly PropertyInfo<String> pty_Type = RegisterProperty<String>(p => p.Type);
        #endregion

        /// <summary>
        /// Capacity
        /// </summary>
        [Description("Capacity")]
        [LinqToDB.Mapping.Column("Capacity")]
        public Int32 Capacity
        {
            get { return GetProperty(pty_Capacity); }
            set { SetProperty(pty_Capacity, value); }
        }
        /// <summary>
        /// Comment
        /// </summary>
        [Description("Comment")]
        [LinqToDB.Mapping.Column("Comment")]
        public String Comment
        {
            get { return GetProperty(pty_Comment); }
            set { SetProperty(pty_Comment, value); }
        }
        /// <summary>
        /// HardType
        /// </summary>
        [Description("HardType")]
        [LinqToDB.Mapping.Column("Hard_Type")]
        public String HardType
        {
            get { return GetProperty(pty_HardType); }
            set { SetProperty(pty_HardType, value); }
        }
        /// <summary>
        /// Name
        /// </summary>
        [Description("Name")]
        [LinqToDB.Mapping.Column("Name")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String Name
        {
            get { return GetProperty(pty_Name); }
            set { SetProperty(pty_Name, value); }
        }
        /// <summary>
        /// TotalSize
        /// </summary>
        [Description("TotalSize")]
        [LinqToDB.Mapping.Column("Total_Size")]
        public Int32 TotalSize
        {
            get { return GetProperty(pty_TotalSize); }
            set { SetProperty(pty_TotalSize, value); }
        }
        /// <summary>
        /// Type
        /// </summary>
        [Description("Type")]
        [LinqToDB.Mapping.Column("Type")]
        public String Type
        {
            get { return GetProperty(pty_Type); }
            set { SetProperty(pty_Type, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Capacity, "Capacity是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Comment, 255, "Comment不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_HardType, 1, "HardType不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Name, "Name是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Name, 255, "Name不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_TotalSize, "TotalSize是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Type, 1, "Type不能超过1个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Name; }
        #endregion
    }       
        
    [Serializable]
    public partial class TpZoneList : GEntityList<TpZoneList, TpZone>
    {
        private TpZoneList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
