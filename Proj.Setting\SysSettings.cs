﻿using System;
using System.Collections.Generic;
using Proj.Log;
using Proj.DataTypeDef;
using Proj.DB;

namespace Proj.Setting
{
    public class SysSettings
    {
        private static SysSettings m_Instanse;
        private static readonly object mSyncObject = new object();
        private SysSettings() { }
        public static SysSettings Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new SysSettings();
                        }
                    }
                }
                return m_Instanse;
            }
        }

        private CraneIDRReadRule craneIDRReadRule = new CraneIDRReadRule();
        private TimeOut timeOut = new TimeOut();



        public CraneIDRReadRule CraneIDRReadRules
        {
            get { return craneIDRReadRule; }
        }
        public TimeOut TimeOuts
        {
            get { return timeOut; }
        }

        public bool LoadSysSettings()
        {
            if (GetCraneIDRReadRule()
                && true) //其他配置信息
            {
                return true;
            }

            return false;
        }

        private bool GetCraneIDRReadRule()
        {
            Dictionary<string, string> dicSettings = DbSettings.Instance.GetGroupSettings("CraneIDRReadRule");

            try
            {
                craneIDRReadRule.iMaxReadFailCount = int.Parse(dicSettings["MaxReadFailCount"]);
                craneIDRReadRule.bShowIDRBreakDown = bool.Parse(dicSettings["ShowIDRBreakDown"]);
                craneIDRReadRule.bShelf2ShelfReadBypass = bool.Parse(dicSettings["Shelf2ShelfReadBypass"]);
                craneIDRReadRule.bShelf2OutPortReadBypass = bool.Parse(dicSettings["Shelf2OutPortReadBypass"]);
                craneIDRReadRule.bShelf2EqPortReadBypass = bool.Parse(dicSettings["Shelf2EqPortReadBypass"]);
                craneIDRReadRule.bInPort2ShelfReadBypass = bool.Parse(dicSettings["InPort2ShelfReadBypass"]);
                craneIDRReadRule.bInPort2OutPortReadBypass = bool.Parse(dicSettings["InPort2OutPortReadBypass"]);
                craneIDRReadRule.bInPort2EqPortReadBypass = bool.Parse(dicSettings["InPort2EqPortReadBypass"]);
                craneIDRReadRule.bEqPort2ShelfReadBypass = bool.Parse(dicSettings["EqPort2ShelfReadBypass"]);
                craneIDRReadRule.bEqPort2OutPortReadBypass = bool.Parse(dicSettings["EqPort2OutPortReadBypass"]);
                craneIDRReadRule.bEqPort2EqPortReadBypass = bool.Parse(dicSettings["EqPort2EqPortReadBypass"]);
                craneIDRReadRule.bShelf2CraneReadBypass = bool.Parse(dicSettings["Shelf2CraneReadBypass"]);
                craneIDRReadRule.bInPort2CraneReadBypass = bool.Parse(dicSettings["InPort2CraneReadBypass"]);
                craneIDRReadRule.bEqPort2CraneReadBypass = bool.Parse(dicSettings["EqPort2CraneReadBypass"]);
            }
            catch(Exception ex)
            {
                //记录程序异常日志
                Logger.Instance.ExceptionLog("SysSettings.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }

            return true;
        }

        private bool GetTimeOuts()
        {
            Dictionary<string, string> dicSettings = DbSettings.Instance.GetGroupSettings("CraneIDRReadRule");
            try
            {
                timeOut.iCraneOccupied = int.Parse(dicSettings["CraneOccupied"]);
                timeOut.iInPortOccupied = int.Parse(dicSettings["InPortOccupied"]);
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Logger.Instance.ExceptionLog("SysSettings.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }
            return true;
        }

    }
}
