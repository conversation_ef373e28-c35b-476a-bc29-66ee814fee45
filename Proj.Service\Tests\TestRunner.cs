using Proj.Service.Tests;
using Proj.Log;

namespace Proj.Service.Tests
{
    /// <summary>
    /// 测试运行器
    /// </summary>
    public class TestRunner
    {
        private readonly Logger _logger;

        public TestRunner()
        {
            _logger = Logger.Instance;
        }

        /// <summary>
        /// 运行测试
        /// </summary>
        /// <param name="args">命令行参数</param>
        public static async Task RunAsync(string[] args)
        {
            var runner = new TestRunner();
            await runner.RunTestsAsync(args);
        }

        /// <summary>
        /// 运行测试
        /// </summary>
        /// <param name="args">命令行参数</param>
        public async Task RunTestsAsync(string[] args)
        {
            try
            {
                _logger.Info("Starting Stocker Service Test Runner");

                // 解析命令行参数
                var baseUrl = "http://localhost:9900";
                if (args.Length > 0)
                {
                    baseUrl = args[0];
                }

                _logger.Info($"Testing service at: {baseUrl}");

                // 等待服务启动
                _logger.Info("Waiting for service to start...");
                await Task.Delay(5000);

                // 创建测试实例
                using var serviceTest = new ServiceTest(baseUrl);

                // 运行所有测试
                var allTestsPassed = await serviceTest.RunAllTestsAsync();

                if (allTestsPassed)
                {
                    _logger.Info("All tests PASSED!");
                    Console.WriteLine("✅ All tests PASSED!");
                }
                else
                {
                    _logger.Error("Some tests FAILED!");
                    Console.WriteLine("❌ Some tests FAILED!");
                }

                // 等待用户输入
                Console.WriteLine("\nPress any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"TestRunner error: {ex.Message}, Stack: {ex.StackTrace}");
                Console.WriteLine($"❌ Test runner error: {ex.Message}");
            }
        }
    }
}
