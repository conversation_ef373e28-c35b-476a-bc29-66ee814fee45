﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region ThSc and List
    [Serializable]
    [Description("TH_SC")]
    [LinqToDB.Mapping.Table("TH_SC")]
    public partial class ThSc : GEntity<ThSc>
    {
        #region Contructor(s)

        private ThSc()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_Action = RegisterProperty<String>(p => p.Action);
        private static readonly PropertyInfo<Int64> pty_Id = RegisterProperty<Int64>(p => p.Id);
        private static readonly PropertyInfo<String> pty_NewState = RegisterProperty<String>(p => p.NewState);
        private static readonly PropertyInfo<String> pty_OldState = RegisterProperty<String>(p => p.OldState);
        private static readonly PropertyInfo<DateTime?> pty_Time = RegisterProperty<DateTime?>(p => p.Time);
        private static readonly PropertyInfo<String> pty_Unit = RegisterProperty<String>(p => p.Unit);
        #endregion

        /// <summary>
        /// Action
        /// </summary>
        [Description("Action")]
        [LinqToDB.Mapping.Column("Action")]
        public String Action
        {
            get { return GetProperty(pty_Action); }
            set { SetProperty(pty_Action, value); }
        }
        /// <summary>
        /// PrimaryKey
        /// </summary>
        [Description("PrimaryKey")]
        [LinqToDB.Mapping.Column("ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int64 Id
        {
            get { return GetProperty(pty_Id); }
            set { SetProperty(pty_Id, value); }
        }
        /// <summary>
        /// NewState
        /// </summary>
        [Description("NewState")]
        [LinqToDB.Mapping.Column("New_State")]
        public String NewState
        {
            get { return GetProperty(pty_NewState); }
            set { SetProperty(pty_NewState, value); }
        }
        /// <summary>
        /// OldState
        /// </summary>
        [Description("OldState")]
        [LinqToDB.Mapping.Column("Old_State")]
        public String OldState
        {
            get { return GetProperty(pty_OldState); }
            set { SetProperty(pty_OldState, value); }
        }
        /// <summary>
        /// Time
        /// </summary>
        [Description("Time")]
        [LinqToDB.Mapping.Column("Time")]
        public DateTime? Time
        {
            get { return GetProperty(pty_Time); }
            set { SetProperty(pty_Time, value); }
        }
        /// <summary>
        /// Unit
        /// </summary>
        [Description("Unit")]
        [LinqToDB.Mapping.Column("Unit")]
        public String Unit
        {
            get { return GetProperty(pty_Unit); }
            set { SetProperty(pty_Unit, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Action, 64, "Action不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Id, "PrimaryKey是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_NewState, 64, "NewState不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_OldState, 64, "OldState不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Unit, 32, "Unit不能超过32个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Id.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class ThScList : GEntityList<ThScList, ThSc>
    {
        private ThScList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
