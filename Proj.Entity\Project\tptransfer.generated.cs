﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region TpTransfer and List
    [Serializable]
    [Description("指令数据表")]
    [LinqToDB.Mapping.Table("TP_TRANSFER")]
    public partial class TpTransfer : GEntity<TpTransfer>
    {
        #region Contructor(s)

        private TpTransfer()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_Id = RegisterProperty<String>(p => p.Id);
        private static readonly PropertyInfo<String> pty_CarrierId = RegisterProperty<String>(p => p.CarrierId);
        private static readonly PropertyInfo<String> pty_SourceLocation = RegisterProperty<String>(p => p.SourceLocation);
        private static readonly PropertyInfo<String> pty_DestLocation = RegisterProperty<String>(p => p.DestLocation);
        private static readonly PropertyInfo<Int32> pty_Priority = RegisterProperty<Int32>(p => p.Priority);
        private static readonly PropertyInfo<String> pty_CommandType = RegisterProperty<String>(p => p.CommandType);
        private static readonly PropertyInfo<DateTime?> pty_CreateTime = RegisterProperty<DateTime?>(p => p.CreateTime);
        private static readonly PropertyInfo<String> pty_State = RegisterProperty<String>(p => p.State);
        private static readonly PropertyInfo<String> pty_CmdSource = RegisterProperty<String>(p => p.CmdSource);
        private static readonly PropertyInfo<String> pty_TransferType = RegisterProperty<String>(p => p.TransferType);
        private static readonly PropertyInfo<String> pty_CraneName = RegisterProperty<String>(p => p.CraneName);
        private static readonly PropertyInfo<String> pty_AltLocation = RegisterProperty<String>(p => p.AltLocation);
        private static readonly PropertyInfo<String> pty_AltCraneName = RegisterProperty<String>(p => p.AltCraneName);
        private static readonly PropertyInfo<Int32> pty_CalcPriority = RegisterProperty<Int32>(p => p.CalcPriority);
        private static readonly PropertyInfo<String> pty_DelayReason = RegisterProperty<String>(p => p.DelayReason);
        private static readonly PropertyInfo<String> pty_Comment = RegisterProperty<String>(p => p.Comment);
        #endregion

        /// <summary>
        /// CmdID
        /// </summary>
        [Description("CmdID")]
        [LinqToDB.Mapping.Column("Id")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String Id
        {
            get { return GetProperty(pty_Id); }
            set { SetProperty(pty_Id, value); }
        }
        /// <summary>
        /// CarrierID
        /// </summary>
        [Description("CarrierID")]
        [LinqToDB.Mapping.Column("Carrier_ID")]
        public String CarrierId
        {
            get { return GetProperty(pty_CarrierId); }
            set { SetProperty(pty_CarrierId, value); }
        }
        /// <summary>
        /// SrcLoc
        /// </summary>
        [Description("SrcLoc")]
        [LinqToDB.Mapping.Column("Source_Location")]
        public String SourceLocation
        {
            get { return GetProperty(pty_SourceLocation); }
            set { SetProperty(pty_SourceLocation, value); }
        }
        /// <summary>
        /// DestLoc
        /// </summary>
        [Description("DestLoc")]
        [LinqToDB.Mapping.Column("Dest_Location")]
        public String DestLocation
        {
            get { return GetProperty(pty_DestLocation); }
            set { SetProperty(pty_DestLocation, value); }
        }
        /// <summary>
        /// Priority
        /// </summary>
        [Description("Priority")]
        [LinqToDB.Mapping.Column("Priority")]
        public Int32 Priority
        {
            get { return GetProperty(pty_Priority); }
            set { SetProperty(pty_Priority, value); }
        }
        /// <summary>
        /// CommandType
        /// </summary>
        [Description("CommandType")]
        [LinqToDB.Mapping.Column("Command_Type")]
        public String CommandType
        {
            get { return GetProperty(pty_CommandType); }
            set { SetProperty(pty_CommandType, value); }
        }
        /// <summary>
        /// CreateTime
        /// </summary>
        [Description("CreateTime")]
        [LinqToDB.Mapping.Column("Create_Time")]
        public DateTime? CreateTime
        {
            get { return GetProperty(pty_CreateTime); }
            set { SetProperty(pty_CreateTime, value); }
        }
        /// <summary>
        /// State
        /// </summary>
        [Description("State")]
        [LinqToDB.Mapping.Column("State")]
        public String State
        {
            get { return GetProperty(pty_State); }
            set { SetProperty(pty_State, value); }
        }
        /// <summary>
        /// CmdSource
        /// </summary>
        [Description("CmdSource")]
        [LinqToDB.Mapping.Column("Cmd_Source")]
        public String CmdSource
        {
            get { return GetProperty(pty_CmdSource); }
            set { SetProperty(pty_CmdSource, value); }
        }
        /// <summary>
        /// Type
        /// </summary>
        [Description("Type")]
        [LinqToDB.Mapping.Column("Transfer_Type")]
        public String TransferType
        {
            get { return GetProperty(pty_TransferType); }
            set { SetProperty(pty_TransferType, value); }
        }
        /// <summary>
        /// CraneName
        /// </summary>
        [Description("CraneName")]
        [LinqToDB.Mapping.Column("Crane_Name")]
        public String CraneName
        {
            get { return GetProperty(pty_CraneName); }
            set { SetProperty(pty_CraneName, value); }
        }
        /// <summary>
        /// AltLocation
        /// </summary>
        [Description("AltLocation")]
        [LinqToDB.Mapping.Column("Alt_Location")]
        public String AltLocation
        {
            get { return GetProperty(pty_AltLocation); }
            set { SetProperty(pty_AltLocation, value); }
        }
        /// <summary>
        /// AltCraneName
        /// </summary>
        [Description("AltCraneName")]
        [LinqToDB.Mapping.Column("Alt_Crane_Name")]
        public String AltCraneName
        {
            get { return GetProperty(pty_AltCraneName); }
            set { SetProperty(pty_AltCraneName, value); }
        }
        /// <summary>
        /// CalcPriority
        /// </summary>
        [Description("CalcPriority")]
        [LinqToDB.Mapping.Column("Calc_Priority")]
        public Int32 CalcPriority
        {
            get { return GetProperty(pty_CalcPriority); }
            set { SetProperty(pty_CalcPriority, value); }
        }
        /// <summary>
        /// DelayReason
        /// </summary>
        [Description("DelayReason")]
        [LinqToDB.Mapping.Column("Delay_Reason")]
        public String DelayReason
        {
            get { return GetProperty(pty_DelayReason); }
            set { SetProperty(pty_DelayReason, value); }
        }
        /// <summary>
        /// Comment
        /// </summary>
        [Description("Comment")]
        [LinqToDB.Mapping.Column("Comment")]
        public String Comment
        {
            get { return GetProperty(pty_Comment); }
            set { SetProperty(pty_Comment, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Id, "CmdID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Id, 64, "CmdID不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CarrierId, 64, "CarrierID不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_SourceLocation, 64, "SrcLoc不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_DestLocation, 64, "DestLoc不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Priority, "Priority是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CommandType, "CommandType是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CommandType, 20, "CommandType不能超过20个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_State, 1, "State不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CmdSource, 1, "CmdSource不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_TransferType, 2, "Type不能超过2个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CraneName, 64, "CraneName不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_AltLocation, 64, "AltLocation不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_AltCraneName, 64, "AltCraneName不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CalcPriority, "CalcPriority是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_DelayReason, 128, "DelayReason不能超过128个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Comment, 255, "Comment不能超过255个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Id; }
        #endregion
    }       
        
    [Serializable]
    public partial class TpTransferList : GEntityList<TpTransferList, TpTransfer>
    {
        private TpTransferList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
