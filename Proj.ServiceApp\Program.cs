﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Proj.Service.Core
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                //设置应用程序处理异常方式：ThreadException处理
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                //处理UI线程异常
                Application.ThreadException += new System.Threading.ThreadExceptionEventHandler(Application_ThreadException);
                //处理非UI线程异常
                AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);

                #region 应用程序的主入口点
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new ServiceTestForm());
                #endregion
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统错误: " + ex.Message, "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            string strException = "ThreadException: " + e.Exception.Message + ", Stack:" + e.Exception.StackTrace;
            Proj.History.HistoryWriter.Instance.ExceptionLog(strException);
            MessageBox.Show(strException, "线程异常捕捉", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception ex = e.ExceptionObject as Exception;
            string strException = "UnhandledException: " + ex.Message + ", Stack:" + ex.StackTrace;
            Proj.History.HistoryWriter.Instance.ExceptionLog(strException);
            MessageBox.Show(strException, "未知错误捕捉", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

    }
}
