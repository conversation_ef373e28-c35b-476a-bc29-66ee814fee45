# Stocker Service 启动脚本
param(
    [string]$Port = "9900",
    [switch]$Test = $false
)

Write-Host "=== Stocker Service (.NET 8.0) 启动脚本 ===" -ForegroundColor Green
Write-Host ""

# 检查.NET 8.0是否安装
try {
    $dotnetVersion = dotnet --version
    Write-Host "检测到 .NET 版本: $dotnetVersion" -ForegroundColor Yellow
} catch {
    Write-Host "错误: 未检测到 .NET 8.0，请先安装 .NET 8.0 SDK" -ForegroundColor Red
    exit 1
}

# 检查端口是否被占用
$portCheck = netstat -an | Select-String ":$Port"
if ($portCheck) {
    Write-Host "警告: 端口 $Port 可能已被占用" -ForegroundColor Yellow
    Write-Host $portCheck
    Write-Host ""
}

# 创建日志目录
$logDir = "D:\STK_Log\Server_Log"
if (!(Test-Path $logDir)) {
    try {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
        Write-Host "创建日志目录: $logDir" -ForegroundColor Green
    } catch {
        Write-Host "警告: 无法创建日志目录 $logDir" -ForegroundColor Yellow
    }
}

Write-Host "准备启动服务..." -ForegroundColor Yellow
Write-Host "端口: $Port"
Write-Host "URL: http://localhost:$Port"
Write-Host "Swagger UI: http://localhost:$Port"
Write-Host "SignalR Hub: http://localhost:$Port/stockerhub"
Write-Host "健康检查: http://localhost:$Port/health"
Write-Host ""

if ($Test) {
    Write-Host "=== 测试模式 ===" -ForegroundColor Cyan
    Write-Host "构建项目..."
    
    try {
        dotnet build --configuration Release
        if ($LASTEXITCODE -ne 0) {
            Write-Host "构建失败" -ForegroundColor Red
            exit 1
        }
        Write-Host "构建成功" -ForegroundColor Green
    } catch {
        Write-Host "构建过程中出现错误: $_" -ForegroundColor Red
        exit 1
    }
    
    Write-Host ""
    Write-Host "启动服务进行测试..."
    Write-Host "按 Ctrl+C 停止服务" -ForegroundColor Yellow
    Write-Host ""
    
    try {
        dotnet run --urls "http://localhost:$Port"
    } catch {
        Write-Host "服务启动失败: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "=== 生产模式 ===" -ForegroundColor Cyan
    Write-Host "发布项目..."
    
    try {
        dotnet publish --configuration Release --output ./publish
        if ($LASTEXITCODE -ne 0) {
            Write-Host "发布失败" -ForegroundColor Red
            exit 1
        }
        Write-Host "发布成功" -ForegroundColor Green
    } catch {
        Write-Host "发布过程中出现错误: $_" -ForegroundColor Red
        exit 1
    }
    
    Write-Host ""
    Write-Host "启动已发布的服务..."
    Write-Host "按 Ctrl+C 停止服务" -ForegroundColor Yellow
    Write-Host ""
    
    try {
        Set-Location ./publish
        $env:ASPNETCORE_URLS = "http://localhost:$Port"
        dotnet Proj.Service.dll
    } catch {
        Write-Host "服务启动失败: $_" -ForegroundColor Red
        exit 1
    } finally {
        Set-Location ..
    }
}

Write-Host ""
Write-Host "服务已停止" -ForegroundColor Yellow
