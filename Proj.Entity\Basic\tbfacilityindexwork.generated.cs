﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;


namespace Proj.Entity
{
    #region TbFacilityIndexwork and List
    [Serializable]
    [Description("设备指标完成情况表")]
    [LinqToDB.Mapping.Table("TB_FACILITY_INDEXWORK")]
    public partial class TbFacilityIndexwork : GEntity<TbFacilityIndexwork>, ITimestamp
    {
        #region Contructor(s)

        private TbFacilityIndexwork()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_CFacilityindexworkId = RegisterProperty<String>(p => p.CFacilityindexworkId);
        private static readonly PropertyInfo<String> pty_CFacilitylistId = RegisterProperty<String>(p => p.CFacilitylistId);
        private static readonly PropertyInfo<String> pty_CState = RegisterProperty<String>(p => p.CState);
        private static readonly PropertyInfo<String> pty_CIndexworkvalue = RegisterProperty<String>(p => p.CIndexworkvalue);
        private static readonly PropertyInfo<String> pty_CIndexworkstart = RegisterProperty<String>(p => p.CIndexworkstart);
        private static readonly PropertyInfo<String> pty_CIndexworkend = RegisterProperty<String>(p => p.CIndexworkend);
        private static readonly PropertyInfo<String> pty_CFacilityindexId = RegisterProperty<String>(p => p.CFacilityindexId);
        private static readonly PropertyInfo<String> pty_CIndextypeId = RegisterProperty<String>(p => p.CIndextypeId);
        private static readonly PropertyInfo<String> pty_CIndexType = RegisterProperty<String>(p => p.CIndexType);
        private static readonly PropertyInfo<String> pty_CFacilityId = RegisterProperty<String>(p => p.CFacilityId);
        private static readonly PropertyInfo<String> pty_CFacilityName = RegisterProperty<String>(p => p.CFacilityName);
        private static readonly PropertyInfo<String> pty_CExtendfielda = RegisterProperty<String>(p => p.CExtendfielda);
        private static readonly PropertyInfo<String> pty_CExtendfieldb = RegisterProperty<String>(p => p.CExtendfieldb);
        private static readonly PropertyInfo<String> pty_CExtendfieldc = RegisterProperty<String>(p => p.CExtendfieldc);
        #endregion

        /// <summary>
        /// 设备指标完成情况编号
        /// </summary>
        [Description("设备指标完成情况编号")]
        [LinqToDB.Mapping.Column("C_FACILITYINDEXWORKID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String CFacilityindexworkId
        {
            get { return GetProperty(pty_CFacilityindexworkId); }
            set { SetProperty(pty_CFacilityindexworkId, value); }
        }
        /// <summary>
        /// 个体设备编号
        /// </summary>
        [Description("个体设备编号")]
        [LinqToDB.Mapping.Column("C_FACILITYLISTID")]
        public String CFacilitylistId
        {
            get { return GetProperty(pty_CFacilitylistId); }
            set { SetProperty(pty_CFacilitylistId, value); }
        }
        /// <summary>
        /// 个体设备状态
        /// </summary>
        [Description("个体设备状态")]
        [LinqToDB.Mapping.Column("C_STATE")]
        public String CState
        {
            get { return GetProperty(pty_CState); }
            set { SetProperty(pty_CState, value); }
        }
        /// <summary>
        /// 设备指标值
        /// </summary>
        [Description("设备指标值")]
        [LinqToDB.Mapping.Column("C_INDEXWORKVALUE")]
        public String CIndexworkvalue
        {
            get { return GetProperty(pty_CIndexworkvalue); }
            set { SetProperty(pty_CIndexworkvalue, value); }
        }
        /// <summary>
        /// 指标统计开始时间
        /// </summary>
        [Description("指标统计开始时间")]
        [LinqToDB.Mapping.Column("C_INDEXWORKSTART")]
        public String CIndexworkstart
        {
            get { return GetProperty(pty_CIndexworkstart); }
            set { SetProperty(pty_CIndexworkstart, value); }
        }
        /// <summary>
        /// 指标统计完成时间
        /// </summary>
        [Description("指标统计完成时间")]
        [LinqToDB.Mapping.Column("C_INDEXWORKEND")]
        public String CIndexworkend
        {
            get { return GetProperty(pty_CIndexworkend); }
            set { SetProperty(pty_CIndexworkend, value); }
        }
        /// <summary>
        /// 设备指标编号
        /// </summary>
        [Description("设备指标编号")]
        [LinqToDB.Mapping.Column("C_FACILITYINDEXID")]
        public String CFacilityindexId
        {
            get { return GetProperty(pty_CFacilityindexId); }
            set { SetProperty(pty_CFacilityindexId, value); }
        }
        /// <summary>
        /// 指标类型
        /// </summary>
        [Description("指标类型")]
        [LinqToDB.Mapping.Column("C_INDEXTYPEID")]
        public String CIndextypeId
        {
            get { return GetProperty(pty_CIndextypeId); }
            set { SetProperty(pty_CIndextypeId, value); }
        }
        /// <summary>
        /// 中文指标
        /// </summary>
        [Description("中文指标")]
        [LinqToDB.Mapping.Column("C_INDEXTYPE")]
        public String CIndexType
        {
            get { return GetProperty(pty_CIndexType); }
            set { SetProperty(pty_CIndexType, value); }
        }
        /// <summary>
        /// 设备编码
        /// </summary>
        [Description("设备编码")]
        [LinqToDB.Mapping.Column("C_FACILITYID")]
        public String CFacilityId
        {
            get { return GetProperty(pty_CFacilityId); }
            set { SetProperty(pty_CFacilityId, value); }
        }
        /// <summary>
        /// 设备名称
        /// </summary>
        [Description("设备名称")]
        [LinqToDB.Mapping.Column("C_FACILITYNAME")]
        public String CFacilityName
        {
            get { return GetProperty(pty_CFacilityName); }
            set { SetProperty(pty_CFacilityName, value); }
        }
        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("时间戳")]
        [LinqToDB.Mapping.VersionColumn("C_TIMESTAMP")]
        public DateTime CTimestamp
        {
            get; set;
        }
        /// <summary>
        /// 扩展字段A
        /// </summary>
        [Description("扩展字段A")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDA")]
        public String CExtendfielda
        {
            get { return GetProperty(pty_CExtendfielda); }
            set { SetProperty(pty_CExtendfielda, value); }
        }
        /// <summary>
        /// 扩展字段B
        /// </summary>
        [Description("扩展字段B")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDB")]
        public String CExtendfieldb
        {
            get { return GetProperty(pty_CExtendfieldb); }
            set { SetProperty(pty_CExtendfieldb, value); }
        }
        /// <summary>
        /// 扩展字段C
        /// </summary>
        [Description("扩展字段C")]
        [LinqToDB.Mapping.Column("C_EXTENDFIELDC")]
        public String CExtendfieldc
        {
            get { return GetProperty(pty_CExtendfieldc); }
            set { SetProperty(pty_CExtendfieldc, value); }
        }
        #endregion


        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CFacilityindexworkId, "设备指标完成情况编号是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityindexworkId, 36, "设备指标完成情况编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilitylistId, 36, "个体设备编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CState, 40, "个体设备状态不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CIndexworkvalue, 100, "设备指标值不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CIndexworkstart, 76, "指标统计开始时间不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CIndexworkend, 76, "指标统计完成时间不能超过76个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityindexId, 36, "设备指标编号不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CIndextypeId, 36, "指标类型不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CIndexType, 100, "中文指标不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityId, 36, "设备编码不能超过36个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CFacilityName, 100, "设备名称不能超过100个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfielda, 40, "扩展字段A不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldb, 40, "扩展字段B不能超过40个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CExtendfieldc, 40, "扩展字段C不能超过40个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.CFacilityindexworkId; }
        #endregion
    }       
        
    [Serializable]
    public partial class TbFacilityIndexworkList : GEntityList<TbFacilityIndexworkList, TbFacilityIndexwork>
    {
        private TbFacilityIndexworkList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
