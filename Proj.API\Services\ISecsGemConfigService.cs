namespace Proj.API.Services
{
    /// <summary>
    /// SECS/GEM 配置服务接口
    /// </summary>
    public interface ISecsGemConfigService
    {
        /// <summary>
        /// 加载配置
        /// </summary>
        /// <returns>配置对象</returns>
        SecsGemConfig LoadConfig();

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="config">配置对象</param>
        void SaveConfig(SecsGemConfig config);

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        string GetConfigFilePath();
    }
}
