using Proj.Service.Interfaces;
using Proj.Service.Models;
using Proj.Service.Events;
using Proj.CacheData;
using Proj.Log;

namespace Proj.Service.Services
{
    /// <summary>
    /// Stocker服务实现类
    /// </summary>
    public class StockerService : IStockerService
    {
        private readonly Logger _logger;
        private readonly ServiceEventManager _eventManager;

        public StockerService()
        {
            _logger = Logger.Instance;
            _eventManager = ServiceEventManager.Instance;
        }

        /// <summary>
        /// 处理客户端发送的消息
        /// </summary>
        /// <param name="function">功能名称</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>处理结果</returns>
        public async Task<object?> ProcessClientMessageAsync(string function, Dictionary<string, object> parameters)
        {
            return await Task.Run(() =>
            {
                try
                {
                    switch (function)
                    {
                        case "ConnectTest":
                            return true;

                        case "GetState":
                            if (parameters.TryGetValue("NAME", out var nameObj) && nameObj is string stateName)
                            {
                                return GetStateValue(stateName);
                            }
                            return string.Empty;

                        case "GetAllState":
                            return GetAllStatesString();

                        case "GetTagValue":
                            if (parameters.TryGetValue("TAGNAME", out var tagNameObj) && tagNameObj is string tagName)
                            {
                                return _eventManager.OnClientGetTagValue(tagName);
                            }
                            return null;

                        default:
                            // 对于其他功能，触发事件让外部处理
                            return _eventManager.OnClientSendMessage(function, parameters);
                    }
                }
                catch (Exception ex)
                {
                    _logger.ExceptionLog($"ProcessClientMessageAsync error: {ex.Message}, Stack: {ex.StackTrace}");
                    return false;
                }
            });
        }

        /// <summary>
        /// 获取系统状态
        /// </summary>
        /// <param name="stateName">状态名称</param>
        /// <returns>状态值</returns>
        public async Task<string> GetStateAsync(string stateName)
        {
            return await Task.Run(() => GetStateValue(stateName));
        }

        /// <summary>
        /// 获取所有系统状态
        /// </summary>
        /// <returns>所有状态信息</returns>
        public async Task<SystemStateInfo> GetAllStatesAsync()
        {
            return await Task.Run(() => new SystemStateInfo
            {
                HSMSState = GlobalData.Instance.gbHSMSState.ToString(),
                MCSCommunication = GlobalData.Instance.gbMCSCommunication.ToString(),
                ControlState = GlobalData.Instance.gbControlState.ToString(),
                SCState = GlobalData.Instance.gbSCState.ToString(),
                SCMode = GlobalData.Instance.gbSCMode.ToString(),
                MachineState = GlobalData.Instance.gbMachineState.ToString()
            });
        }

        /// <summary>
        /// 获取标签值
        /// </summary>
        /// <param name="tagName">标签名称</param>
        /// <returns>标签值</returns>
        public async Task<object?> GetTagValueAsync(string tagName)
        {
            return await Task.Run(() => _eventManager.OnClientGetTagValue(tagName));
        }

        /// <summary>
        /// 连接测试
        /// </summary>
        /// <returns>连接是否正常</returns>
        public async Task<bool> TestConnectionAsync()
        {
            return await Task.Run(() => true);
        }

        /// <summary>
        /// 获取状态值
        /// </summary>
        /// <param name="stateName">状态名称</param>
        /// <returns>状态值</returns>
        private string GetStateValue(string stateName)
        {
            try
            {
                return stateName switch
                {
                    "HSMS State" => GlobalData.Instance.gbHSMSState.ToString(),
                    "MCS Communication" => GlobalData.Instance.gbMCSCommunication.ToString(),
                    "Control State" => GlobalData.Instance.gbControlState.ToString(),
                    "SC State" => GlobalData.Instance.gbSCState.ToString(),
                    "SC Mode" => GlobalData.Instance.gbSCMode.ToString(),
                    "Machine State" => GlobalData.Instance.gbMachineState.ToString(),
                    _ => string.Empty
                };
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetStateValue error for {stateName}: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取所有状态的字符串表示
        /// </summary>
        /// <returns>状态字符串</returns>
        private string GetAllStatesString()
        {
            try
            {
                var states = new List<string>
                {
                    $"HSMS State,{GlobalData.Instance.gbHSMSState}",
                    $"MCS Communication,{GlobalData.Instance.gbMCSCommunication}",
                    $"Control State,{GlobalData.Instance.gbControlState}",
                    $"SC State,{GlobalData.Instance.gbSCState}",
                    $"SC Mode,{GlobalData.Instance.gbSCMode}",
                    $"Machine State,{GlobalData.Instance.gbMachineState}"
                };

                return string.Join(";", states);
            }
            catch (Exception ex)
            {
                _logger.ExceptionLog($"GetAllStatesString error: {ex.Message}");
                return string.Empty;
            }
        }
    }
}
