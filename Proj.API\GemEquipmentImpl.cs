using Microsoft.Extensions.Options;
using Proj.API.Services;
using Proj.Log;
using Secs4Net;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace Proj.API
{
    public class GemEquipmentImpl : IGemEquipment, IDisposable
    {
        private readonly BindingList<Secs4Net.PrimaryMessageWrapper> recvBuffer = new();
        private CancellationTokenSource _cancellationTokenSource = new();
        private readonly ISecsGemLogger _logger;
        private readonly ISecsGemConfigService _configService;
        private readonly ISecsConnectionFactory _connectionFactory;
        public SecsGem SecsGem { get; set; }
        public IOptions<SecsGemOptions> Option { get; set; }
        private bool _disposed = false;

        public string MDLN { get; set; } = "AMHS";

        /// <summary>
        /// 软件版本号 - 只读属性，自动获取程序集版本号
        /// </summary>
        public string SoftRev
        {
            get
            {
                try
                {
                    var assembly = Assembly.GetExecutingAssembly();
                    var version = assembly.GetName().Version;
                    return version != null ? $"V{version.Major}.{version.Minor}.{version.Build}" : "V1.0.0";
                }
                catch
                {
                    return "V1.0.0";
                }
            }
        }
        public int CommunicationState { get; set; }
        public int ControlState { get; set; }
        public bool IsOnline { get; private set; }
        public bool IsLocal { get; private set; } = true;
        public SecsMessage? PrimaryMessage { get; set; }

        public event EventHandler<ConnectionState>? ConnectionChanged;
        public event EventHandler<uint>? EventPosted;
        public event EventHandler<uint>? AlarmPosted;
        public event EventHandler<uint>? AlarmCleared;

        // 数据存储
        private readonly Dictionary<uint, DVItem> _dataVariables = new();
        private readonly Dictionary<uint, SVItem> _statusVariables = new();
        private readonly Dictionary<uint, ECItem> _equipmentConstants = new();
        private readonly Dictionary<uint, EventItem> _events = new();
        private readonly Dictionary<uint, AlarmItem> _alarms = new();
        private readonly Dictionary<uint, uint> _eventReportLinks = new();
        private readonly Dictionary<uint, List<uint>> _reportVariables = new();
        private readonly HashSet<uint> _activeAlarms = new();

        /// <summary>
        /// 依赖注入构造函数 (推荐使用)
        /// </summary>
        /// <param name="configService">配置服务</param>
        /// <param name="connectionFactory">连接工厂</param>
        /// <param name="logger">日志记录器</param>
        public GemEquipmentImpl(
            ISecsGemConfigService configService,
            ISecsConnectionFactory connectionFactory,
            ISecsGemLogger logger)
        {
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));
            _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeFromConfig();
        }

        /// <summary>
        /// 无参构造函数 - 从配置文件加载参数 (兼容性保留)
        /// </summary>
        public GemEquipmentImpl()
        {
            // 创建默认依赖
            _configService = new SecsGemConfigService();
            _connectionFactory = new SecsConnectionFactory();
            _logger = new SecsLogger();

            InitializeFromConfig();
        }

        /// <summary>
        /// 从配置初始化设备
        /// </summary>
        private void InitializeFromConfig()
        {
            ControlState = (int)Proj.API.ControlState.OfflineEquipment;
            CommunicationState = 0;

            // 从配置服务加载配置
            var config = _configService.LoadConfig();

            var options = Options.Create(new SecsGemOptions
            {
                IsActive = config.IsActive,
                IpAddress = config.IpAddress,
                Port = config.Port,
                SocketReceiveBufferSize = config.SocketReceiveBufferSize,
                DeviceId = config.DeviceId,
                // 设置超时时间 (单位: 毫秒)
                T3 = config.T3,  // Reply Timeout - 回复超时时间
                T5 = config.T5,  // Connect Separation Time - 连接分离时间
                T6 = config.T6,  // Control Transaction Timeout - 控制事务超时
                T7 = config.T7,  // Not Selected Timeout - 未选择超时
                T8 = config.T8,  // Network Intercharacter Timeout - 网络字符间超时
            });

            Option = options;
            Connection = _connectionFactory.CreateHsmsConnection(Option, _logger);
            SecsGem = _connectionFactory.CreateSecsGem(Option, Connection, _logger);

            // 订阅连接状态变化事件
            Connection.ConnectionChanged += OnConnectionStateChanged;

            Console.WriteLine($"[GemEquipmentImpl] 初始化完成 - IsActive: {config.IsActive}, IP: {config.IpAddress}:{config.Port}, DeviceId: {config.DeviceId}");
        }



        /// <summary>
        /// 有参构造函数 - 直接传入参数
        /// </summary>
        public GemEquipmentImpl(bool isactive,string ip,int port,int socketReceiveBufferSize, ushort deviceId)
        {
            ControlState = (int)Proj.API.ControlState.OfflineEquipment;
            CommunicationState = 0;

            var options = Options.Create(new SecsGemOptions
            {
                IsActive = isactive,
                IpAddress = ip,
                Port = port,
                SocketReceiveBufferSize = socketReceiveBufferSize,
                DeviceId = deviceId,
                // 设置超时时间 (单位: 毫秒)
                T3 = 45000,    // Reply Timeout - 回复超时时间 (45秒)
                T5 = 10000,    // Connect Separation Time - 连接分离时间 (10秒)
                T6 = 5000,     // Control Transaction Timeout - 控制事务超时 (5秒)
                T7 = 10000,    // Not Selected Timeout - 未选择超时 (10秒)
                T8 = 5000      // Network Intercharacter Timeout - 网络字符间超时 (5秒)
            });
            Option = options;
            Connection = new HsmsConnection(Option, _logger);
            SecsGem = new SecsGem(Option, Connection, _logger);
            // 订阅连接状态变化事件
            Connection.ConnectionChanged += OnConnectionStateChanged;
        }

        public async Task ConnectAsync()
        {
            try
            {
                if (Connection?.State == ConnectionState.Selected)
                {
                    return;
                }

                Connection.Start(_cancellationTokenSource.Token);

                // 启动消息处理循环
                _ = Task.Run(async () => await ProcessMessagesAsync());

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                throw;
            }
        }







        private void OnConnectionStateChanged(object? sender, ConnectionState state)
        {
            CommunicationState = (int)state;
            IsOnline = state == ConnectionState.Selected;
            ConnectionChanged?.Invoke(this, state);
            Console.WriteLine($"Connection state changed to: {state}");
        }



        /// <summary>
        /// 处理消息的主循环 - 基于 Secs4Net 标准模式
        /// </summary>
        private async Task ProcessMessagesAsync()
        {
            try
            {
                await foreach (var primaryMessage in SecsGem.GetPrimaryMessageAsync(_cancellationTokenSource.Token))
                {
                    if (_cancellationTokenSource.Token.IsCancellationRequested)
                        break;

                    recvBuffer.Add(primaryMessage);

                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            var response = await ProcessPrimaryMessageAsync(primaryMessage.PrimaryMessage);
                            if (response != null)
                            {
                                await primaryMessage.TryReplyAsync(response);
                            }
                        }
                        catch (Exception ex)
                        {
                            // 记录错误但不中断处理
                        }
                    });
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消
            }
            catch (Exception ex)
            {
                // 记录错误
            }
        }







        public void Disconnect()
        {
            try
            {
                Console.WriteLine("[DEBUG] 开始断开连接...");

                // 取消当前的操作
                _cancellationTokenSource?.Cancel();

                // 强制停止连接
                if (Connection != null)
                {
                    Console.WriteLine($"[DEBUG] 当前连接状态: {Connection.State}");

                    // 强制断开连接
                    try
                    {
                        Connection.DisposeAsync().AsTask().Wait(TimeSpan.FromSeconds(3));
                        Console.WriteLine("[DEBUG] Connection 已强制断开");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[DEBUG] 强制断开连接时出错: {ex.Message}");
                    }
                    finally
                    {
                        // 清空连接对象
                        Connection = null!;
                        Console.WriteLine("[DEBUG] Connection 对象已清空");
                    }
                }

                // 更新状态
                IsOnline = false;
                CommunicationState = 0;
                ControlState = (int)Proj.API.ControlState.OfflineEquipment;

                // 连接已断开

                Console.WriteLine("[DEBUG] GEM Equipment disconnected successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error disconnecting equipment: {ex.Message}");
                throw;
            }
        }



        /// <summary>
        /// 模拟消息接收处理
        /// </summary>
        public async Task SimulateMessageReceived(SecsMessage message)
        {
            await Task.CompletedTask;
            Console.WriteLine($"Simulating received message S{message.S}F{message.F}");
            PrimaryMessage = message;

            // 处理消息并生成回复
            var response = await ProcessPrimaryMessageAsync(message);

            if (response != null)
            {
                Console.WriteLine($"Generated response S{response.S}F{response.F}");
            }
        }



        private async Task<SecsMessage?> ProcessPrimaryMessageAsync(SecsMessage message)
        {
            try
            {
                _logger?.Info($"处理消息: S{message.S}F{message.F}");

                return (message.S, message.F) switch
                {
                    // S1F1 - Are You There Request
                    (1, 1) => new SecsMessage(1, 2) { SecsItem = Item.L() },

                    // S1F3 - Selected Equipment Status Request
                    (1, 3) => await ProcessS1F3Async(message),

                    // S1F13 - Establish Communications Request
                    (1, 13) => new SecsMessage(1, 14)
                    {
                        SecsItem = Item.L(
                            Item.U1(0), // COMMACK - Accept
                            Item.L(
                                Item.A(MDLN ?? ""),
                                Item.A(SoftRev ?? "")
                            )
                        )
                    },

                    // S1F15 - Request OFF-LINE
                    (1, 15) => await ProcessS1F15Async(message),

                    // S1F17 - Request ON-LINE
                    (1, 17) => await ProcessS1F17Async(message),

                    // S2F17 - Date and Time Request
                    (2, 17) => new SecsMessage(2, 18)
                    {
                        SecsItem = Item.A(DateTime.Now.ToString("yyyyMMddHHmmss"))
                    },

                    // S2F31 - Date and Time Set Request
                    (2, 31) => await ProcessS2F31Async(message),

                    // S2F33 - Define Report
                    (2, 33) => await ProcessS2F33Async(message),

                    // S2F35 - Link Event Report
                    (2, 35) => await ProcessS2F35Async(message),

                    // S2F37 - Enable/Disable Event Report
                    (2, 37) => await ProcessS2F37Async(message),

                    // S6F15 - Event Report Request
                    (6, 15) => await ProcessS6F15Async(message),

                    // S6F19 - Individual Report Request
                    (6, 19) => await ProcessS6F19Async(message),

                    _ => await ProcessUnknownMessageAsync(message)
                };
            }
            catch (Exception ex)
            {
                _logger?.Error($"处理消息失败: S{message.S}F{message.F}, {ex.Message}", message, ex);
                return null;
            }
        }

        public async Task<SecsMessage> SendAsync(SecsMessage message, CancellationToken cancellationToken = default)
        {
            // 检查连接状态是否为 Selected
            if (Connection?.State != ConnectionState.Selected)
            {
                throw new InvalidOperationException($"Equipment not in Selected state. Current state: {Connection?.State}. Please wait for connection to be established.");
            }

            Console.WriteLine($"[SEND] S{message.S}F{message.F} - 开始发送...");

            try
            {
                // 使用自定义超时时间
                using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

                var reply = await SecsGem.SendAsync(message, combinedCts.Token);
                Console.WriteLine($"[SUCCESS] S{message.S}F{message.F} → S{reply.S}F{reply.F}");
                return reply;
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                Console.WriteLine($"[CANCELLED] S{message.S}F{message.F} - 操作被取消");
                throw;
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine($"[TIMEOUT] S{message.S}F{message.F} - T3 超时 (10秒内未收到回复)");
                throw new TimeoutException($"S{message.S}F{message.F} 消息超时：10秒内未收到回复");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] S{message.S}F{message.F} - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 生成模拟的回复消息
        /// </summary>
        private async Task<SecsMessage> GenerateSimulatedResponse(SecsMessage primaryMessage)
        {
            await Task.CompletedTask;

            return (primaryMessage.S, primaryMessage.F) switch
            {
                // S1F1 -> S1F2
                (1, 1) => new SecsMessage(1, 2) { SecsItem = Item.L() },

                // S1F13 -> S1F14
                (1, 13) => new SecsMessage(1, 14)
                {
                    SecsItem = Item.L(
                        Item.U1(0), // COMMACK - Accept
                        Item.L(
                            Item.A(MDLN ?? ""),
                            Item.A(SoftRev ?? "")
                        )
                    )
                },

                // S2F17 -> S2F18
                (2, 17) => new SecsMessage(2, 18)
                {
                    SecsItem = Item.A(DateTime.Now.ToString("yyyyMMddHHmmss"))
                },

                // S1F3 -> S1F4
                (1, 3) => new SecsMessage(1, 4)
                {
                    SecsItem = Item.L(
                        Item.U1((byte)CommunicationState),
                        Item.U1((byte)ControlState)
                    )
                },

                // 默认回复
                _ => new SecsMessage((byte)primaryMessage.S, (byte)(primaryMessage.F + 1))
                {
                    SecsItem = Item.L()
                }
            };
        }




        public HsmsConnection Connection { get; set; } = null!; // 简化实现

        // 添加缺失的方法
        public void SendMessage(int stream, int function, Item secsItem, bool replyExpected = true)
        {
            if (SecsGem == null)
                throw new InvalidOperationException("SecsGem not initialized. Call Connect() first.");

            Console.WriteLine($"Sending message S{stream}F{function}");
            var message = new SecsMessage((byte)stream, (byte)function) { SecsItem = secsItem };

            _ = Task.Run(async () =>
            {
                try
                {
                    if (replyExpected)
                    {
                        var response = await SecsGem.SendAsync(message);
                        Console.WriteLine($"Received response S{response.S}F{response.F}");
                    }
                    else
                    {
                        await SecsGem.SendAsync(message);
                        Console.WriteLine($"Message S{stream}F{function} sent successfully");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error sending message S{stream}F{function}: {ex.Message}");
                }
            });
        }

        public void ReplyMessage(SecsMessage primaryMessage, SecsMessage replyMessage)
        {
            if (!IsOnline)
            {
                Console.WriteLine("Warning: Equipment not connected, cannot send reply");
                return;
            }

            try
            {
                // 模拟回复发送
                Console.WriteLine($"Reply sent: S{replyMessage.S}F{replyMessage.F} to S{primaryMessage.S}F{primaryMessage.F}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending reply: {ex.Message}");
            }
        }

        public ConnectionState GetConnectionState() => (ConnectionState)CommunicationState;
        public ControlState GetControlState() => (ControlState)ControlState;

        // 简化的接口实现
        public virtual async Task<bool> OnCommEstablishedAsync() { await Task.CompletedTask; return true; }
        public virtual async Task OnCommDisabledAsync() { await Task.CompletedTask; }
        public virtual async Task<bool> OnHostRequestOnlineAsync() { await Task.CompletedTask; return true; }
        public virtual async Task OnSwitchOfflineAsync() { await Task.CompletedTask; }
        public virtual async Task OnSwitchLocalAsync() { await Task.CompletedTask; }
        public virtual async Task OnSwitchRemoteAsync() { await Task.CompletedTask; }
        public virtual async Task<Item> OnGetDVAsync(uint dvid)
        {
            await Task.CompletedTask;
            try
            {
                if (_dataVariables.TryGetValue(dvid, out var dv))
                {
                    // 根据数据类型转换为相应的SECS Item
                    return ConvertValueToSecsItem(dv.Value);
                }
                else
                {
                    _logger?.Warning($"请求的数据变量不存在: DVID={dvid}");
                    return Item.L(); // 返回空列表表示无效
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"获取数据变量失败: DVID={dvid}, {ex.Message}", null, ex);
                return Item.L();
            }
        }

        public virtual async Task<Item> OnGetSVAsync(uint svid)
        {
            await Task.CompletedTask;
            try
            {
                if (_statusVariables.TryGetValue(svid, out var sv))
                {
                    return ConvertValueToSecsItem(sv.Value);
                }
                else
                {
                    _logger?.Warning($"请求的状态变量不存在: SVID={svid}");
                    return Item.L();
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"获取状态变量失败: SVID={svid}, {ex.Message}", null, ex);
                return Item.L();
            }
        }

        public virtual async Task<Item> OnGetECAsync(uint ecid)
        {
            await Task.CompletedTask;
            try
            {
                if (_equipmentConstants.TryGetValue(ecid, out var ec))
                {
                    return ConvertValueToSecsItem(ec.Value);
                }
                else
                {
                    _logger?.Warning($"请求的设备常数不存在: ECID={ecid}");
                    return Item.L();
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"获取设备常数失败: ECID={ecid}, {ex.Message}", null, ex);
                return Item.L();
            }
        }

        public virtual async Task<bool> OnSetECAsync(uint ecid, Item value)
        {
            await Task.CompletedTask;
            try
            {
                if (_equipmentConstants.TryGetValue(ecid, out var ec))
                {
                    var newValue = ConvertSecsItemToValue(value);

                    // 验证值范围（如果定义了）
                    if (IsValueInRange(newValue, ec.MinValue, ec.MaxValue))
                    {
                        ec.Value = newValue;
                        _logger?.Info($"设备常数已更新: ECID={ecid}, 新值={newValue}");
                        return true;
                    }
                    else
                    {
                        _logger?.Warning($"设备常数值超出范围: ECID={ecid}, 值={newValue}, 范围=[{ec.MinValue}, {ec.MaxValue}]");
                        return false;
                    }
                }
                else
                {
                    _logger?.Warning($"要设置的设备常数不存在: ECID={ecid}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"设置设备常数失败: ECID={ecid}, {ex.Message}", null, ex);
                return false;
            }
        }

        public async Task<bool> UpdateSVAsync(uint svid, object value)
        {
            await Task.CompletedTask;
            try
            {
                if (_statusVariables.TryGetValue(svid, out var sv))
                {
                    var oldValue = sv.Value;
                    sv.Value = value;
                    _logger?.Info($"状态变量已更新: SVID={svid}, 原值={oldValue}, 新值={value}");

                    // 发送状态变量变化事件 (可选)
                    await PostEventAsync(500, Item.L(
                        Item.U4(svid),
                        ConvertValueToSecsItem(oldValue),
                        ConvertValueToSecsItem(value)
                    ));

                    return true;
                }
                else
                {
                    _logger?.Warning($"要更新的状态变量不存在: SVID={svid}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"更新状态变量失败: SVID={svid}, {ex.Message}", null, ex);
                return false;
            }
        }
        public virtual bool OnConnect() { return true; }
        public virtual void OnDisconnect() { }

        /// <summary>
        /// 将C#值转换为SECS Item
        /// </summary>
        private Item ConvertValueToSecsItem(object value)
        {
            return value switch
            {
                string str => Item.A(str),
                bool b => Item.Boolean(b),
                byte b => Item.U1(b),
                ushort us => Item.U2(us),
                uint ui => Item.U4(ui),
                ulong ul => Item.U8(ul),
                sbyte sb => Item.I1(sb),
                short s => Item.I2(s),
                int i => Item.I4(i),
                long l => Item.I8(l),
                float f => Item.F4(f),
                double d => Item.F8(d),
                DateTime dt => Item.A(dt.ToString("yyyyMMddHHmmss")),
                _ => Item.A(value?.ToString() ?? "")
            };
        }

        /// <summary>
        /// 将SECS Item转换为C#值
        /// </summary>
        private object ConvertSecsItemToValue(Item item)
        {
            try
            {
                return item.Format switch
                {
                    SecsFormat.ASCII => item.ToString(),
                    SecsFormat.Boolean => item.ToString() == "True",
                    SecsFormat.U1 => Convert.ToByte(item.ToString()),
                    SecsFormat.U2 => Convert.ToUInt16(item.ToString()),
                    SecsFormat.U4 => Convert.ToUInt32(item.ToString()),
                    SecsFormat.U8 => Convert.ToUInt64(item.ToString()),
                    SecsFormat.I1 => Convert.ToSByte(item.ToString()),
                    SecsFormat.I2 => Convert.ToInt16(item.ToString()),
                    SecsFormat.I4 => Convert.ToInt32(item.ToString()),
                    SecsFormat.I8 => Convert.ToInt64(item.ToString()),
                    SecsFormat.F4 => Convert.ToSingle(item.ToString()),
                    SecsFormat.F8 => Convert.ToDouble(item.ToString()),
                    _ => item.ToString()
                };
            }
            catch (Exception ex)
            {
                _logger?.Warning($"SECS Item转换失败: {ex.Message}, 使用字符串值");
                return item.ToString();
            }
        }

        /// <summary>
        /// 检查值是否在指定范围内
        /// </summary>
        private bool IsValueInRange(object value, object minValue, object maxValue)
        {
            try
            {
                if (value is IComparable comparable && minValue is IComparable min && maxValue is IComparable max)
                {
                    return comparable.CompareTo(min) >= 0 && comparable.CompareTo(max) <= 0;
                }
                return true; // 如果无法比较，则认为有效
            }
            catch
            {
                return true; // 比较失败时认为有效
            }
        }

        /// <summary>
        /// 获取变量值（支持DV和SV）
        /// </summary>
        private async Task<Item> GetVariableValueAsync(uint vid)
        {
            await Task.CompletedTask;

            // 先尝试从DV中获取
            if (_dataVariables.TryGetValue(vid, out var dv))
            {
                return ConvertValueToSecsItem(dv.Value);
            }

            // 再尝试从SV中获取
            if (_statusVariables.TryGetValue(vid, out var sv))
            {
                return ConvertValueToSecsItem(sv.Value);
            }

            // 如果都没找到，返回空值
            return Item.A("");
        }

        /// <summary>
        /// 发送S6F11事件报告
        /// </summary>
        private async Task SendEventReportAsync(uint eventId, List<Item> reportData)
        {
            try
            {
                if (Connection == null || Connection.State != ConnectionState.Selected)
                {
                    _logger?.Warning($"连接未建立，无法发送事件报告。Connection: {Connection?.State}");
                    Console.WriteLine($"[DEBUG] 事件发送失败 - 连接状态: {Connection?.State}");
                    return;
                }

                var eventReport = Item.L(
                    Item.U4(eventId),                    // CEID - Collection Event ID
                    Item.L(reportData.ToArray())         // Report Data
                );

                var message = new SecsMessage(6, 11, false) // S6F11 - Event Report Send
                {
                    SecsItem = eventReport
                };

                await SecsGem.SendAsync(message);
                _logger?.Info($"S6F11事件报告已发送: EventID={eventId}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"发送S6F11事件报告失败: EventID={eventId}, {ex.Message}", null, ex);
            }
        }

        /// <summary>
        /// 发送S5F1报警报告
        /// </summary>
        private async Task SendAlarmReportAsync(uint alarmId, bool isSet, string? alarmText = null, Item? alarmData = null)
        {
            try
            {
                if (Connection == null || Connection.State != ConnectionState.Selected)
                {
                    _logger?.Warning($"连接未建立，无法发送报警报告。Connection: {Connection?.State}");
                    Console.WriteLine($"[DEBUG] 报警发送失败 - 连接状态: {Connection?.State}");
                    return;
                }

                // 处理中文字符编码问题
                var alarmTextBytes = System.Text.Encoding.UTF8.GetBytes(alarmText ?? "");
                var alarmTextAscii = System.Text.Encoding.ASCII.GetString(
                    System.Text.Encoding.Convert(System.Text.Encoding.UTF8, System.Text.Encoding.ASCII, alarmTextBytes)
                        .Select(b => b == 63 ? (byte)32 : b).ToArray()); // 将无法转换的字符替换为空格

                var alarmItems = new List<Item>
                {
                    Item.U1((byte)(isSet ? 128 : 0)),    // ALCD - Alarm Code (128=Set, 0=Clear)
                    Item.U4(alarmId),                     // ALID - Alarm ID
                    Item.A(alarmTextAscii)                // ALTX - Alarm Text (ASCII only)
                };

                if (alarmData != null)
                {
                    alarmItems.Add(alarmData);
                }

                var message = new SecsMessage(5, 1, true) // S5F1 - Alarm Report Send
                {
                    SecsItem = Item.L(alarmItems.ToArray())
                };

                var response = await SecsGem.SendAsync(message);
                _logger?.Info($"S5F1报警报告已发送: AlarmID={alarmId}, IsSet={isSet}, Response=S{response.S}F{response.F}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"发送S5F1报警报告失败: AlarmID={alarmId}, {ex.Message}", null, ex);
            }
        }

        /// <summary>
        /// 处理S1F3 - Selected Equipment Status Request
        /// </summary>
        private async Task<SecsMessage> ProcessS1F3Async(SecsMessage message)
        {
            await Task.CompletedTask;

            // 获取请求的状态变量ID列表
            var requestedSvids = new List<uint>();
            if (message.SecsItem != null && message.SecsItem.Format == SecsFormat.List)
            {
                for (int i = 0; i < message.SecsItem.Count; i++)
                {
                    var item = message.SecsItem[i];
                    if (item.Format == SecsFormat.U4)
                    {
                        requestedSvids.Add(Convert.ToUInt32(item.ToString()));
                    }
                }
            }

            // 构建响应数据
            var responseItems = new List<Item>();
            foreach (var svid in requestedSvids)
            {
                var value = await OnGetSVAsync(svid);
                responseItems.Add(value);
            }

            return new SecsMessage(1, 4)
            {
                SecsItem = Item.L(responseItems.ToArray())
            };
        }

        /// <summary>
        /// 处理S1F15 - Request OFF-LINE
        /// </summary>
        private async Task<SecsMessage> ProcessS1F15Async(SecsMessage message)
        {
            await Task.CompletedTask;

            try
            {
                await SwitchOfflineAsync();
                return new SecsMessage(1, 16) { SecsItem = Item.U1(0) }; // OFLACK - Acknowledge
            }
            catch
            {
                return new SecsMessage(1, 16) { SecsItem = Item.U1(1) }; // OFLACK - Denied
            }
        }

        /// <summary>
        /// 处理S1F17 - Request ON-LINE
        /// </summary>
        private async Task<SecsMessage> ProcessS1F17Async(SecsMessage message)
        {
            await Task.CompletedTask;

            try
            {
                await SwitchOnlineAsync();
                return new SecsMessage(1, 18) { SecsItem = Item.U1(0) }; // ONLACK - Acknowledge
            }
            catch
            {
                return new SecsMessage(1, 18) { SecsItem = Item.U1(1) }; // ONLACK - Denied
            }
        }

        /// <summary>
        /// 处理S2F31 - Date and Time Set Request
        /// </summary>
        private async Task<SecsMessage> ProcessS2F31Async(SecsMessage message)
        {
            await Task.CompletedTask;

            try
            {
                // 这里可以实现时间同步逻辑
                // 目前简单返回接受
                return new SecsMessage(2, 32) { SecsItem = Item.U1(0) }; // TIACK - Acknowledge
            }
            catch
            {
                return new SecsMessage(2, 32) { SecsItem = Item.U1(1) }; // TIACK - Error
            }
        }

        /// <summary>
        /// 处理S2F33 - Define Report
        /// </summary>
        private async Task<SecsMessage> ProcessS2F33Async(SecsMessage message)
        {
            await Task.CompletedTask;

            try
            {
                // 解析报告定义
                if (message.SecsItem != null && message.SecsItem.Format == SecsFormat.List)
                {
                    // 这里可以实现报告定义的解析和存储
                    // 简化实现，直接返回接受
                }

                return new SecsMessage(2, 34) { SecsItem = Item.U1(0) }; // DRACK - Acknowledge
            }
            catch
            {
                return new SecsMessage(2, 34) { SecsItem = Item.U1(1) }; // DRACK - Denied
            }
        }

        /// <summary>
        /// 处理S2F35 - Link Event Report
        /// </summary>
        private async Task<SecsMessage> ProcessS2F35Async(SecsMessage message)
        {
            await Task.CompletedTask;

            try
            {
                // 解析事件报告关联
                if (message.SecsItem != null && message.SecsItem.Format == SecsFormat.List)
                {
                    // 这里可以实现事件报告关联的解析和存储
                    // 简化实现，直接返回接受
                }

                return new SecsMessage(2, 36) { SecsItem = Item.U1(0) }; // LRACK - Acknowledge
            }
            catch
            {
                return new SecsMessage(2, 36) { SecsItem = Item.U1(1) }; // LRACK - Denied
            }
        }

        /// <summary>
        /// 处理S2F37 - Enable/Disable Event Report
        /// </summary>
        private async Task<SecsMessage> ProcessS2F37Async(SecsMessage message)
        {
            await Task.CompletedTask;

            try
            {
                // 解析事件启用/禁用请求
                if (message.SecsItem != null && message.SecsItem.Format == SecsFormat.List)
                {
                    // 这里可以实现事件启用/禁用的逻辑
                    // 简化实现，直接返回接受
                }

                return new SecsMessage(2, 38) { SecsItem = Item.U1(0) }; // ERACK - Acknowledge
            }
            catch
            {
                return new SecsMessage(2, 38) { SecsItem = Item.U1(1) }; // ERACK - Denied
            }
        }

        /// <summary>
        /// 处理S6F15 - Event Report Request
        /// </summary>
        private async Task<SecsMessage> ProcessS6F15Async(SecsMessage message)
        {
            await Task.CompletedTask;

            try
            {
                // 获取请求的事件ID
                var requestedCeids = new List<uint>();
                if (message.SecsItem != null && message.SecsItem.Format == SecsFormat.List)
                {
                    for (int i = 0; i < message.SecsItem.Count; i++)
                    {
                        var item = message.SecsItem[i];
                        if (item.Format == SecsFormat.U4)
                        {
                            requestedCeids.Add(Convert.ToUInt32(item.ToString()));
                        }
                    }
                }

                // 构建事件报告响应
                var eventReports = new List<Item>();
                foreach (var ceid in requestedCeids)
                {
                    if (_events.ContainsKey(ceid))
                    {
                        // 构建事件报告数据
                        var reportData = new List<Item>();
                        if (_eventReportLinks.TryGetValue(ceid, out var reportId))
                        {
                            if (_reportVariables.TryGetValue(reportId, out var variables))
                            {
                                foreach (var vid in variables)
                                {
                                    var value = await GetVariableValueAsync(vid);
                                    reportData.Add(value);
                                }
                            }
                        }

                        eventReports.Add(Item.L(
                            Item.U4(ceid),
                            Item.L(reportData.ToArray())
                        ));
                    }
                }

                return new SecsMessage(6, 16)
                {
                    SecsItem = Item.L(eventReports.ToArray())
                };
            }
            catch (Exception ex)
            {
                _logger?.Error($"处理S6F15失败: {ex.Message}", message, ex);
                return new SecsMessage(6, 16) { SecsItem = Item.L() };
            }
        }

        /// <summary>
        /// 处理S6F19 - Individual Report Request
        /// </summary>
        private async Task<SecsMessage> ProcessS6F19Async(SecsMessage message)
        {
            await Task.CompletedTask;

            try
            {
                // 获取请求的报告ID
                var requestedRptids = new List<uint>();
                if (message.SecsItem != null && message.SecsItem.Format == SecsFormat.List)
                {
                    for (int i = 0; i < message.SecsItem.Count; i++)
                    {
                        var item = message.SecsItem[i];
                        if (item.Format == SecsFormat.U4)
                        {
                            requestedRptids.Add(Convert.ToUInt32(item.ToString()));
                        }
                    }
                }

                // 构建报告响应
                var reports = new List<Item>();
                foreach (var rptid in requestedRptids)
                {
                    if (_reportVariables.TryGetValue(rptid, out var variables))
                    {
                        var reportData = new List<Item>();
                        foreach (var vid in variables)
                        {
                            var value = await GetVariableValueAsync(vid);
                            reportData.Add(value);
                        }

                        reports.Add(Item.L(reportData.ToArray()));
                    }
                    else
                    {
                        reports.Add(Item.L()); // 空报告
                    }
                }

                return new SecsMessage(6, 20)
                {
                    SecsItem = Item.L(reports.ToArray())
                };
            }
            catch (Exception ex)
            {
                _logger?.Error($"处理S6F19失败: {ex.Message}", message, ex);
                return new SecsMessage(6, 20) { SecsItem = Item.L() };
            }
        }

        /// <summary>
        /// 处理未知消息
        /// </summary>
        private async Task<SecsMessage?> ProcessUnknownMessageAsync(SecsMessage message)
        {
            await Task.CompletedTask;

            _logger?.Warning($"收到未处理的消息: S{message.S}F{message.F}");

            // 对于未知的主要消息，返回S9F1 (Unrecognized Device ID)
            // 或者根据具体情况返回适当的错误消息
            return null;
        }
        public virtual async Task<Item> OnHostCommandAsync(string command, Item parameters) { await Task.CompletedTask; return Item.L(); }
        public virtual async Task<Item> OnHostEnhancedCommandAsync(string command, string objType, Item parameters) { await Task.CompletedTask; return Item.L(); }
        public virtual async Task<SecsMessage> OnReceiveMessageAsync(SecsMessage message) { await Task.CompletedTask; return new SecsMessage(9, 1); }

        // PostEvent 方法
        public virtual void PostEvent(uint eventId, Item eventData)
        {
            // 默认实现：发送事件报告
            try
            {
                var eventMessage = new SecsMessage(6, 11)
                {
                    SecsItem = Item.L(
                        Item.U4(eventId),
                        eventData
                    )
                };
                // 这里应该发送事件，但在基类中只是空实现
            }
            catch
            {
                // 忽略错误
            }
        }

        public async IAsyncEnumerable<PrimaryMessageWrapper> GetPrimaryMessageAsync(CancellationToken cancellationToken = default)
        {
            await Task.CompletedTask;
            yield break; // 简化实现，不返回任何消息
        }

        // 数据加载方法
        public async Task<bool> LoadDVAsync(IEnumerable<DVItem> items)
        {
            await Task.CompletedTask;
            try
            {
                _dataVariables.Clear();
                foreach (var item in items)
                {
                    _dataVariables[item.Id] = item;
                }
                _logger?.Info($"已加载 {_dataVariables.Count} 个数据变量(DV)");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error($"加载数据变量失败: {ex.Message}", null, ex);
                return false;
            }
        }

        public async Task<bool> LoadSVAsync(IEnumerable<SVItem> items)
        {
            await Task.CompletedTask;
            try
            {
                _statusVariables.Clear();
                foreach (var item in items)
                {
                    _statusVariables[item.Id] = item;
                }
                _logger?.Info($"已加载 {_statusVariables.Count} 个状态变量(SV)");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error($"加载状态变量失败: {ex.Message}", null, ex);
                return false;
            }
        }

        public async Task<bool> LoadECAsync(IEnumerable<ECItem> items)
        {
            await Task.CompletedTask;
            try
            {
                _equipmentConstants.Clear();
                foreach (var item in items)
                {
                    _equipmentConstants[item.Id] = item;
                }
                _logger?.Info($"已加载 {_equipmentConstants.Count} 个设备常数(EC)");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error($"加载设备常数失败: {ex.Message}", null, ex);
                return false;
            }
        }

        public async Task<bool> LoadEventAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger?.Warning($"事件定义文件不存在: {filePath}");
                    return false;
                }

                var lines = await File.ReadAllLinesAsync(filePath);
                var events = new List<EventItem>();

                // 跳过标题行
                for (int i = 1; i < lines.Length; i++)
                {
                    var parts = lines[i].Split(',');
                    if (parts.Length >= 3)
                    {
                        if (uint.TryParse(parts[0], out uint id) && bool.TryParse(parts[2], out bool enabled))
                        {
                            // TODO:需要测试确认
                            events.Add(new EventItem(id, parts[1].Trim('"'), enabled, ""));
                        }
                    }
                }

                return await LoadEventAsync(events);
            }
            catch (Exception ex)
            {
                _logger?.Error($"从文件加载事件定义失败: {ex.Message}", null, ex);
                return false;
            }
        }

        public async Task<bool> LoadEventAsync(IEnumerable<EventItem> items)
        {
            await Task.CompletedTask;
            try
            {
                _events.Clear();
                foreach (var item in items)
                {
                    _events[item.Id] = item;
                }
                _logger?.Info($"已加载 {_events.Count} 个事件定义");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error($"加载事件定义失败: {ex.Message}", null, ex);
                return false;
            }
        }

        public async Task<bool> LoadAlarmAsync(IEnumerable<AlarmItem> items)
        {
            await Task.CompletedTask;
            try
            {
                _alarms.Clear();
                foreach (var item in items)
                {
                    _alarms[item.Id] = item;
                }
                _logger?.Info($"已加载 {_alarms.Count} 个报警定义");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error($"加载报警定义失败: {ex.Message}", null, ex);
                return false;
            }
        }

        // 事件和报警方法
        public void LinkEventReport(uint eventId, uint reportId)
        {
            try
            {
                _eventReportLinks[eventId] = reportId;
                _logger?.Info($"事件与报告关联: EventID={eventId} -> ReportID={reportId}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"关联事件与报告失败: EventID={eventId}, ReportID={reportId}, {ex.Message}", null, ex);
            }
        }

        public async Task PostEventAsync(uint eventId, Item? eventData = null)
        {
            try
            {
                if (!_events.TryGetValue(eventId, out var eventItem) || !eventItem.IsEnabled)
                {
                    _logger?.Warning($"事件未定义或未启用: EventID={eventId}");
                    return;
                }

                // 构建事件报告数据
                var reportData = new List<Item>();

                // 如果有关联的报告，添加相关变量
                if (_eventReportLinks.TryGetValue(eventId, out var reportId))
                {
                    if (_reportVariables.TryGetValue(reportId, out var variables))
                    {
                        foreach (var vid in variables)
                        {
                            var variableValue = await GetVariableValueAsync(vid);
                            reportData.Add(variableValue);
                        }
                    }
                }

                // 添加事件特定数据
                if (eventData != null)
                {
                    reportData.Add(eventData);
                }

                // 发送S6F11事件报告
                await SendEventReportAsync(eventId, reportData);

                EventPosted?.Invoke(this, eventId);
                _logger?.Info($"事件已发送: EventID={eventId}, Name={eventItem.Name}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"发送事件失败: EventID={eventId}, {ex.Message}", null, ex);
            }
        }

        public void ReportAppendVID(uint reportId, uint vid)
        {
            try
            {
                if (!_reportVariables.ContainsKey(reportId))
                {
                    _reportVariables[reportId] = new List<uint>();
                }

                if (!_reportVariables[reportId].Contains(vid))
                {
                    _reportVariables[reportId].Add(vid);
                    _logger?.Info($"变量已添加到报告: ReportID={reportId}, VID={vid}");
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"添加变量到报告失败: ReportID={reportId}, VID={vid}, {ex.Message}", null, ex);
            }
        }

        public async Task ClearAlarmAsync(uint alarmId, string? alarmText = null)
        {
            try
            {
                if (_activeAlarms.Contains(alarmId))
                {
                    _activeAlarms.Remove(alarmId);

                    // 发送S5F1报警清除
                    await SendAlarmReportAsync(alarmId, false, alarmText);

                    AlarmCleared?.Invoke(this, alarmId);
                    _logger?.Info($"报警已清除: AlarmID={alarmId}, Text={alarmText}");
                }
                else
                {
                    _logger?.Warning($"尝试清除不存在的报警: AlarmID={alarmId}");
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"清除报警失败: AlarmID={alarmId}, {ex.Message}", null, ex);
            }
        }

        public async Task PostAlarmAsync(uint alarmId, string? alarmText = null, Item? alarmData = null)
        {
            try
            {
                if (!_alarms.TryGetValue(alarmId, out var alarmItem) || !alarmItem.IsEnabled)
                {
                    _logger?.Warning($"报警未定义或未启用: AlarmID={alarmId}");
                    return;
                }

                if (!_activeAlarms.Contains(alarmId))
                {
                    _activeAlarms.Add(alarmId);

                    // 发送S5F1报警报告
                    await SendAlarmReportAsync(alarmId, true, alarmText ?? alarmItem.Description, alarmData);

                    AlarmPosted?.Invoke(this, alarmId);
                    _logger?.Info($"报警已发送: AlarmID={alarmId}, Name={alarmItem.Name}, Text={alarmText}");
                }
                else
                {
                    _logger?.Warning($"报警已存在，忽略重复发送: AlarmID={alarmId}");
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"发送报警失败: AlarmID={alarmId}, {ex.Message}", null, ex);
            }
        }

        // GEM 控制方法
        public async Task StartGEMAsync()
        {
            try
            {
                _logger?.Info("启动GEM服务");

                // 首先初始化默认变量
                await InitializeDefaultVariablesAsync();

                // 然后启动SECS连接
                _logger?.Info("正在建立SECS连接...");
                await ConnectAsync();

                _logger?.Info("GEM服务启动完成");
            }
            catch (Exception ex)
            {
                _logger?.Error($"启动GEM服务失败: {ex.Message}", null, ex);
                throw;
            }
        }

        public async Task StopGEMAsync()
        {
            try
            {
                _logger?.Info("停止GEM服务");

                // 取消消息处理循环
                _cancellationTokenSource?.Cancel();

                // 断开连接
                Disconnect();

                // 发送离线事件
                await SwitchOfflineAsync();

                _logger?.Info("GEM服务已停止");
            }
            catch (Exception ex)
            {
                _logger?.Error($"停止GEM服务失败: {ex.Message}", null, ex);
            }
        }

        public async Task<bool> SwitchOfflineAsync()
        {
            await Task.CompletedTask;
            try
            {
                var previousState = ControlState;
                ControlState = (int)Proj.API.ControlState.OfflineEquipment;
                IsOnline = false;

                _logger?.Info($"设备状态切换: {(Proj.API.ControlState)previousState} -> OfflineEquipment");

                // 发送状态变化事件
                await PostEventAsync(1, Item.L(
                    Item.U1((byte)previousState),
                    Item.U1((byte)ControlState)
                ));

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error($"切换到离线状态失败: {ex.Message}", null, ex);
                return false;
            }
        }

        public async Task<bool> SwitchOnlineAsync()
        {
            await Task.CompletedTask;
            try
            {
                var previousState = ControlState;
                ControlState = IsLocal ? (int)Proj.API.ControlState.OnlineLocal : (int)Proj.API.ControlState.OnlineRemote;
                IsOnline = true;

                _logger?.Info($"设备状态切换: {(Proj.API.ControlState)previousState} -> {(Proj.API.ControlState)ControlState}");

                // 发送状态变化事件
                await PostEventAsync(2, Item.L(
                    Item.U1((byte)previousState),
                    Item.U1((byte)ControlState)
                ));

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error($"切换到在线状态失败: {ex.Message}", null, ex);
                return false;
            }
        }

        public async Task<bool> SwitchLocalAsync()
        {
            await Task.CompletedTask;
            try
            {
                var previousState = ControlState;
                IsLocal = true;

                if (IsOnline)
                {
                    ControlState = (int)Proj.API.ControlState.OnlineLocal;

                    _logger?.Info($"设备状态切换: {(Proj.API.ControlState)previousState} -> OnlineLocal");

                    // 发送状态变化事件
                    await PostEventAsync(3, Item.L(
                        Item.U1((byte)previousState),
                        Item.U1((byte)ControlState)
                    ));
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error($"切换到本地控制失败: {ex.Message}", null, ex);
                return false;
            }
        }

        public async Task<bool> SwitchRemoteAsync()
        {
            await Task.CompletedTask;
            try
            {
                var previousState = ControlState;
                IsLocal = false;

                if (IsOnline)
                {
                    ControlState = (int)Proj.API.ControlState.OnlineRemote;

                    _logger?.Info($"设备状态切换: {(Proj.API.ControlState)previousState} -> OnlineRemote");

                    // 发送状态变化事件
                    await PostEventAsync(4, Item.L(
                        Item.U1((byte)previousState),
                        Item.U1((byte)ControlState)
                    ));
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error($"切换到远程控制失败: {ex.Message}", null, ex);
                return false;
            }
        }

        /// <summary>
        /// 初始化默认变量
        /// </summary>
        public async Task InitializeDefaultVariablesAsync()
        {
            await Task.CompletedTask;

            try
            {
                // 初始化基本状态变量
                var defaultSVs = new List<SVItem>
                {
                    new( 1, "ControlState",  ControlState),
                    new(2, "PreviousControlState",  1 ),
                    new(3, "EventsEnabled", true ),
                    new( 4, "AlarmsEnabled", true ),
                    new( 5,  "Clock", DateTime.Now.ToString("yyyyMMddHHmmss") )
                };

                await LoadSVAsync(defaultSVs);

                // 初始化基本数据变量
                var defaultDVs = new List<DVItem>
                {
                    new( 1, "EquipmentName",  MDLN ),
                    new(2, "SoftwareRevision",SoftRev ),
                    new( 3,  "EquipmentState", "Online" )
                };

                await LoadDVAsync(defaultDVs);

                _logger?.Info("默认变量初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.Error($"初始化默认变量失败: {ex.Message}", null, ex);
            }
        }

        #region SEMI上报功能扩展

        /// <summary>
        /// 发送载体事件上报
        /// </summary>
        /// <param name="carrierId">载体ID</param>
        /// <param name="portId">端口ID</param>
        /// <param name="eventType">事件类型（到达、离开等）</param>
        public async Task PostCarrierEventAsync(string carrierId, string portId, string eventType)
        {
            try
            {
                var eventData = Item.L(
                    Item.A(carrierId),
                    Item.A(portId),
                    Item.A(eventType),
                    Item.A(DateTime.Now.ToString("yyyyMMddHHmmss"))
                );

                await PostEventAsync(100, eventData); // 假设100为载体事件ID
                _logger?.Info($"载体事件已发送: CarrierID={carrierId}, Port={portId}, Type={eventType}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"发送载体事件失败: {ex.Message}", null, ex);
            }
        }

        /// <summary>
        /// 发送搬送事件上报
        /// </summary>
        /// <param name="transferId">搬送ID</param>
        /// <param name="sourcePort">源端口</param>
        /// <param name="destPort">目标端口</param>
        /// <param name="status">搬送状态</param>
        public async Task PostTransferEventAsync(string transferId, string sourcePort, string destPort, string status)
        {
            try
            {
                var eventData = Item.L(
                    Item.A(transferId),
                    Item.A(sourcePort),
                    Item.A(destPort),
                    Item.A(status),
                    Item.A(DateTime.Now.ToString("yyyyMMddHHmmss"))
                );

                await PostEventAsync(200, eventData); // 假设200为搬送事件ID
                _logger?.Info($"搬送事件已发送: TransferID={transferId}, {sourcePort}->{destPort}, Status={status}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"发送搬送事件失败: {ex.Message}", null, ex);
            }
        }

        /// <summary>
        /// 发送端口状态事件上报
        /// </summary>
        /// <param name="portId">端口ID</param>
        /// <param name="portStatus">端口状态</param>
        /// <param name="accessMode">访问模式</param>
        public async Task PostPortEventAsync(string portId, string portStatus, string accessMode)
        {
            try
            {
                var eventData = Item.L(
                    Item.A(portId),
                    Item.A(portStatus),
                    Item.A(accessMode),
                    Item.A(DateTime.Now.ToString("yyyyMMddHHmmss"))
                );

                await PostEventAsync(300, eventData); // 假设300为端口事件ID
                _logger?.Info($"端口事件已发送: PortID={portId}, Status={portStatus}, AccessMode={accessMode}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"发送端口事件失败: {ex.Message}", null, ex);
            }
        }

        /// <summary>
        /// 发送设备单元报警事件
        /// </summary>
        /// <param name="unitId">单元ID</param>
        /// <param name="alarmCode">报警代码</param>
        /// <param name="alarmText">报警文本</param>
        /// <param name="isSet">是否为设置报警（true）还是清除报警（false）</param>
        public async Task PostUnitAlarmEventAsync(string unitId, uint alarmCode, string alarmText, bool isSet)
        {
            try
            {
                var eventData = Item.L(
                    Item.A(unitId),
                    Item.U4(alarmCode),
                    Item.A(alarmText),
                    Item.Boolean(isSet),
                    Item.A(DateTime.Now.ToString("yyyyMMddHHmmss"))
                );

                if (isSet)
                {
                    await PostAlarmAsync(alarmCode, $"{unitId}: {alarmText}", eventData);
                }
                else
                {
                    await ClearAlarmAsync(alarmCode, $"{unitId}: {alarmText}");
                }

                _logger?.Info($"单元报警事件已发送: UnitID={unitId}, AlarmCode={alarmCode}, IsSet={isSet}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"发送单元报警事件失败: {ex.Message}", null, ex);
            }
        }

        /// <summary>
        /// 发送ID读写事件上报
        /// </summary>
        /// <param name="carrierId">载体ID</param>
        /// <param name="readerId">读写器ID</param>
        /// <param name="operation">操作类型（读取、写入）</param>
        /// <param name="result">操作结果</param>
        public async Task PostIDREventAsync(string carrierId, string readerId, string operation, string result)
        {
            try
            {
                var eventData = Item.L(
                    Item.A(carrierId),
                    Item.A(readerId),
                    Item.A(operation),
                    Item.A(result),
                    Item.A(DateTime.Now.ToString("yyyyMMddHHmmss"))
                );

                await PostEventAsync(400, eventData); // 假设400为ID读写事件ID
                _logger?.Info($"ID读写事件已发送: CarrierID={carrierId}, Reader={readerId}, Operation={operation}, Result={result}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"发送ID读写事件失败: {ex.Message}", null, ex);
            }
        }

        /// <summary>
        /// 发送手动操作事件上报
        /// </summary>
        /// <param name="operatorId">操作员ID</param>
        /// <param name="operation">操作内容</param>
        /// <param name="targetObject">操作对象</param>
        public async Task PostManualOperationEventAsync(string operatorId, string operation, string targetObject)
        {
            try
            {
                var eventData = Item.L(
                    Item.A(operatorId),
                    Item.A(operation),
                    Item.A(targetObject),
                    Item.A(DateTime.Now.ToString("yyyyMMddHHmmss"))
                );

                await PostEventAsync(500, eventData); // 假设500为手动操作事件ID
                _logger?.Info($"手动操作事件已发送: Operator={operatorId}, Operation={operation}, Target={targetObject}");
            }
            catch (Exception ex)
            {
                _logger?.Error($"发送手动操作事件失败: {ex.Message}", null, ex);
            }
        }

        /// <summary>
        /// 批量更新状态变量并发送事件
        /// </summary>
        /// <param name="updates">状态变量更新列表</param>
        public async Task BatchUpdateStatusVariablesAsync(Dictionary<uint, object> updates)
        {
            try
            {
                var updatedVariables = new List<Item>();

                foreach (var update in updates)
                {
                    if (_statusVariables.TryGetValue(update.Key, out var sv))
                    {
                        var oldValue = sv.Value;
                        sv.Value = update.Value;

                        updatedVariables.Add(Item.L(
                            Item.U4(update.Key),
                            ConvertValueToSecsItem(oldValue),
                            ConvertValueToSecsItem(update.Value)
                        ));
                    }
                }

                if (updatedVariables.Count > 0)
                {
                    var eventData = Item.L(updatedVariables.ToArray());
                    await PostEventAsync(600, eventData); // 假设600为批量状态更新事件ID
                    _logger?.Info($"批量状态变量更新事件已发送: 更新了{updatedVariables.Count}个变量");
                }
            }
            catch (Exception ex)
            {
                _logger?.Error($"批量更新状态变量失败: {ex.Message}", null, ex);
            }
        }

        #endregion

        public void Dispose()
        {
            if (!_disposed)
            {
                Console.WriteLine("[DEBUG] 开始释放 GemEquipmentImpl 资源...");

                // 取消操作
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();

                // 取消事件订阅
                if (Connection != null)
                {
                    Connection.ConnectionChanged -= OnConnectionStateChanged;
                }

                // 销毁连接对象
                SecsGem?.Dispose();
                Connection?.DisposeAsync();

                _disposed = true;
                Console.WriteLine("[DEBUG] GemEquipmentImpl 资源释放完成");
            }
            GC.SuppressFinalize(this);
        }


    }

    /// <summary>
    /// SECS/GEM 日志记录器实现
    /// 使用 Proj.Log.Logger 进行日志记录
    /// </summary>
    public sealed class SecsLogger : ISecsGemLogger
    {
        private readonly Proj.Log.Logger _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public SecsLogger()
        {
            _logger = Proj.Log.Logger.Instance;
        }

        /// <summary>
        /// 记录接收到的SECS消息
        /// </summary>
        /// <param name="msg">SECS消息</param>
        /// <param name="id">消息ID</param>
        public void MessageIn(SecsMessage msg, int id)
        {
            try
            {
                var logMessage = $"[SECS消息接收] ID:{id} S{msg.S}F{msg.F} " +
                               $"ReplyExpected:{msg.ReplyExpected} " +
                               $"Data:{FormatSecsData(msg.SecsItem)}";

                _logger.HostLog(logMessage);
            }
            catch (Exception ex)
            {
                // 避免日志记录本身出错影响主流程
                Console.WriteLine($"[SecsLogger] MessageIn 日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录发送的SECS消息
        /// </summary>
        /// <param name="msg">SECS消息</param>
        /// <param name="id">消息ID</param>
        public void MessageOut(SecsMessage msg, int id)
        {
            try
            {
                var logMessage = $"[SECS消息发送] ID:{id} S{msg.S}F{msg.F} " +
                               $"ReplyExpected:{msg.ReplyExpected} " +
                               $"Data:{FormatSecsData(msg.SecsItem)}";

                _logger.HostLog(logMessage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SecsLogger] MessageOut 日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="msg">日志消息</param>
        public void Info(string msg)
        {
            try
            {
                var logMessage = $"[SECS信息] {msg}";
                _logger.OperationLog(logMessage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SecsLogger] Info 日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="msg">日志消息</param>
        public void Warning(string msg)
        {
            try
            {
                var logMessage = $"[SECS警告] {msg}";
                _logger.AlarmLog(logMessage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SecsLogger] Warning 日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="msg">错误消息</param>
        /// <param name="message">相关的SECS消息</param>
        /// <param name="ex">异常信息</param>
        public void Error(string msg, SecsMessage? message, Exception? ex)
        {
            try
            {
                var logMessage = $"[SECS错误] {msg}";

                if (message != null)
                {
                    logMessage += $" | 消息: S{message.S}F{message.F}";
                }

                if (ex != null)
                {
                    logMessage += $" | 异常: {ex.Message}";
                    logMessage += $" | 堆栈: {ex.StackTrace}";
                }

                _logger.ExceptionLog(logMessage);
            }
            catch (Exception logEx)
            {
                Console.WriteLine($"[SecsLogger] Error 日志记录失败: {logEx.Message}");
            }
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="msg">调试消息</param>
        public void Debug(string msg)
        {
            try
            {
                var logMessage = $"[SECS调试] {msg}";
                _logger.OperationLog(logMessage);

                // 同时输出到控制台便于调试
                Console.WriteLine($"[DEBUG] {DateTime.Now:HH:mm:ss.fff} {msg}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SecsLogger] Debug 日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 格式化SECS数据项为字符串
        /// </summary>
        /// <param name="item">SECS数据项</param>
        /// <returns>格式化后的字符串</returns>
        private string FormatSecsData(Item? item)
        {
            if (item == null)
                return "null";

            try
            {
                // 根据数据类型进行格式化
                return item.Format switch
                {
                    SecsFormat.List => $"List[{item.Count}]",
                    SecsFormat.ASCII => $"ASCII:\"{item}\"",
                    SecsFormat.Binary => $"Binary[{item.Count}]",
                    SecsFormat.Boolean => $"Boolean:{item}",
                    SecsFormat.I1 => $"I1:{item}",
                    SecsFormat.I2 => $"I2:{item}",
                    SecsFormat.I4 => $"I4:{item}",
                    SecsFormat.I8 => $"I8:{item}",
                    SecsFormat.U1 => $"U1:{item}",
                    SecsFormat.U2 => $"U2:{item}",
                    SecsFormat.U4 => $"U4:{item}",
                    SecsFormat.U8 => $"U8:{item}",
                    SecsFormat.F4 => $"F4:{item}",
                    SecsFormat.F8 => $"F8:{item}",
                    _ => $"{item.Format}[{item.Count}]"
                };
            }
            catch (Exception ex)
            {
                return $"FormatError:{ex.Message}";
            }
        }
    }
}
