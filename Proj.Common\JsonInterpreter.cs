﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Newtonsoft.Json;

namespace Common
{
    /// <summary>
    /// Json字符串处理
    /// </summary>
    public class JsonInterpreter
    {

        #region 变量定义
        private static JsonInterpreter obj = null;
        private static object instLock = new object();
        #endregion

        #region 构造函数
        public JsonInterpreter()
        { }
        #endregion

        #region 实例

        /// <summary>
        /// 实例方法
        /// </summary>
        public static JsonInterpreter Instance()
        {
            if (obj == null)
            {
                lock (instLock)
                {
                    if (obj == null)
                    {
                        obj = new JsonInterpreter();
                    }
                }
            }
            return obj;
        }
        #endregion

        public T DeSerialize<T>(string jsonStr)
        {
            return JsonConvert.DeserializeObject<T>(jsonStr);
        }
        public string Serialize<T>(T obj)
        {
            return JsonConvert.SerializeObject(obj);
        }
    }
}
