{"format": 1, "restore": {"\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj": {}}, "projects": {"\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj": {"version": "1.2.0", "restore": {"projectUniqueName": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj", "projectName": "Proj.API", "projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\Proj.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.API\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj": {"projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.0, )"}, "Secs4Net": {"target": "Package", "version": "[2.4.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj", "projectName": "Proj.<PERSON><PERSON>", "projectPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\Proj.Log.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "\\\\vmware-host\\Shared Folders\\gitsource\\STKC.Net8\\Proj.Log\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"log4net": {"target": "Package", "version": "[2.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}