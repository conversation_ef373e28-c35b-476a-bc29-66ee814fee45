﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Csla;
using System.ComponentModel;
using SS.CslaBase;

namespace Proj.Entity
{
    #region TpLocation and List
    [Serializable]
    [Description("TP_LOCATION")]
    [LinqToDB.Mapping.Table("TP_LOCATION")]
    public partial class TpLocation : GEntity<TpLocation>
    {
        #region Contructor(s)

        private TpLocation()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_Address = RegisterProperty<String>(p => p.Address);
        private static readonly PropertyInfo<String> pty_Name = RegisterProperty<String>(p => p.Name);
        private static readonly PropertyInfo<String> pty_Type = RegisterProperty<String>(p => p.Type);
        private static readonly PropertyInfo<Int32> pty_DispColumn = RegisterProperty<Int32>(p => p.DispColumn);
        private static readonly PropertyInfo<Int32> pty_DispRow = RegisterProperty<Int32>(p => p.DispRow);
        private static readonly PropertyInfo<String> pty_CarrierId = RegisterProperty<String>(p => p.CarrierId);
        private static readonly PropertyInfo<Int32> pty_IsOccupied = RegisterProperty<Int32>(p => p.IsOccupied);
        private static readonly PropertyInfo<Int32> pty_IsProhibited = RegisterProperty<Int32>(p => p.IsProhibited);
        private static readonly PropertyInfo<Int32> pty_IsReserved = RegisterProperty<Int32>(p => p.IsReserved);
        private static readonly PropertyInfo<String> pty_ZoneName = RegisterProperty<String>(p => p.ZoneName);
        private static readonly PropertyInfo<String> pty_Comment = RegisterProperty<String>(p => p.Comment);
        private static readonly PropertyInfo<Int32> pty_Order = RegisterProperty<Int32>(p => p.Order);
        #endregion

        /// <summary>
        /// Address 储位编号(PLC)
        /// </summary>
        [Description("Address")]
        [LinqToDB.Mapping.Column("Address")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public String Address
        {
            get { return GetProperty(pty_Address); }
            set { SetProperty(pty_Address, value); }
        }
        /// <summary>
        /// Name 储位名
        /// </summary>
        [Description("Name")]
        [LinqToDB.Mapping.Column("Name")]
        public String Name
        {
            get { return GetProperty(pty_Name); }
            set { SetProperty(pty_Name, value); }
        }
        /// <summary>
        /// Type 位置类型 shelf/port......
        /// </summary>
        [Description("Type")]
        [LinqToDB.Mapping.Column("Type")]
        public String Type
        {
            get { return GetProperty(pty_Type); }
            set { SetProperty(pty_Type, value); }
        }
        /// <summary>
        /// DispColumn
        /// </summary>
        [Description("DispColumn")]
        [LinqToDB.Mapping.Column("Disp_Column")]
        public Int32 DispColumn
        {
            get { return GetProperty(pty_DispColumn); }
            set { SetProperty(pty_DispColumn, value); }
        }
        /// <summary>
        /// DispRow
        /// </summary>
        [Description("DispRow")]
        [LinqToDB.Mapping.Column("Disp_Row")]
        public Int32 DispRow
        {
            get { return GetProperty(pty_DispRow); }
            set { SetProperty(pty_DispRow, value); }
        }
        /// <summary>
        /// CarrierID
        /// </summary>
        [Description("CarrierID")]
        [LinqToDB.Mapping.Column("Carrier_ID")]
        public String CarrierId
        {
            get { return GetProperty(pty_CarrierId); }
            set { SetProperty(pty_CarrierId, value); }
        }
        /// <summary>
        /// IsOccupied 是否占用
        /// </summary>
        [Description("IsOccupied")]
        [LinqToDB.Mapping.Column("Is_Occupied")]
        public Int32 IsOccupied
        {
            get { return GetProperty(pty_IsOccupied); }
            set
            {
                SetProperty(pty_IsOccupied, value);
            }
        }
        /// <summary>
        /// IsProhibited 是否占用
        /// </summary>
        [Description("IsProhibited")]
        [LinqToDB.Mapping.Column("Is_Prohibited")]
        public Int32 IsProhibited
        {
            get { return GetProperty(pty_IsProhibited); }
            set { SetProperty(pty_IsProhibited, value); }
        }
        /// <summary>
        /// IsReserved 是否预留
        /// </summary>
        [Description("IsReserved")]
        [LinqToDB.Mapping.Column("Is_Reserved")]
        public Int32 IsReserved
        {
            get { return GetProperty(pty_IsReserved); }
            set { SetProperty(pty_IsReserved, value); }
        }
        /// <summary>
        /// ZoneName  储位名称
        /// </summary>
        [Description("ZoneName")]
        [LinqToDB.Mapping.Column("Zone_Name")]
        public String ZoneName
        {
            get { return GetProperty(pty_ZoneName); }
            set { SetProperty(pty_ZoneName, value); }
        }
        /// <summary>
        /// Comment 注释
        /// </summary>
        [Description("Comment")]
        [LinqToDB.Mapping.Column("Comment")]
        public String Comment
        {
            get { return GetProperty(pty_Comment); }
            set { SetProperty(pty_Comment, value); }
        }

        /// <summary>
        /// Order
        /// </summary>
        [Description("Order")]
        [LinqToDB.Mapping.Column("Order")]
        public Int32 Order
        {
            get { return GetProperty(pty_Order); }
            set { SetProperty(pty_Order, value); }
        }

        #endregion


        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Address, "Address是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Address, 5, "Address不能超过5个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Name, 255, "Name不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Type, 1, "Type不能超过1个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_DispColumn, "DispColumn是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_DispRow, "DispRow是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CarrierId, 255, "CarrierID不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_IsOccupied, "IsOccupied是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_IsProhibited, "IsProhibited是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_IsReserved, "IsReserved是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_ZoneName, 255, "ZoneName不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Comment, 500, "Comment不能超过500个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Order, 5, "Order不能超过5个字符"));

            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Address; }
        #endregion
    }       
        
    [Serializable]
    public partial class TpLocationList : GEntityList<TpLocationList, TpLocation>
    {
        private TpLocationList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
