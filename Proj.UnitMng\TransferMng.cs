﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


using Proj.Entity;
using Proj.DB;
using Proj.CacheData;
using Proj.DataTypeDef;
using Proj.History;

namespace Proj.UnitMng
{
    public class TransferMng
    {
        private static TransferMng m_Instanse;
        private static readonly object mSyncObject = new object();
        private TransferMng() { }
        public static TransferMng Instance
        {
            get
            {
                if (m_Instanse == null)
                {
                    lock (mSyncObject)
                    {
                        if (m_Instanse == null)
                        {
                            m_Instanse = new TransferMng();
                        }
                    }
                }
                return m_Instanse;
            }
        }
        private uint innerTransferCmdCounter = 0;
        public string GenScanCommandID()
        {
            if (innerTransferCmdCounter == 999)
            {
                innerTransferCmdCounter = 0;
            }
            
            StringBuilder sb = new StringBuilder();
            sb.Append("SCAN");
            sb.Append(DateTime.Now.ToString("yyyyMMddHHmmss"));
            sb.Append((++innerTransferCmdCounter).ToString("D3"));
            return sb.ToString();
        }

        public string GenManualCommandID()
        {
            if (innerTransferCmdCounter == 999)
            {
                innerTransferCmdCounter = 0;
            }

            StringBuilder sb = new StringBuilder();
            sb.Append("MANUAL");
            sb.Append(DateTime.Now.ToString("yyyyMMddHHmmss"));
            sb.Append((++innerTransferCmdCounter).ToString("D3"));
            return sb.ToString();
        }

        public bool Initialize()
        {
            bool bRes = false;
            try
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    TpTransferList tpTransferList = DbTransfer.Instance.GetDbTransferList();
                    GlobalData.Instance.gbEnhancedTransfers.Clear();
                    foreach (TpTransfer tpTransfer in tpTransferList)
                    {
                        EnhancedTransferCommand eTransferCmd = new EnhancedTransferCommand();
                        eTransferCmd.strCommandName = tpTransfer.CommandType;
                        eTransferCmd.strCommandID = tpTransfer.Id;
                        eTransferCmd.u2Priority = (uint)tpTransfer.Priority;
                        eTransferCmd.strCarrierID = tpTransfer.CarrierId;
                        eTransferCmd.strCarrierLoc = tpTransfer.SourceLocation;
                        eTransferCmd.strDest = tpTransfer.DestLocation;
                        eTransferCmd.strStockerCraneID = tpTransfer.CraneName;
                        eTransferCmd.transferType = (TransferType)Convert.ToInt32(tpTransfer.TransferType);
                        eTransferCmd.transferState = (TransferState)int.Parse(tpTransfer.State);
                        eTransferCmd.strSourceZone = LocationMng.Instance.GetLocationZone(tpTransfer.SourceLocation);
                        eTransferCmd.strDestZone = LocationMng.Instance.GetLocationZone(tpTransfer.DestLocation);

                        GlobalData.Instance.gbEnhancedTransfers.Add(eTransferCmd);
                    }
                    bRes = true;
                }
            }
            catch (Exception ex)
            {
                //记录程序异常日志
                Log.Logger.Instance.ExceptionLog("TransferMng.cs: " + ex.Message + ", Stack: " + ex.StackTrace);
            }

            return bRes;
        }

        public bool AddTransferCommandInfo(string cmdSource, EnhancedTransferCommand cmd)
        {
            if (DbTrans.Instance.AddTransferCommand(cmdSource, ref cmd))
            {
                string strDestAddress = cmd.strDest;
                lock (GlobalData.Instance.objRWLock)
                {
                    GlobalData.Instance.gbEnhancedTransfers.Add(cmd);
                }
                HistoryWriter.Instance.CreateTransferCmd(DateTime.Now, cmd.strCommandID, cmd.strCarrierID, cmdSource,
                    cmd.strCommandName, cmd.transferType.ToString(), (int)cmd.u2Priority, cmd.strRealSource, cmd.strRealDest, (int)cmd.u2Priority);
                return true;
            }
            return false;
        }

        public void UpdateTransferCrane(string commandID, string craneID)
        {
            if (DbTrans.Instance.UpdateTransferCrane(commandID, craneID))
            {
                lock(GlobalData.Instance.objRWLock)
                {
                    foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                    {
                        if (enhancedTransferCommand.strCommandID == commandID)
                        {
                            enhancedTransferCommand.strStockerCraneID = craneID;
                            break;
                        }
                    }
                    foreach (CurrentCraneState currentCraneState in GlobalData.Instance.gbCurrentCraneState)
                    {
                        if (currentCraneState.strStockerCraneID == craneID)
                        {
                            currentCraneState.strCommandID = commandID;
                        }
                    }
                }
            }
        }

        public void UpdataTransferState(string commandID, int iFirstSecond, TransferState state, ResultCode code)
        {
            if (DbTransfer.Instance.UpdateTransferState(commandID, state))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                    {
                        if (enhancedTransferCommand.strCommandID == commandID)
                        {
                            enhancedTransferCommand.transferState = state;
                        }
                    }
                }
            }

            //switch (code)
            //{
            //    case ResultCode.None:
            //        {
            //            if (DbTransfer.Instance.UpdateTransferState(commandID, state))
            //            {
            //                lock (GlobalData.Instance.objRWLock)
            //                {
            //                    foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
            //                    {
            //                        if (enhancedTransferCommand.strCommandID == commandID)
            //                        {
            //                            enhancedTransferCommand.transferState = state;
            //                        }
            //                    }
            //                }
            //            }
            //        }
            //        break;
            //    case ResultCode.Success:
            //    case ResultCode.DuplicateID:
            //    case ResultCode.MismatchID:
            //    case ResultCode.IDReadFail:
            //    case ResultCode.TypeMismatch:
            //    case ResultCode.ShelfZoneFull:
            //    case ResultCode.OtherError:
            //    default:
            //        {
            //            HistoryWriter.Instance.CraneTransferEnd(commandID, iFirstSecond, code.ToString(), "");
            //            if (DbTransfer.Instance.DeleteTransfer(commandID))
            //            {
            //                lock(GlobalData.Instance.objRWLock)
            //                {
            //                    //目的地解除预留
            //                    string strDestAddress = "";
            //                    foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
            //                    {
            //                        if (enhancedTransferCommand.strCommandID == commandID)
            //                        {
            //                            strDestAddress = enhancedTransferCommand.strRealDest;
            //                            break;
            //                        }
            //                    }
            //                    LocationMng.Instance.UnReserveLocation(strDestAddress);
            //                }
            //            }
            //        }
            //        break;
            //}
        }

        public void UpdataTransferPriority(string commandID, int iPriority)
        {
            if (DbTransfer.Instance.UpdateTransferPriority(commandID, iPriority))
            {
                lock (GlobalData.Instance.objRWLock)
                {
                    foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                    {
                        if (enhancedTransferCommand.strCommandID == commandID)
                        {
                            enhancedTransferCommand.u2Priority = Convert.ToUInt32(iPriority);
                        }
                    }
                }
            }
        }

        private bool bCheckPortIsCanBeTranfer(EnhancedTransferCommand tempCommand)
        {
            try
            {
            //源地址检查
            if (LocationType.IoPort == tempCommand.sourceLocType)
            {
                IOPortWorkingStatus ioPortWorkingStatus = PortMng.Instance.GetIOPortWorkingState(tempCommand.strSourceZone);
                if (IOPortWorkingStatus.WaitCranePick != ioPortWorkingStatus)
                {
                    if ("source address port does not meet the conditions" != tempCommand.strDelayReason)
                    {
                        tempCommand.strDelayReason = "source address port does not meet the conditions";
                        TpTransfer tpTranfer = TpTransfer.GetById(tempCommand.strCommandID);
                        tpTranfer.DelayReason = tempCommand.strDelayReason;
                        tpTranfer.Save();
                    }
                    return false;
                }

                //新增，当CV->EQ时，如果EQ状态不满足，直接搬送到Shelf，防止CV拥堵
                if (LocationType.EqPort == tempCommand.destLocType)
                {
                    EqPortInfo eqInfo = PortMng.Instance.GetEQPortInfo(tempCommand.strDestZone);
                    if (eqInfo == null)
                    {
                        tempCommand.strDelayReason = "Can not find Eqport " + tempCommand.strDestZone;
                        TpTransfer tpTranfer = TpTransfer.GetById(tempCommand.strCommandID);
                        tpTranfer.DelayReason = tempCommand.strDelayReason;
                        tpTranfer.Save();
                        return false;
                    }
                    else if (eqInfo.eqReqStatus != EqReqStatus.LReq)
                    {
                        string strDelayReason = "Destination Eqport " + tempCommand.strDestZone + " is not LReq";
                        string strDestAddress = LocationMng.Instance.GetEmptyShelf();

                        //修改数据库中指令的目标位置
                        TpTransfer tpTranfer = TpTransfer.GetById(tempCommand.strCommandID);
                        tpTranfer.DelayReason = tempCommand.strDelayReason;
                        tpTranfer.AltLocation = strDestAddress;
                        tpTranfer.Save();

                        //修改内存中指令的目标位置
                        lock (GlobalData.Instance.objRWLock)
                        {
                            foreach (EnhancedTransferCommand cmd in GlobalData.Instance.gbEnhancedTransfers)
                            {
                                if (cmd.strCommandID == tempCommand.strCommandID)
                                {
                                    cmd.strOriginalDest = tempCommand.strRealDest;
                                    cmd.transferType = TransferType.InPort2Shelf;
                                    cmd.destLocType = LocationType.Shelf;
                                    cmd.strDest = strDestAddress;
                                    cmd.strRealDest = strDestAddress;
                                    cmd.strDestZone = LocationMng.Instance.GetLocationZone(strDestAddress);
                                    cmd.strDelayReason = strDelayReason;
                                    cmd.iAltStep = 1;
                                }
                            }
                        }
                        return true;
                    }
                }
                //新增，当CV->MGV时，如果EQ状态不满足，直接搬送到Shelf，防止CV拥堵
                else if (LocationType.IoPort == tempCommand.destLocType)
                {
                    IOPortWorkingStatus mgvPortWorkingStatus = PortMng.Instance.GetIOPortWorkingState(tempCommand.strDestZone);
                    if (IOPortWorkingStatus.WaitCranePlace != mgvPortWorkingStatus)
                    {
                        string strDelayReason = "Destination ioPort " + tempCommand.strDestZone + " is not WaitCranePlace";
                        string strDestAddress = LocationMng.Instance.GetEmptyShelf();

                        //修改数据库中指令的目标位置
                        TpTransfer tpTranfer = TpTransfer.GetById(tempCommand.strCommandID);
                        tpTranfer.DelayReason = tempCommand.strDelayReason;
                        tpTranfer.AltLocation = strDestAddress;
                        tpTranfer.Save();

                        //修改内存中指令的目标位置
                        lock (GlobalData.Instance.objRWLock)
                        {
                            foreach (EnhancedTransferCommand cmd in GlobalData.Instance.gbEnhancedTransfers)
                            {
                                if (cmd.strCommandID == tempCommand.strCommandID)
                                {
                                    cmd.strOriginalDest = tempCommand.strRealDest;
                                    cmd.transferType = TransferType.InPort2Shelf;
                                    cmd.destLocType = LocationType.Shelf;
                                    cmd.strDest = strDestAddress;
                                    cmd.strRealDest = strDestAddress;
                                    cmd.strDestZone = LocationMng.Instance.GetLocationZone(strDestAddress);
                                    cmd.strDelayReason = strDelayReason;
                                    cmd.iAltStep = 1;
                                }
                            }
                        }
                        return true;
                    }
                }
            }
            else if (LocationType.EqPort == tempCommand.sourceLocType)
            {
                EqPortInfo eqInfo = PortMng.Instance.GetEQPortInfo(tempCommand.strSourceZone);
                if (eqInfo == null || eqInfo.eqReqStatus != EqReqStatus.UReq)
                {
                    if ("Source address Eqport does not unload signal" != tempCommand.strDelayReason)
                    {
                        tempCommand.strDelayReason = "Source address Eqport does not unload signal";
                        TpTransfer tpTranfer = TpTransfer.GetById(tempCommand.strCommandID);
                        tpTranfer.DelayReason = tempCommand.strDelayReason;
                        tpTranfer.Save();
                    }
                    return false;
                }
            }

            //目的地地址检查
            if (LocationType.IoPort == tempCommand.destLocType)
            {
                IOPortWorkingStatus ioPortWorkingStatus = PortMng.Instance.GetIOPortWorkingState(tempCommand.strDestZone);
                if (IOPortWorkingStatus.WaitCranePlace != ioPortWorkingStatus)
                {
                    if ("Destination address port does not meet the conditions" != tempCommand.strDelayReason)
                    {
                        tempCommand.strDelayReason = "Destination address port does not meet the conditions";
                        TpTransfer tpTranfer = TpTransfer.GetById(tempCommand.strCommandID);
                        tpTranfer.DelayReason = tempCommand.strDelayReason;
                        tpTranfer.Save();
                    }
                    return false;
                }
            }
            else if (LocationType.EqPort == tempCommand.destLocType)
            {
                EqPortInfo eqInfo = PortMng.Instance.GetEQPortInfo(tempCommand.strDestZone);
                if (eqInfo == null || eqInfo.eqReqStatus != EqReqStatus.LReq)
                {
                    if ("Destination address Eqport does not unload signal" != tempCommand.strDelayReason)
                    {
                        tempCommand.strDelayReason = "Destination address Eqport does not unload signal";
                        TpTransfer tpTranfer = TpTransfer.GetById(tempCommand.strCommandID);
                        tpTranfer.DelayReason = tempCommand.strDelayReason;
                        tpTranfer.Save();
                    }
                    return false;
                }
            }
            }
            catch (Exception ex)
            {
                Log.Logger.Instance.ExceptionLog("TransferMng.cs:bCheckPortIsCanBeTranfer " + ex.Message + ", Stack: " + ex.StackTrace);
                return false;
            }

            return true;
        }

        public bool FindCommandByCarrierID(string strCarrierID)
        {
            foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
            {
                if (enhancedTransferCommand.strCarrierID == strCarrierID)
                {
                    return true;
                }
            }
            return false;
        }

        public EnhancedTransferCommand GetNextExecuteCommand(string strCraneID)
        {
            EnhancedTransferCommand tempCommand = null;
            lock (GlobalData.Instance.objRWLock)
            {
                int iIndex = GlobalData.Instance.gbEnhancedTransfers.Count;
                if (iIndex > 0)
                {
                    //tempCommand = GlobalData.Instance.gbEnhancedTransfers[0];
                    bool bCraneHaveCase = CraneMng.Instance.IsHaveCase();

                    for (int i = 0; i < iIndex; i++)
                    {
                        tempCommand = GlobalData.Instance.gbEnhancedTransfers[i];

                        if (bCraneHaveCase)
                        {
                            if (tempCommand.transferType == TransferType.Crane2Shelf
                                || tempCommand.transferType == TransferType.Crane2EqPort
                                || tempCommand.transferType == TransferType.Crane2OutPort)
                            {
                                //当前Command可以继续向下执行
                            }
                            else
                            {
                                tempCommand = null;
                                continue;  //当前Command不可执行，跳转判断下一个指令
                            }
                        }

                        if (tempCommand.transferType != TransferType.DoubleCheck)
                        {
                            //检查源地址和目的地
                            lock (GlobalData.Instance.objRWLock)
                            {
                                foreach (LocationInfo loc in GlobalData.Instance.gbLocations)
                                {
                                    if (loc.strAddress == tempCommand.strRealSource) //源地址必须有货
                                    {
                                        if (loc.IsProhibited || loc.IsOccupied == false)
                                        {
                                            continue;
                                        }
                                    }
                                    else if (loc.strAddress == tempCommand.strRealDest) //目的地必须无货
                                    {
                                        if (loc.IsProhibited || loc.IsOccupied)
                                        {
                                            continue;
                                        }
                                    }
                                }
                            }
                        }

                        if (!bCheckPortIsCanBeTranfer(tempCommand) 
                            || GlobalData.Instance.gbEnhancedTransfers[i].transferState != TransferState.Queue)
                        {
                            if (iIndex == (i+1))
                            {
                                tempCommand = null;
                            }
                            continue;
                        }

                        if (GlobalData.Instance.gbEnhancedTransfers[i].transferState == TransferState.Queue)
                        {
                            tempCommand = GlobalData.Instance.gbEnhancedTransfers[i];
                            break;
                        }
                    }
                }
            }
            return tempCommand;
        }

        public bool IsHaveCraneCommand(string strCraneID, string strCarrierID)
        {
            lock (GlobalData.Instance.objRWLock)
            {
                foreach (EnhancedTransferCommand enhancedTransferCommand in GlobalData.Instance.gbEnhancedTransfers)
                {
                    if (enhancedTransferCommand.strCarrierID == strCarrierID 
                        && enhancedTransferCommand.strStockerCraneID == strCraneID)
                    {
                        return true;
                    }
                }
            }

            return false;
        }
        
    }
}
