﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text; 

using Csla;
using System.ComponentModel;
using SS.CslaBase;

 
namespace Proj.Entity
{
    #region ThTransfer and List
    [Serializable]
    [Description("搬运历史")]
    [LinqToDB.Mapping.Table("TH_TRANSFER")]
    public partial class ThTransfer : GEntity<ThTransfer>
    {
        #region Contructor(s)

        private ThTransfer()
        { /* Require use of factory methods */ }

        #endregion

        #region Properties

        #region Managed backup field
        private static readonly PropertyInfo<String> pty_AltCraneName = RegisterProperty<String>(p => p.AltCraneName);
        private static readonly PropertyInfo<DateTime?> pty_AltEndTime = RegisterProperty<DateTime?>(p => p.AltEndTime);
        private static readonly PropertyInfo<String> pty_AltLocation = RegisterProperty<String>(p => p.AltLocation);
        private static readonly PropertyInfo<DateTime?> pty_AltStartTime = RegisterProperty<DateTime?>(p => p.AltStartTime);
        private static readonly PropertyInfo<Int32> pty_CalcPriority = RegisterProperty<Int32>(p => p.CalcPriority);
        private static readonly PropertyInfo<String> pty_CarrierId = RegisterProperty<String>(p => p.CarrierId);
        private static readonly PropertyInfo<String> pty_CmdId = RegisterProperty<String>(p => p.CmdId);
        private static readonly PropertyInfo<String> pty_CmdSource = RegisterProperty<String>(p => p.CmdSource);
        private static readonly PropertyInfo<String> pty_CmdState = RegisterProperty<String>(p => p.CmdState);
        private static readonly PropertyInfo<DateTime?> pty_CmdTime = RegisterProperty<DateTime?>(p => p.CmdTime);
        private static readonly PropertyInfo<String> pty_CmdType = RegisterProperty<String>(p => p.CmdType);
        private static readonly PropertyInfo<String> pty_Comment = RegisterProperty<String>(p => p.Comment);
        private static readonly PropertyInfo<DateTime?> pty_CraneEndTime = RegisterProperty<DateTime?>(p => p.CraneEndTime);
        private static readonly PropertyInfo<String> pty_CraneName = RegisterProperty<String>(p => p.CraneName);
        private static readonly PropertyInfo<DateTime?> pty_CraneStartTime = RegisterProperty<DateTime?>(p => p.CraneStartTime);
        private static readonly PropertyInfo<String> pty_DelayReason = RegisterProperty<String>(p => p.DelayReason);
        private static readonly PropertyInfo<String> pty_DestLocation = RegisterProperty<String>(p => p.DestLocation);
        private static readonly PropertyInfo<Int64> pty_Id = RegisterProperty<Int64>(p => p.Id);
        private static readonly PropertyInfo<Int32> pty_Priority = RegisterProperty<Int32>(p => p.Priority);
        private static readonly PropertyInfo<String> pty_SourceLocation = RegisterProperty<String>(p => p.SourceLocation);
        private static readonly PropertyInfo<String> pty_TransferType = RegisterProperty<String>(p => p.TransferType);
        #endregion

        /// <summary>
        /// AltCraneName
        /// </summary>
        [Description("AltCraneName")]
        [LinqToDB.Mapping.Column("Alt_Crane_Name")]
        public String AltCraneName
        {
            get { return GetProperty(pty_AltCraneName); }
            set { SetProperty(pty_AltCraneName, value); }
        }
        /// <summary>
        /// AltEndTime
        /// </summary>
        [Description("AltEndTime")]
        [LinqToDB.Mapping.Column("Alt_End_Time")]
        public DateTime? AltEndTime
        {
            get { return GetProperty(pty_AltEndTime); }
            set { SetProperty(pty_AltEndTime, value); }
        }
        /// <summary>
        /// AltLocation
        /// </summary>
        [Description("AltLocation")]
        [LinqToDB.Mapping.Column("Alt_Location")]
        public String AltLocation
        {
            get { return GetProperty(pty_AltLocation); }
            set { SetProperty(pty_AltLocation, value); }
        }
        /// <summary>
        /// AltStartTime
        /// </summary>
        [Description("AltStartTime")]
        [LinqToDB.Mapping.Column("Alt_Start_Time")]
        public DateTime? AltStartTime
        {
            get { return GetProperty(pty_AltStartTime); }
            set { SetProperty(pty_AltStartTime, value); }
        }
        /// <summary>
        /// CalcPriority
        /// </summary>
        [Description("CalcPriority")]
        [LinqToDB.Mapping.Column("Calc_Priority")]
        public Int32 CalcPriority
        {
            get { return GetProperty(pty_CalcPriority); }
            set { SetProperty(pty_CalcPriority, value); }
        }
        /// <summary>
        /// CarrierID
        /// </summary>
        [Description("CarrierID")]
        [LinqToDB.Mapping.Column("Carrier_ID")]
        public String CarrierId
        {
            get { return GetProperty(pty_CarrierId); }
            set { SetProperty(pty_CarrierId, value); }
        }
        /// <summary>
        /// CmdID
        /// </summary>
        [Description("CmdID")]
        [LinqToDB.Mapping.Column("Cmd_ID")]
        public String CmdId
        {
            get { return GetProperty(pty_CmdId); }
            set { SetProperty(pty_CmdId, value); }
        }
        /// <summary>
        /// CmdSource
        /// </summary>
        [Description("CmdSource")]
        [LinqToDB.Mapping.Column("Cmd_Source")]
        public String CmdSource
        {
            get { return GetProperty(pty_CmdSource); }
            set { SetProperty(pty_CmdSource, value); }
        }
        /// <summary>
        /// CmdState
        /// </summary>
        [Description("CmdState")]
        [LinqToDB.Mapping.Column("Cmd_State")]
        public String CmdState
        {
            get { return GetProperty(pty_CmdState); }
            set { SetProperty(pty_CmdState, value); }
        }
        /// <summary>
        /// CmdTime
        /// </summary>
        [Description("CmdTime")]
        [LinqToDB.Mapping.Column("Cmd_Time")]
        public DateTime? CmdTime
        {
            get { return GetProperty(pty_CmdTime); }
            set { SetProperty(pty_CmdTime, value); }
        }
        /// <summary>
        /// CmdType
        /// </summary>
        [Description("CmdType")]
        [LinqToDB.Mapping.Column("Cmd_Type")]
        public String CmdType
        {
            get { return GetProperty(pty_CmdType); }
            set { SetProperty(pty_CmdType, value); }
        }
        /// <summary>
        /// Comment
        /// </summary>
        [Description("Comment")]
        [LinqToDB.Mapping.Column("Comment")]
        public String Comment
        {
            get { return GetProperty(pty_Comment); }
            set { SetProperty(pty_Comment, value); }
        }
        /// <summary>
        /// CraneEndTime
        /// </summary>
        [Description("CraneEndTime")]
        [LinqToDB.Mapping.Column("Crane_End_Time")]
        public DateTime? CraneEndTime
        {
            get { return GetProperty(pty_CraneEndTime); }
            set { SetProperty(pty_CraneEndTime, value); }
        }
        /// <summary>
        /// CraneName
        /// </summary>
        [Description("CraneName")]
        [LinqToDB.Mapping.Column("Crane_Name")]
        public String CraneName
        {
            get { return GetProperty(pty_CraneName); }
            set { SetProperty(pty_CraneName, value); }
        }
        /// <summary>
        /// CraneStartTime
        /// </summary>
        [Description("CraneStartTime")]
        [LinqToDB.Mapping.Column("Crane_Start_Time")]
        public DateTime? CraneStartTime
        {
            get { return GetProperty(pty_CraneStartTime); }
            set { SetProperty(pty_CraneStartTime, value); }
        }
        /// <summary>
        /// DelayReason
        /// </summary>
        [Description("DelayReason")]
        [LinqToDB.Mapping.Column("Delay_Reason")]
        public String DelayReason
        {
            get { return GetProperty(pty_DelayReason); }
            set { SetProperty(pty_DelayReason, value); }
        }
        /// <summary>
        /// DestLocation
        /// </summary>
        [Description("DestLocation")]
        [LinqToDB.Mapping.Column("Dest_Location")]
        public String DestLocation
        {
            get { return GetProperty(pty_DestLocation); }
            set { SetProperty(pty_DestLocation, value); }
        }
        /// <summary>
        /// ID
        /// </summary>
        [Description("ID")]
        [LinqToDB.Mapping.Column("ID")]
        [LinqToDB.Mapping.PrimaryKey, DataObjectFieldAttribute(true)] 
        public Int64 Id
        {
            get { return GetProperty(pty_Id); }
            set { SetProperty(pty_Id, value); }
        }
        /// <summary>
        /// Priority
        /// </summary>
        [Description("Priority")]
        [LinqToDB.Mapping.Column("Priority")]
        public Int32 Priority
        {
            get { return GetProperty(pty_Priority); }
            set { SetProperty(pty_Priority, value); }
        }
        /// <summary>
        /// SrcLocation
        /// </summary>
        [Description("SrcLocation")]
        [LinqToDB.Mapping.Column("Source_Location")]
        public String SourceLocation
        {
            get { return GetProperty(pty_SourceLocation); }
            set { SetProperty(pty_SourceLocation, value); }
        }
        /// <summary>
        /// TransferType
        /// </summary>
        [Description("TransferType")]
        [LinqToDB.Mapping.Column("Transfer_Type")]
        public String TransferType
        {
            get { return GetProperty(pty_TransferType); }
            set { SetProperty(pty_TransferType, value); }
        }
		#endregion
     
        
        #region Methods
        protected override void AddBusinessRules()
        { 
            base.AddBusinessRules();
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_AltCraneName, 64, "AltCraneName不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_AltLocation, 64, "AltLocation不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_CalcPriority, "CalcPriority是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CarrierId, 64, "CarrierID不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CmdId, 64, "CmdID不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CmdSource, 32, "CmdSource不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CmdState, 32, "CmdState不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CmdType, 32, "CmdType不能超过32个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_Comment, 255, "Comment不能超过255个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_CraneName, 64, "CraneName不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_DelayReason, 128, "DelayReason不能超过128个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_DestLocation, 64, "DestLocation不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Id, "ID是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.Required(pty_Priority, "Priority是必填项"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_SourceLocation, 64, "SrcLocation不能超过64个字符"));
            BusinessRules.AddRule(new Csla.Rules.CommonRules.MaxLength(pty_TransferType, 32, "TransferType不能超过32个字符"));
            
            AddCustomBusinessRules();
        }
        public override string GetPrimaryKeyValue() { return this.Id.ToString(); }
        #endregion
    }       
        
    [Serializable]
    public partial class ThTransferList : GEntityList<ThTransferList, ThTransfer>
    {
        private ThTransferList() { /* Require use of factory methods */ }
    }
    
    #endregion
     
} 
